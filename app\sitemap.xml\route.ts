import { NextRequest, NextResponse } from 'next/server'
import sitemap from '../sitemap'

/**
 * Sitemap XML Route Handler
 * Ensures proper XML content-type and caching headers for Google 2025 standards
 */
export async function GET(request: NextRequest) {
  try {
    // Generate sitemap data
    const sitemapData = sitemap()
    
    // Build XML manually to ensure proper formatting
    const xmlHeader = '<?xml version="1.0" encoding="UTF-8"?>'
    const urlsetOpen = '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">'
    const urlsetClose = '</urlset>'
    
    const urls = sitemapData.map(item => {
      return `  <url>
    <loc>${item.url}</loc>
    <lastmod>${item.lastModified}</lastmod>
    <changefreq>${item.changeFrequency}</changefreq>
    <priority>${item.priority}</priority>
  </url>`
    }).join('\n')
    
    const xmlContent = `${xmlHeader}
${urlsetOpen}
${urls}
${urlsetClose}`
    
    // Return with proper XML headers
    return new NextResponse(xmlContent, {
      status: 200,
      headers: {
        'Content-Type': 'application/xml; charset=utf-8',
        'Cache-Control': 'public, max-age=3600, s-maxage=3600', // Cache for 1 hour
        'X-Robots-Tag': 'noindex', // Don't index the sitemap itself
      },
    })
    
  } catch (error) {
    console.error('Sitemap generation error:', error)
    
    // Return error response
    return new NextResponse('Sitemap generation failed', {
      status: 500,
      headers: {
        'Content-Type': 'text/plain',
      },
    })
  }
}

// Edge runtime for better performance
export const runtime = 'edge'
