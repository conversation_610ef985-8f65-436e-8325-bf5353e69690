'use client'

import { useEffect, useState } from 'react'
import { motion } from 'framer-motion'
import { 
  ArrowLeft, 
  Clock, 
  CheckCircle,
  ArrowRight,
  Users,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Target,
  Heart,
  Zap,
  BarChart3,
  AlertTriangle,
  Award,
  RefreshCw,
  UserCheck,
  Calendar,
  Gift,
  MessageCircle,
  Star
} from 'lucide-react'
import Link from 'next/link'

const retentionPhases = [
  {
    phase: "1",
    title: "The Welcome Experience",
    description: "First 30 days that determine if customers stay or leave",
    color: "blue",
    retention: "89%",
    details: [
      "Onboarding automation sequence",
      "Expectation setting and education", 
      "Quick wins and early value delivery",
      "Personal connection building"
    ]
  },
  {
    phase: "2", 
    title: "The Engagement Engine",
    description: "Months 2-6: Building habits and deepening relationships",
    color: "green",
    retention: "76%",
    details: [
      "Regular value-driven touchpoints",
      "Progress tracking and celebration",
      "Community building initiatives",
      "Feedback loops and improvements"
    ]
  },
  {
    phase: "3",
    title: "The Loyalty Loop", 
    description: "Months 7-12: Creating advocates and superfans",
    color: "purple",
    retention: "92%",
    details: [
      "VIP treatment and exclusive access",
      "Referral program activation",
      "Success story amplification",
      "Long-term relationship nurturing"
    ]
  },
  {
    phase: "4",
    title: "The Expansion Opportunity",
    description: "Year 2+: Maximizing customer lifetime value",
    color: "orange",
    retention: "94%",
    details: [
      "Upselling and cross-selling automation",
      "Premium service offerings",
      "Partnership and collaboration opportunities",
      "Lifetime value optimization"
    ]
  }
]

export default function RetentionRevolutionClient() {
  // Calculator state for retention ROI
  const [retentionData, setRetentionData] = useState({
    monthlyNewCustomers: '',
    acquisitionCost: '',
    avgCustomerValue: '',
    currentRetentionRate: '',
    targetRetentionRate: ''
  })

  // Calculate retention ROI
  const currentLTV = (parseFloat(retentionData.avgCustomerValue) || 0) * 
                     (parseFloat(retentionData.currentRetentionRate) || 0) / 100 * 12

  const improvedLTV = (parseFloat(retentionData.avgCustomerValue) || 0) * 
                      (parseFloat(retentionData.targetRetentionRate) || 0) / 100 * 12

  const ltvIncrease = improvedLTV - currentLTV
  const monthlyRevenue = (parseFloat(retentionData.monthlyNewCustomers) || 0) * ltvIncrease
  const annualRevenue = monthlyRevenue * 12

  const handleRetentionInputChange = (field: string, value: string) => {
    setRetentionData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  // Mark lesson as completed when user visits
  useEffect(() => {
    const markAsCompleted = () => {
      const saved = localStorage.getItem('genlogic-academy-progress')
      const completed = saved ? JSON.parse(saved) : []
      if (!completed.includes('retention-revolution')) {
        completed.push('retention-revolution')
        localStorage.setItem('genlogic-academy-progress', JSON.stringify(completed))
        
        // Dispatch custom event to notify other components
        window.dispatchEvent(new CustomEvent('academyProgressUpdate'))
      }
    }
    
    // Mark as completed after user has been on page for 30 seconds
    const timer = setTimeout(markAsCompleted, 30000)
    return () => clearTimeout(timer)
  }, [])

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-blue-50/30 to-background dark:from-background dark:via-blue-950/30 dark:to-background">
      {/* Navigation */}
      <div className="pt-40 pb-8">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-5xl mx-auto">
            <Link 
              href="/learn" 
              className="inline-flex items-center gap-2 bg-background/80 backdrop-blur-sm border border-border/50 rounded-2xl px-4 py-2 text-primary-600 hover:text-primary-700 font-medium transition-all duration-300 hover:shadow-md"
            >
              <ArrowLeft className="w-4 h-4" />
              Back to Academy
            </Link>
          </div>
        </div>
      </div>

      {/* Hero Section */}
      <section className="pb-16">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-5xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              {/* Lesson Meta */}
              <div className="flex items-center justify-center gap-6 mb-8">
                <div className="flex items-center gap-2 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 px-4 py-2 rounded-2xl">
                  <Heart className="w-4 h-4" />
                  <span className="font-medium text-sm">Customer Retention</span>
                </div>
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Clock className="w-4 h-4" />
                  <span className="text-sm">18 min read</span>
                </div>
                <div className="bg-muted px-3 py-1 rounded-full text-xs font-medium text-muted-foreground">
                  Business Growth
                </div>
              </div>

              {/* Main Title */}
              <h1 className="text-4xl md:text-6xl font-bold text-foreground mb-6 leading-tight">
                The Retention
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-500 to-purple-500"> Revolution:</span>
                <br />Why Chasing New Customers
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-red-500 to-orange-500"> is Killing</span> Your Business
              </h1>

              <p className="text-xl md:text-2xl text-muted-foreground mb-8 leading-relaxed max-w-4xl mx-auto">
                While most UK businesses burn cash chasing new customers, smart companies discovered the secret. 
                <strong className="text-foreground"> Acquiring new customers costs 5x more than keeping existing ones, yet 89% of businesses focus on acquisition.</strong>
                Here's the 4-phase retention system that increased customer lifetime value by 340%.
              </p>

              {/* Shocking Stats */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
                <div className="bg-gradient-to-br from-red-50 to-red-100 dark:from-red-950/30 dark:to-red-900/30 rounded-3xl p-6 border border-red-200/50 dark:border-red-800/50">
                  <div className="flex items-center gap-3 mb-3">
                    <TrendingDown className="w-6 h-6 text-red-500" />
                    <div className="text-3xl font-bold text-red-600 dark:text-red-400">5x</div>
                  </div>
                  <div className="text-sm text-red-700 dark:text-red-300 font-medium">Higher cost to acquire vs retain customers</div>
                </div>
                <div className="bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-950/30 dark:to-orange-900/30 rounded-3xl p-6 border border-orange-200/50 dark:border-orange-800/50">
                  <div className="flex items-center gap-3 mb-3">
                    <AlertTriangle className="w-6 h-6 text-orange-500" />
                    <div className="text-3xl font-bold text-orange-600 dark:text-orange-400">68%</div>
                  </div>
                  <div className="text-sm text-orange-700 dark:text-orange-300 font-medium">Of customers leave due to poor experience</div>
                </div>
                <div className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950/30 dark:to-green-900/30 rounded-3xl p-6 border border-green-200/50 dark:border-green-800/50">
                  <div className="flex items-center gap-3 mb-3">
                    <TrendingUp className="w-6 h-6 text-green-500" />
                    <div className="text-3xl font-bold text-green-600 dark:text-green-400">340%</div>
                  </div>
                  <div className="text-sm text-green-700 dark:text-green-300 font-medium">LTV increase with proper retention</div>
                </div>
              </div>
            </motion.div>

            {/* What You'll Learn Card */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-950/50 dark:to-purple-950/50 rounded-3xl p-8 border border-blue-200/50 dark:border-blue-800/50 shadow-xl"
            >
              <div className="flex items-start gap-6">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-500 rounded-2xl flex items-center justify-center flex-shrink-0">
                  <Heart className="w-8 h-8 text-white" />
                </div>
                <div className="flex-1">
                  <h3 className="text-2xl font-bold text-foreground mb-4">What You'll Master in This Lesson</h3>
                  <div className="grid md:grid-cols-2 gap-4">
                    <div className="space-y-3">
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-foreground">Why 89% of businesses waste money on acquisition</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-foreground">The 4-phase retention system that works</span>
                      </div>
                    </div>
                    <div className="space-y-3">
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-foreground">How to automate customer loyalty and advocacy</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-foreground">Case study: £180k → £612k with retention focus</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Main Content - The Acquisition Trap */}
      <section className="pb-16">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-4xl mx-auto space-y-12">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="bg-background/80 backdrop-blur-sm rounded-3xl p-8 border border-border/50 shadow-xl"
            >
              <div className="flex items-start gap-6 mb-8">
                <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-orange-500 rounded-xl flex items-center justify-center flex-shrink-0">
                  <AlertTriangle className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h2 className="text-3xl font-bold text-foreground mb-4">The Acquisition Trap That's Bleeding You Dry</h2>
                  <p className="text-lg text-muted-foreground leading-relaxed">
                    Every day, UK businesses throw money at Facebook ads, Google campaigns, and lead generation, while their existing customers quietly slip away. Here's why this strategy is financial suicide.
                  </p>
                </div>
              </div>

              <div className="bg-gradient-to-r from-red-50 to-red-100 dark:from-red-950/30 dark:to-red-900/30 rounded-2xl p-6 border border-red-200/50 dark:border-red-800/50 mb-8">
                <h3 className="text-xl font-bold text-red-700 dark:text-red-300 mb-4">The Brutal Math of Customer Acquisition</h3>
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-bold text-red-600 dark:text-red-400 mb-3">Acquisition Costs (Average UK Business)</h4>
                    <ul className="space-y-2 text-sm text-red-600 dark:text-red-400">
                      <li>• Facebook Ads: £45-120 per customer</li>
                      <li>• Google Ads: £60-180 per customer</li>
                      <li>• Sales team: £200-500 per customer</li>
                      <li>• Referral programs: £30-80 per customer</li>
                      <li>• Content marketing: £80-200 per customer</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-bold text-red-600 dark:text-red-400 mb-3">Retention Costs (Same Business)</h4>
                    <ul className="space-y-2 text-sm text-red-600 dark:text-red-400">
                      <li>• Email automation: £2-5 per customer</li>
                      <li>• SMS campaigns: £3-8 per customer</li>
                      <li>• Loyalty programs: £5-15 per customer</li>
                      <li>• Personal outreach: £10-25 per customer</li>
                      <li>• Customer success: £15-40 per customer</li>
                    </ul>
                  </div>
                </div>
              </div>

              <div className="space-y-6">
                <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
                  <h4 className="text-lg font-bold text-red-600 dark:text-red-400 mb-3">Why Most Businesses Choose the Expensive Path</h4>
                  <div className="space-y-3">
                    <div className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                      <div>
                        <strong className="text-red-600 dark:text-red-400">Instant Gratification Bias:</strong>
                        <span className="text-muted-foreground"> New customers feel like immediate progress. Retention feels like maintenance.</span>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                      <div>
                        <strong className="text-red-600 dark:text-red-400">Vanity Metrics:</strong>
                        <span className="text-muted-foreground"> "We got 100 new customers this month!" sounds better than "We kept 95% of existing customers."</span>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                      <div>
                        <strong className="text-red-600 dark:text-red-400">Marketing Industry Pressure:</strong>
                        <span className="text-muted-foreground"> Agencies make more money selling acquisition campaigns than retention systems.</span>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                      <div>
                        <strong className="text-red-600 dark:text-red-400">Lack of Systems:</strong>
                        <span className="text-muted-foreground"> Most businesses have no idea how to systematically retain customers.</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Real Case Study: The £432k Transformation */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="bg-background/80 backdrop-blur-sm rounded-3xl p-8 border border-border/50 shadow-xl"
            >
              <div className="flex items-start gap-6 mb-8">
                <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-blue-500 rounded-xl flex items-center justify-center flex-shrink-0">
                  <Award className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h2 className="text-3xl font-bold text-foreground mb-4">Case Study: How Elite Dental Increased Revenue by £432k</h2>
                  <p className="text-lg text-muted-foreground leading-relaxed">
                    Dr. James ran a successful dental practice in Bristol, but was trapped in the acquisition hamster wheel. Spending £8,000/month on ads to replace churning patients. Here's how retention focus transformed everything.
                  </p>
                </div>
              </div>

              <div className="bg-gradient-to-r from-red-50 to-red-100 dark:from-red-950/30 dark:to-red-900/30 rounded-2xl p-6 border border-red-200/50 dark:border-red-800/50 mb-8">
                <h3 className="text-xl font-bold text-red-700 dark:text-red-300 mb-4">The Acquisition Addiction</h3>
                <div className="grid md:grid-cols-3 gap-4 mb-4">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-red-600 dark:text-red-400 mb-2">£8k</div>
                    <div className="text-sm text-red-700 dark:text-red-300">Monthly ad spend</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-red-600 dark:text-red-400 mb-2">47%</div>
                    <div className="text-sm text-red-700 dark:text-red-300">Annual patient churn</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-red-600 dark:text-red-400 mb-2">£180k</div>
                    <div className="text-sm text-red-700 dark:text-red-300">Annual revenue plateau</div>
                  </div>
                </div>
                <p className="text-red-600 dark:text-red-400 text-sm">
                  "I was constantly stressed about getting new patients. Every month felt like starting from zero.
                  I was spending more on marketing than I was making in profit, but I didn't know any other way."
                </p>
              </div>

              <div className="space-y-8">
                {/* Phase 1: Welcome Experience */}
                <div className="bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-950/30 dark:to-blue-900/30 rounded-2xl p-6 border border-blue-200/50 dark:border-blue-800/50">
                  <h4 className="text-lg font-bold text-blue-700 dark:text-blue-300 mb-4">Phase 1: The Welcome Experience Revolution</h4>
                  <p className="text-blue-600 dark:text-blue-400 mb-4">
                    <strong>The Problem:</strong> New patients felt anxious and uncertain. 23% never returned after their first visit.
                  </p>

                  <div className="bg-white dark:bg-gray-800 rounded-xl p-4 mb-4">
                    <h5 className="font-bold text-blue-700 dark:text-blue-300 mb-3">The 30-Day Welcome Journey</h5>
                    <div className="space-y-4">
                      <div className="bg-blue-50 dark:bg-blue-900/30 rounded-lg p-3">
                        <strong className="text-blue-600">Day 0 (Booking Confirmation):</strong>
                        <div className="mt-2 text-sm text-blue-600">
                          "Welcome to Elite Dental! Here's what to expect at your first visit + virtual tour video.
                          We've reserved extra time to answer all your questions."
                        </div>
                      </div>
                      <div className="bg-blue-50 dark:bg-blue-900/30 rounded-lg p-3">
                        <strong className="text-blue-600">Day 1 (After First Visit):</strong>
                        <div className="mt-2 text-sm text-blue-600">
                          "How did your visit go? Here's your personalized treatment plan + educational videos
                          about your specific needs. Any questions? Reply to this message."
                        </div>
                      </div>
                      <div className="bg-blue-50 dark:bg-blue-900/30 rounded-lg p-3">
                        <strong className="text-blue-600">Day 7 (Check-in):</strong>
                        <div className="mt-2 text-sm text-blue-600">
                          "How are you feeling after your cleaning? Here are 3 tips to maintain that fresh feeling
                          + reminder about your 6-month appointment."
                        </div>
                      </div>
                      <div className="bg-blue-50 dark:bg-blue-900/30 rounded-lg p-3">
                        <strong className="text-blue-600">Day 30 (Relationship Building):</strong>
                        <div className="mt-2 text-sm text-blue-600">
                          "You're officially part of the Elite family! Here's your VIP patient portal access
                          + exclusive dental health newsletter."
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-green-100 dark:bg-green-900/30 rounded-xl p-4">
                    <h6 className="font-bold text-green-700 dark:text-green-300 mb-2">Phase 1 Results:</h6>
                    <div className="grid md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <div className="text-green-600 dark:text-green-400">✓ First-visit return rate: 77% → 94%</div>
                        <div className="text-green-600 dark:text-green-400">✓ Patient anxiety scores: -67%</div>
                      </div>
                      <div>
                        <div className="text-green-600 dark:text-green-400">✓ Treatment acceptance: +89%</div>
                        <div className="text-green-600 dark:text-green-400">✓ Referrals from new patients: +156%</div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Phase 2: Engagement Engine */}
                <div className="bg-gradient-to-r from-green-50 to-green-100 dark:from-green-950/30 dark:to-green-900/30 rounded-2xl p-6 border border-green-200/50 dark:border-green-800/50">
                  <h4 className="text-lg font-bold text-green-700 dark:text-green-300 mb-4">Phase 2: The Engagement Engine</h4>
                  <p className="text-green-600 dark:text-green-400 mb-4">
                    <strong>The Strategy:</strong> Keep patients engaged between visits with valuable content and personal touches.
                  </p>

                  <div className="bg-white dark:bg-gray-800 rounded-xl p-4 mb-4">
                    <h5 className="font-bold text-green-700 dark:text-green-300 mb-3">Monthly Engagement Touchpoints</h5>
                    <div className="grid md:grid-cols-2 gap-4">
                      <div>
                        <h6 className="font-bold text-green-600 dark:text-green-400 mb-2">Educational Content</h6>
                        <ul className="space-y-1 text-sm text-green-600 dark:text-green-400">
                          <li>• Monthly dental health newsletter</li>
                          <li>• Seasonal oral care tips</li>
                          <li>• Treatment explanation videos</li>
                          <li>• Nutrition and dental health guides</li>
                        </ul>
                      </div>
                      <div>
                        <h6 className="font-bold text-green-600 dark:text-green-400 mb-2">Personal Touches</h6>
                        <ul className="space-y-1 text-sm text-green-600 dark:text-green-400">
                          <li>• Birthday wishes with special offers</li>
                          <li>• Anniversary of first visit celebrations</li>
                          <li>• Progress photos and improvements</li>
                          <li>• Family dental health check-ins</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <div className="bg-green-50 dark:bg-green-900/30 rounded-xl p-4 mb-4">
                    <h6 className="font-bold text-green-700 dark:text-green-300 mb-2">The Habit Formation System</h6>
                    <p className="text-sm text-green-600 dark:text-green-400 mb-3">
                      Dr. James implemented a system to make dental care a positive habit rather than a dreaded chore:
                    </p>
                    <div className="space-y-2 text-sm text-green-600 dark:text-green-400">
                      <div>• <strong>Reward System:</strong> Points for keeping appointments, referrals, and good oral health</div>
                      <div>• <strong>Progress Tracking:</strong> Before/after photos showing improvements over time</div>
                      <div>• <strong>Community Building:</strong> Patient success stories and testimonials</div>
                      <div>• <strong>Gamification:</strong> Oral health challenges and family competitions</div>
                    </div>
                  </div>

                  <div className="bg-green-100 dark:bg-green-900/30 rounded-xl p-4">
                    <h6 className="font-bold text-green-700 dark:text-green-300 mb-2">Phase 2 Results:</h6>
                    <div className="grid md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <div className="text-green-600 dark:text-green-400">✓ 6-month appointment attendance: +78%</div>
                        <div className="text-green-600 dark:text-green-400">✓ Treatment plan completion: +134%</div>
                      </div>
                      <div>
                        <div className="text-green-600 dark:text-green-400">✓ Patient engagement scores: +245%</div>
                        <div className="text-green-600 dark:text-green-400">✓ Average revenue per patient: +89%</div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Phase 3: Loyalty Loop */}
                <div className="bg-gradient-to-r from-purple-50 to-purple-100 dark:from-purple-950/30 dark:to-purple-900/30 rounded-2xl p-6 border border-purple-200/50 dark:border-purple-800/50">
                  <h4 className="text-lg font-bold text-purple-700 dark:text-purple-300 mb-4">Phase 3: The Loyalty Loop</h4>
                  <p className="text-purple-600 dark:text-purple-400 mb-4">
                    <strong>The Goal:</strong> Transform satisfied patients into passionate advocates who actively promote the practice.
                  </p>

                  <div className="bg-white dark:bg-gray-800 rounded-xl p-4 mb-4">
                    <h5 className="font-bold text-purple-700 dark:text-purple-300 mb-3">VIP Patient Program</h5>
                    <div className="space-y-4">
                      <div className="bg-purple-50 dark:bg-purple-900/30 rounded-lg p-3">
                        <strong className="text-purple-600">Exclusive Benefits:</strong>
                        <div className="mt-2 space-y-1 text-sm text-purple-600">
                          <div>• Priority booking for appointments</div>
                          <div>• Complimentary teeth whitening annually</div>
                          <div>• Direct access to Dr. James for urgent questions</div>
                          <div>• Family member discounts</div>
                        </div>
                      </div>
                      <div className="bg-purple-50 dark:bg-purple-900/30 rounded-lg p-3">
                        <strong className="text-purple-600">Referral Rewards:</strong>
                        <div className="mt-2 space-y-1 text-sm text-purple-600">
                          <div>• £50 credit for each successful referral</div>
                          <div>• Bonus rewards for family referrals</div>
                          <div>• Annual "Top Referrer" recognition and prizes</div>
                          <div>• Exclusive referrer-only events</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-purple-100 dark:bg-purple-900/30 rounded-xl p-4">
                    <h6 className="font-bold text-purple-700 dark:text-purple-300 mb-2">Phase 3 Results:</h6>
                    <div className="grid md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <div className="text-purple-600 dark:text-purple-400">✓ Patient referrals: +340%</div>
                        <div className="text-purple-600 dark:text-purple-400">✓ Online reviews: +567%</div>
                      </div>
                      <div>
                        <div className="text-purple-600 dark:text-purple-400">✓ Patient lifetime value: +234%</div>
                        <div className="text-purple-600 dark:text-purple-400">✓ Word-of-mouth marketing: 78% of new patients</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-950/30 dark:to-blue-950/30 rounded-2xl p-6 border border-green-200/50 dark:border-green-800/50 mt-8">
                <h4 className="text-xl font-bold text-green-700 dark:text-green-300 mb-4">Total Transformation: £432,000 Revenue Increase</h4>
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h5 className="font-bold text-green-600 dark:text-green-400 mb-2">Financial Results:</h5>
                    <ul className="space-y-1 text-sm text-green-600 dark:text-green-400">
                      <li>• Annual revenue: £180k → £612k</li>
                      <li>• Marketing spend: £96k → £24k</li>
                      <li>• Profit margin: 15% → 42%</li>
                      <li>• Patient lifetime value: +340%</li>
                      <li><strong>• Net profit increase: £432k</strong></li>
                    </ul>
                  </div>
                  <div>
                    <h5 className="font-bold text-blue-600 dark:text-blue-400 mb-2">Business Transformation:</h5>
                    <ul className="space-y-1 text-sm text-blue-600 dark:text-blue-400">
                      <li>• Patient retention: 53% → 94%</li>
                      <li>• Referral rate: +340%</li>
                      <li>• Treatment acceptance: +89%</li>
                      <li>• Staff satisfaction: Dramatically improved</li>
                      <li><strong>• Dr. James stress level: Minimal</strong></li>
                    </ul>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Retention ROI Calculator */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              className="bg-background/80 backdrop-blur-sm rounded-3xl p-8 border border-border/50 shadow-xl"
            >
              <div className="flex items-start gap-6 mb-8">
                <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl flex items-center justify-center flex-shrink-0">
                  <BarChart3 className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h2 className="text-3xl font-bold text-foreground mb-4">Retention ROI Calculator</h2>
                  <p className="text-lg text-muted-foreground leading-relaxed">
                    Calculate exactly how much additional revenue you could generate by improving customer retention.
                  </p>
                </div>
              </div>

              <div className="bg-gradient-to-r from-orange-50 to-red-50 dark:from-orange-950/30 dark:to-red-950/30 rounded-2xl p-6 border border-orange-200/50 dark:border-orange-800/50">
                <h3 className="text-xl font-bold text-orange-700 dark:text-orange-300 mb-6">Interactive Retention Calculator</h3>

                <div className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
                      <h4 className="font-bold text-orange-600 dark:text-orange-400 mb-3">Business Metrics</h4>
                      <div className="space-y-3 text-sm">
                        <div className="flex items-center gap-2">
                          <label className="min-w-0 flex-1">Monthly new customers:</label>
                          <input
                            type="number"
                            value={retentionData.monthlyNewCustomers}
                            onChange={(e) => handleRetentionInputChange('monthlyNewCustomers', e.target.value)}
                            className="w-20 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-foreground"
                            placeholder="50"
                          />
                        </div>
                        <div className="flex items-center gap-2">
                          <label className="min-w-0 flex-1">Customer acquisition cost (£):</label>
                          <input
                            type="number"
                            value={retentionData.acquisitionCost}
                            onChange={(e) => handleRetentionInputChange('acquisitionCost', e.target.value)}
                            className="w-20 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-foreground"
                            placeholder="120"
                          />
                        </div>
                        <div className="flex items-center gap-2">
                          <label className="min-w-0 flex-1">Avg monthly customer value (£):</label>
                          <input
                            type="number"
                            value={retentionData.avgCustomerValue}
                            onChange={(e) => handleRetentionInputChange('avgCustomerValue', e.target.value)}
                            className="w-20 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-foreground"
                            placeholder="200"
                          />
                        </div>
                      </div>
                    </div>

                    <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
                      <h4 className="font-bold text-orange-600 dark:text-orange-400 mb-3">Retention Rates</h4>
                      <div className="space-y-3 text-sm">
                        <div className="flex items-center gap-2">
                          <label className="min-w-0 flex-1">Current retention rate (%):</label>
                          <input
                            type="number"
                            value={retentionData.currentRetentionRate}
                            onChange={(e) => handleRetentionInputChange('currentRetentionRate', e.target.value)}
                            className="w-20 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-foreground"
                            placeholder="60"
                          />
                        </div>
                        <div className="flex items-center gap-2">
                          <label className="min-w-0 flex-1">Target retention rate (%):</label>
                          <input
                            type="number"
                            value={retentionData.targetRetentionRate}
                            onChange={(e) => handleRetentionInputChange('targetRetentionRate', e.target.value)}
                            className="w-20 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-foreground"
                            placeholder="85"
                          />
                        </div>
                        <div className="bg-orange-50 dark:bg-orange-900/30 rounded-lg p-3">
                          <div className="text-xs text-orange-600 dark:text-orange-400">
                            <strong>Industry Benchmarks:</strong><br/>
                            • Poor retention: 40-60%<br/>
                            • Good retention: 70-80%<br/>
                            • Excellent retention: 85-95%
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-green-100 to-blue-100 dark:from-green-900/30 dark:to-blue-900/30 rounded-xl p-6 text-center border-2 border-green-300 dark:border-green-700">
                    <h4 className="text-2xl font-bold text-green-700 dark:text-green-300 mb-3">Revenue Impact</h4>
                    <div className="grid md:grid-cols-3 gap-4 mb-4">
                      <div>
                        <div className="text-lg font-bold text-blue-600 dark:text-blue-400">Current LTV</div>
                        <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">{formatCurrency(currentLTV)}</div>
                      </div>
                      <div>
                        <div className="text-lg font-bold text-green-600 dark:text-green-400">Improved LTV</div>
                        <div className="text-2xl font-bold text-green-600 dark:text-green-400">{formatCurrency(improvedLTV)}</div>
                      </div>
                      <div>
                        <div className="text-lg font-bold text-purple-600 dark:text-purple-400">LTV Increase</div>
                        <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">{formatCurrency(ltvIncrease)}</div>
                      </div>
                    </div>
                    <div className="border-t pt-4">
                      <div className="text-3xl font-bold text-green-600 dark:text-green-400 mb-2">Annual Revenue Increase</div>
                      <div className="text-5xl font-bold text-green-600 dark:text-green-400 mb-2">{formatCurrency(annualRevenue)}</div>
                      <div className="text-lg font-bold text-green-600 dark:text-green-400">Monthly: {formatCurrency(monthlyRevenue)}</div>
                    </div>
                    {annualRevenue > 0 && (
                      <div className="mt-4 p-3 bg-green-200 dark:bg-green-800/50 rounded-lg">
                        <p className="text-sm font-bold text-green-700 dark:text-green-300">
                          💡 This is pure profit increase - no additional acquisition costs required!
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Call to Action */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
              className="bg-gradient-to-r from-blue-500 to-purple-500 rounded-3xl p-8 text-white text-center"
            >
              <h2 className="text-3xl font-bold mb-4">Something's Changing</h2>
              <p className="text-xl mb-6 opacity-90">
                Smart business owners are quietly discovering what successful companies already know about customer retention. The shift is happening whether you participate or not.
              </p>
              <Link
                href="/demo"
                className="inline-flex items-center gap-2 bg-white text-blue-600 px-8 py-4 rounded-2xl font-bold text-lg hover:bg-gray-100 transition-all duration-300 shadow-lg hover:shadow-xl"
              >
                See What They Know
                <ArrowRight className="w-5 h-5" />
              </Link>
              <p className="text-sm mt-4 opacity-75">
                Get a custom retention roadmap and see exactly how to increase your customer lifetime value in 30 minutes
              </p>
            </motion.div>

            {/* Next Lesson Navigation */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.7 }}
              className="bg-background rounded-3xl border border-border shadow-lg p-8"
            >
              <h3 className="text-xl font-bold text-foreground mb-6 text-center">Continue Your Learning Journey</h3>

              <div className="grid md:grid-cols-2 gap-6">
                <Link
                  href="/learn/fundamentals/referral-engine"
                  className="group bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950/30 dark:to-green-900/30 rounded-2xl p-6 border border-green-200/50 dark:border-green-800/50 hover:shadow-lg transition-all duration-300"
                >
                  <div className="flex items-center gap-4 mb-4">
                    <div className="w-12 h-12 bg-green-100 dark:bg-green-900/50 rounded-xl flex items-center justify-center">
                      <ArrowRight className="w-6 h-6 text-green-600 group-hover:translate-x-1 transition-transform" />
                    </div>
                    <div>
                      <h4 className="font-bold text-foreground">Next Lesson</h4>
                      <p className="text-sm text-muted-foreground">Lesson 12 of 15</p>
                    </div>
                  </div>
                  <h5 className="font-semibold text-foreground mb-2">The Referral Engine</h5>
                  <p className="text-sm text-muted-foreground">
                    How to turn your customers into salespeople and build an automated referral system
                  </p>
                </Link>

                <Link
                  href="/learn"
                  className="group bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-950/30 dark:to-gray-900/30 rounded-2xl p-6 border border-gray-200/50 dark:border-gray-800/50 hover:shadow-lg transition-all duration-300"
                >
                  <div className="flex items-center gap-4 mb-4">
                    <div className="w-12 h-12 bg-gray-100 dark:bg-gray-900/50 rounded-xl flex items-center justify-center">
                      <ArrowLeft className="w-6 h-6 text-gray-600 group-hover:-translate-x-1 transition-transform" />
                    </div>
                    <div>
                      <h4 className="font-bold text-foreground">Academy</h4>
                      <p className="text-sm text-muted-foreground">All lessons</p>
                    </div>
                  </div>
                  <h5 className="font-semibold text-foreground mb-2">Back to Academy</h5>
                  <p className="text-sm text-muted-foreground">
                    View all lessons and track your progress
                  </p>
                </Link>
              </div>
            </motion.div>
          </div>
        </div>
      </section>
    </div>
  )
}
