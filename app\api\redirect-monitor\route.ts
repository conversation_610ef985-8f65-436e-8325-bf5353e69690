import { NextRequest, NextResponse } from 'next/server'
import { getVercelRedirectStats, getVercelRedirectValidation } from '@/lib/vercel-redirect-monitor'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type') || 'stats'

    switch (type) {
      case 'stats':
        const stats = getVercelRedirectStats()
        return NextResponse.json({
          success: true,
          data: stats,
          timestamp: new Date().toISOString()
        })

      case 'validation':
        const validation = getVercelRedirectValidation()
        return NextResponse.json({
          success: true,
          data: validation,
          timestamp: new Date().toISOString()
        })

      case 'health':
        const healthStats = getVercelRedirectStats()
        const healthValidation = getVercelRedirectValidation()

        const health = {
          status: 'healthy',
          environment: healthStats.isServerless ? 'serverless' : 'traditional',
          totalRedirects: healthValidation.totalRedirects,
          monitoring: healthStats.monitoring,
          allChecks: {
            destinationsValid: healthValidation.allDestinationsValid,
            noRedirectChains: healthValidation.noRedirectChains,
            noRedirectLoops: healthValidation.noRedirectLoops,
            sitemapClean: healthValidation.sitemapClean
          },
          lastCheck: new Date().toISOString()
        }

        // All checks should pass for healthy status
        const allChecksPass = Object.values(health.allChecks).every(check => check === true)
        if (!allChecksPass) {
          health.status = 'warning'
        }

        return NextResponse.json({
          success: true,
          data: health,
          timestamp: new Date().toISOString()
        })

      default:
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid type parameter. Use: stats, validation, or health'
          },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error('Redirect monitor API error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error' 
      },
      { status: 500 }
    )
  }
}

// Optional: Allow clearing logs in development
export async function DELETE(request: NextRequest) {
  if (process.env.NODE_ENV !== 'development') {
    return NextResponse.json(
      { 
        success: false, 
        error: 'Only available in development' 
      },
      { status: 403 }
    )
  }

  try {
    // Import here to avoid issues in production
    const { redirectMonitor } = await import('@/lib/redirect-monitor')
    redirectMonitor.clearLogs()
    
    return NextResponse.json({
      success: true,
      message: 'Redirect logs cleared'
    })
  } catch (error) {
    console.error('Error clearing redirect logs:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to clear logs' 
      },
      { status: 500 }
    )
  }
}
