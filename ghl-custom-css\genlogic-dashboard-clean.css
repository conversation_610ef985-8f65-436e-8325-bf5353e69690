/* ===== GENLOGIC DASHBOARD BRANDING FOR GOHIGHLEVEL ===== */
/* Based on ACTUAL GoHighLevel dashboard HTML structure */

/* Import GenLogic Font */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* ===== GENLOGIC COLOR VARIABLES ===== */
:root {
    /* Override GHL's existing color variables with GenLogic colors */
    --primary-600: #a855f7 !important;
    --primary-700: #7c3aed !important;
    --primary-500: #a855f7 !important;
    --primary-800: #6d28d9 !important;
    
    /* GenLogic specific variables */
    --genlogic-primary: #a855f7;
    --genlogic-primary-dark: #7c3aed;
    --genlogic-accent: #f97316;
    --genlogic-success: #059669;
    --genlogic-dark: #0f172a;
    --genlogic-gray: #64748b;
    --genlogic-light: #f8fafc;
    --genlogic-white: #ffffff;
    
    /* Override specific GHL variables */
    --custom-primary: #a855f7 !important;
    --custom-primary-border-color: rgba(168, 85, 247, 0.1) !important;
    --loginBtnColor: #a855f7 !important;
    --loginBtnHover: #7c3aed !important;
}

/* ===== SIDEBAR BRANDING ===== */
/* Target BOTH sidebar types in GoHighLevel */

/* Main Navigation Sidebar (#sidebar-v2) */
#sidebar-v2 {
    background: white !important;
    font-family: 'Inter', sans-serif !important;
    border-right: 1px solid #e2e8f0 !important;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.05) !important;
}

.sidebar-v2-agency {
    background: white !important;
}

/* Settings Sidebar (different structure) */
.hl_settings aside,
aside.hl_settings-sidebar {
    background: white !important;
    font-family: 'Inter', sans-serif !important;
    border-right: 1px solid #e2e8f0 !important;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.05) !important;
}

/* Agency logo container */
.agency-logo-container {
    padding: 20px !important;
    background: white !important;
    border-bottom: 1px solid #e2e8f0 !important;
}

.agency-logo {
    max-height: 50px !important;
    width: auto !important;
}

/* Location switcher */
#location-switcher-sidbar-v2 {
    background: rgba(168, 85, 247, 0.1) !important;
    border-radius: 12px !important;
    margin: 0 12px 16px 12px !important;
    padding: 12px !important;
    transition: all 0.3s ease !important;
    color: var(--genlogic-dark) !important;
}

#location-switcher-sidbar-v2:hover {
    background: rgba(168, 85, 247, 0.15) !important;
}

#location-switcher-sidbar-v2 * {
    color: var(--genlogic-dark) !important;
}

.hl_location_icon {
    background: var(--genlogic-primary) !important;
    border-radius: 8px !important;
}

.hl_switcher-loc-name {
    color: var(--genlogic-dark) !important;
    font-weight: 500 !important;
}

/* Navigation menu items - Main Sidebar */
.hl_nav-header nav a,
.hl_nav-header-without-footer nav a {
    color: var(--genlogic-gray) !important;
    font-family: 'Inter', sans-serif !important;
    font-weight: 500 !important;
    padding: 12px 16px !important;
    margin: 2px 12px !important;
    border-radius: 8px !important;
    transition: all 0.3s ease !important;
}

.hl_nav-header nav a:hover,
.hl_nav-header-without-footer nav a:hover {
    background: rgba(168, 85, 247, 0.1) !important;
    color: var(--genlogic-primary) !important;
}

.hl_nav-header nav a.active,
.hl_nav-header-without-footer nav a.active,
.hl_nav-header nav a.exact-active-class,
.hl_nav-header-without-footer nav a.exact-active-class {
    background: var(--genlogic-primary) !important;
    color: white !important;
    font-weight: 600 !important;
}

/* Force navigation text colors */
.hl_nav-header nav a span,
.hl_nav-header nav a .nav-title,
.hl_nav-header-without-footer nav a span,
.hl_nav-header-without-footer nav a .nav-title {
    color: inherit !important;
}

/* Settings Navigation - Different structure */
.hl_nav-settings nav a {
    color: var(--genlogic-gray) !important;
    font-family: 'Inter', sans-serif !important;
    font-weight: 500 !important;
    padding: 12px 16px !important;
    margin: 2px 12px !important;
    border-radius: 8px !important;
    transition: all 0.3s ease !important;
}

.hl_nav-settings nav a:hover {
    background: rgba(168, 85, 247, 0.1) !important;
    color: var(--genlogic-primary) !important;
}

.hl_nav-settings nav a.active {
    background: var(--genlogic-primary) !important;
    color: white !important;
    font-weight: 600 !important;
}

/* Menu title */
.menu-title {
    color: var(--genlogic-dark) !important;
    font-family: 'Inter', sans-serif !important;
    font-weight: 600 !important;
    padding: 16px !important;
    margin: 0 12px !important;
    border-bottom: 1px solid #e2e8f0 !important;
}

/* Back button */
#backButtonv2 {
    color: var(--genlogic-gray) !important;
    font-family: 'Inter', sans-serif !important;
    margin: 0 12px 16px 12px !important;
    padding: 12px !important;
    border-radius: 8px !important;
    transition: all 0.3s ease !important;
}

#backButtonv2:hover {
    background: rgba(168, 85, 247, 0.1) !important;
    color: var(--genlogic-primary) !important;
}

#backButtonv2 span {
    color: inherit !important;
}

/* ===== SPECIFIC NAVIGATION ITEMS ===== */
/* Target specific navigation items from the HTML */

/* Main navigation items */
#sb_ai-employee-promo,
#sb_agency-dashboard,
#sb_location-prospect,
#sb_agency-accounts,
#sb_agency-account-snapshots,
#sb_agency-account-reselling,
#sb_agency-marketplace,
#sb_agency-affiliate-portal,
#sb_agency-template-library,
#sb_agency-partners,
#sb_agency-university,
#sb_saas-education,
#sb_ghl-swag,
#sb_agency-ideas,
#sb_mobile-app-customiser,
#sb_agency-settings {
    color: var(--genlogic-gray) !important;
    font-family: 'Inter', sans-serif !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
}

/* Hover states for specific items */
#sb_ai-employee-promo:hover,
#sb_agency-dashboard:hover,
#sb_location-prospect:hover,
#sb_agency-accounts:hover,
#sb_agency-account-snapshots:hover,
#sb_agency-account-reselling:hover,
#sb_agency-marketplace:hover,
#sb_agency-affiliate-portal:hover,
#sb_agency-template-library:hover,
#sb_agency-partners:hover,
#sb_agency-university:hover,
#sb_saas-education:hover,
#sb_ghl-swag:hover,
#sb_agency-ideas:hover,
#sb_mobile-app-customiser:hover,
#sb_agency-settings:hover {
    background: rgba(168, 85, 247, 0.1) !important;
    color: var(--genlogic-primary) !important;
}

/* Active states */
#sb_ai-employee-promo.active,
#sb_agency-dashboard.active,
#sb_location-prospect.active,
#sb_agency-accounts.active,
#sb_agency-account-snapshots.active,
#sb_agency-account-reselling.active,
#sb_agency-marketplace.active,
#sb_agency-affiliate-portal.active,
#sb_agency-template-library.active,
#sb_agency-partners.active,
#sb_agency-university.active,
#sb_saas-education.active,
#sb_ghl-swag.active,
#sb_agency-ideas.active,
#sb_mobile-app-customiser.active,
#sb_agency-settings.active {
    background: var(--genlogic-primary) !important;
    color: white !important;
    font-weight: 600 !important;
}

/* ===== HEADER BRANDING ===== */
.hl_header {
    background: white !important;
    border-bottom: 1px solid var(--genlogic-border, #e2e8f0) !important;
    font-family: 'Inter', sans-serif !important;
}

/* Header buttons */
.hl_header--controls .btn {
    font-family: 'Inter', sans-serif !important;
    font-weight: 500 !important;
}

.btn-primary {
    background: var(--genlogic-primary) !important;
    border-color: var(--genlogic-primary) !important;
    color: white !important;
}

.btn-primary:hover {
    background: var(--genlogic-primary-dark) !important;
    border-color: var(--genlogic-primary-dark) !important;
}

/* ===== MAIN CONTENT BRANDING ===== */
.hl_wrapper {
    font-family: 'Inter', sans-serif !important;
}

/* Page titles */
.hl-display-sm-medium,
.hl-text-lg-semibold {
    color: var(--genlogic-dark) !important;
    font-family: 'Inter', sans-serif !important;
    font-weight: 700 !important;
}

/* ===== FORM ELEMENTS ===== */
/* Target the actual form elements from your HTML */
.n-input {
    font-family: 'Inter', sans-serif !important;
}

.n-input:focus-within {
    border-color: var(--genlogic-primary) !important;
    box-shadow: 0 0 0 2px rgba(168, 85, 247, 0.2) !important;
}

/* Buttons */
.n-button--default-type {
    font-family: 'Inter', sans-serif !important;
    font-weight: 500 !important;
}

.n-button--default-type:hover {
    border-color: var(--genlogic-primary) !important;
    color: var(--genlogic-primary) !important;
}

/* Primary buttons */
.n-button:not(.n-button--error-type):not(.n-button--default-type) {
    background: var(--genlogic-primary) !important;
    border-color: var(--genlogic-primary) !important;
}

.n-button:not(.n-button--error-type):not(.n-button--default-type):hover {
    background: var(--genlogic-primary-dark) !important;
    border-color: var(--genlogic-primary-dark) !important;
}

/* ===== TABS ===== */
.n-tabs-tab--active {
    color: var(--genlogic-primary) !important;
}

.n-tabs-bar {
    background: var(--genlogic-primary) !important;
}

/* ===== CARDS ===== */
.hl-card {
    font-family: 'Inter', sans-serif !important;
    border-radius: 12px !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

/* ===== SWITCHES ===== */
.n-switch[aria-checked="true"] {
    background: var(--genlogic-primary) !important;
}

/* ===== LINKS ===== */
a {
    color: var(--genlogic-primary) !important;
    font-family: 'Inter', sans-serif !important;
}

a:hover {
    color: var(--genlogic-primary-dark) !important;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .agency-logo-container {
        padding: 16px !important;
    }
    
    #location-switcher-sidbar-v2 {
        margin: 0 8px 12px 8px !important;
        padding: 8px !important;
    }
    
    .hl_nav-header-without-footer nav a {
        margin: 2px 8px !important;
        padding: 10px 12px !important;
    }
}

/* ===== FORCE INTER FONT ON KEY ELEMENTS ===== */
.n-form-item-label__text,
.hl-text-sm-medium,
.hl-text-sm-regular,
.hl-text-md-medium,
span,
div,
p,
label {
    font-family: 'Inter', sans-serif !important;
}

/* ===== END GENLOGIC DASHBOARD BRANDING ===== */
