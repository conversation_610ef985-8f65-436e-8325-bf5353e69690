# SVG to WebP Conversion Script

This script converts all SVG social media images to WebP format for better compatibility with social media platforms like LinkedIn, Facebook, and Twitter.

## Why WebP?

- **Better Compatibility**: Social media platforms prefer WebP/JPG/PNG over SVG
- **Smaller File Sizes**: WebP provides excellent compression
- **High Quality**: Maintains visual quality at smaller file sizes
- **Wide Support**: Supported by all modern browsers and social platforms

## Prerequisites

### Required:
- **Node.js** (Download from https://nodejs.org/)

### Optional (choose one):
- **ImageMagick** (Recommended - better quality)
  - Windows: Download from https://imagemagick.org/script/download.php#windows
  - macOS: `brew install imagemagick`
  - Ubuntu/Debian: `sudo apt-get install imagemagick`

- **Sharp** (Alternative if ImageMagick not available)
  - Install with: `npm install sharp`

## How to Run

### Option 1: Windows
```bash
# Double-click the batch file or run in Command Prompt:
convert-images.bat
```

### Option 2: Mac/Linux
```bash
# Make script executable and run:
chmod +x convert-images.sh
./convert-images.sh
```

### Option 3: Direct Node.js
```bash
node convert-svg-to-webp.js
```

## What Gets Converted

The script will convert these files:

### Main OG Images:
- `og-image.svg` → `og-image.webp`
- `og-blog.svg` → `og-blog.webp`
- `og-contact.svg` → `og-contact.webp`
- `og-demo.svg` → `og-demo.webp`
- `og-about.svg` → `og-about.webp`
- `og-pricing.svg` → `og-pricing.webp`
- `og-website-development.svg` → `og-website-development.webp`
- `og-business-automation.svg` → `og-business-automation.webp`

### Blog Post Images:
- `stop-working-late-automation-guide.svg` → `.webp`
- `payment-chasing-nightmare.svg` → `.webp`
- `social-media-time-trap.svg` → `.webp`
- `customer-service-automation.svg` → `.webp`
- `review-generation-system.svg` → `.webp`
- `no-show-nightmare-solution.svg` → `.webp`
- `weekend-warrior-to-family-time.svg` → `.webp`
- `nextjs-website-development.svg` → `.webp`

## After Conversion

1. **Update Metadata Files**: Change all `.svg` references to `.webp` in:
   - `app/layout.tsx`
   - `app/blog/page.tsx`
   - `app/website-development/page.tsx`
   - All blog post files in `app/blog/posts/`

2. **Test Social Sharing**: Use tools like:
   - LinkedIn Post Inspector
   - Facebook Sharing Debugger
   - Twitter Card Validator

3. **Deploy**: Push changes to your hosting platform

## Output

The script will show:
- ✅ Successfully converted files
- ❌ Any failed conversions
- 📊 Summary of results

## Troubleshooting

### "ImageMagick not found"
- Install ImageMagick or Sharp as mentioned in Prerequisites
- Restart your terminal after installation

### "Permission denied" (Mac/Linux)
```bash
chmod +x convert-images.sh
```

### "Node.js not found"
- Install Node.js from https://nodejs.org/
- Restart your terminal

### Poor Quality Output
- ImageMagick generally produces better quality than Sharp
- Adjust the quality setting in the script (currently 90)

## File Sizes

Expected file sizes after conversion:
- Original SVG: ~15-25KB
- WebP output: ~50-150KB (still very small for 1200x630 images)

The slight size increase is worth it for the improved social media compatibility!
