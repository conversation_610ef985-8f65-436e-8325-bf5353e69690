import { Metadata } from 'next'
import MultiChannelRevolutionClient from './MultiChannelRevolutionClient'

export const metadata: Metadata = {
  title: 'Beyond Email: The Multi-Channel Revolution | GenLogic Academy',
  description: 'Discover why email-only follow-up is dying and what\'s replacing it. Learn the SMS strategy that gets 98% open rates and how to orchestrate seamless multi-channel sequences.',
  keywords: 'multi-channel marketing, SMS marketing, email marketing, customer communication, marketing automation, UK business communication',
  openGraph: {
    title: 'Beyond Email: The Multi-Channel Revolution',
    description: 'Discover why email-only follow-up is dying and what\'s replacing it. Learn the SMS strategy that gets 98% open rates and how to orchestrate seamless multi-channel sequences.',
    type: 'article',
    url: 'https://genlogic.io/learn/fundamentals/multi-channel-revolution',
    images: [
      {
        url: '/academy-images/og-multi-channel-revolution.webp',
        width: 1200,
        height: 630,
        alt: 'Beyond Email: The Multi-Channel Revolution',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Beyond Email: The Multi-Channel Revolution',
    description: 'Discover why email-only follow-up is dying and what\'s replacing it. Learn the SMS strategy that gets 98% open rates and how to orchestrate seamless multi-channel sequences.',
    images: ['/academy-images/og-multi-channel-revolution.webp'],
  },
}

export default function MultiChannelRevolutionPage() {
  return <MultiChannelRevolutionClient />
}
