import Link from 'next/link'
import { CustomButton } from '@/components/ui/custom-button'
import { ArrowRight, Home, Search } from 'lucide-react'

export default function NotFound() {
  return (
    <div className="header-spacing min-h-[calc(100vh-12rem)] bg-gradient-to-br from-background via-primary-50/30 to-background dark:from-background dark:via-primary-950/30 dark:to-background flex items-center justify-center px-4">
      <div className="max-w-2xl mx-auto text-center">
        <div className="mb-8">
          <h1 className="text-8xl font-bold text-primary-600 dark:text-primary-400 mb-4">404</h1>
          <h2 className="text-3xl font-bold text-foreground mb-4">Page Not Found</h2>
          <p className="text-xl text-muted-foreground mb-8">
            Sorry, we couldn't find the page you're looking for. It might have been moved, deleted, or you entered the wrong URL.
          </p>
        </div>

        <div className="space-y-4 mb-8">
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <CustomButton
              variant="primary"
              size="lg"
              href="/"
              icon={<Home className="w-5 h-5" />}
              iconPosition="left"
            >
              Go Home
            </CustomButton>
            
            <CustomButton
              variant="outline"
              size="lg"
              href="/contact"
              icon={<ArrowRight className="w-5 h-5" />}
              iconPosition="right"
            >
              Contact Us
            </CustomButton>
          </div>
        </div>

        <div className="bg-primary-50 dark:bg-primary-950/30 rounded-2xl p-6 border border-primary-200/30 dark:border-primary-800/30">
          <h3 className="text-lg font-semibold text-foreground mb-4">Popular Pages</h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            <Link 
              href="/services/business-automation" 
              className="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors"
            >
              Customer Conversion System
            </Link>
            <Link 
              href="/website-development" 
              className="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors"
            >
              Website Development
            </Link>
            <Link 
              href="/blog" 
              className="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors"
            >
              Blog & Insights
            </Link>
            <Link 
              href="/pricing" 
              className="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors"
            >
              Pricing Plans
            </Link>
          </div>
        </div>

        <div className="mt-8 text-sm text-muted-foreground">
          <p>
            Need help? <Link href="/contact" className="text-primary-600 dark:text-primary-400 hover:underline">Contact our support team</Link>
          </p>
        </div>
      </div>
    </div>
  )
}
