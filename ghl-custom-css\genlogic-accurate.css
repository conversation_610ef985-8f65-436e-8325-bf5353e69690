/* ===== GENLOGIC PREMIUM BRANDING FOR GOHIGHLEVEL ===== */
/* Enhanced styling with modern design */

/* Import GenLogic Font */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* GenLogic Enhanced Color Variables */
:root {
    --loginBtnColor: #a855f7;
    --loginBtnHover: #7c3aed;
    --bodyBgColor: #ffffff;
    --login-button-background-color: linear-gradient(135deg, #a855f7 0%, #7c3aed 100%);
    --login-button-background-hover-color: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
    --login-button-text-color: #ffffff;
    --genlogic-shadow: 0 20px 25px -5px rgba(168, 85, 247, 0.1), 0 10px 10px -5px rgba(168, 85, 247, 0.04);
    --genlogic-border: #e2e8f0;
    --genlogic-text: #0f172a;
    --genlogic-text-muted: #64748b;
}

/* GHL Login Page Styling - Enhanced Premium Design */
.hl_login {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;
    background-position: center center;
    background-size: cover;
    background-repeat: no-repeat;
    display: flex !important;
    flex-direction: column !important;
    justify-content: center !important;
    align-items: center !important;
    margin: 0 auto !important;
    padding: 20px !important;
    margin-top: 0 !important;
    height: 100vh;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

.hl_login--header {
    background: transparent !important;
    border-bottom: 0 !important;
    padding: 20px 0 !important;
    margin-bottom: 20px !important;
    width: 100% !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
}

div.hl_login--body {
    max-width: 480px !important;
    width: 100% !important;
    background: var(--bodyBgColor) !important;
    border-radius: 20px !important;
    box-shadow: var(--genlogic-shadow) !important;
    border: 1px solid var(--genlogic-border) !important;
    backdrop-filter: blur(10px) !important;
    transition: all 0.3s ease !important;
}

.hl_login--body .card {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 auto !important;
    box-shadow: none !important;
    padding: 0 !important;
    margin: 0 !important;
}

.hl_login--body .card .card-body {
    max-width: 100% !important;
    width: 100% !important;
    margin: 0 auto;
    padding: 60px 40px !important;
    font-family: 'Inter', sans-serif !important;
}

/* GHL Form Controls - Enhanced Design */
.hl_login .form-control {
    background: #fff !important;
    font-size: 16px !important;
    color: var(--genlogic-text) !important;
    border: 2px solid var(--genlogic-border) !important;
    border-radius: 12px !important;
    padding: 16px 20px !important;
    font-family: 'Inter', sans-serif !important;
    font-weight: 400 !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

.hl_login .form-control:focus {
    border-color: var(--loginBtnColor) !important;
    box-shadow: 0 0 0 3px rgba(168, 85, 247, 0.1) !important;
    outline: none !important;
    transform: translateY(-1px) !important;
}

.hl_login .form-control::placeholder {
    color: var(--genlogic-text-muted) !important;
    font-weight: 400 !important;
}

.hl_login--body .login-card-heading {
    margin-bottom: 32px !important;
    text-align: center;
    font-family: 'Inter', sans-serif !important;
}

.hl_login--body .heading2 {
    margin-bottom: 8px !important;
    font-size: 32px !important;
    font-weight: 700 !important;
    color: var(--genlogic-text) !important;
    font-family: 'Inter', sans-serif !important;
    letter-spacing: -0.025em !important;
}

.hl_login--body p {
    color: var(--genlogic-text-muted) !important;
    font-size: 16px !important;
    font-weight: 400 !important;
    margin-bottom: 32px !important;
    font-family: 'Inter', sans-serif !important;
    line-height: 1.5 !important;
}

.hl_login .button-holder.form-group {
    margin: 24px auto !important;
}

/* GHL Buttons - Enhanced Premium Design */
.hl_login .btn.btn-blue,
.hl_login .btn.btn-blue:active,
.hl_login .btn.btn-blue:focus {
    background: var(--login-button-background-color) !important;
    border: none !important;
    color: var(--login-button-text-color) !important;
    font-size: 16px !important;
    font-weight: 600 !important;
    padding: 16px 32px !important;
    border-radius: 12px !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 4px 6px -1px rgba(168, 85, 247, 0.2), 0 2px 4px -1px rgba(168, 85, 247, 0.06) !important;
    font-family: 'Inter', sans-serif !important;
    letter-spacing: 0.025em !important;
    min-height: 56px !important;
    width: 100% !important;
    cursor: pointer !important;
}

.hl_login .btn.btn-blue:hover {
    background: var(--login-button-background-hover-color) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 10px 15px -3px rgba(168, 85, 247, 0.3), 0 4px 6px -2px rgba(168, 85, 247, 0.05) !important;
}

.hl_login button {
    background: var(--login-button-background-color) !important;
    color: var(--login-button-text-color) !important;
    font-size: 16px !important;
    font-weight: 600 !important;
    padding: 16px 32px !important;
    border-radius: 12px !important;
    border: none !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 4px 6px -1px rgba(168, 85, 247, 0.2) !important;
    font-family: 'Inter', sans-serif !important;
    min-height: 56px !important;
    width: 100% !important;
    cursor: pointer !important;
}

.hl_login .hover\:bg-curious-blue-600:hover,
.hl_login button:hover {
    background: var(--login-button-background-hover-color) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 10px 15px -3px rgba(168, 85, 247, 0.3) !important;
}

/* Additional form styling */
.hl_login label {
    color: var(--genlogic-text) !important;
    font-weight: 500 !important;
    font-size: 14px !important;
    margin-bottom: 8px !important;
    font-family: 'Inter', sans-serif !important;
}

.hl_login a {
    color: var(--loginBtnColor) !important;
    text-decoration: none !important;
    font-weight: 500 !important;
    transition: color 0.3s ease !important;
    font-family: 'Inter', sans-serif !important;
}

.hl_login a:hover {
    color: var(--loginBtnHover) !important;
}

/* Terms links styling - clickable and styled */
.hl_login a[href*="terms"],
.hl_login a[href*="Terms"],
.hl_login a[href*="TERMS"] {
    color: var(--loginBtnColor) !important;
    text-decoration: underline !important;
    cursor: pointer !important;
}

.hl_login a[href*="terms"]:hover,
.hl_login a[href*="Terms"]:hover,
.hl_login a[href*="TERMS"]:hover {
    color: var(--loginBtnHover) !important;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .hl_login {
        padding: 16px !important;
    }

    div.hl_login--body {
        max-width: 100% !important;
        margin: 0 16px !important;
    }

    .hl_login--body .card .card-body {
        padding: 40px 24px !important;
    }

    .hl_login--body .heading2 {
        font-size: 28px !important;
    }
}

/* ===== GENLOGIC FAVICON SCRIPT ===== */
/* Add this script to apply GenLogic favicon on login pages */