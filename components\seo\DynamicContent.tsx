'use client'

import { useState, useEffect } from 'react'
import { useDynamicKeywords, generateDynamicMeta } from '@/lib/dynamic-keywords'

interface DynamicContentProps {
  children: (keywords: ReturnType<typeof useDynamicKeywords>) => React.ReactNode
  userIndustry?: string
  updateInterval?: number // in milliseconds
}

export function DynamicContent({ children, userIndustry, updateInterval = 30000 }: DynamicContentProps) {
  const [mounted, setMounted] = useState(false)
  const [refreshKey, setRefreshKey] = useState(0)
  
  const keywords = useDynamicKeywords(userIndustry)

  useEffect(() => {
    setMounted(true)
    
    // Set up rotation interval
    const interval = setInterval(() => {
      setRefreshKey(prev => prev + 1)
    }, updateInterval)

    return () => clearInterval(interval)
  }, [updateInterval])

  // Don't render on server to avoid hydration mismatch
  if (!mounted) {
    return null
  }

  return <>{children(keywords)}</>
}

// Dynamic headline component
export function DynamicHeadline({ 
  template, 
  userIndustry, 
  className = "",
  updateInterval = 45000 
}: { 
  template: string
  userIndustry?: string
  className?: string
  updateInterval?: number
}) {
  return (
    <DynamicContent userIndustry={userIndustry} updateInterval={updateInterval}>
      {(keywords) => {
        // Replace placeholders in template
        let headline = template
          .replace('{primary}', keywords.primary)
          .replace('{secondary}', keywords.secondary)
          .replace('{location}', keywords.location)
          .replace('{industry}', keywords.industry)
          .replace('{painPoint}', keywords.painPoint)
          .replace('{solution}', keywords.solution)
          .replace('{benefit}', keywords.benefit)
          .replace('{timeframe}', keywords.timeframe)

        return <span className={className}>{headline}</span>
      }}
    </DynamicContent>
  )
}

// Dynamic meta tags component
export function DynamicMetaTags({ userIndustry }: { userIndustry?: string }) {
  const [mounted, setMounted] = useState(false)
  const keywords = useDynamicKeywords(userIndustry)

  useEffect(() => {
    setMounted(true)
    
    if (mounted) {
      const meta = generateDynamicMeta(keywords)
      
      // Update page title
      document.title = meta.title
      
      // Update meta description
      const descriptionMeta = document.querySelector('meta[name="description"]')
      if (descriptionMeta) {
        descriptionMeta.setAttribute('content', meta.description)
      }
      
      // Update meta keywords
      let keywordsMeta = document.querySelector('meta[name="keywords"]')
      if (!keywordsMeta) {
        keywordsMeta = document.createElement('meta')
        keywordsMeta.setAttribute('name', 'keywords')
        document.head.appendChild(keywordsMeta)
      }
      keywordsMeta.setAttribute('content', meta.keywords)
    }
  }, [mounted, keywords, userIndustry])

  return null
}

// Dynamic CTA button
export function DynamicCTA({ 
  baseText = "Get Started", 
  userIndustry,
  className = "",
  onClick 
}: { 
  baseText?: string
  userIndustry?: string
  className?: string
  onClick?: () => void
}) {
  return (
    <DynamicContent userIndustry={userIndustry} updateInterval={60000}>
      {(keywords) => (
        <button className={className} onClick={onClick}>
          {baseText} - {keywords.getBenefit()}
        </button>
      )}
    </DynamicContent>
  )
}

// Dynamic testimonial quote
export function DynamicTestimonial({ userIndustry }: { userIndustry?: string }) {
  return (
    <DynamicContent userIndustry={userIndustry} updateInterval={90000}>
      {(keywords) => (
        <blockquote className="text-lg italic">
          "GenLogic's {keywords.solution} helped our {keywords.industry.slice(0, -1)} achieve {keywords.benefit}. 
          We went from {keywords.painPoint} to having {keywords.timeKeywords[0]} every evening!"
        </blockquote>
      )}
    </DynamicContent>
  )
}

// Dynamic feature list
export function DynamicFeatures({ userIndustry }: { userIndustry?: string }) {
  return (
    <DynamicContent userIndustry={userIndustry} updateInterval={120000}>
      {(keywords) => (
        <ul className="space-y-2">
          <li>✅ {keywords.solution} for {keywords.industry}</li>
          <li>✅ {keywords.benefit} guaranteed</li>
          <li>✅ Perfect for {keywords.location}</li>
          <li>✅ Stop {keywords.painPoint} forever</li>
          <li>✅ Results {keywords.timeframe}</li>
        </ul>
      )}
    </DynamicContent>
  )
}

// Dynamic pricing value proposition
export function DynamicPricing({ userIndustry }: { userIndustry?: string }) {
  return (
    <DynamicContent userIndustry={userIndustry} updateInterval={180000}>
      {(keywords) => (
        <div className="text-center">
          <h3 className="text-2xl font-bold mb-4">
            {keywords.primary} for {keywords.industry}
          </h3>
          <p className="text-lg mb-6">
            Join {keywords.location} who've achieved {keywords.benefit} with our {keywords.solution}
          </p>
          <div className="bg-green-100 dark:bg-green-900/30 rounded-lg p-4">
            <p className="font-semibold text-green-800 dark:text-green-300">
              🎯 Stop {keywords.painPoint} - Get results {keywords.timeframe}
            </p>
          </div>
        </div>
      )}
    </DynamicContent>
  )
}

// Dynamic FAQ question
export function DynamicFAQ({ userIndustry }: { userIndustry?: string }) {
  return (
    <DynamicContent userIndustry={userIndustry} updateInterval={300000}>
      {(keywords) => (
        <div className="space-y-4">
          <h4 className="font-semibold">
            How quickly can {keywords.industry} see results with {keywords.primary}?
          </h4>
          <p>
            Most {keywords.industry} in {keywords.location} start seeing {keywords.benefit} {keywords.timeframe}. 
            Our {keywords.solution} is designed specifically to help you stop {keywords.painPoint} 
            and achieve {keywords.secondary} immediately.
          </p>
        </div>
      )}
    </DynamicContent>
  )
}

// Dynamic schema markup
export function DynamicSchema({ userIndustry }: { userIndustry?: string }) {
  const [mounted, setMounted] = useState(false)
  const keywords = useDynamicKeywords(userIndustry)

  useEffect(() => {
    setMounted(true)
    
    if (mounted) {
      const schema = {
        "@context": "https://schema.org",
        "@type": "SoftwareApplication",
        "name": `GenLogic ${keywords.primary}`,
        "description": `${keywords.solution} software helping ${keywords.industry} achieve ${keywords.benefit}`,
        "applicationCategory": "BusinessApplication",
        "operatingSystem": "Web",
        "offers": {
          "@type": "Offer",
          "price": "120",
          "priceCurrency": "GBP",
          "description": `${keywords.primary} for ${keywords.location}`
        },
        "audience": {
          "@type": "BusinessAudience",
          "businessFunction": keywords.industry,
          "geographicArea": keywords.location
        }
      }

      // Add schema to head
      const script = document.createElement('script')
      script.type = 'application/ld+json'
      script.textContent = JSON.stringify(schema)
      script.id = 'dynamic-schema'
      
      // Remove existing dynamic schema
      const existing = document.getElementById('dynamic-schema')
      if (existing) {
        existing.remove()
      }
      
      document.head.appendChild(script)
    }
  }, [mounted, keywords, userIndustry])

  return null
}
