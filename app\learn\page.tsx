import { Metadata } from 'next'
import AcademyClient from './components/AcademyClient'

export const metadata: Metadata = {
  title: 'GenLogic Academy - Master Business Automation | GenLogic',
  description: 'Discover what successful UK business owners know about automation. Learn the proven strategies that transform struggling businesses into thriving enterprises.',
  keywords: 'business automation training, UK business growth, automation academy, business transformation, follow-up strategies',
  openGraph: {
    title: 'GenLogic Academy - Master Business Automation',
    description: 'Discover what successful UK business owners know about automation. Learn the proven strategies that transform struggling businesses into thriving enterprises.',
    type: 'website',
    url: 'https://genlogic.io/learn',
    images: [
      {
        url: '/academy-images/og-academy.webp',
        width: 1200,
        height: 630,
        alt: 'GenLogic Academy - Master Business Automation',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'GenLogic Academy - Master Business Automation',
    description: 'Discover what successful UK business owners know about automation. Learn the proven strategies that transform struggling businesses into thriving enterprises.',
    images: ['/academy-images/og-academy.webp'],
  },
}

export default function AcademyPage() {
  return <AcademyClient />
}
