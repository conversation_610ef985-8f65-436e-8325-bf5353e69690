/**
 * Serverless-Compatible Redirect Monitoring System
 * Optimized for Vercel hobby plan and serverless environments
 */

interface RedirectLog {
  timestamp: string;
  fromPath: string;
  toPath: string;
  userAgent: string;
  referer?: string;
  type: 'service' | 'common' | 'trailing_slash';
}

interface RedirectStats {
  totalRedirects: number;
  redirectsByType: Record<string, number>;
  redirectsByPath: Record<string, number>;
  recentRedirects: RedirectLog[];
  isServerless: boolean;
  note?: string;
}

class RedirectMonitor {
  private logs: RedirectLog[] = [];
  private maxLogs = 100; // Reduced for serverless (memory constraints)
  private isServerless = typeof process.env.VERCEL === 'string';

  logRedirect(
    fromPath: string,
    toPath: string,
    type: 'service' | 'common' | 'trailing_slash',
    userAgent: string,
    referer?: string
  ) {
    const log: RedirectLog = {
      timestamp: new Date().toISOString(),
      fromPath,
      toPath,
      userAgent,
      referer,
      type
    };

    // In serverless, only keep current session logs
    if (this.isServerless) {
      this.logs.push(log);
      // Keep only last 10 logs in serverless to save memory
      if (this.logs.length > 10) {
        this.logs = this.logs.slice(-10);
      }
    } else {
      this.logs.push(log);
      if (this.logs.length > this.maxLogs) {
        this.logs = this.logs.slice(-this.maxLogs);
      }
    }

    // Always log to console for debugging
    console.log(`[REDIRECT] ${type.toUpperCase()}: ${fromPath} → ${toPath}`);

    // Send to analytics in production (works in serverless)
    if (process.env.NODE_ENV === 'production') {
      this.sendToAnalytics(log);
    }
  }

  private sendToAnalytics(log: RedirectLog) {
    // Send to Google Analytics, Mixpanel, or your preferred analytics service
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('event', 'redirect', {
        event_category: 'navigation',
        event_label: `${log.fromPath} → ${log.toPath}`,
        custom_map: {
          redirect_type: log.type,
          from_path: log.fromPath,
          to_path: log.toPath
        }
      });
    }
  }

  getStats(): RedirectStats {
    const redirectsByType: Record<string, number> = {};
    const redirectsByPath: Record<string, number> = {};

    this.logs.forEach(log => {
      redirectsByType[log.type] = (redirectsByType[log.type] || 0) + 1;
      redirectsByPath[log.fromPath] = (redirectsByPath[log.fromPath] || 0) + 1;
    });

    return {
      totalRedirects: this.logs.length,
      redirectsByType,
      redirectsByPath,
      recentRedirects: this.logs.slice(-10),
      isServerless: this.isServerless,
      note: this.isServerless
        ? 'Serverless environment: Stats reset on each function invocation'
        : 'Persistent environment: Stats accumulate over time'
    };
  }

  // Get redirects that might indicate problems
  getPotentialIssues(): {
    highVolumeRedirects: Array<{ path: string; count: number }>;
    recentErrors: RedirectLog[];
    suspiciousPatterns: string[];
  } {
    const stats = this.getStats();
    const issues = {
      highVolumeRedirects: [] as Array<{ path: string; count: number }>,
      recentErrors: [] as RedirectLog[],
      suspiciousPatterns: [] as string[]
    };

    // Find high-volume redirects (might indicate missing pages)
    Object.entries(stats.redirectsByPath).forEach(([path, count]) => {
      if (count > 10) { // More than 10 redirects from same path
        issues.highVolumeRedirects.push({ path, count });
      }
    });

    // Sort by count descending
    issues.highVolumeRedirects.sort((a, b) => b.count - a.count);

    // Look for suspicious patterns
    const recentPaths = this.logs.slice(-50).map(log => log.fromPath);
    const pathCounts = recentPaths.reduce((acc, path) => {
      acc[path] = (acc[path] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    Object.entries(pathCounts).forEach(([path, count]) => {
      if (count > 5) { // Same path redirected 5+ times recently
        issues.suspiciousPatterns.push(`High frequency: ${path} (${count} times recently)`);
      }
    });

    return issues;
  }

  // Export logs for analysis
  exportLogs(): string {
    return JSON.stringify(this.logs, null, 2);
  }

  // Clear logs (useful for testing)
  clearLogs() {
    this.logs = [];
  }
}

// Singleton instance
export const redirectMonitor = new RedirectMonitor();

// Helper function for middleware
export function logRedirectInMiddleware(
  fromPath: string,
  toPath: string,
  type: 'service' | 'common' | 'trailing_slash',
  request: any
) {
  const userAgent = request.headers.get('user-agent') || 'Unknown';
  const referer = request.headers.get('referer') || undefined;
  
  redirectMonitor.logRedirect(fromPath, toPath, type, userAgent, referer);
}

// API endpoint helpers
export function getRedirectStats() {
  return redirectMonitor.getStats();
}

export function getRedirectIssues() {
  return redirectMonitor.getPotentialIssues();
}
