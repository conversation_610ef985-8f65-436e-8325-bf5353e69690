#!/usr/bin/env node

/**
 * Sitemap Route Testing Script
 * Verifies that /sitemap.xml returns valid XML with proper MIME type
 */

const http = require('http');
const https = require('https');

async function testSitemapRoute(baseUrl) {
  console.log('🗺️  Testing Sitemap Route Configuration');
  console.log('=' .repeat(50));
  
  const sitemapUrl = `${baseUrl}/sitemap.xml`;
  console.log(`📍 Testing: ${sitemapUrl}\n`);
  
  return new Promise((resolve) => {
    const client = sitemapUrl.startsWith('https:') ? https : http;
    
    const req = client.request(sitemapUrl, { method: 'GET' }, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        const result = {
          url: sitemapUrl,
          statusCode: res.statusCode,
          contentType: res.headers['content-type'],
          contentLength: res.headers['content-length'],
          data: data,
          issues: [],
          success: false
        };
        
        console.log('📊 Response Analysis:');
        console.log(`   Status Code: ${res.statusCode}`);
        console.log(`   Content-Type: ${res.headers['content-type'] || 'Not set'}`);
        console.log(`   Content-Length: ${res.headers['content-length'] || 'Not set'}`);
        
        // Validate status code
        if (res.statusCode !== 200) {
          result.issues.push(`❌ Expected status 200, got ${res.statusCode}`);
        } else {
          console.log('   ✅ Status code: OK');
        }
        
        // Validate content type
        const contentType = res.headers['content-type'] || '';
        if (!contentType.includes('xml')) {
          result.issues.push(`❌ Expected XML content-type, got: ${contentType}`);
        } else {
          console.log('   ✅ Content-Type: XML detected');
        }
        
        // Validate XML structure
        if (!data.startsWith('<?xml')) {
          result.issues.push('❌ Response does not start with XML declaration');
        } else {
          console.log('   ✅ XML declaration: Present');
        }
        
        if (!data.includes('<urlset')) {
          result.issues.push('❌ Missing <urlset> root element');
        } else {
          console.log('   ✅ Sitemap structure: Valid');
        }
        
        // Check for script tags
        if (data.includes('<script')) {
          const scriptCount = (data.match(/<script/g) || []).length;
          result.issues.push(`❌ Found ${scriptCount} <script> tags in XML`);
        } else {
          console.log('   ✅ Script tags: None found');
        }
        
        // Count URLs
        const urlCount = (data.match(/<loc>/g) || []).length;
        console.log(`   📊 URLs found: ${urlCount}`);
        
        // Check date formats
        const lastmodMatches = data.match(/<lastmod>(.*?)<\/lastmod>/g) || [];
        let invalidDates = 0;
        lastmodMatches.forEach(match => {
          const dateStr = match.replace(/<\/?lastmod>/g, '');
          const date = new Date(dateStr);
          if (isNaN(date.getTime()) || !dateStr.includes('T')) {
            invalidDates++;
          }
        });
        
        if (invalidDates > 0) {
          result.issues.push(`❌ Found ${invalidDates} invalid date formats`);
        } else if (lastmodMatches.length > 0) {
          console.log('   ✅ Date formats: All valid ISO 8601');
        }
        
        // Overall success
        result.success = result.issues.length === 0;
        
        console.log('\n🎯 Test Results:');
        if (result.success) {
          console.log('✅ All tests passed! Sitemap route is correctly configured.');
        } else {
          console.log('❌ Issues found:');
          result.issues.forEach(issue => console.log(`   ${issue}`));
        }
        
        resolve(result);
      });
    });
    
    req.on('error', (err) => {
      console.error(`❌ Request failed: ${err.message}`);
      resolve({
        url: sitemapUrl,
        error: err.message,
        success: false
      });
    });
    
    req.setTimeout(10000, () => {
      req.destroy();
      console.error('❌ Request timeout');
      resolve({
        url: sitemapUrl,
        error: 'Request timeout',
        success: false
      });
    });
    
    req.end();
  });
}

async function testMultipleRoutes() {
  const testUrls = [
    'http://localhost:3000',
    'https://genlogic.io'
  ];
  
  console.log('🚀 Testing Sitemap Routes\n');
  
  for (const baseUrl of testUrls) {
    try {
      const result = await testSitemapRoute(baseUrl);
      
      if (!result.success) {
        console.log('\n💡 Recommendations:');
        
        if (result.error) {
          if (result.error.includes('ECONNREFUSED')) {
            console.log('   • Start development server: npm run dev');
          } else {
            console.log('   • Check network connectivity');
            console.log('   • Verify URL is accessible');
          }
        }
        
        if (result.issues) {
          result.issues.forEach(issue => {
            if (issue.includes('content-type')) {
              console.log('   • Ensure sitemap.ts is in app/ directory');
              console.log('   • Check Next.js App Router configuration');
            }
            if (issue.includes('script')) {
              console.log('   • Remove browser extensions during testing');
              console.log('   • Check for middleware interference');
            }
            if (issue.includes('date')) {
              console.log('   • Use .toISOString() for all lastModified dates');
            }
          });
        }
      }
      
      console.log('\n' + '='.repeat(50) + '\n');
      
    } catch (error) {
      console.error(`Failed to test ${baseUrl}:`, error.message);
    }
  }
}

// Check if this is a direct execution
if (require.main === module) {
  testMultipleRoutes().catch(console.error);
}

module.exports = { testSitemapRoute };
