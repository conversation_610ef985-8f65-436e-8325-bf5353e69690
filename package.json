{"name": "genlogic-v2-mvp", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "analyze": "ANALYZE=true npm run build", "build:analyze": "cross-env ANALYZE=true npm run build", "test:redirects": "node scripts/test-redirects.js", "validate:redirects": "node scripts/validate-redirects.js", "validate:sitemap": "node scripts/validate-sitemap.js", "test:sitemap-route": "node scripts/test-sitemap-route.js", "test:performance": "node scripts/test-performance-2025.js", "validate:cwv-2025": "node scripts/test-performance-2025.js", "redirect:monitor": "curl http://localhost:3000/api/redirect-monitor?type=stats", "redirect:health": "curl http://localhost:3000/api/redirect-monitor?type=health", "redirect:validation": "curl http://localhost:3000/api/redirect-monitor?type=validation"}, "dependencies": {"clsx": "^2.1.1", "framer-motion": "^12.23.5", "lucide-react": "^0.525.0", "next": "14.0.4", "react": "^18", "react-dom": "^18", "tailwind-merge": "^3.3.1", "zod": "^4.0.5"}, "devDependencies": {"@next/bundle-analyzer": "^15.4.1", "@svgr/webpack": "^8.1.0", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "cross-env": "^7.0.3", "eslint": "^8", "eslint-config-next": "14.0.4", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}}