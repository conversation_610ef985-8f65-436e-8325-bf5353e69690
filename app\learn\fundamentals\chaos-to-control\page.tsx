import { Metadata } from 'next'
import ChaosToControlClient from './ChaosToControlClient'

export const metadata: Metadata = {
  title: 'From Chaos to Control: The Business Owner\'s Evolution | GenLogic Academy',
  description: 'How successful entrepreneurs transformed from working IN their business to working ON it. Discover the evolution that separates thriving business owners from struggling ones.',
  keywords: 'business transformation, entrepreneur evolution, business systems, working on business, UK business growth',
  openGraph: {
    title: 'From Chaos to Control: The Business Owner\'s Evolution',
    description: 'How successful entrepreneurs transformed from working IN their business to working ON it. Discover the evolution that separates thriving business owners from struggling ones.',
    type: 'article',
    url: 'https://genlogic.io/learn/fundamentals/chaos-to-control',
    images: [
      {
        url: '/academy-images/og-chaos-to-control.webp',
        width: 1200,
        height: 630,
        alt: 'From Chaos to Control: The Business Owner\'s Evolution',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'From Chaos to Control: The Business Owner\'s Evolution',
    description: 'How successful entrepreneurs transformed from working IN their business to working ON it. Discover the evolution that separates thriving business owners from struggling ones.',
    images: ['/academy-images/og-chaos-to-control.webp'],
  },
}

export default function ChaosToControlPage() {
  return <ChaosToControlClient />
}
