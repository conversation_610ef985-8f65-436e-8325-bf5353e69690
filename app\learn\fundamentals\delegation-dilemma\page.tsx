import { Metadata } from 'next'
import DelegationDilemmaClient from './DelegationDilemmaClient'

export const metadata: Metadata = {
  title: 'The Delegation Dilemma: How to Clone Yourself | GenLogic Academy',
  description: 'Discover why most delegation fails and the 3-step fix. Learn the decision tree method for training team members and how to automate quality control without micromanaging.',
  keywords: 'business delegation, team management, business automation, staff training, quality control, UK business management',
  openGraph: {
    title: 'The Delegation Dilemma: How to Clone Yourself',
    description: 'Discover why most delegation fails and the 3-step fix. Learn the decision tree method for training team members and how to automate quality control without micromanaging.',
    type: 'article',
    url: 'https://genlogic.io/learn/fundamentals/delegation-dilemma',
    images: [
      {
        url: '/academy-images/og-delegation-dilemma.webp',
        width: 1200,
        height: 630,
        alt: 'The Delegation Dilemma: How to Clone Yourself',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'The Delegation Dilemma: How to Clone Yourself',
    description: 'Discover why most delegation fails and the 3-step fix. Learn the decision tree method for training team members and how to automate quality control without micromanaging.',
    images: ['/academy-images/og-delegation-dilemma.webp'],
  },
}

export default function DelegationDilemmaPage() {
  return <DelegationDilemmaClient />
}
