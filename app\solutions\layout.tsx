import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'GoHighLevel Automation Solutions for UK Local Businesses - GenLogic',
  description: 'Discover how successful UK businesses are quietly transforming their operations with white-label GoHighLevel automation. Join 500+ businesses converting 89% of prospects into customers.',
  keywords: 'GoHighLevel automation UK, business automation solutions, local business automation, customer conversion system, booking automation, SMS automation, email follow-up',
  openGraph: {
    title: 'GoHighLevel Automation Solutions for UK Local Businesses - GenLogic',
    description: 'Discover how successful UK businesses are quietly transforming their operations with white-label GoHighLevel automation.',
    url: 'https://genlogic.io/solutions',
    siteName: 'GenLogic',
    images: [
      {
        url: 'https://genlogic.io/og-solutions.jpg',
        width: 1200,
        height: 630,
        alt: 'GenLogic GoHighLevel Automation Solutions',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'GoHighLevel Automation Solutions for UK Local Businesses - GenLogic',
    description: 'Discover how successful UK businesses are quietly transforming their operations with white-label GoHighLevel automation.',
    images: ['https://genlogic.io/og-solutions.jpg'],
  },
}

export default function SolutionsLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return children
}
