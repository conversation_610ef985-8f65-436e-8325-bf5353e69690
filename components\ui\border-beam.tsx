'use client'

import { cn } from '@/lib/utils'

interface BorderBeamProps {
  className?: string
  size?: number
  duration?: number
  reverse?: boolean
}

export function BorderBeam({
  className,
  size = 200,
  duration = 15,
  reverse = false,
}: BorderBeamProps) {
  return (
    <div
      className={cn(
        "absolute inset-0 overflow-hidden rounded-[inherit]",
        className
      )}
    >
      <div
        className={cn(
          "absolute inset-0 rounded-[inherit]",
          "before:absolute before:inset-0 before:rounded-[inherit] before:p-[1px]",
          "before:bg-gradient-to-r before:from-transparent before:via-primary/40 before:to-transparent",
          "before:animate-spin",
          reverse && "before:animate-reverse-spin"
        )}
        style={{
          '--size': `${size}px`,
          '--duration': `${duration}s`,
          animation: `border-beam ${duration}s linear infinite ${reverse ? 'reverse' : ''}`,
        } as React.CSSProperties}
      >
        <div className="absolute inset-[1px] rounded-[inherit] bg-background" />
      </div>
      
      <style jsx>{`
        @keyframes border-beam {
          0% {
            transform: rotate(0deg);
          }
          100% {
            transform: rotate(360deg);
          }
        }
        
        @keyframes reverse-spin {
          0% {
            transform: rotate(360deg);
          }
          100% {
            transform: rotate(0deg);
          }
        }
      `}</style>
    </div>
  )
}
