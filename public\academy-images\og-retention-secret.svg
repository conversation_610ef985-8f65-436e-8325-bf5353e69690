<svg width="1200" height="630" viewBox="0 0 1200 630" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#8b5cf6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#dc2626;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="cardGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.95" />
      <stop offset="100%" style="stop-color:#f0f9ff;stop-opacity:0.9" />
    </linearGradient>
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8b5cf6;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="8" stdDeviation="16" flood-color="#000000" flood-opacity="0.1"/>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="1200" height="630" fill="url(#bgGradient)"/>
  
  <!-- Decorative Elements -->
  <circle cx="100" cy="100" r="60" fill="#ffffff" opacity="0.1"/>
  <circle cx="1100" cy="530" r="80" fill="#ffffff" opacity="0.1"/>
  <path d="M950,50 L1150,150 L1050,250 L950,150 Z" fill="#ffffff" opacity="0.05"/>
  
  <!-- Main Content Card -->
  <rect x="80" y="80" width="1040" height="470" rx="24" fill="url(#cardGradient)" filter="url(#shadow)"/>
  
  <!-- GenLogic Academy Badge -->
  <rect x="120" y="120" width="180" height="40" rx="20" fill="#1e40af"/>
  <text x="210" y="140" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="14" font-weight="bold">GENLOGIC ACADEMY</text>
  
  <!-- Early Warning System Visual -->
  <g transform="translate(380, 125)">
    <!-- Warning bell -->
    <path d="M30,20 Q20,10 10,20 Q10,30 20,40 Q30,50 40,40 Q50,30 50,20 Q40,10 30,20" fill="url(#accentGradient)"/>
    <circle cx="30" cy="55" r="3" fill="#dc2626"/>
    
    <!-- Warning signals -->
    <circle cx="10" cy="75" r="2" fill="#dc2626" opacity="0.8"/>
    <circle cx="20" cy="80" r="2" fill="#f59e0b" opacity="0.8"/>
    <circle cx="40" cy="80" r="2" fill="#f59e0b" opacity="0.8"/>
    <circle cx="50" cy="75" r="2" fill="#dc2626" opacity="0.8"/>
    
    <!-- Signal waves -->
    <path d="M5,35 Q15,25 25,35" stroke="#3b82f6" stroke-width="2" fill="none" opacity="0.6"/>
    <path d="M35,35 Q45,25 55,35" stroke="#3b82f6" stroke-width="2" fill="none" opacity="0.6"/>
    <path d="M0,40 Q15,20 30,40" stroke="#8b5cf6" stroke-width="2" fill="none" opacity="0.4"/>
    <path d="M30,40 Q45,20 60,40" stroke="#8b5cf6" stroke-width="2" fill="none" opacity="0.4"/>
    
    <!-- Customer retention funnel -->
    <g transform="translate(80, 0)">
      <path d="M0,20 L40,20 L35,80 L5,80 Z" fill="#dc2626" opacity="0.3"/>
      <text x="20" y="50" text-anchor="middle" fill="#dc2626" font-family="Arial, sans-serif" font-size="10" font-weight="bold">73%</text>
      <text x="20" y="95" text-anchor="middle" fill="#dc2626" font-family="Arial, sans-serif" font-size="8">Leave Silent</text>
    </g>
  </g>
  
  <!-- Main Headline -->
  <text x="120" y="250" fill="#1e293b" font-family="Arial, sans-serif" font-size="40" font-weight="bold">
    <tspan x="120" dy="0">The Retention</tspan>
    <tspan x="120" dy="50" fill="#3b82f6">Secret:</tspan>
    <tspan x="120" dy="50">Why Customers</tspan>
    <tspan x="120" dy="50" fill="#dc2626">Really Leave</tspan>
  </text>
  
  <!-- Subtitle -->
  <text x="120" y="420" fill="#64748b" font-family="Arial, sans-serif" font-size="18" font-weight="normal">
    <tspan x="120" dy="0">Why 73% of customers leave before you know they're unhappy</tspan>
    <tspan x="120" dy="24">and the early warning system that saves £89k annually</tspan>
  </text>
  
  <!-- Retention Stats Box -->
  <g transform="translate(650, 180)">
    <rect x="0" y="0" width="400" height="280" rx="20" fill="#f8fafc" stroke="#e2e8f0" stroke-width="2"/>
    <text x="200" y="35" text-anchor="middle" fill="#1e293b" font-family="Arial, sans-serif" font-size="18" font-weight="bold">Hidden Churn Reasons</text>
    
    <!-- Silent Dissatisfaction -->
    <rect x="30" y="60" width="340" height="45" rx="10" fill="#fef2f2" stroke="#dc2626" stroke-width="1"/>
    <text x="50" y="80" fill="#dc2626" font-family="Arial, sans-serif" font-size="16" font-weight="bold">Silent Dissatisfaction: 43%</text>
    <text x="50" y="95" fill="#64748b" font-family="Arial, sans-serif" font-size="12">Unhappy but never complain - just disappear</text>
    
    <!-- Value Perception Gap -->
    <rect x="30" y="115" width="340" height="45" rx="10" fill="#fef3c7" stroke="#f59e0b" stroke-width="1"/>
    <text x="50" y="135" fill="#f59e0b" font-family="Arial, sans-serif" font-size="16" font-weight="bold">Value Perception Gap: 31%</text>
    <text x="50" y="150" fill="#64748b" font-family="Arial, sans-serif" font-size="12">Don't see ongoing value in your service</text>
    
    <!-- Early Warning Success -->
    <rect x="30" y="170" width="340" height="45" rx="10" fill="#f0fdf4" stroke="#10b981" stroke-width="1"/>
    <text x="50" y="190" fill="#10b981" font-family="Arial, sans-serif" font-size="16" font-weight="bold">Early Warning: 89% save rate</text>
    <text x="50" y="205" fill="#64748b" font-family="Arial, sans-serif" font-size="12">Detect problems 73% earlier than complaints</text>
    
    <!-- What You'll Get -->
    <text x="50" y="235" fill="#1e293b" font-family="Arial, sans-serif" font-size="14" font-weight="bold">Detection System:</text>
    <text x="50" y="250" fill="#64748b" font-family="Arial, sans-serif" font-size="12">✓ 4 warning signals</text>
    <text x="200" y="250" fill="#64748b" font-family="Arial, sans-serif" font-size="12">✓ Automated alerts</text>
  </g>
  
  <!-- Bottom Stats -->
  <rect x="120" y="460" width="100" height="50" rx="10" fill="#f0f9ff"/>
  <text x="170" y="480" text-anchor="middle" fill="#3b82f6" font-family="Arial, sans-serif" font-size="16" font-weight="bold">18 min</text>
  <text x="170" y="495" text-anchor="middle" fill="#64748b" font-family="Arial, sans-serif" font-size="12">read time</text>
  
  <rect x="240" y="460" width="120" height="50" rx="10" fill="#f3e8ff"/>
  <text x="300" y="480" text-anchor="middle" fill="#8b5cf6" font-family="Arial, sans-serif" font-size="16" font-weight="bold">Lesson 14</text>
  <text x="300" y="495" text-anchor="middle" fill="#64748b" font-family="Arial, sans-serif" font-size="12">of 15</text>
  
  <!-- URL -->
  <text x="1080" y="600" text-anchor="end" fill="#64748b" font-family="Arial, sans-serif" font-size="14">genlogic.io/learn</text>
</svg>
