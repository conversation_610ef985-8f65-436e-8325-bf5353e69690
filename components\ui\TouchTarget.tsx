import { ReactNode } from 'react'
import { cn } from '@/lib/utils'

interface TouchTargetProps {
  children: ReactNode
  className?: string
  minSize?: 'sm' | 'md' | 'lg'
}

export function TouchTarget({ children, className, minSize = 'md' }: TouchTargetProps) {
  const sizeClasses = {
    sm: 'min-h-[44px] min-w-[44px]', // 44px minimum for accessibility
    md: 'min-h-[48px] min-w-[48px]', // 48px recommended
    lg: 'min-h-[56px] min-w-[56px]'  // 56px for better UX
  }

  return (
    <div className={cn(
      'flex items-center justify-center',
      sizeClasses[minSize],
      className
    )}>
      {children}
    </div>
  )
}
