'use client'

import { useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  ArrowLeft, 
  Clock, 
  Zap, 
  TrendingUp, 
  CheckCircle,
  ArrowRight,
  Brain,
  Users,
  Target,
  Shield,
  Lightbulb,
  Settings,
  BarChart3,
  MessageSquare,
  Calendar,
  Phone,
  Mail,
  Workflow,
  Timer,
  Eye
} from 'lucide-react'
import Link from 'next/link'

const automationExamples = [
  {
    business: "Dental Practice",
    owner: "Dr. <PERSON>",
    location: "Leeds",
    challenge: "Spending 2 hours daily on appointment confirmations and follow-ups",
    solution: "Smart automation that feels personal",
    results: [
      "Reduced admin time by 85%",
      "Increased appointment attendance by 34%",
      "Improved patient satisfaction scores",
      "Generated £47k additional revenue in 6 months"
    ],
    automation: {
      trigger: "Appointment booked",
      sequence: [
        "Immediate confirmation with practice details",
        "24-hour reminder with preparation instructions",
        "2-hour reminder with parking information",
        "Post-appointment care instructions",
        "Follow-up satisfaction survey",
        "Rebooking invitation based on treatment type"
      ]
    }
  },
  {
    business: "Fitness Studio",
    owner: "<PERSON> Thompson",
    location: "Manchester",
    challenge: "High no-show rates and difficulty retaining new members",
    solution: "Behavioral automation based on member actions",
    results: [
      "Reduced no-shows by 67%",
      "Increased member retention by 45%",
      "Doubled new member referrals",
      "Saved 15 hours per week on admin"
    ],
    automation: {
      trigger: "Member behavior patterns",
      sequence: [
        "Welcome series for new members",
        "Motivation messages based on attendance",
        "Re-engagement for inactive members",
        "Achievement celebrations and rewards",
        "Referral invitations for engaged members",
        "Renewal reminders with personal progress"
      ]
    }
  }
]

export default function AutomationUndergroundClient() {
  // Mark lesson as completed when user visits
  useEffect(() => {
    const markAsCompleted = () => {
      const saved = localStorage.getItem('genlogic-academy-progress')
      const completed = saved ? JSON.parse(saved) : []
      if (!completed.includes('automation-underground-movement')) {
        completed.push('automation-underground-movement')
        localStorage.setItem('genlogic-academy-progress', JSON.stringify(completed))

        // Dispatch custom event to notify other components
        window.dispatchEvent(new CustomEvent('academyProgressUpdate'))
      }
    }

    // Mark as completed after user has been on page for 30 seconds
    const timer = setTimeout(markAsCompleted, 30000)
    return () => clearTimeout(timer)
  }, [])

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-primary-50/30 to-background dark:from-background dark:via-primary-950/30 dark:to-background">
      {/* Navigation */}
      <div className="pt-40 pb-8">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-5xl mx-auto">
            <Link
              href="/learn"
              className="inline-flex items-center gap-2 bg-background/80 backdrop-blur-sm border border-border/50 rounded-2xl px-4 py-2 text-primary-600 hover:text-primary-700 font-medium transition-all duration-300 hover:shadow-md"
            >
              <ArrowLeft className="w-4 h-4" />
              Back to Academy
            </Link>
          </div>
        </div>
      </div>

      {/* Hero Section */}
      <section className="pb-16">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-5xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              {/* Lesson Meta */}
              <div className="flex items-center justify-center gap-6 mb-8">
                <div className="flex items-center gap-2 bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 px-4 py-2 rounded-2xl">
                  <Zap className="w-4 h-4" />
                  <span className="font-medium text-sm">Advanced Strategy</span>
                </div>
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Clock className="w-4 h-4" />
                  <span className="text-sm">15 min read</span>
                </div>
                <div className="bg-muted px-3 py-1 rounded-full text-xs font-medium text-muted-foreground">
                  Fundamentals
                </div>
              </div>

              {/* Main Title */}
              <h1 className="text-4xl md:text-6xl font-bold text-foreground mb-6 leading-tight">
                The Underground Movement:
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-500 to-blue-500"> How Smart Businesses Automate</span>
              </h1>

              <p className="text-xl md:text-2xl text-muted-foreground mb-8 leading-relaxed max-w-4xl mx-auto">
                Something's happening quietly across UK businesses. While most owners are still drowning in manual tasks,
                <strong className="text-foreground"> a small group discovered automation patterns that don't just save time - they transform entire businesses.</strong>
                Here's what they know that others don't.
              </p>

              {/* Stats Row */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
                <div className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950/30 dark:to-purple-900/30 rounded-3xl p-6 border border-purple-200/50 dark:border-purple-800/50">
                  <div className="text-3xl font-bold text-purple-600 dark:text-purple-400 mb-2">200+</div>
                  <div className="text-sm text-purple-700 dark:text-purple-300 font-medium">UK businesses analyzed</div>
                </div>
                <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/30 dark:to-blue-900/30 rounded-3xl p-6 border border-blue-200/50 dark:border-blue-800/50">
                  <div className="text-3xl font-bold text-blue-600 dark:text-blue-400 mb-2">85%</div>
                  <div className="text-sm text-blue-700 dark:text-blue-300 font-medium">Time saved on admin</div>
                </div>
                <div className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950/30 dark:to-green-900/30 rounded-3xl p-6 border border-green-200/50 dark:border-green-800/50">
                  <div className="text-3xl font-bold text-green-600 dark:text-green-400 mb-2">4</div>
                  <div className="text-sm text-green-700 dark:text-green-300 font-medium">Underground principles</div>
                </div>
              </div>
            </motion.div>

            {/* What You'll Learn Card */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="bg-gradient-to-br from-purple-50 to-blue-50 dark:from-purple-950/50 dark:to-blue-950/50 rounded-3xl p-8 border border-purple-200/50 dark:border-purple-800/50 shadow-xl"
            >
              <div className="flex items-start gap-6">
                <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-blue-500 rounded-2xl flex items-center justify-center flex-shrink-0">
                  <Brain className="w-8 h-8 text-white" />
                </div>
                <div className="flex-1">
                  <h3 className="text-2xl font-bold text-foreground mb-4">What You'll Uncover in This Lesson</h3>
                  <div className="grid md:grid-cols-2 gap-4">
                    <div className="space-y-3">
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-foreground">The secret patterns successful businesses use for automation</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-foreground">Why most automation feels robotic (and how to avoid it)</span>
                      </div>
                    </div>
                    <div className="space-y-3">
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-foreground">Real examples from thriving UK businesses</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-foreground">How to join the underground movement</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Article Content */}
      <section className="pb-20">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="prose prose-lg max-w-none"
            >
              {/* The Discovery */}
              <div className="bg-background/80 backdrop-blur-sm border border-border/50 rounded-2xl p-8 mb-12">
                <h2 className="text-2xl font-bold text-foreground mb-6 flex items-center gap-3">
                  <Eye className="w-6 h-6 text-blue-500" />
                  What I Discovered in 200+ UK Businesses
                </h2>
                
                <p className="text-muted-foreground mb-6 leading-relaxed">
                  Last year, I had the privilege of looking inside the operations of over 200 UK businesses. 
                  What I found was fascinating: the most successful ones weren't working harder - 
                  they had quietly discovered automation patterns that their competitors didn't even know existed.
                </p>

                <p className="text-muted-foreground mb-6 leading-relaxed">
                  These weren't tech companies or startups. These were local businesses - dental practices, 
                  fitness studios, salons, consultancies - that had cracked the code on automation without losing their human touch.
                </p>

                <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-xl p-6 mb-6">
                  <p className="text-foreground font-medium">
                    The pattern was so consistent, I started calling it "The Underground Movement" - 
                    because these businesses were quietly transforming while their competitors remained stuck in manual mode.
                  </p>
                </div>

                <p className="text-muted-foreground leading-relaxed">
                  Here's what they discovered, and how you can join them.
                </p>
              </div>

              {/* The Problem with Traditional Automation */}
              <div className="bg-background/80 backdrop-blur-sm border border-border/50 rounded-2xl p-8 mb-12">
                <h2 className="text-2xl font-bold text-foreground mb-6 flex items-center gap-3">
                  <Settings className="w-6 h-6 text-red-500" />
                  Why Most Automation Feels Robotic (And Backfires)
                </h2>

                <p className="text-muted-foreground mb-6 leading-relaxed">
                  Before we dive into what works, let's understand why most automation attempts fail. 
                  I've seen countless businesses try automation and abandon it because it felt "too robotic" or "impersonal."
                </p>

                <div className="space-y-6 mb-8">
                  <div className="flex gap-4">
                    <div className="bg-red-100 dark:bg-red-900/30 p-2 rounded-lg flex-shrink-0 h-fit">
                      <Timer className="w-5 h-5 text-red-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-foreground mb-2">The Calendar Trap</h3>
                      <p className="text-muted-foreground">
                        Most businesses automate based on time: "Send this message 3 days after signup." 
                        But customers don't live on your schedule - they have their own rhythms and needs.
                        <br /><br />
                        <strong>Result:</strong> Messages that feel irrelevant and poorly timed.
                      </p>
                    </div>
                  </div>

                  <div className="flex gap-4">
                    <div className="bg-red-100 dark:bg-red-900/30 p-2 rounded-lg flex-shrink-0 h-fit">
                      <MessageSquare className="w-5 h-5 text-red-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-foreground mb-2">The Template Trap</h3>
                      <p className="text-muted-foreground">
                        Using the same generic message for everyone: "Hi [NAME], hope you're well..." 
                        These messages scream "automation" and get ignored.
                        <br /><br />
                        <strong>Result:</strong> Lower engagement and damaged relationships.
                      </p>
                    </div>
                  </div>

                  <div className="flex gap-4">
                    <div className="bg-red-100 dark:bg-red-900/30 p-2 rounded-lg flex-shrink-0 h-fit">
                      <Target className="w-5 h-5 text-red-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-foreground mb-2">The One-Size-Fits-All Trap</h3>
                      <p className="text-muted-foreground">
                        Treating all customers the same regardless of their behavior, preferences, or stage in the journey.
                        <br /><br />
                        <strong>Result:</strong> Irrelevant messages that feel like spam.
                      </p>
                    </div>
                  </div>
                </div>

                <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl p-6">
                  <p className="text-foreground font-medium">
                    The underground movement discovered that successful automation isn't about replacing human touch - 
                    it's about amplifying it intelligently.
                  </p>
                </div>
              </div>

              {/* The Underground Principles */}
              <div className="bg-background/80 backdrop-blur-sm border border-border/50 rounded-2xl p-8 mb-12">
                <h2 className="text-2xl font-bold text-foreground mb-6 flex items-center gap-3">
                  <Lightbulb className="w-6 h-6 text-yellow-500" />
                  The 4 Underground Principles
                </h2>

                <p className="text-muted-foreground mb-8 leading-relaxed">
                  The businesses that cracked automation follow four core principles that make their automated messages 
                  feel more personal than most manual ones:
                </p>

                <div className="space-y-8">
                  <div className="flex gap-4">
                    <div className="bg-yellow-100 dark:bg-yellow-900/30 p-3 rounded-lg flex-shrink-0 h-fit">
                      <span className="font-bold text-yellow-600">1</span>
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-foreground mb-3">Behavior-Based Triggers</h3>
                      <p className="text-muted-foreground mb-4">
                        Instead of calendar-based automation, they trigger messages based on what customers actually do. 
                        Booked an appointment? Visited the website? Opened an email? Each action triggers a relevant response.
                      </p>
                      <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                        <p className="text-sm text-foreground">
                          <strong>Example:</strong> A fitness studio sends workout tips only to members who've been active, 
                          and re-engagement messages only to those who've been absent - not to everyone on a schedule.
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="flex gap-4">
                    <div className="bg-yellow-100 dark:bg-yellow-900/30 p-3 rounded-lg flex-shrink-0 h-fit">
                      <span className="font-bold text-yellow-600">2</span>
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-foreground mb-3">Context-Aware Messaging</h3>
                      <p className="text-muted-foreground mb-4">
                        Every message includes relevant context - why they're receiving it, what triggered it, 
                        and how it relates to their specific situation.
                      </p>
                      <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                        <p className="text-sm text-foreground">
                          <strong>Example:</strong> "Hi Sarah, I noticed you booked a color consultation for next week. 
                          Here are some inspiration photos based on the look you mentioned wanting to achieve..."
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="flex gap-4">
                    <div className="bg-yellow-100 dark:bg-yellow-900/30 p-3 rounded-lg flex-shrink-0 h-fit">
                      <span className="font-bold text-yellow-600">3</span>
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-foreground mb-3">Value-First Automation</h3>
                      <p className="text-muted-foreground mb-4">
                        Every automated message provides genuine value - tips, insights, reminders, or resources - 
                        rather than just asking for something.
                      </p>
                      <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                        <p className="text-sm text-foreground">
                          <strong>Example:</strong> A dental practice sends oral health tips triggered by seasonal changes, 
                          not just appointment reminders.
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="flex gap-4">
                    <div className="bg-yellow-100 dark:bg-yellow-900/30 p-3 rounded-lg flex-shrink-0 h-fit">
                      <span className="font-bold text-yellow-600">4</span>
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-foreground mb-3">Human Escape Hatches</h3>
                      <p className="text-muted-foreground mb-4">
                        They always provide easy ways for customers to reach a real person when needed, 
                        and they monitor for situations that require human intervention.
                      </p>
                      <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                        <p className="text-sm text-foreground">
                          <strong>Example:</strong> If someone replies to an automated message with a question, 
                          it immediately alerts a team member to respond personally.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Real Underground Examples */}
              <div className="bg-background/80 backdrop-blur-sm border border-border/50 rounded-2xl p-8 mb-12">
                <h2 className="text-2xl font-bold text-foreground mb-6 flex items-center gap-3">
                  <Users className="w-6 h-6 text-green-500" />
                  Real Underground Examples
                </h2>

                <p className="text-muted-foreground mb-8 leading-relaxed">
                  Here are two real businesses that joined the underground movement and transformed their operations:
                </p>

                <div className="space-y-12">
                  {automationExamples.map((example, index) => (
                    <div key={index} className="border-l-4 border-primary-500 pl-6">
                      <div className="flex items-start justify-between mb-4">
                        <div>
                          <h3 className="text-xl font-semibold text-foreground">{example.business}</h3>
                          <p className="text-muted-foreground">{example.owner} • {example.location}</p>
                        </div>
                      </div>

                      <div className="grid md:grid-cols-2 gap-6 mb-6">
                        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl p-4">
                          <h4 className="font-semibold text-foreground mb-2">The Challenge</h4>
                          <p className="text-muted-foreground text-sm">{example.challenge}</p>
                        </div>

                        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-xl p-4">
                          <h4 className="font-semibold text-foreground mb-2">The Solution</h4>
                          <p className="text-muted-foreground text-sm">{example.solution}</p>
                        </div>
                      </div>

                      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-xl p-6 mb-6">
                        <h4 className="font-semibold text-foreground mb-4">Automation Sequence</h4>
                        <p className="text-sm text-muted-foreground mb-4">
                          <strong>Trigger:</strong> {example.automation.trigger}
                        </p>
                        <div className="space-y-2">
                          {example.automation.sequence.map((step, stepIndex) => (
                            <div key={stepIndex} className="flex items-center gap-3">
                              <div className="bg-primary-100 dark:bg-primary-900/30 w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0">
                                <span className="text-xs font-medium text-primary-600">{stepIndex + 1}</span>
                              </div>
                              <p className="text-sm text-foreground">{step}</p>
                            </div>
                          ))}
                        </div>
                      </div>

                      <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-xl p-6">
                        <h4 className="font-semibold text-foreground mb-4">Results After 6 Months</h4>
                        <div className="grid sm:grid-cols-2 gap-3">
                          {example.results.map((result, resultIndex) => (
                            <div key={resultIndex} className="flex items-center gap-2">
                              <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                              <p className="text-sm text-foreground">{result}</p>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* The GenLogic Method */}
              <div className="bg-background/80 backdrop-blur-sm border border-border/50 rounded-2xl p-8 mb-12">
                <h2 className="text-2xl font-bold text-foreground mb-6 flex items-center gap-3">
                  <Workflow className="w-6 h-6 text-purple-500" />
                  The GenLogic Method: How We Make This Simple
                </h2>

                <p className="text-muted-foreground mb-6 leading-relaxed">
                  At GenLogic, we've distilled these underground principles into a simple methodology that any business can implement.
                  Here's how we help businesses join the movement:
                </p>

                <div className="space-y-6">
                  <div className="bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-xl p-6">
                    <h3 className="font-semibold text-foreground mb-4">Phase 1: Behavior Mapping</h3>
                    <p className="text-muted-foreground mb-4">
                      We identify the key behaviors and touchpoints in your customer journey:
                    </p>
                    <ul className="space-y-2 text-muted-foreground">
                      <li className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                        Initial inquiry and interest signals
                      </li>
                      <li className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                        Booking and appointment behaviors
                      </li>
                      <li className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                        Engagement and interaction patterns
                      </li>
                      <li className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                        Purchase and loyalty indicators
                      </li>
                    </ul>
                  </div>

                  <div className="bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-xl p-6">
                    <h3 className="font-semibold text-foreground mb-4">Phase 2: Value Creation</h3>
                    <p className="text-muted-foreground mb-4">
                      We help you create a library of valuable content for each customer segment:
                    </p>
                    <ul className="space-y-2 text-muted-foreground">
                      <li className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                        Educational content relevant to their needs
                      </li>
                      <li className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                        Seasonal tips and recommendations
                      </li>
                      <li className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                        Personalized reminders and care instructions
                      </li>
                      <li className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                        Success stories and social proof
                      </li>
                    </ul>
                  </div>

                  <div className="bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-xl p-6">
                    <h3 className="font-semibold text-foreground mb-4">Phase 3: Intelligent Automation</h3>
                    <p className="text-muted-foreground mb-4">
                      We set up automation that responds intelligently to customer behavior:
                    </p>
                    <ul className="space-y-2 text-muted-foreground">
                      <li className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                        Behavior-triggered sequences that feel natural
                      </li>
                      <li className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                        Context-aware messaging that's always relevant
                      </li>
                      <li className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                        Human escalation for complex situations
                      </li>
                      <li className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                        Continuous optimization based on results
                      </li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* The Transformation */}
              <div className="bg-background/80 backdrop-blur-sm border border-border/50 rounded-2xl p-8 mb-12">
                <h2 className="text-2xl font-bold text-foreground mb-6 flex items-center gap-3">
                  <BarChart3 className="w-6 h-6 text-blue-500" />
                  What Happens When You Join the Movement
                </h2>

                <div className="grid md:grid-cols-2 gap-8 mb-8">
                  <div>
                    <h3 className="font-semibold text-foreground mb-4">Immediate Changes (First 30 Days)</h3>
                    <ul className="space-y-3 text-muted-foreground">
                      <li className="flex items-center gap-2">
                        <TrendingUp className="w-4 h-4 text-green-500 flex-shrink-0" />
                        Dramatic reduction in manual admin tasks
                      </li>
                      <li className="flex items-center gap-2">
                        <TrendingUp className="w-4 h-4 text-green-500 flex-shrink-0" />
                        More consistent customer communication
                      </li>
                      <li className="flex items-center gap-2">
                        <TrendingUp className="w-4 h-4 text-green-500 flex-shrink-0" />
                        Fewer missed follow-ups and opportunities
                      </li>
                      <li className="flex items-center gap-2">
                        <TrendingUp className="w-4 h-4 text-green-500 flex-shrink-0" />
                        Improved customer satisfaction scores
                      </li>
                    </ul>
                  </div>

                  <div>
                    <h3 className="font-semibold text-foreground mb-4">Long-term Transformation (3-6 months)</h3>
                    <ul className="space-y-3 text-muted-foreground">
                      <li className="flex items-center gap-2">
                        <TrendingUp className="w-4 h-4 text-green-500 flex-shrink-0" />
                        Customers actively look forward to your messages
                      </li>
                      <li className="flex items-center gap-2">
                        <TrendingUp className="w-4 h-4 text-green-500 flex-shrink-0" />
                        Significant increase in customer lifetime value
                      </li>
                      <li className="flex items-center gap-2">
                        <TrendingUp className="w-4 h-4 text-green-500 flex-shrink-0" />
                        More referrals from engaged customers
                      </li>
                      <li className="flex items-center gap-2">
                        <TrendingUp className="w-4 h-4 text-green-500 flex-shrink-0" />
                        Competitive advantage in your market
                      </li>
                    </ul>
                  </div>
                </div>

                <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-xl p-6">
                  <p className="text-foreground font-medium">
                    <strong>The Real Transformation:</strong> You stop working IN your business and start working ON it.
                    Automation handles the routine, so you can focus on growth, strategy, and the work only you can do.
                  </p>
                </div>
              </div>

              {/* Join the Movement */}
              <div className="bg-gradient-to-br from-primary-50 to-primary-100 dark:from-primary-900/20 dark:to-primary-800/20 border border-primary-200 dark:border-primary-800 rounded-2xl p-8 mb-12">
                <h2 className="text-2xl font-bold text-foreground mb-6 flex items-center gap-3">
                  <Shield className="w-6 h-6 text-primary-600" />
                  Something's Changing
                </h2>

                <p className="text-muted-foreground mb-6 leading-relaxed">
                  This isn't about replacing human connection - it's about amplifying it. The businesses that understand this
                  are quietly building stronger relationships, saving countless hours, and growing faster than their competitors.
                </p>

                <p className="text-muted-foreground mb-6 leading-relaxed">
                  The underground movement is happening whether you participate or not. Your competitors are discovering these patterns.
                  Your customers are experiencing this level of service elsewhere.
                </p>

                <p className="text-muted-foreground mb-8 leading-relaxed">
                  Smart business owners are quietly discovering what successful companies already know about automation transformation. The shift is happening whether you participate or not.
                </p>

                <div className="text-center">
                  <Link
                    href="/demo"
                    className="inline-flex items-center gap-2 bg-primary-600 hover:bg-primary-700 text-white px-8 py-4 rounded-xl font-semibold transition-all duration-300 hover:shadow-lg hover:shadow-primary-500/25"
                  >
                    See What They Know
                    <ArrowRight className="w-5 h-5" />
                  </Link>
                  <p className="text-sm text-muted-foreground mt-4">
                    Join the business owners who've already discovered the underground movement
                  </p>
                </div>
              </div>

              {/* Next Lesson Navigation */}
              <div className="bg-background rounded-3xl border border-border shadow-lg p-8">
                <h3 className="text-xl font-bold text-foreground mb-6 text-center">Continue Your Learning Journey</h3>

                <div className="grid md:grid-cols-2 gap-6">
                  <Link
                    href="/learn/fundamentals/three-touch-rule"
                    className="group bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/30 dark:to-blue-900/30 rounded-2xl p-6 border border-blue-200/50 dark:border-blue-800/50 hover:shadow-lg transition-all duration-300"
                  >
                    <div className="flex items-center gap-4 mb-4">
                      <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/50 rounded-xl flex items-center justify-center">
                        <ArrowRight className="w-6 h-6 text-blue-600 group-hover:translate-x-1 transition-transform" />
                      </div>
                      <div>
                        <h4 className="font-bold text-foreground">Next Lesson</h4>
                        <p className="text-sm text-muted-foreground">Lesson 3 of 15</p>
                      </div>
                    </div>
                    <h5 className="font-semibold text-foreground mb-2">The 3-Touch Rule</h5>
                    <p className="text-sm text-muted-foreground">
                      The psychological sequence that converts 40% more prospects
                    </p>
                  </Link>

                  <Link
                    href="/learn"
                    className="group bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-950/30 dark:to-gray-900/30 rounded-2xl p-6 border border-gray-200/50 dark:border-gray-800/50 hover:shadow-lg transition-all duration-300"
                  >
                    <div className="flex items-center gap-4 mb-4">
                      <div className="w-12 h-12 bg-gray-100 dark:bg-gray-900/50 rounded-xl flex items-center justify-center">
                        <ArrowLeft className="w-6 h-6 text-gray-600 group-hover:-translate-x-1 transition-transform" />
                      </div>
                      <div>
                        <h4 className="font-bold text-foreground">Academy</h4>
                        <p className="text-sm text-muted-foreground">All lessons</p>
                      </div>
                    </div>
                    <h5 className="font-semibold text-foreground mb-2">Back to Academy</h5>
                    <p className="text-sm text-muted-foreground">
                      View all lessons and track your progress
                    </p>
                  </Link>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>
    </div>
  )
}
