/* ===== GENLOGIC MINIMAL BRANDING FOR GOHIGHLEVEL ===== */
/* Conservative approach - only essential elements */

/* ===== GENLOGIC COLORS ===== */
:root {
  --genlogic-primary: #a855f7;
  --genlogic-primary-dark: #7c3aed;
  --genlogic-accent: #f97316;
  --genlogic-success: #059669;
  --genlogic-dark: #0f172a;
  --genlogic-gray: #64748b;
  --genlogic-light: #f8fafc;
}

/* ===== ONLY TARGET SAFE ELEMENTS ===== */

/* Override GHL orange colors specifically */
[style*="background-color: #ff6b35"] {
  background-color: var(--genlogic-primary) !important;
}

[style*="background: #ff6b35"] {
  background: var(--genlogic-primary) !important;
}

[style*="color: #ff6b35"] {
  color: var(--genlogic-primary) !important;
}

/* Target specific button classes only */
.btn-primary {
  background-color: var(--genlogic-primary) !important;
  border-color: var(--genlogic-primary) !important;
}

.btn-primary:hover {
  background-color: var(--genlogic-primary-dark) !important;
  border-color: var(--genlogic-primary-dark) !important;
}

/* Target submit buttons */
input[type="submit"] {
  background-color: var(--genlogic-primary) !important;
  border-color: var(--genlogic-primary) !important;
  color: white !important;
}

input[type="submit"]:hover {
  background-color: var(--genlogic-primary-dark) !important;
}

/* Target focus states for inputs */
input:focus,
textarea:focus,
select:focus {
  border-color: var(--genlogic-primary) !important;
  box-shadow: 0 0 0 2px rgba(168, 85, 247, 0.2) !important;
}

/* Target links */
a {
  color: var(--genlogic-primary) !important;
}

a:hover {
  color: var(--genlogic-primary-dark) !important;
}

/* Target badges */
.badge-primary {
  background-color: var(--genlogic-primary) !important;
}

.badge-success {
  background-color: var(--genlogic-success) !important;
}

.badge-warning {
  background-color: var(--genlogic-accent) !important;
}

/* Target progress bars */
.progress-bar {
  background-color: var(--genlogic-primary) !important;
}

/* Target alerts */
.alert-primary {
  background-color: rgba(168, 85, 247, 0.1) !important;
  border-color: var(--genlogic-primary) !important;
  color: var(--genlogic-primary) !important;
}

/* Target active navigation items */
.nav-link.active {
  color: var(--genlogic-primary) !important;
}

.nav-link:hover {
  color: var(--genlogic-primary) !important;
}

/* Target dropdown items */
.dropdown-item:hover {
  background-color: rgba(168, 85, 247, 0.1) !important;
  color: var(--genlogic-primary) !important;
}

/* Target table hover */
.table-hover tbody tr:hover {
  background-color: rgba(168, 85, 247, 0.05) !important;
}

/* Target loader */
.lds-ring div {
  border-color: var(--genlogic-primary) transparent transparent transparent !important;
}

/* ===== END MINIMAL BRANDING ===== */
