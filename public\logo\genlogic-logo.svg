<?xml version="1.0" encoding="UTF-8"?>
<svg width="200" height="60" viewBox="0 0 200 60" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Logo Background with Gradient -->
  <defs>
    <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2563eb;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#1d4ed8;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1e40af;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1f2937;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#374151;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Logo Icon -->
  <rect x="5" y="10" width="40" height="40" rx="8" fill="url(#logoGradient)"/>
  
  <!-- Letter G -->
  <text x="25" y="35" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="white" text-anchor="middle">G</text>
  
  <!-- Company Name -->
  <text x="55" y="32" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="url(#textGradient)">GenLogic</text>
  
  <!-- Tagline -->
  <text x="55" y="45" font-family="Arial, sans-serif" font-size="10" font-weight="500" fill="#6b7280">Business Automation</text>
  
  <!-- Sparkle Effect -->
  <circle cx="40" cy="15" r="1.5" fill="#f59e0b" opacity="0.8">
    <animate attributeName="opacity" values="0.8;0.3;0.8" dur="2s" repeatCount="indefinite"/>
  </circle>
  <circle cx="42" cy="12" r="1" fill="#f59e0b" opacity="0.6">
    <animate attributeName="opacity" values="0.6;0.2;0.6" dur="2.5s" repeatCount="indefinite"/>
  </circle>
</svg>
