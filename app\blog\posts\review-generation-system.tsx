import React from 'react'
import { BlogPost } from '../types'

const BlogContent = () => {
  return (
    <div className="max-w-4xl mx-auto space-y-12">
      
      {/* Opening Hook */}
      <div className="bg-gradient-to-br from-yellow-50 to-orange-50 dark:from-yellow-950/20 dark:to-orange-950/20 rounded-3xl p-8 border border-yellow-200/50 dark:border-yellow-800/30">
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6 text-center">
          You've just delivered amazing service.
        </h2>
        <p className="text-lg text-gray-700 dark:text-gray-300 mb-4 text-center">
          Your customer is thrilled. They're telling their friends how brilliant you are. But when it comes to leaving a Google review? 
          Silence. Crickets. Nothing.
        </p>
        <p className="text-lg text-gray-700 dark:text-gray-300 text-center font-medium">
          Meanwhile, your competitor with average service has 47 five-star reviews.
        </p>
      </div>

      {/* The Problem */}
      <div>
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
          The Review Generation Crisis
        </h2>
        <p className="text-lg text-gray-700 dark:text-gray-300 mb-8">
          Only 5-9% of satisfied customers leave reviews without being asked. Yet 88% of consumers trust online reviews as much as personal recommendations. This creates a massive problem for quality businesses.
        </p>

        <div className="grid md:grid-cols-3 gap-6 mb-8">
          <div className="bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800/30 rounded-2xl p-6 text-center">
            <div className="text-4xl mb-4">😤</div>
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3">The Frustration</h3>
            <div className="text-3xl font-bold text-red-600 dark:text-red-400 mb-2">5-9%</div>
            <p className="text-gray-700 dark:text-gray-300">Of happy customers leave reviews naturally</p>
          </div>
          <div className="bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800/30 rounded-2xl p-6 text-center">
            <div className="text-4xl mb-4">⭐</div>
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3">The Impact</h3>
            <div className="text-3xl font-bold text-red-600 dark:text-red-400 mb-2">88%</div>
            <p className="text-gray-700 dark:text-gray-300">Of consumers trust online reviews</p>
          </div>
          <div className="bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800/30 rounded-2xl p-6 text-center">
            <div className="text-4xl mb-4">📈</div>
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3">The Opportunity</h3>
            <div className="text-3xl font-bold text-red-600 dark:text-red-400 mb-2">25%</div>
            <p className="text-gray-700 dark:text-gray-300">More bookings with good reviews</p>
          </div>
        </div>

        <div className="bg-yellow-50 dark:bg-yellow-950/20 border border-yellow-200 dark:border-yellow-800/30 rounded-2xl p-6">
          <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-4">
            🧮 The Hidden Cost of Missing Reviews
          </h3>
          <p className="text-gray-700 dark:text-gray-300 mb-4">
            Every missing review costs you potential customers. Here's the math:
          </p>
          <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-bold text-gray-900 dark:text-gray-100 mb-2">Without Reviews:</h4>
                <ul className="text-gray-700 dark:text-gray-300 space-y-1">
                  <li>• 3.2 star average (few reviews)</li>
                  <li>• 67% of people scroll past</li>
                  <li>• Lower Google ranking</li>
                  <li>• Customers choose competitors</li>
                </ul>
              </div>
              <div>
                <h4 className="font-bold text-gray-900 dark:text-gray-100 mb-2">With Automated Reviews:</h4>
                <ul className="text-gray-700 dark:text-gray-300 space-y-1">
                  <li>• 4.6+ star average (many reviews)</li>
                  <li>• 89% more likely to be chosen</li>
                  <li>• Higher Google ranking</li>
                  <li>• 25% more bookings</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* The Solution */}
      <div>
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
          The Automated Review Generation System
        </h2>
        <p className="text-lg text-gray-700 dark:text-gray-300 mb-8">
          Here's the exact system that helped UK businesses increase their review rate from 5% to 45% - completely automated.
        </p>

        <div className="grid md:grid-cols-2 gap-6 mb-8">
          <div className="bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800/30 rounded-2xl p-6">
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-4">❌ What Most Businesses Do</h3>
            <ul className="space-y-2">
              <li className="flex items-center text-gray-700 dark:text-gray-300">
                <span className="w-2 h-2 bg-red-500 rounded-full mr-3"></span>
                Hope customers leave reviews naturally
              </li>
              <li className="flex items-center text-gray-700 dark:text-gray-300">
                <span className="w-2 h-2 bg-red-500 rounded-full mr-3"></span>
                Occasionally ask in person (awkward)
              </li>
              <li className="flex items-center text-gray-700 dark:text-gray-300">
                <span className="w-2 h-2 bg-red-500 rounded-full mr-3"></span>
                Put up signs asking for reviews
              </li>
              <li className="flex items-center text-gray-700 dark:text-gray-300">
                <span className="w-2 h-2 bg-red-500 rounded-full mr-3"></span>
                Get frustrated with low review count
              </li>
            </ul>
          </div>
          
          <div className="bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800/30 rounded-2xl p-6">
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-4">✅ What Smart Businesses Do</h3>
            <ul className="space-y-2">
              <li className="flex items-center text-gray-700 dark:text-gray-300">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                Automated review requests after service
              </li>
              <li className="flex items-center text-gray-700 dark:text-gray-300">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                Perfect timing for maximum response
              </li>
              <li className="flex items-center text-gray-700 dark:text-gray-300">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                Direct links to review platforms
              </li>
              <li className="flex items-center text-gray-700 dark:text-gray-300">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                Consistent 4.6+ star ratings
              </li>
            </ul>
          </div>
        </div>
      </div>

      {/* The 3-Step System */}
      <div>
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
          The 3-Step Automated Review System
        </h2>

        <div className="space-y-6">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700">
            <div className="flex items-start space-x-4">
              <div className="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0 mt-1">1</div>
              <div>
                <h4 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3">The Thank You Message (2 Hours After Service)</h4>
                <p className="text-gray-700 dark:text-gray-300 mb-4">
                  Strike while the experience is fresh and positive emotions are high.
                </p>
                <div className="bg-green-50 dark:bg-green-950/20 rounded-xl p-4">
                  <h5 className="font-bold text-green-800 dark:text-green-300 mb-2">Message Template:</h5>
                  <div className="text-green-700 dark:text-green-400 text-sm font-mono bg-white dark:bg-gray-800 p-3 rounded border">
                    "Hi [Name]! 😊<br/><br/>
                    Thank you for choosing [Business Name] today. We hope you're delighted with your [service]!<br/><br/>
                    We'd love to hear about your experience. If you have 30 seconds, would you mind sharing a quick review?<br/><br/>
                    👉 Leave a Google review: [direct-link]<br/><br/>
                    Your feedback helps other customers find us and helps us improve our service.<br/><br/>
                    Thanks again!<br/>
                    [Your Name]"
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700">
            <div className="flex items-start space-x-4">
              <div className="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0 mt-1">2</div>
              <div>
                <h4 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3">The Gentle Reminder (1 Week Later)</h4>
                <p className="text-gray-700 dark:text-gray-300 mb-4">
                  For those who didn't respond to the first message - a softer approach.
                </p>
                <div className="bg-blue-50 dark:bg-blue-950/20 rounded-xl p-4">
                  <h5 className="font-bold text-blue-800 dark:text-blue-300 mb-2">Follow-up Template:</h5>
                  <div className="text-blue-700 dark:text-blue-400 text-sm font-mono bg-white dark:bg-gray-800 p-3 rounded border">
                    "Hi [Name],<br/><br/>
                    Hope you're still enjoying the results of your [service] from last week!<br/><br/>
                    If you have a spare moment, we'd be incredibly grateful for a quick review. It really helps our small business grow.<br/><br/>
                    👉 Quick review here: [direct-link]<br/><br/>
                    No worries if you're too busy - we appreciate you either way!<br/><br/>
                    Best regards,<br/>
                    [Your Name]"
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700">
            <div className="flex items-start space-x-4">
              <div className="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0 mt-1">3</div>
              <div>
                <h4 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3">The Incentive Offer (1 Month Later)</h4>
                <p className="text-gray-700 dark:text-gray-300 mb-4">
                  Final attempt with a small incentive for future bookings.
                </p>
                <div className="bg-purple-50 dark:bg-purple-950/20 rounded-xl p-4">
                  <h5 className="font-bold text-purple-800 dark:text-purple-300 mb-2">Incentive Template:</h5>
                  <div className="text-purple-700 dark:text-purple-400 text-sm font-mono bg-white dark:bg-gray-800 p-3 rounded border">
                    "Hi [Name],<br/><br/>
                    It's been a month since your [service] - hope you're still loving the results!<br/><br/>
                    We're always looking to improve, and honest reviews help us do that. As a thank you for taking 2 minutes to leave a review, we'd love to offer you 10% off your next appointment.<br/><br/>
                    👉 Leave review & get discount: [link]<br/><br/>
                    Thanks for being an amazing customer!<br/><br/>
                    [Your Name]"
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Technical Implementation */}
      <div>
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
          Technical Implementation Guide
        </h2>

        <div className="space-y-6">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700">
            <h4 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-4">🔗 Setting Up Direct Review Links</h4>
            <p className="text-gray-700 dark:text-gray-300 mb-4">
              Make it as easy as possible for customers to leave reviews with direct links.
            </p>
            <div className="grid md:grid-cols-2 gap-4">
              <div className="bg-blue-50 dark:bg-blue-950/20 rounded-xl p-4">
                <h5 className="font-bold text-blue-800 dark:text-blue-300 mb-2">Google Reviews:</h5>
                <p className="text-blue-700 dark:text-blue-400 text-sm mb-2">
                  Find your Google Business Profile and create a direct review link:
                </p>
                <code className="text-xs bg-white dark:bg-gray-800 p-2 rounded block">
                  https://search.google.com/local/writereview?placeid=[YOUR_PLACE_ID]
                </code>
              </div>
              <div className="bg-green-50 dark:bg-green-950/20 rounded-xl p-4">
                <h5 className="font-bold text-green-800 dark:text-green-300 mb-2">Facebook Reviews:</h5>
                <p className="text-green-700 dark:text-green-400 text-sm mb-2">
                  Use your Facebook page URL with review parameter:
                </p>
                <code className="text-xs bg-white dark:bg-gray-800 p-2 rounded block">
                  https://facebook.com/[YOUR_PAGE]/reviews
                </code>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700">
            <h4 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-4">⚙️ Automation Setup</h4>
            <p className="text-gray-700 dark:text-gray-300 mb-4">
              Connect your booking system to trigger automated review requests.
            </p>
            <div className="bg-yellow-50 dark:bg-yellow-950/20 rounded-xl p-4">
              <h5 className="font-bold text-yellow-800 dark:text-yellow-300 mb-3">Automation Triggers:</h5>
              <ul className="text-yellow-700 dark:text-yellow-400 space-y-2">
                <li><strong>Trigger 1:</strong> 2 hours after appointment marked "completed"</li>
                <li><strong>Trigger 2:</strong> 7 days after first message (if no review detected)</li>
                <li><strong>Trigger 3:</strong> 30 days after first message (if no review detected)</li>
                <li><strong>Stop condition:</strong> Customer leaves review (stop all future messages)</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Results */}
      <div>
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
          The Results: What You'll Achieve
        </h2>
        
        <div className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-950/20 dark:to-emerald-950/20 rounded-3xl p-8 border border-green-200/50 dark:border-green-800/30 text-center mb-8">
          <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6">
            After Implementing Automated Review Generation:
          </h3>
          
          <div className="grid md:grid-cols-3 gap-6 mb-6">
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
              <div className="text-4xl font-bold text-green-600 dark:text-green-400 mb-2">45%</div>
              <div className="text-gray-700 dark:text-gray-300">Review response rate</div>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
              <div className="text-4xl font-bold text-green-600 dark:text-green-400 mb-2">4.6+</div>
              <div className="text-gray-700 dark:text-gray-300">Average star rating</div>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
              <div className="text-4xl font-bold text-green-600 dark:text-green-400 mb-2">25%</div>
              <div className="text-gray-700 dark:text-gray-300">More bookings</div>
            </div>
          </div>
          
          <p className="text-xl font-bold text-gray-900 dark:text-gray-100">
            From invisible online to the top-rated business in your area.
          </p>
        </div>

        <div className="text-center">
          <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4">
            Stop Hoping for Reviews. Start Generating Them.
          </h3>
          <p className="text-lg text-gray-700 dark:text-gray-300">
            Every day without this system is another day your competitors are building their online reputation while you stay invisible.
          </p>
        </div>
      </div>

    </div>
  )
}

export const reviewGenerationPost: BlogPost = {
  id: '7',
  slug: 'review-generation-system',
  title: 'Stop Hoping for Reviews: The Automated System That Gets 45% Response Rate',
  excerpt: 'Only 5-9% of satisfied customers leave reviews naturally. Here\'s the automated 3-step system that increased review rates to 45% for UK businesses.',
  content: <BlogContent />,
  author: {
    name: 'Chedi Rach',
    role: 'Business Automation Specialist',
    avatar: '/logo/genlogic-icon-only.svg',
    bio: 'Chedi helps UK businesses build strong online reputations through automated review generation and reputation management.'
  },
  publishedAt: '2025-07-03',
  readingTime: '6 min read',
  category: 'Marketing',
  tags: ['reviews', 'reputation', 'automation', 'google-reviews'],
  featured: false,
  image: '/blog-images/review-generation-system.webp',
  views: 1834,
  likes: 102
}
