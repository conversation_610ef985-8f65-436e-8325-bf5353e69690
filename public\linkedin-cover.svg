<svg width="1584" height="396" viewBox="0 0 1584 396" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Background Gradients -->
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0f172a;stop-opacity:1" />
      <stop offset="30%" style="stop-color:#1e293b;stop-opacity:1" />
      <stop offset="70%" style="stop-color:#334155;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#475569;stop-opacity:1" />
    </linearGradient>
    
    <!-- Purple Gradient -->
    <linearGradient id="purpleGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#a855f7;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#7c3aed;stop-opacity:1" />
    </linearGradient>
    
    <!-- Orange Gradient -->
    <linearGradient id="orangeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f97316;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ea580c;stop-opacity:1" />
    </linearGradient>
    
    <!-- Green Gradient -->
    <linearGradient id="greenGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#059669;stop-opacity:1" />
    </linearGradient>
    
    <!-- Text Gradient -->
    <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#a855f7;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#f97316;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#10b981;stop-opacity:1" />
    </linearGradient>
    
    <!-- Glow Filter -->
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    
    <!-- Shadow Filter -->
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="4" stdDeviation="8" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="1584" height="396" fill="url(#bgGradient)"/>
  
  <!-- Decorative Background Elements -->
  <circle cx="1400" cy="80" r="120" fill="url(#purpleGradient)" opacity="0.1"/>
  <circle cx="1500" cy="300" r="80" fill="url(#orangeGradient)" opacity="0.1"/>
  <circle cx="200" cy="350" r="100" fill="url(#greenGradient)" opacity="0.1"/>
  
  <!-- Grid Pattern -->
  <defs>
    <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
      <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#475569" stroke-width="0.5" opacity="0.3"/>
    </pattern>
  </defs>
  <rect width="1584" height="396" fill="url(#grid)"/>
  
  <!-- Left Side Content -->
  <g transform="translate(60, 40)">

    <!-- GenLogic Logo and Brand -->
    <g transform="translate(0, 0)">
      <!-- Logo Background -->
      <rect x="0" y="0" width="50" height="50" rx="10" fill="url(#purpleGradient)" filter="url(#shadow)"/>
      <!-- Logo Letter -->
      <text x="25" y="35" text-anchor="middle" font-family="Arial, sans-serif" font-size="28" font-weight="bold" fill="white">G</text>

      <!-- Company Name -->
      <text x="65" y="28" font-family="Arial, sans-serif" font-size="32" font-weight="bold" fill="white">
        GenLogic
      </text>
      <text x="65" y="45" font-family="Arial, sans-serif" font-size="14" font-weight="500" fill="#94a3b8">
        Business Automation Specialist
      </text>
    </g>

    <!-- Main Headline -->
    <g transform="translate(0, 100)">
      <text x="0" y="0" font-family="Arial, sans-serif" font-size="42" font-weight="bold" fill="white" filter="url(#glow)">
        Convert
        <tspan fill="url(#textGradient)">89% of Prospects</tspan>
      </text>
      <text x="0" y="50" font-family="Arial, sans-serif" font-size="42" font-weight="bold" fill="white" filter="url(#glow)">
        Into Customers
      </text>
    </g>

    <!-- Subheadline -->
    <text x="0" y="210" font-family="Arial, sans-serif" font-size="20" font-weight="500" fill="#e2e8f0" opacity="0.9">
      Helping UK businesses increase revenue by £50K+ annually
    </text>
    <text x="0" y="235" font-family="Arial, sans-serif" font-size="20" font-weight="500" fill="#e2e8f0" opacity="0.9">
      through smart follow-up automation
    </text>

    <!-- Contact Info -->
    <g transform="translate(0, 280)">
      <rect x="0" y="0" width="600" height="50" rx="25" fill="url(#purpleGradient)" opacity="0.8" filter="url(#shadow)"/>
      <text x="30" y="32" font-family="Arial, sans-serif" font-size="16" font-weight="600" fill="white">
        📞 +44 7401 137621  •  ✉️ <EMAIL>  •  🌐 genlogic.io
      </text>
    </g>

  </g>
  
  <!-- Right Side Stats -->
  <g transform="translate(1050, 60)">

    <!-- Stats Container -->
    <rect x="0" y="0" width="480" height="280" rx="24" fill="rgba(255, 255, 255, 0.08)" stroke="rgba(168, 85, 247, 0.4)" stroke-width="2" filter="url(#shadow)"/>

    <!-- Stats Title -->
    <text x="240" y="40" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="white">
      Proven Results
    </text>

    <!-- Stat 1: Conversion Rate -->
    <g transform="translate(60, 80)">
      <circle cx="0" cy="0" r="30" fill="url(#purpleGradient)" filter="url(#shadow)"/>
      <text x="0" y="8" text-anchor="middle" font-family="Arial, sans-serif" font-size="22" font-weight="bold" fill="white">89%</text>
      <text x="50" y="0" font-family="Arial, sans-serif" font-size="18" font-weight="600" fill="white">Conversion Rate</text>
      <text x="50" y="20" font-family="Arial, sans-serif" font-size="14" fill="#94a3b8">vs 15% industry average</text>
    </g>

    <!-- Stat 2: Revenue Increase -->
    <g transform="translate(60, 150)">
      <circle cx="0" cy="0" r="30" fill="url(#greenGradient)" filter="url(#shadow)"/>
      <text x="0" y="8" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="white">£50K+</text>
      <text x="50" y="0" font-family="Arial, sans-serif" font-size="18" font-weight="600" fill="white">Revenue Increase</text>
      <text x="50" y="20" font-family="Arial, sans-serif" font-size="14" fill="#94a3b8">Average annual growth</text>
    </g>

    <!-- Stat 3: Clients Served -->
    <g transform="translate(60, 220)">
      <circle cx="0" cy="0" r="30" fill="url(#orangeGradient)" filter="url(#shadow)"/>
      <text x="0" y="8" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="white">500+</text>
      <text x="50" y="0" font-family="Arial, sans-serif" font-size="18" font-weight="600" fill="white">UK Businesses</text>
      <text x="50" y="20" font-family="Arial, sans-serif" font-size="14" fill="#94a3b8">Successfully automated</text>
    </g>

  </g>
  
  <!-- Decorative Elements -->
  <g opacity="0.4">
    <!-- Floating Icons - Better Spaced -->
    <circle cx="800" cy="80" r="4" fill="#a855f7"/>
    <circle cx="850" cy="120" r="3" fill="#f97316"/>
    <circle cx="780" cy="160" r="3.5" fill="#10b981"/>

    <circle cx="900" cy="250" r="4" fill="#a855f7"/>
    <circle cx="950" cy="280" r="3" fill="#f97316"/>
    <circle cx="870" cy="320" r="3.5" fill="#10b981"/>

    <!-- Additional scattered dots -->
    <circle cx="400" cy="50" r="2" fill="#a855f7"/>
    <circle cx="450" cy="80" r="1.5" fill="#f97316"/>
    <circle cx="380" cy="110" r="2.5" fill="#10b981"/>
  </g>

  <!-- UK Flag Accent -->
  <g transform="translate(1480, 25)">
    <rect x="0" y="0" width="60" height="36" rx="6" fill="rgba(30, 41, 59, 0.8)" stroke="#475569" stroke-width="1"/>
    <text x="30" y="25" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="600" fill="#94a3b8">UK</text>
  </g>
  
</svg>
