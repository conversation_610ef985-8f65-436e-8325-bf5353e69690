/* ===== GENLOGIC SIMPLE BRANDING FOR GOHIGHLEVEL ===== */
/* Simplified, aggressive CSS that targets all common elements */

/* Import GenLogic Font */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* ===== FORCE GENLOGIC FONT EVERYWHERE ===== */
* {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

/* ===== GENLOGIC COLORS ===== */
:root {
  --genlogic-purple: #a855f7;
  --genlogic-purple-dark: #7c3aed;
  --genlogic-orange: #f97316;
  --genlogic-gray: #64748b;
  --genlogic-dark: #0f172a;
}

/* ===== AGGRESSIVE BUTTON STYLING ===== */
/* Target ALL possible button selectors */
button,
input[type="submit"],
input[type="button"],
.btn,
.button,
[class*="btn"],
[class*="button"],
[class*="submit"],
[role="button"],
a[class*="btn"],
a[class*="button"] {
  background: linear-gradient(135deg, var(--genlogic-purple) 0%, var(--genlogic-purple-dark) 100%) !important;
  border: none !important;
  color: white !important;
  font-weight: 600 !important;
  padding: 12px 24px !important;
  border-radius: 12px !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 4px 6px -1px rgba(168, 85, 247, 0.2) !important;
  cursor: pointer !important;
  text-decoration: none !important;
  display: inline-block !important;
  font-family: 'Inter', sans-serif !important;
}

button:hover,
input[type="submit"]:hover,
input[type="button"]:hover,
.btn:hover,
.button:hover,
[class*="btn"]:hover,
[class*="button"]:hover,
[class*="submit"]:hover,
[role="button"]:hover,
a[class*="btn"]:hover,
a[class*="button"]:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 10px 15px -3px rgba(168, 85, 247, 0.3) !important;
  background: linear-gradient(135deg, var(--genlogic-purple-dark) 0%, #6d28d9 100%) !important;
}

/* ===== AGGRESSIVE INPUT STYLING ===== */
/* Target ALL possible input selectors */
input,
textarea,
select,
.input,
.form-control,
[class*="input"],
[class*="field"],
[type="text"],
[type="email"],
[type="tel"],
[type="password"],
[type="number"],
[type="url"] {
  border: 2px solid #e2e8f0 !important;
  border-radius: 12px !important;
  padding: 12px 16px !important;
  font-size: 14px !important;
  transition: all 0.3s ease !important;
  background: white !important;
  width: 100% !important;
  font-family: 'Inter', sans-serif !important;
  box-sizing: border-box !important;
}

input:focus,
textarea:focus,
select:focus,
.input:focus,
.form-control:focus,
[class*="input"]:focus,
[class*="field"]:focus,
[type="text"]:focus,
[type="email"]:focus,
[type="tel"]:focus,
[type="password"]:focus,
[type="number"]:focus,
[type="url"]:focus {
  border-color: var(--genlogic-purple) !important;
  box-shadow: 0 0 0 3px rgba(168, 85, 247, 0.1) !important;
  outline: none !important;
}

/* ===== AGGRESSIVE FORM STYLING ===== */
/* Target ALL possible form containers */
form,
.form,
.form-container,
.form-wrapper,
[class*="form"],
.survey,
.quiz,
[class*="survey"],
[class*="quiz"] {
  background: white !important;
  border-radius: 16px !important;
  padding: 24px !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
  border: 1px solid #e2e8f0 !important;
  font-family: 'Inter', sans-serif !important;
}

/* ===== AGGRESSIVE LABEL STYLING ===== */
label,
.label,
[class*="label"] {
  color: var(--genlogic-dark) !important;
  font-weight: 500 !important;
  margin-bottom: 6px !important;
  display: block !important;
  font-family: 'Inter', sans-serif !important;
}

/* ===== AGGRESSIVE HEADING STYLING ===== */
h1, h2, h3, h4, h5, h6 {
  color: var(--genlogic-dark) !important;
  font-weight: 700 !important;
  font-family: 'Inter', sans-serif !important;
  line-height: 1.2 !important;
}

/* ===== AGGRESSIVE LINK STYLING ===== */
a {
  color: var(--genlogic-purple) !important;
  text-decoration: none !important;
  transition: color 0.3s ease !important;
}

a:hover {
  color: var(--genlogic-purple-dark) !important;
}

/* ===== REMOVE ALL ORANGE COLORS ===== */
/* Aggressively override GoHighLevel's default orange */
[style*="#ff6b35"],
[style*="#f46a35"],
[style*="rgb(255, 107, 53)"],
[style*="rgb(244, 106, 53)"] {
  background: var(--genlogic-purple) !important;
  color: white !important;
}

/* ===== CALENDAR STYLING ===== */
.calendar,
[class*="calendar"] {
  border-radius: 16px !important;
  overflow: hidden !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
}

.calendar-header,
[class*="calendar-header"] {
  background: linear-gradient(135deg, var(--genlogic-purple) 0%, var(--genlogic-purple-dark) 100%) !important;
  color: white !important;
  padding: 16px !important;
  font-weight: 600 !important;
}

/* ===== CHAT WIDGET STYLING ===== */
.chat,
[class*="chat"] {
  border-radius: 50px !important;
  background: linear-gradient(135deg, var(--genlogic-purple) 0%, var(--genlogic-purple-dark) 100%) !important;
  box-shadow: 0 4px 12px rgba(168, 85, 247, 0.3) !important;
}

/* ===== POPUP/MODAL STYLING ===== */
.modal,
.popup,
[class*="modal"],
[class*="popup"] {
  background: white !important;
  border-radius: 20px !important;
  padding: 32px !important;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.2) !important;
  border: none !important;
}

/* ===== RADIO BUTTONS & CHECKBOXES ===== */
input[type="radio"],
input[type="checkbox"] {
  accent-color: var(--genlogic-purple) !important;
  transform: scale(1.2) !important;
  margin-right: 8px !important;
}

/* ===== PROGRESS BARS ===== */
.progress,
[class*="progress"] {
  background: #e2e8f0 !important;
  border-radius: 10px !important;
  overflow: hidden !important;
}

.progress-bar,
[class*="progress-bar"] {
  background: linear-gradient(90deg, var(--genlogic-purple) 0%, var(--genlogic-purple-dark) 100%) !important;
  transition: width 0.3s ease !important;
}

/* ===== BADGES & TAGS ===== */
.badge,
.tag,
[class*="badge"],
[class*="tag"] {
  background: rgba(168, 85, 247, 0.1) !important;
  color: var(--genlogic-purple) !important;
  padding: 4px 12px !important;
  border-radius: 20px !important;
  font-size: 12px !important;
  font-weight: 600 !important;
}

/* ===== TABLES ===== */
table {
  border-collapse: collapse !important;
  width: 100% !important;
  background: white !important;
  border-radius: 12px !important;
  overflow: hidden !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

th {
  background: #f8fafc !important;
  color: var(--genlogic-dark) !important;
  font-weight: 600 !important;
  padding: 12px 16px !important;
  text-align: left !important;
}

td {
  padding: 12px 16px !important;
  border-bottom: 1px solid #f1f5f9 !important;
  color: var(--genlogic-gray) !important;
}

tr:hover {
  background: #f8fafc !important;
}

/* ===== MOBILE RESPONSIVENESS ===== */
@media (max-width: 768px) {
  form,
  .form,
  .form-container,
  [class*="form"] {
    padding: 16px !important;
    margin: 8px !important;
  }
  
  button,
  .btn,
  [class*="btn"] {
    padding: 10px 20px !important;
    font-size: 14px !important;
  }
}

/* ===== FORCE OVERRIDE EVERYTHING ===== */
/* Nuclear option - override any remaining elements */
div[style*="background"],
span[style*="background"],
p[style*="background"] {
  background: white !important;
}

div[style*="color"],
span[style*="color"],
p[style*="color"] {
  color: var(--genlogic-gray) !important;
}

/* ===== END GENLOGIC SIMPLE BRANDING ===== */
