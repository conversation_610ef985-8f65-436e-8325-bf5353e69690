import { seoConfig } from '@/lib/seo-config'

interface SchemaMarkupProps {
  type: 'organization' | 'localBusiness' | 'softwareApplication' | 'service' | 'faq' | 'review' | 'website'
  data?: any
}

export function SchemaMarkup({ type, data = {} }: SchemaMarkupProps) {
  const generateSchema = () => {
    const baseUrl = seoConfig.business.url
    
    switch (type) {
      case 'organization':
        return {
          '@context': 'https://schema.org',
          '@type': 'Organization',
          name: seoConfig.business.name,
          legalName: seoConfig.business.legalName,
          url: seoConfig.business.url,
          logo: seoConfig.business.logo,
          description: seoConfig.business.description,
          foundingDate: seoConfig.business.foundingDate,
          address: {
            '@type': 'PostalAddress',
            streetAddress: seoConfig.location.streetAddress,
            addressLocality: seoConfig.location.addressLocality,
            addressRegion: seoConfig.location.addressRegion,
            postalCode: seoConfig.location.postalCode,
            addressCountry: seoConfig.location.addressCountry
          },
          contactPoint: {
            '@type': 'ContactPoint',
            telephone: seoConfig.location.telephone,
            email: seoConfig.location.email,
            contactType: 'customer service',
            availableLanguage: 'English',
            areaServed: 'GB'
          },
          sameAs: [
            seoConfig.social.linkedin,
            seoConfig.social.facebook,
            seoConfig.social.instagram
          ]
        }

      case 'website':
        return {
          '@context': 'https://schema.org',
          '@type': 'WebSite',
          name: 'GenLogic - Customer Conversion System for UK Businesses',
          description: 'Convert 89% of prospects into customers. GenLogic helps UK businesses increase revenue through smart follow-up automation.',
          url: seoConfig.business.url,
          publisher: {
            '@type': 'Organization',
            name: seoConfig.business.name
          },
          potentialAction: {
            '@type': 'SearchAction',
            target: {
              '@type': 'EntryPoint',
              urlTemplate: `${seoConfig.business.url}/search?q={search_term_string}`
            },
            'query-input': 'required name=search_term_string'
          },
          mainEntity: {
            '@type': 'ItemList',
            numberOfItems: 7,
            itemListElement: [
              {
                '@type': 'SiteNavigationElement',
                position: 1,
                name: 'Home',
                url: seoConfig.business.url
              },
              {
                '@type': 'SiteNavigationElement',
                position: 2,
                name: 'Solutions',
                url: `${seoConfig.business.url}/solutions`
              },
              {
                '@type': 'SiteNavigationElement',
                position: 3,
                name: 'Services',
                url: `${seoConfig.business.url}/services/business-automation`
              },
              {
                '@type': 'SiteNavigationElement',
                position: 4,
                name: 'Pricing',
                url: `${seoConfig.business.url}/pricing`
              },
              {
                '@type': 'SiteNavigationElement',
                position: 5,
                name: 'About',
                url: `${seoConfig.business.url}/about`
              },
              {
                '@type': 'SiteNavigationElement',
                position: 6,
                name: 'Blog',
                url: `${seoConfig.business.url}/blog`
              },
              {
                '@type': 'SiteNavigationElement',
                position: 7,
                name: 'Contact',
                url: `${seoConfig.business.url}/contact`
              }
            ]
          }
        }

      case 'localBusiness':
        return {
          '@context': 'https://schema.org',
          '@type': 'LocalBusiness',
          '@id': `${baseUrl}#business`,
          name: seoConfig.business.name,
          description: seoConfig.business.description,
          url: seoConfig.business.url,
          telephone: seoConfig.location.telephone,
          email: seoConfig.location.email,
          address: {
            '@type': 'PostalAddress',
            streetAddress: seoConfig.location.streetAddress,
            addressLocality: seoConfig.location.addressLocality,
            addressRegion: seoConfig.location.addressRegion,
            addressCountry: seoConfig.location.addressCountry
          },
          geo: {
            '@type': 'GeoCoordinates',
            latitude: seoConfig.location.latitude,
            longitude: seoConfig.location.longitude
          },
          openingHoursSpecification: {
            '@type': 'OpeningHoursSpecification',
            dayOfWeek: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'],
            opens: '09:00',
            closes: '18:00'
          },
          areaServed: seoConfig.serviceAreas.map(area => ({
            '@type': 'City',
            name: area,
            containedInPlace: {
              '@type': 'Country',
              name: 'United Kingdom'
            }
          })),
          serviceType: 'Business Automation Software',
          priceRange: '£29-£99'
        }

      case 'softwareApplication':
        return {
          '@context': 'https://schema.org',
          '@type': 'SoftwareApplication',
          name: 'GenLogic Business Automation Platform',
          description: 'Complete business automation software for UK local businesses. Automate bookings, reduce no-shows, and save 20+ hours weekly.',
          url: seoConfig.business.url,
          applicationCategory: 'BusinessApplication',
          operatingSystem: 'Web Browser',
          offers: {
            '@type': 'Offer',
            price: '29',
            priceCurrency: 'GBP',
            priceValidUntil: '2025-12-31',
            availability: 'https://schema.org/InStock',
            seller: {
              '@type': 'Organization',
              name: seoConfig.business.name
            }
          },
          aggregateRating: {
            '@type': 'AggregateRating',
            ratingValue: '4.8',
            reviewCount: '127',
            bestRating: '5',
            worstRating: '1'
          },
          featureList: [
            'Automated booking confirmations',
            'SMS and email reminders',
            'Customer follow-up sequences',
            'No-show reduction',
            'Calendar integration',
            'Analytics and reporting'
          ]
        }

      case 'service':
        return {
          '@context': 'https://schema.org',
          '@type': 'Service',
          name: data.name || 'Business Automation Services',
          description: data.description || seoConfig.business.description,
          provider: {
            '@type': 'LocalBusiness',
            name: seoConfig.business.name,
            address: {
              '@type': 'PostalAddress',
              addressLocality: seoConfig.location.addressLocality,
              addressCountry: seoConfig.location.addressCountry
            }
          },
          areaServed: seoConfig.serviceAreas.map(area => ({
            '@type': 'City',
            name: area
          })),
          serviceType: 'Business Process Automation',
          category: 'Software as a Service'
        }

      case 'faq':
        return {
          '@context': 'https://schema.org',
          '@type': 'FAQPage',
          mainEntity: data.faqs?.map((faq: any) => ({
            '@type': 'Question',
            name: faq.question,
            acceptedAnswer: {
              '@type': 'Answer',
              text: faq.answer
            }
          })) || []
        }

      case 'review':
        return {
          '@context': 'https://schema.org',
          '@type': 'Review',
          itemReviewed: {
            '@type': 'SoftwareApplication',
            name: 'GenLogic Business Automation'
          },
          reviewRating: {
            '@type': 'Rating',
            ratingValue: data.rating || '5',
            bestRating: '5'
          },
          author: {
            '@type': 'Person',
            name: data.author || 'UK Business Owner'
          },
          reviewBody: data.review || 'GenLogic has transformed our business operations.',
          publisher: {
            '@type': 'Organization',
            name: seoConfig.business.name
          }
        }

      default:
        return null
    }
  }

  const schema = generateSchema()
  
  if (!schema) return null

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(schema) }}
    />
  )
}

// Breadcrumb Schema Component
export function BreadcrumbSchema({ items }: { items: Array<{ name: string; url: string }> }) {
  const schema = {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: items.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.name,
      item: item.url
    }))
  }

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(schema) }}
    />
  )
}

// Article Schema for Blog Posts
export function ArticleSchema({ 
  title, 
  description, 
  author, 
  datePublished, 
  dateModified, 
  url,
  image 
}: {
  title: string
  description: string
  author: string
  datePublished: string
  dateModified?: string
  url: string
  image?: string
}) {
  const schema = {
    '@context': 'https://schema.org',
    '@type': 'Article',
    headline: title,
    description: description,
    author: {
      '@type': 'Person',
      name: author
    },
    publisher: {
      '@type': 'Organization',
      name: seoConfig.business.name,
      logo: {
        '@type': 'ImageObject',
        url: seoConfig.business.logo
      }
    },
    datePublished: datePublished,
    dateModified: dateModified || datePublished,
    url: url,
    image: image || seoConfig.business.image,
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': url
    }
  }

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(schema) }}
    />
  )
}
