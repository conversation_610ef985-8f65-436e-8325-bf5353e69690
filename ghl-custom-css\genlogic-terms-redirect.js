/* ===== GENLOGIC TERMS REDIRECT SCRIPT ===== */
/* Add this JavaScript to GoHighLevel Custom JS section */

// Wait for page to load
document.addEventListener('DOMContentLoaded', function() {
    // Function to redirect terms links
    function redirectTermsLinks() {
        // Find all links that contain "terms" in href or text
        const termsLinks = document.querySelectorAll('a[href*="terms"], a[href*="Terms"], a[href*="TERMS"]');
        
        // Also find links with terms text content
        const allLinks = document.querySelectorAll('a');
        const termsTextLinks = Array.from(allLinks).filter(link => 
            link.textContent.toLowerCase().includes('terms') ||
            link.textContent.toLowerCase().includes('privacy') ||
            link.textContent.toLowerCase().includes('policy')
        );
        
        // Combine both arrays
        const allTermsLinks = [...termsLinks, ...termsTextLinks];
        
        // Add click event to redirect to GenLogic terms page
        allTermsLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                // Redirect to GenLogic terms page
                window.open('https://genlogic.io/terms', '_blank');
            });
            
            // Update href to GenLogic terms page
            link.href = 'https://genlogic.io/terms';
            link.target = '_blank';
        });
    }
    
    // Run immediately
    redirectTermsLinks();
    
    // Also run after a delay to catch dynamically loaded content
    setTimeout(redirectTermsLinks, 1000);
    setTimeout(redirectTermsLinks, 3000);
    
    // Watch for DOM changes and reapply
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                setTimeout(redirectTermsLinks, 100);
            }
        });
    });
    
    // Start observing
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
});

/* ===== END GENLOGIC TERMS REDIRECT ===== */
