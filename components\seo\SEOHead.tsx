import Head from 'next/head'
import { seoConfig } from '@/lib/seo-config'

interface SEOHeadProps {
  title?: string
  description?: string
  keywords?: string
  canonical?: string
  ogImage?: string
  ogType?: 'website' | 'article' | 'product'
  noIndex?: boolean
  location?: string
  service?: string
  structuredData?: any
}

export function SEOHead({
  title,
  description,
  keywords,
  canonical,
  ogImage,
  ogType = 'website',
  noIndex = false,
  location,
  service,
  structuredData
}: SEOHeadProps) {
  // Generate location-specific meta if location provided
  const locationMeta = location ? {
    title: `Customer Conversion ${location} - GenLogic | Convert 89% of Prospects`,
    description: `Convert more prospects into customers in ${location}. GenLogic helps local businesses achieve 89% conversion rates through smart follow-up automation. Free sales consultation for ${location} businesses.`,
    keywords: `customer conversion ${location}, increase sales ${location}, lead conversion ${location}, sales optimization ${location}`
  } : {}

  // Generate service-specific meta if service provided
  const serviceMeta = service ? {
    title: `${service} for UK Businesses - GenLogic | Free Sales Consultation`,
    description: `Professional ${service.toLowerCase()} for UK local businesses. Convert prospects into customers, increase sales, grow revenue. Trusted by 500+ businesses.`,
    keywords: `${service.toLowerCase()}, customer conversion UK, sales optimization, lead conversion, revenue growth`
  } : {}

  // Final meta values with fallbacks
  const finalTitle = title || serviceMeta.title || locationMeta.title || seoConfig.defaultMeta.title
  const finalDescription = description || serviceMeta.description || locationMeta.description || seoConfig.defaultMeta.description
  const finalKeywords = keywords || serviceMeta.keywords || locationMeta.keywords || seoConfig.defaultMeta.keywords
  const finalCanonical = canonical || seoConfig.business.url
  const finalOgImage = ogImage || seoConfig.business.image

  return (
    <Head>
      {/* Basic Meta Tags */}
      <title>{finalTitle}</title>
      <meta name="description" content={finalDescription} />
      <meta name="keywords" content={finalKeywords} />
      <meta name="author" content={seoConfig.defaultMeta.author} />
      <meta name="viewport" content={seoConfig.defaultMeta.viewport} />
      <meta charSet={seoConfig.defaultMeta.charset} />
      
      {/* Robots */}
      <meta name="robots" content={noIndex ? 'noindex, nofollow' : seoConfig.defaultMeta.robots} />
      
      {/* Canonical URL */}
      <link rel="canonical" href={finalCanonical} />
      
      {/* Hreflang for UK targeting */}
      <link rel="alternate" hrefLang="en-GB" href={finalCanonical} />
      <link rel="alternate" hrefLang="en" href={finalCanonical} />
      
      {/* Open Graph Meta Tags */}
      <meta property="og:type" content={ogType} />
      <meta property="og:title" content={finalTitle} />
      <meta property="og:description" content={finalDescription} />
      <meta property="og:url" content={finalCanonical} />
      <meta property="og:image" content={finalOgImage} />
      <meta property="og:image:width" content="1200" />
      <meta property="og:image:height" content="630" />
      <meta property="og:site_name" content={seoConfig.business.name} />
      <meta property="og:locale" content="en_GB" />
      
      {/* Twitter Card Meta Tags */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={finalTitle} />
      <meta name="twitter:description" content={finalDescription} />
      <meta name="twitter:image" content={finalOgImage} />
      
      {/* Business-specific Meta Tags */}
      <meta name="geo.region" content="GB" />
      <meta name="geo.placename" content="United Kingdom" />
      <meta name="geo.position" content={`${seoConfig.location.latitude};${seoConfig.location.longitude}`} />
      <meta name="ICBM" content={`${seoConfig.location.latitude}, ${seoConfig.location.longitude}`} />
      
      {/* Local Business Meta */}
      {location && (
        <>
          <meta name="geo.placename" content={location} />
          <meta name="location" content={location} />
        </>
      )}
      
      {/* Contact Information */}
      <meta name="contact" content={seoConfig.location.email} />
      <meta name="phone" content={seoConfig.location.telephone} />
      
      {/* Favicon and Icons */}
      <link rel="icon" href="/favicon.ico" />
      <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
      <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
      <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
      <link rel="manifest" href="/site.webmanifest" />
      
      {/* Preconnect for Performance */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      
      {/* DNS Prefetch for External Resources */}
      <link rel="dns-prefetch" href="//www.google-analytics.com" />
      <link rel="dns-prefetch" href="//fonts.googleapis.com" />
      
      {/* Structured Data */}
      {structuredData && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
        />
      )}
    </Head>
  )
}

// Specialized SEO components for different page types
export function HomePageSEO() {
  return (
    <SEOHead
      title="GenLogic - Stop Working Late | Business Automation for UK Businesses"
      description="Stop working until 9pm every night. GenLogic automates your bookings, reduces no-shows by 85%, and helps UK businesses save 20+ hours weekly. Free demo available."
      keywords="business automation UK, stop working late, reduce no shows, booking automation, SMS reminders, UK business software"
      canonical="https://genlogic.io"
      ogType="website"
    />
  )
}

export function LocationPageSEO({ city }: { city: string }) {
  return (
    <SEOHead
      title={`Business Automation ${city} - GenLogic | Save 20+ Hours Weekly`}
      description={`Stop working late in ${city}. GenLogic helps local businesses reduce no-shows by 85% and save 20+ hours weekly. Free demo for ${city} businesses.`}
      keywords={`business automation ${city}, booking system ${city}, SMS reminders ${city}, customer automation ${city}, reduce no shows ${city}`}
      canonical={`https://genlogic.io/locations/${city.toLowerCase().replace(/\s+/g, '-')}`}
      location={city}
    />
  )
}

export function ServicePageSEO({ service, description }: { service: string; description: string }) {
  return (
    <SEOHead
      title={`${service} for UK Businesses - GenLogic | Free Demo`}
      description={`${description} Trusted by 500+ UK businesses. Free 14-day trial.`}
      keywords={`${service.toLowerCase()}, UK business software, automation tools, customer management`}
      canonical={`https://genlogic.io/services/${service.toLowerCase().replace(/\s+/g, '-')}`}
      service={service}
    />
  )
}

export function BlogPostSEO({ 
  title, 
  description, 
  slug, 
  publishDate 
}: { 
  title: string
  description: string
  slug: string
  publishDate: string
}) {
  return (
    <SEOHead
      title={`${title} - GenLogic Blog`}
      description={description}
      canonical={`https://genlogic.io/blog/${slug}`}
      ogType="article"
      structuredData={{
        '@context': 'https://schema.org',
        '@type': 'Article',
        headline: title,
        description: description,
        datePublished: publishDate,
        author: {
          '@type': 'Organization',
          name: seoConfig.business.name
        },
        publisher: {
          '@type': 'Organization',
          name: seoConfig.business.name,
          logo: seoConfig.business.logo
        }
      }}
    />
  )
}
