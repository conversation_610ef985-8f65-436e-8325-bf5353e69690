'use client'

import Link from 'next/link'
import { motion, useInView } from 'framer-motion'
import { useRef } from 'react'
import { Spotlight } from '@/components/ui/spotlight'
import {
  Rocket,
  Target,
  Users,
  Heart,
  Lightbulb,
  Sparkles,
  Globe,
} from 'lucide-react'


export default function AboutPage() {
  const missionRef = useRef(null)
  const valuesRef = useRef(null)

  const missionInView = useInView(missionRef, { once: true, amount: 0.3 })
  const valuesInView = useInView(valuesRef, { once: true, amount: 0.3 })

  const values = [
    {
      title: 'Simplicity',
      description: 'We believe powerful technology should be simple to use. No technical expertise required.',
      icon: 'Lightbulb',
    },
    {
      title: 'Customer First',
      description: "We're obsessed with helping our customers succeed. Your growth is our growth.",
      icon: 'Users',
    },
    {
      title: 'Excellence',
      description: 'We strive for perfection in everything we do, consistently delivering high-quality work.',
      icon: 'Sparkles',
    },
    {
      title: 'Trust & Security',
      description: 'We take data protection seriously. Your business information is always secure with us.',
      icon: 'Heart',
    },
  ]

  const iconComponents = {
    Users: Users,
    Heart: Heart,
    Lightbulb: Lightbulb,
    Globe: Globe,
    Sparkles: Sparkles,
    Rocket: Rocket,
    Target: Target,
  }

  return (
    <div className="bg-background transition-colors duration-300">
      <Spotlight
        gradientFirst="radial-gradient(68.54% 68.72% at 55.02% 31.46%, hsla(221, 83%, 53%, 0.08) 0, hsla(221, 83%, 53%, 0.04) 50%, hsla(221, 83%, 53%, 0) 80%)"
        gradientSecond="radial-gradient(50% 50% at 50% 50%, hsla(221, 83%, 53%, 0.08) 0, hsla(221, 83%, 53%, 0.04) 80%, transparent 100%)"
        gradientThird="radial-gradient(50% 50% at 50% 50%, hsla(221, 83%, 53%, 0.06) 0, hsla(221, 83%, 53%, 0.06) 80%, transparent 100%)"
      />

      {/* Header */}
      <section className="relative header-spacing pb-16 overflow-hidden">
        <div className="max-w-7xl relative z-10 mx-auto px-4 md:px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: 'easeOut' }}
            className="mx-auto max-w-4xl text-center"
          >
            <div className="bg-background border border-primary-200/20 dark:border-primary-800/20 rounded-3xl p-12 shadow-lg backdrop-blur-sm">
              <h1 className="text-4xl font-bold text-foreground sm:text-5xl md:text-6xl mb-6">
                We Know You're Losing Too Many <span className="text-red-600 dark:text-red-400">Prospects</span>
              </h1>
              <p className="text-xl text-muted-foreground leading-relaxed mb-6">
                Every day, thousands of UK business owners watch potential customers slip away to competitors, losing revenue because of poor follow-up and weak conversion systems.
              </p>
              <p className="text-lg text-muted-foreground leading-relaxed">
                <strong className="text-foreground">We built GenLogic because we believe you deserve better.</strong> You started your business to grow and succeed, not to watch prospects go cold and lose sales to competitors.
              </p>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Mission & Vision Section */}
      <section ref={missionRef} className="py-20 relative">
        <div className="max-w-7xl relative z-10 mx-auto px-4 md:px-6">
          <motion.div
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2, ease: 'easeOut' }}
            className="relative z-10 grid gap-12 md:grid-cols-2 mb-16"
          >
            <motion.div
              whileHover={{ y: -5 }}
              className="group relative bg-background border border-primary-200/20 dark:border-primary-800/20 rounded-3xl p-10 shadow-lg hover:shadow-xl transition-all duration-300"
            >
              <div className="mb-6 inline-flex h-16 w-16 items-center justify-center rounded-2xl bg-primary-100 dark:bg-primary-900/30">
                <Rocket className="h-8 w-8 text-primary-700 dark:text-primary-400" />
              </div>

              <div className="space-y-4">
                <h2 className="mb-4 text-3xl font-bold text-primary-700 dark:text-primary-400">
                  Your Revenue Matters
                </h2>

                <p className="text-lg leading-relaxed text-muted-foreground mb-4">
                  <strong className="text-foreground">You didn't start your business to lose customers to competitors.</strong> Yet here you are, watching prospects slip away, missing sales opportunities, and seeing competitors grow while you struggle with poor conversion rates.
                </p>

                <p className="text-lg leading-relaxed text-muted-foreground">
                  Our mission is simple: <strong className="text-foreground">turn your prospects into paying customers.</strong> We optimize your sales process and automate follow-ups, so you can focus on what you love about your business - and actually convert the customers you've worked so hard to attract.
                </p>
              </div>
            </motion.div>

            <motion.div
              whileHover={{ y: -5 }}
              className="group relative bg-background border border-primary-200/20 dark:border-primary-800/20 rounded-3xl p-10 shadow-lg hover:shadow-xl transition-all duration-300"
            >
              <div className="mb-6 inline-flex h-16 w-16 items-center justify-center rounded-2xl bg-primary-100 dark:bg-primary-900/30">
                <Target className="h-8 w-8 text-primary-700 dark:text-primary-400" />
              </div>

              <h2 className="mb-4 text-3xl font-bold text-primary-700 dark:text-primary-400">
                From Lost Sales to Revenue Growth
              </h2>

              <p className="text-lg leading-relaxed text-muted-foreground mb-4">
                <strong className="text-foreground">Sarah from Manchester was only converting 15% of her salon inquiries into customers.</strong> Her business was getting plenty of leads, but prospects were going cold, competitors were winning customers, and she was losing thousands in potential revenue every month.
              </p>

              <p className="text-lg leading-relaxed text-muted-foreground mb-4">
                <strong className="text-foreground">Three months after implementing GenLogic, Sarah converts 89% of prospects into customers.</strong> Her revenue increased by £50,000 annually, her customer base doubled, and she's booked solid months in advance.
              </p>

              <p className="text-lg leading-relaxed text-muted-foreground">
                We founded GenLogic in 2022 because every UK business owner deserves Sarah's transformation. <strong className="text-foreground">You shouldn't have to choose between losing customers and growing your business.</strong>
              </p>
            </motion.div>
          </motion.div>

          {/* Company Info Card */}
          <motion.div
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4, ease: 'easeOut' }}
            whileHover={{ y: -5 }}
            className="group relative bg-background border border-primary-200/20 dark:border-primary-800/20 rounded-3xl p-10 shadow-lg hover:shadow-xl transition-all duration-300 max-w-2xl mx-auto"
          >
            <div className="text-center">
              <div className="w-20 h-20 bg-primary-100 dark:bg-primary-900/30 rounded-full flex items-center justify-center mx-auto mb-6">
                <Globe className="w-10 h-10 text-primary-700 dark:text-primary-400" />
              </div>
              <h3 className="text-2xl font-bold text-primary-700 dark:text-primary-400 mb-4">
                Built for UK Businesses, By UK Business Owners
              </h3>
              <p className="text-muted-foreground mb-6 text-lg">
                <strong className="text-foreground">We understand the unique challenges you face.</strong> From GDPR compliance to UK customer expectations, we've built GenLogic specifically for local businesses across England, Scotland, Wales, and Northern Ireland.
              </p>
              <div className="space-y-3">
                <div className="flex items-center justify-center">
                  <div className="w-5 h-5 rounded-full bg-primary-100 dark:bg-primary-900/30 flex items-center justify-center mr-3">
                    <svg className="w-3 h-3 text-primary-700 dark:text-primary-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <span className="text-foreground"><EMAIL></span>
                </div>
                <div className="flex items-center justify-center">
                  <div className="w-5 h-5 rounded-full bg-primary-100 dark:bg-primary-900/30 flex items-center justify-center mr-3">
                    <svg className="w-3 h-3 text-primary-700 dark:text-primary-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                    </svg>
                  </div>
                  <span className="text-foreground">+44 7401 137621</span>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Our Values */}
      <section ref={valuesRef} className="py-20 relative">
        <div className="max-w-7xl relative z-10 mx-auto px-4 md:px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={valuesInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.6, ease: 'easeOut' }}
            className="mb-12 text-center"
          >
            <div className="bg-background border border-primary-200/20 dark:border-primary-800/20 rounded-3xl p-8 shadow-lg max-w-3xl mx-auto">
              <h2 className="text-3xl font-bold text-foreground sm:text-4xl mb-4">
                What We <span className="text-primary-700 dark:text-primary-400">Believe</span>
              </h2>
              <p className="text-lg text-muted-foreground">
                <strong className="text-foreground">Your success shouldn't come at the cost of your wellbeing.</strong> These values guide how we help UK business owners reclaim their time and grow sustainably.
              </p>
            </div>
          </motion.div>

          <div className="grid gap-6 md:grid-cols-2 xl:grid-cols-4">
            {values.map((value, index) => {
              const IconComponent = iconComponents[value.icon as keyof typeof iconComponents]

              return (
                <motion.div
                  key={value.title}
                  initial={{ opacity: 0, y: 30 }}
                  animate={valuesInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
                  transition={{
                    duration: 0.6,
                    delay: index * 0.1 + 0.2,
                    ease: 'easeOut',
                  }}
                  whileHover={{ y: -5, scale: 1.02 }}
                  className="bg-background border border-primary-200/20 dark:border-primary-800/20 rounded-3xl p-6 shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  <div className="mb-4 inline-flex h-12 w-12 items-center justify-center rounded-2xl bg-primary-100 dark:bg-primary-900/30">
                    <IconComponent className="h-6 w-6 text-primary-700 dark:text-primary-400" />
                  </div>
                  <h3 className="text-xl font-bold text-foreground mb-3">{value.title}</h3>
                  <p className="text-muted-foreground leading-relaxed">{value.description}</p>
                </motion.div>
              )
            })}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 relative">
        <div className="max-w-7xl relative z-10 mx-auto px-4 md:px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: 'easeOut' }}
            viewport={{ once: true }}
            className="text-center"
          >
            <div className="bg-primary-50 dark:bg-primary-950/30 border border-primary-200/30 dark:border-primary-800/30 rounded-3xl p-12 shadow-lg max-w-4xl mx-auto">
              <h2 className="text-3xl font-bold text-foreground sm:text-4xl mb-6">
                Start Converting <span className="text-green-700 dark:text-green-400">89% of Prospects</span>
              </h2>
              <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
                <strong className="text-foreground">Imagine converting 89% of your prospects into paying customers.</strong> Join 500+ UK business owners who've transformed their sales and grown their revenue with GenLogic.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link
                  href="/demo"
                  className="inline-flex items-center justify-center px-8 py-4 bg-primary-700 hover:bg-primary-800 dark:bg-primary-600 dark:hover:bg-primary-700 text-white rounded-2xl transition-all duration-300 hover:shadow-lg text-lg font-medium"
                >
                  Book Your Free Demo
                </Link>
                <Link
                  href="/contact"
                  className="inline-flex items-center justify-center px-8 py-4 text-primary-700 dark:text-primary-400 rounded-2xl border-2 border-primary-200 dark:border-primary-800 hover:border-primary-300 dark:hover:border-primary-700 hover:bg-primary-50 dark:hover:bg-primary-900/20 transition-all duration-300 text-lg font-medium"
                >
                  Contact Us
                </Link>
              </div>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  )
}
