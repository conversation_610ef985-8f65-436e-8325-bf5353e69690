import React from 'react'
import { BlogPost } from '../types'

const BlogContent = () => {
  return (
    <div className="max-w-4xl mx-auto space-y-12">
      
      {/* Opening Hook */}
      <div className="bg-gradient-to-br from-red-50 to-orange-50 dark:from-red-950/20 dark:to-orange-950/20 rounded-3xl p-8 border border-red-200/50 dark:border-red-800/30">
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6 text-center">
          Your WordPress website just crashed... again.
        </h2>
        <p className="text-lg text-gray-700 dark:text-gray-300 mb-4 text-center">
          It's the third time this month. Your customers can't book appointments, your site takes 12 seconds to load, and you're paying £300/month for hosting and plugins that barely work. <strong>Sound familiar?</strong>
        </p>
        <p className="text-lg text-gray-700 dark:text-gray-300 text-center font-medium">
          There's a better way.
        </p>
      </div>

      {/* Problem Section */}
      <div>
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
          The WordPress Nightmare Every UK Business Owner Knows
        </h2>
        
        <div className="bg-gray-50 dark:bg-gray-900/50 rounded-2xl p-6 mb-8">
          <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
            Does this sound like your business?
          </h3>
          <ul className="space-y-3">
            <li className="flex items-start space-x-3">
              <span className="text-red-500 font-bold text-lg">×</span>
              <span className="text-gray-700 dark:text-gray-300">Website takes 8+ seconds to load (customers leave before it even loads)</span>
            </li>
            <li className="flex items-start space-x-3">
              <span className="text-red-500 font-bold text-lg">×</span>
              <span className="text-gray-700 dark:text-gray-300">Spending £200-400/month on hosting, plugins, and maintenance</span>
            </li>
            <li className="flex items-start space-x-3">
              <span className="text-red-500 font-bold text-lg">×</span>
              <span className="text-gray-700 dark:text-gray-300">Every plugin update breaks something else</span>
            </li>
            <li className="flex items-start space-x-3">
              <span className="text-red-500 font-bold text-lg">×</span>
              <span className="text-gray-700 dark:text-gray-300">Website got hacked (maybe more than once)</span>
            </li>
            <li className="flex items-start space-x-3">
              <span className="text-red-500 font-bold text-lg">×</span>
              <span className="text-gray-700 dark:text-gray-300">Looks terrible on mobile phones</span>
            </li>
            <li className="flex items-start space-x-3">
              <span className="text-red-500 font-bold text-lg">×</span>
              <span className="text-gray-700 dark:text-gray-300">Can't find your business on Google anymore</span>
            </li>
          </ul>
        </div>

        <p className="text-lg text-gray-700 dark:text-gray-300 leading-relaxed">
          <strong>You're not alone.</strong> We've spoken to hundreds of UK business owners who are fed up with WordPress. The constant maintenance, security scares, and poor performance are costing you customers and keeping you awake at night.
        </p>
      </div>

      {/* Solution Section */}
      <div>
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
          Why Next.js is the Future of Business Websites
        </h2>
        
        <div className="grid md:grid-cols-2 gap-8 mb-8">
          <div className="bg-green-50 dark:bg-green-950/20 rounded-2xl p-6 border border-green-200/50 dark:border-green-800/30">
            <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
              ⚡ Lightning Fast Performance
            </h3>
            <p className="text-gray-700 dark:text-gray-300 mb-4">
              Next.js websites load 3-5x faster than WordPress. Your customers won't wait 8 seconds - they'll go to your competitor instead.
            </p>
            <ul className="space-y-2 text-sm text-green-700 dark:text-green-300">
              <li>✓ Average load time: Under 2 seconds</li>
              <li>✓ 95% faster than typical WordPress sites</li>
              <li>✓ Better user experience = more conversions</li>
            </ul>
          </div>

          <div className="bg-blue-50 dark:bg-blue-950/20 rounded-2xl p-6 border border-blue-200/50 dark:border-blue-800/30">
            <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
              🛡️ Bulletproof Security
            </h3>
            <p className="text-gray-700 dark:text-gray-300 mb-4">
              No plugins to hack, no database vulnerabilities, no security nightmares. Next.js websites are secure by design.
            </p>
            <ul className="space-y-2 text-sm text-blue-700 dark:text-blue-300">
              <li>✓ No plugin vulnerabilities</li>
              <li>✓ Built-in security features</li>
              <li>✓ Sleep peacefully knowing you're protected</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Case Study */}
      <div className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 rounded-3xl p-8 border border-blue-200/50 dark:border-blue-800/30">
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6 text-center">
          Real UK Business Transformation
        </h2>
        
        <div className="max-w-3xl mx-auto">
          <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
            Sarah's Beauty Salon, Leeds
          </h3>
          
          <div className="grid md:grid-cols-2 gap-6 mb-6">
            <div>
              <h4 className="font-semibold text-red-600 dark:text-red-400 mb-3">Before Next.js:</h4>
              <ul className="space-y-2 text-gray-700 dark:text-gray-300">
                <li>• Website took 12 seconds to load</li>
                <li>• Lost 40% of mobile visitors</li>
                <li>• £300/month hosting costs</li>
                <li>• Hacked twice in 6 months</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-green-600 dark:text-green-400 mb-3">After Next.js:</h4>
              <ul className="space-y-2 text-gray-700 dark:text-gray-300">
                <li>• Loads in under 2 seconds</li>
                <li>• 65% increase in mobile bookings</li>
                <li>• £50/month hosting costs</li>
                <li>• Zero security issues</li>
              </ul>
            </div>
          </div>
          
          <blockquote className="text-lg italic text-gray-700 dark:text-gray-300 text-center border-l-4 border-blue-500 pl-6">
            "I wish I'd made the switch years ago. My website actually works now, and I'm getting more bookings than ever."
          </blockquote>
          <p className="text-center text-gray-600 dark:text-gray-400 mt-2">- Sarah, Beauty Salon Owner</p>
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-gradient-to-br from-gray-900 to-gray-800 dark:from-gray-800 dark:to-gray-900 rounded-3xl p-8 text-white text-center">
        <h2 className="text-3xl font-bold mb-4">
          Ready to Escape WordPress Hell?
        </h2>
        <p className="text-xl mb-8 text-gray-300">
          Stop wasting time fighting WordPress. Get a website that actually works for your business.
        </p>
        <div className="space-y-4">
          <a 
            href="/contact" 
            className="inline-block bg-blue-600 hover:bg-blue-700 text-white font-semibold py-4 px-8 rounded-2xl transition-colors duration-300"
          >
            Get Your Next.js Website Quote
          </a>
          <p className="text-sm text-gray-400">
            Free consultation • No obligation • UK-based team
          </p>
        </div>
      </div>

    </div>
  )
}

export const nextjsWebsitePost: BlogPost = {
  id: '8',
  slug: 'nextjs-website-development',
  title: 'Tired of Your WordPress Nightmare? Why UK Businesses Are Switching to Next.js',
  excerpt: 'Stop fighting WordPress plugins and security scares. Discover why 200+ UK businesses switched to Next.js development for faster, more secure websites that actually work.',
  content: <BlogContent />,
  author: {
    name: 'Chedi Rach',
    role: 'Business Automation Specialist',
    avatar: '/logo/genlogic-icon-only.svg',
    bio: 'Chedi has built 200+ Next.js websites for UK businesses, helping them escape WordPress frustrations and achieve better performance.'
  },
  publishedAt: '2025-07-18',
  readingTime: '8 min read',
  category: 'Website Development',
  tags: ['nextjs', 'wordpress', 'website-development', 'performance', 'security'],
  featured: true,
  image: '/blog-images/nextjs-website-development.webp',
  views: 0,
  likes: 0
}
