'use client'

import Script from 'next/script'
import { motion } from 'framer-motion'
import { Clock, CheckCircle, TrendingUp } from 'lucide-react'

export default function CalendarPage() {
  return (
    <div className="relative w-full overflow-hidden">
      {/* Header Section */}
      <section className="header-spacing py-20 relative">
        <div className="container relative z-10 mx-auto px-4 md:px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: 'easeOut' }}
            className="mx-auto max-w-4xl text-center"
          >
            <div className="bg-background border border-primary-200/20 dark:border-primary-800/20 rounded-3xl p-12 shadow-lg backdrop-blur-sm">
              <h1 className="text-4xl font-bold text-foreground sm:text-5xl md:text-6xl mb-6">
                Get Your <span className="text-primary-700 dark:text-primary-400">Evenings Back</span>
              </h1>
              <p className="text-xl text-muted-foreground leading-relaxed mb-6">
                <strong className="text-foreground">Tired of working until 9pm every night?</strong> Book your free demo and discover exactly how to reclaim 20+ hours weekly while growing your business.
              </p>
              {/* Value Proposition Cards */}
              <div className="grid md:grid-cols-3 gap-6 mb-12">
                <div className="bg-background border border-primary-200/30 dark:border-primary-800/30 rounded-2xl p-6 hover:shadow-lg transition-all duration-300">
                  <div className="w-12 h-12 bg-gradient-to-br from-primary-700 to-primary-800 dark:from-primary-600 dark:to-primary-700 rounded-xl flex items-center justify-center mb-4 mx-auto">
                    <Clock className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="font-semibold text-foreground mb-2 text-center">Stop Working Late</h3>
                  <p className="text-sm text-muted-foreground text-center">Finally finish work by 6pm and spend evenings with your family</p>
                </div>

                <div className="bg-background border border-primary-200/30 dark:border-primary-800/30 rounded-2xl p-6 hover:shadow-lg transition-all duration-300">
                  <div className="w-12 h-12 bg-gradient-to-br from-primary-700 to-primary-800 dark:from-primary-600 dark:to-primary-700 rounded-xl flex items-center justify-center mb-4 mx-auto">
                    <CheckCircle className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="font-semibold text-foreground mb-2 text-center">End No-Show Frustration</h3>
                  <p className="text-sm text-muted-foreground text-center">Never waste time waiting for customers who don't show up again</p>
                </div>

                <div className="bg-background border border-primary-200/30 dark:border-primary-800/30 rounded-2xl p-6 hover:shadow-lg transition-all duration-300">
                  <div className="w-12 h-12 bg-gradient-to-br from-primary-700 to-primary-800 dark:from-primary-600 dark:to-primary-700 rounded-xl flex items-center justify-center mb-4 mx-auto">
                    <TrendingUp className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="font-semibold text-foreground mb-2 text-center">Grow Without Stress</h3>
                  <p className="text-sm text-muted-foreground text-center">Scale your business without working longer hours or hiring more staff</p>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Calendar Section */}
          <div className="max-w-4xl mx-auto">
            {/* Calendar Container */}
            <div className="bg-background border border-primary-200/20 dark:border-primary-800/20 rounded-3xl shadow-xl overflow-hidden">
              <div className="p-8">
                <div className="text-center mb-8">
                  <h2 className="text-2xl font-bold text-foreground mb-2">
                    Pick a Time That <span className="text-primary-700 dark:text-primary-400">Works for You</span>
                  </h2>
                  <p className="text-muted-foreground">
                    Choose a time that fits your schedule. We'll spend 30 minutes showing you exactly how to get your life back.
                  </p>
                </div>

                {/* GHL Calendar Embed */}
                <div className="calendar-container w-full">
                  <iframe
                    src="https://links.genlogic.io/widget/booking/fMkDt9KybbQ4UEnIEyUF"
                    width="100%"
                    height="600"
                    style={{
                      border: 'none',
                      overflow: 'hidden'
                    }}
                    id="fMkDt9KybbQ4UEnIEyUF_1752792010778"
                    title="GenLogic Demo Booking Calendar"
                    loading="lazy"
                    referrerPolicy="strict-origin-when-cross-origin"
                    allow="camera; microphone; geolocation"
                  />
                </div>
              </div>
            </div>

            {/* Trust Indicators */}
            <div className="mt-12 text-center">
              <div className="grid md:grid-cols-2 gap-8 max-w-2xl mx-auto">
                <div className="flex items-center justify-center space-x-3">
                  <div className="w-8 h-8 bg-success-green/10 rounded-full flex items-center justify-center">
                    <CheckCircle className="w-4 h-4 text-success-green" />
                  </div>
                  <span className="text-muted-foreground">Completely Free - No Catch</span>
                </div>

                <div className="flex items-center justify-center space-x-3">
                  <div className="w-8 h-8 bg-primary-700/10 dark:bg-primary-400/10 rounded-full flex items-center justify-center">
                    <CheckCircle className="w-4 h-4 text-primary-700 dark:text-primary-400" />
                  </div>
                  <span className="text-muted-foreground">No Sales Pitch - Just Help</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* GHL Form Embed Script */}
      <Script
        src="https://links.genlogic.io/js/form_embed.js"
        type="text/javascript"
        strategy="afterInteractive"
      />
    </div>
  )
}
