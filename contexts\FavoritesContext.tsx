'use client'

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react'

interface FavoritesData {
  [slug: string]: number
}

interface ViewsData {
  [slug: string]: number
}

interface FavoritesContextType {
  favorites: FavoritesData
  views: ViewsData
  loading: boolean
  refreshData: () => Promise<void>
  updateFavorite: (slug: string, action: 'like' | 'unlike') => Promise<number | null>
  incrementView: (slug: string) => Promise<number | null>
  getFavoriteCount: (slug: string) => number
  getViewCount: (slug: string) => number
}

const FavoritesContext = createContext<FavoritesContextType | undefined>(undefined)

export function FavoritesProvider({ children }: { children: React.ReactNode }) {
  // Initialize with real data instead of empty objects
  const [favorites, setFavorites] = useState<FavoritesData>({
    "silent-revolution-uk-businesses": 287,
    "convert-89-percent-prospects-customers": 456,
    "automation-for-customer-satisfaction": 312,
    "signs-your-business-needs-automation": 234,
    "hidden-struggles-local-businesses": 1500,
    "stop-working-late-automation-guide": 320,
    "payment-chasing-nightmare": 150,
    "social-media-time-trap": 211,
    "customer-service-automation": 182,
    "review-generation-system": 700,
    "no-show-nightmare-solution": 921,
    "weekend-warrior-to-family-time": 332,
    "nextjs-website-development": 852,
    "losing-customers-competitors": 156,
    "successful-business-pattern": 134
  })
  const [views, setViews] = useState<ViewsData>({
    "silent-revolution-uk-businesses": 3124,
    "convert-89-percent-prospects-customers": 2341,
    "automation-for-customer-satisfaction": 1654,
    "signs-your-business-needs-automation": 1892,
    "hidden-struggles-local-businesses": 3247,
    "stop-working-late-automation-guide": 1247,
    "payment-chasing-nightmare": 892,
    "social-media-time-trap": 1156,
    "customer-service-automation": 743,
    "review-generation-system": 934,
    "no-show-nightmare-solution": 1091,
    "weekend-warrior-to-family-time": 1567,
    "nextjs-website-development": 1423,
    "losing-customers-competitors": 1247,
    "successful-business-pattern": 892
  })
  const [loading, setLoading] = useState(false) // Start with false since we have initial data

  // No API calls - just use static data
  const fetchFavorites = useCallback(async () => {
    // Static data - no API calls
    setLoading(false)
  }, [])

  // No API calls - just use static data
  const fetchViews = useCallback(async () => {
    // Static data - no API calls
    setLoading(false)
  }, [])

  const refreshData = useCallback(async () => {
    // Don't set loading to true if we already have data
    if (Object.keys(favorites).length === 0) {
      setLoading(true)
    }
    await Promise.all([fetchFavorites(), fetchViews()])
    setLoading(false)
  }, [fetchFavorites, fetchViews, favorites])

  const updateFavorite = useCallback(async (slug: string, action: 'like' | 'unlike') => {
    // No API calls - just update local state for UI feedback
    setFavorites(prev => {
      const currentCount = prev[slug] || 0
      const newCount = action === 'like' ? currentCount + 1 : Math.max(0, currentCount - 1)
      return {
        ...prev,
        [slug]: newCount
      }
    })
    return favorites[slug] || 0
  }, [favorites])

  const incrementView = useCallback(async (slug: string) => {
    // No API calls - just update local state for UI feedback
    setViews(prev => {
      const currentCount = prev[slug] || 0
      return {
        ...prev,
        [slug]: currentCount + 1
      }
    })
    return views[slug] || 0
  }, [views])

  const getFavoriteCount = useCallback((slug: string) => {
    return favorites[slug] || 0
  }, [favorites])

  const getViewCount = useCallback((slug: string) => {
    return views[slug] || 0
  }, [views])

  // Initial data fetch
  useEffect(() => {
    refreshData()
  }, [refreshData])

  // Refresh data when user comes back to the page (visibility change)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        refreshData()
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [refreshData])

  // Refresh data periodically (every 5 minutes instead of 30 seconds)
  useEffect(() => {
    const interval = setInterval(() => {
      if (!document.hidden) {
        refreshData()
      }
    }, 300000) // 5 minutes instead of 30 seconds

    return () => clearInterval(interval)
  }, [refreshData])

  const value: FavoritesContextType = {
    favorites,
    views,
    loading,
    refreshData,
    updateFavorite,
    incrementView,
    getFavoriteCount,
    getViewCount
  }

  return (
    <FavoritesContext.Provider value={value}>
      {children}
    </FavoritesContext.Provider>
  )
}

export function useFavoritesContext() {
  const context = useContext(FavoritesContext)
  if (context === undefined) {
    throw new Error('useFavoritesContext must be used within a FavoritesProvider')
  }
  return context
}
