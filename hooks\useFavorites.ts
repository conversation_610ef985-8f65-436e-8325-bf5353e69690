import { useState, useEffect } from 'react'
import { useFavoritesContext } from '../contexts/FavoritesContext'

export function usePostFavorites(slug: string) {
  const [isLiked, setIsLiked] = useState(false)
  const { getFavoriteCount, updateFavorite, loading } = useFavoritesContext()

  const currentLikes = getFavoriteCount(slug)

  // Check localStorage for user's like status
  useEffect(() => {
    const likedPosts = JSON.parse(localStorage.getItem('likedPosts') || '[]')
    setIsLiked(likedPosts.includes(slug))
  }, [slug])

  const handleLike = async () => {
    const likedPosts = JSON.parse(localStorage.getItem('likedPosts') || '[]')

    if (isLiked) {
      // Unlike
      setIsLiked(false)
      const updatedLikes = likedPosts.filter((postSlug: string) => postSlug !== slug)
      localStorage.setItem('likedPosts', JSON.stringify(updatedLikes))

      // Update server count via context
      await updateFavorite(slug, 'unlike')
    } else {
      // Like
      setIsLiked(true)
      localStorage.setItem('likedPosts', JSON.stringify([...likedPosts, slug]))

      // Update server count via context
      await updateFavorite(slug, 'like')
    }
  }

  return {
    isLiked,
    currentLikes,
    loading,
    handleLike
  }
}
