'use client'

import { useEffect } from 'react'

/**
 * Optimized Font Loader Component
 * Loads fonts efficiently without preload warnings
 * Uses modern font loading strategies for 2025 performance standards
 */
export function OptimizedFontLoader() {
  useEffect(() => {
    // Only load fonts after initial render to avoid preload warnings
    const loadFonts = () => {
      // Check if fonts are already loaded
      if (document.fonts && document.fonts.check) {
        const interFont = '16px Inter'
        if (!document.fonts.check(interFont)) {
          // Create font face and load it
          const fontFace = new FontFace(
            'Inter',
            'url(https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfAZ9hiA.woff2)',
            {
              style: 'normal',
              weight: '400 700',
              display: 'swap'
            }
          )
          
          fontFace.load().then(() => {
            document.fonts.add(fontFace)
          }).catch((error) => {
            console.warn('Font loading failed:', error)
            // Fallback to CSS loading
            loadFontCSS()
          })
        }
      } else {
        // Fallback for browsers without Font Loading API
        loadFontCSS()
      }
    }

    const loadFontCSS = () => {
      // Check if font CSS is already loaded
      const existingLink = document.querySelector('link[href*="fonts.googleapis.com"]')
      if (!existingLink) {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = 'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap'
        link.crossOrigin = 'anonymous'
        document.head.appendChild(link)
      }
    }

    // Load fonts after a short delay to avoid blocking initial render
    const timer = setTimeout(loadFonts, 100)
    
    return () => clearTimeout(timer)
  }, [])

  return null // This component doesn't render anything
}

/**
 * Critical Font Preloader
 * Only preloads fonts that are critical for above-the-fold content
 */
export function CriticalFontPreloader() {
  return (
    <>
      {/* Only preconnect to font domains - no preload to avoid warnings */}
      <link rel="preconnect" href="https://fonts.googleapis.com" crossOrigin="anonymous" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      
      {/* DNS prefetch for faster connection */}
      <link rel="dns-prefetch" href="//fonts.googleapis.com" />
      <link rel="dns-prefetch" href="//fonts.gstatic.com" />
    </>
  )
}

/**
 * Font Display Optimization
 * Adds font-display: swap to existing font declarations
 */
export function FontDisplayOptimizer() {
  useEffect(() => {
    // Add font-display: swap to any existing font-face declarations
    const style = document.createElement('style')
    style.textContent = `
      @font-face {
        font-family: 'Inter';
        font-style: normal;
        font-weight: 400 700;
        font-display: swap;
        src: url(https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfAZ9hiA.woff2) format('woff2');
        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
      }
    `
    document.head.appendChild(style)
    
    return () => {
      if (style.parentNode) {
        style.parentNode.removeChild(style)
      }
    }
  }, [])

  return null
}

/**
 * Performance-optimized font loading hook
 */
export function useFontLoading() {
  useEffect(() => {
    // Monitor font loading performance
    if ('fonts' in document) {
      document.fonts.ready.then(() => {
        // All fonts have loaded
        if (process.env.NODE_ENV === 'development') {
          console.log('✅ All fonts loaded successfully')
        }
        
        // Track font loading performance
        if (window.gtag) {
          window.gtag('event', 'font_loading_complete', {
            event_category: 'performance',
            event_label: 'inter_font',
            custom_parameter_1: 'font_optimization_2025'
          })
        }
      })
    }
  }, [])
}
