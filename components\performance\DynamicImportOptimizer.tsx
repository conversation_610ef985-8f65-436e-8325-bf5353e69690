'use client'

import { lazy, Suspense, useEffect } from 'react'

// Lazy load heavy components to reduce initial bundle size
const TestimonialsSection = lazy(() => import('@/components/TestimonialsSection'))
const ExitIntentPopup = lazy(() => import('@/components/ExitIntentPopup'))
const CookieConsent = lazy(() => import('@/components/CookieConsent'))

// Loading fallbacks for better UX
const TestimonialsLoading = () => (
  <div className="py-16 bg-gray-50 dark:bg-gray-900">
    <div className="container mx-auto px-6">
      <div className="text-center mb-12">
        <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-64 mx-auto mb-4 animate-pulse"></div>
        <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-96 mx-auto animate-pulse"></div>
      </div>
      <div className="grid md:grid-cols-3 gap-8">
        {[1, 2, 3].map((i) => (
          <div key={i} className="bg-white dark:bg-gray-800 rounded-3xl p-8 animate-pulse">
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded mb-4"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded mb-4"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
          </div>
        ))}
      </div>
    </div>
  </div>
)

const FormLoading = () => (
  <div className="max-w-md mx-auto">
    <div className="space-y-4">
      {[1, 2, 3, 4].map((i) => (
        <div key={i} className="h-12 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
      ))}
      <div className="h-12 bg-blue-200 dark:bg-blue-700 rounded animate-pulse"></div>
    </div>
  </div>
)

// Optimized components with lazy loading
export const OptimizedTestimonialsSection = () => (
  <Suspense fallback={<TestimonialsLoading />}>
    <TestimonialsSection />
  </Suspense>
)

export const OptimizedExitIntentPopup = () => (
  <Suspense fallback={<div>Loading...</div>}>
    <ExitIntentPopup />
  </Suspense>
)

export const OptimizedCookieConsent = () => (
  <Suspense fallback={<div>Loading...</div>}>
    <CookieConsent />
  </Suspense>
)

// Bundle size monitoring
export function BundleSizeMonitor() {
  useEffect(() => {
    // Only run on client side
    if (typeof window === 'undefined') return

    // Monitor bundle loading performance
    const monitorBundlePerformance = () => {
      if ('performance' in window && 'getEntriesByType' in performance) {
        const resourceEntries = performance.getEntriesByType('resource') as PerformanceResourceTiming[]

        // Track JavaScript bundle sizes
        const jsResources = resourceEntries.filter(entry =>
          entry.name.includes('.js') &&
          (entry.name.includes('_next/static') || entry.name.includes('chunks'))
        )

        let totalJSSize = 0
        jsResources.forEach(resource => {
          if (resource.transferSize) {
            totalJSSize += resource.transferSize
          }
        })

        // Report to analytics if bundle is large
        if (totalJSSize > 500000 && window.gtag) { // 500KB threshold
          window.gtag('event', 'large_bundle_detected', {
            event_category: 'performance',
            event_label: 'javascript_bundle_size',
            value: Math.round(totalJSSize / 1024), // Size in KB
            custom_parameter_1: 'bundle_optimization'
          })
        }

        // Track individual chunk loading times
        jsResources.forEach(resource => {
          if (resource.duration > 1000 && window.gtag) { // 1 second threshold
            window.gtag('event', 'slow_chunk_loading', {
              event_category: 'performance',
              event_label: resource.name.split('/').pop() || 'unknown_chunk',
              value: Math.round(resource.duration),
              custom_parameter_1: 'chunk_performance'
            })
          }
        })
      }
    }

    // Run monitoring after page load
    if (document.readyState === 'complete') {
      setTimeout(monitorBundlePerformance, 2000)
    } else {
      window.addEventListener('load', () => {
        setTimeout(monitorBundlePerformance, 2000)
      })
    }
  }, [])

  return null
}

// Tree shaking helper - only import what's needed
export const optimizedImports = {
  // Lucide icons optimized imports (only icons that exist in your project)
  ChevronRight: () => import('lucide-react').then(mod => ({ default: mod.ChevronRight })),
  Mail: () => import('lucide-react').then(mod => ({ default: mod.Mail })),
  Phone: () => import('lucide-react').then(mod => ({ default: mod.Phone })),
  Calendar: () => import('lucide-react').then(mod => ({ default: mod.Calendar })),
  Check: () => import('lucide-react').then(mod => ({ default: mod.Check })),
  X: () => import('lucide-react').then(mod => ({ default: mod.X })),
  Menu: () => import('lucide-react').then(mod => ({ default: mod.Menu })),
  Sun: () => import('lucide-react').then(mod => ({ default: mod.Sun })),
  Moon: () => import('lucide-react').then(mod => ({ default: mod.Moon })),
}

// Code splitting utility - simplified to avoid TypeScript complexity
export function createDynamicComponent(
  importFn: () => Promise<{ default: React.ComponentType<any> }>,
  FallbackComponent?: React.ComponentType
) {
  const Component = lazy(importFn)

  return (props: any) => (
    <Suspense fallback={FallbackComponent ? <FallbackComponent /> : <div>Loading...</div>}>
      <Component {...props} />
    </Suspense>
  )
}

// Preload critical chunks
export function preloadCriticalChunks() {
  if (typeof window === 'undefined') return

  // Preload testimonials if user scrolls past hero
  const preloadTestimonials = () => {
    const heroHeight = window.innerHeight
    if (window.scrollY > heroHeight * 0.5) {
      import('@/components/TestimonialsSection')
      window.removeEventListener('scroll', preloadTestimonials)
    }
  }

  // Preload heavy components on user interaction
  const preloadHeavyComponents = () => {
    import('@/components/ExitIntentPopup')
    import('@/components/CookieConsent')
    document.removeEventListener('mousemove', preloadHeavyComponents)
    document.removeEventListener('touchstart', preloadHeavyComponents)
  }

  // Add event listeners
  window.addEventListener('scroll', preloadTestimonials, { passive: true })
  document.addEventListener('mousemove', preloadHeavyComponents, { once: true })
  document.addEventListener('touchstart', preloadHeavyComponents, { once: true })
}

// Initialize preloading component
export function PreloadInitializer() {
  useEffect(() => {
    // Preload after initial render
    const timer = setTimeout(preloadCriticalChunks, 1000)
    return () => clearTimeout(timer)
  }, [])

  return null
}
