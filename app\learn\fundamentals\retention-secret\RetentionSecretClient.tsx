'use client'

import { useEffect, useState } from 'react'
import { motion } from 'framer-motion'
import { 
  ArrowLeft, 
  Clock, 
  Users, 
  CheckCircle,
  ArrowRight,
  Brain,
  MessageSquare,
  Phone,
  Mail,
  Zap,
  TrendingUp,
  TrendingDown,
  Shield,
  Target,
  Lightbulb,
  AlertTriangle,
  Eye,
  Heart,
  Star,
  Headphones,
  Bot,
  UserCheck,
  Award,
  BarChart3,
  Gift,
  Share2,
  DollarSign,
  Calculator,
  Activity,
  Bell
} from 'lucide-react'
import Link from 'next/link'

const churnReasons = [
  {
    reason: "Silent Dissatisfaction",
    percentage: "43%",
    description: "Customers unhappy but never complain",
    warning_signs: ["Decreased usage", "Delayed payments", "Short responses", "Avoiding contact"],
    prevention: "Proactive satisfaction monitoring",
    color: "red"
  },
  {
    reason: "Value Perception Gap", 
    percentage: "31%",
    description: "Don't see ongoing value in service",
    warning_signs: ["Questions about pricing", "Comparing alternatives", "Reduced engagement", "Feature requests"],
    prevention: "Regular value demonstration",
    color: "orange"
  },
  {
    reason: "Lifecycle Changes",
    percentage: "18%",
    description: "Business or personal circumstances change",
    warning_signs: ["Mentions of changes", "Budget discussions", "Timeline shifts", "New priorities"],
    prevention: "Adaptive service offerings",
    color: "yellow"
  },
  {
    reason: "Poor Communication",
    percentage: "8%",
    description: "Feel ignored or misunderstood",
    warning_signs: ["Complaints ignored", "Slow responses", "Generic communication", "Escalations"],
    prevention: "Communication excellence",
    color: "blue"
  }
]

const earlyWarningSignals = [
  {
    signal: "Engagement Drop",
    description: "50%+ decrease in platform usage or interaction",
    timeframe: "7-14 days",
    action: "Personal check-in call",
    automation: "Usage tracking alerts",
    success_rate: "78%",
    color: "red"
  },
  {
    signal: "Payment Delays",
    description: "Late payments or payment method issues", 
    timeframe: "3-7 days",
    action: "Proactive payment support",
    automation: "Payment failure sequences",
    success_rate: "89%",
    color: "orange"
  },
  {
    signal: "Support Ticket Patterns",
    description: "Increased complaints or frustration indicators",
    timeframe: "1-3 days",
    action: "Escalation to senior team",
    automation: "Sentiment analysis alerts",
    success_rate: "67%",
    color: "yellow"
  },
  {
    signal: "Communication Changes",
    description: "Shorter responses, delayed replies, formal tone",
    timeframe: "5-10 days",
    action: "Relationship repair sequence",
    automation: "Communication pattern analysis",
    success_rate: "54%",
    color: "blue"
  }
]

export default function RetentionSecretClient() {
  // Retention ROI Calculator state
  const [retentionData, setRetentionData] = useState({
    monthlyCustomers: '',
    avgCustomerValue: '',
    currentChurnRate: '',
    targetChurnRate: '',
    customerLifetime: '',
    retentionCost: ''
  })

  // Calculate retention ROI
  const currentChurn = (parseFloat(retentionData.monthlyCustomers) || 0) * (parseFloat(retentionData.currentChurnRate) || 0) / 100
  const targetChurn = (parseFloat(retentionData.monthlyCustomers) || 0) * (parseFloat(retentionData.targetChurnRate) || 0) / 100
  const churnReduction = currentChurn - targetChurn
  
  const monthlyValueSaved = churnReduction * (parseFloat(retentionData.avgCustomerValue) || 0) * (parseFloat(retentionData.customerLifetime) || 1)
  const annualValueSaved = monthlyValueSaved * 12
  const retentionInvestment = (parseFloat(retentionData.retentionCost) || 0) * 12
  const netROI = annualValueSaved - retentionInvestment

  const handleRetentionInputChange = (field: string, value: string) => {
    setRetentionData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  // Mark lesson as completed when user visits
  useEffect(() => {
    const markAsCompleted = () => {
      const saved = localStorage.getItem('genlogic-academy-progress')
      const completed = saved ? JSON.parse(saved) : []
      if (!completed.includes('retention-secret')) {
        completed.push('retention-secret')
        localStorage.setItem('genlogic-academy-progress', JSON.stringify(completed))
        
        // Dispatch custom event to notify other components
        window.dispatchEvent(new CustomEvent('academyProgressUpdate'))
      }
    }
    
    // Mark as completed after user has been on page for 30 seconds
    const timer = setTimeout(markAsCompleted, 30000)
    return () => clearTimeout(timer)
  }, [])

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-blue-50/30 to-background dark:from-background dark:via-blue-950/30 dark:to-background">
      {/* Navigation */}
      <div className="pt-40 pb-8">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-5xl mx-auto">
            <Link 
              href="/learn" 
              className="inline-flex items-center gap-2 bg-background/80 backdrop-blur-sm border border-border/50 rounded-2xl px-4 py-2 text-primary-600 hover:text-primary-700 font-medium transition-all duration-300 hover:shadow-md"
            >
              <ArrowLeft className="w-4 h-4" />
              Back to Academy
            </Link>
          </div>
        </div>
      </div>

      {/* Hero Section */}
      <section className="pb-16">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-5xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              {/* Lesson Meta */}
              <div className="flex items-center justify-center gap-6 mb-8">
                <div className="flex items-center gap-2 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 px-4 py-2 rounded-2xl">
                  <Activity className="w-4 h-4" />
                  <span className="font-medium text-sm">Retention Systems</span>
                </div>
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Clock className="w-4 h-4" />
                  <span className="text-sm">18 min read</span>
                </div>
                <div className="bg-muted px-3 py-1 rounded-full text-xs font-medium text-muted-foreground">
                  Growth Strategy
                </div>
              </div>

              {/* Main Title */}
              <h1 className="text-4xl md:text-6xl font-bold text-foreground mb-6 leading-tight">
                The Retention Secret:
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-500 to-purple-500"> Why Customers</span>
                <br />Really
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-red-500 to-orange-500"> Leave</span>
              </h1>

              <p className="text-xl md:text-2xl text-muted-foreground mb-8 leading-relaxed max-w-4xl mx-auto">
                Something shocking is happening across UK businesses that's making customer retention unpredictable. 
                <strong className="text-foreground"> While most companies wait for complaints, smart businesses discovered the early warning system that detects unhappy customers 73% earlier.</strong>
                Here's why customers really leave and the automated retention sequences that save £89k annually.
              </p>

              {/* Shocking Stats */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
                <div className="bg-gradient-to-br from-red-50 to-red-100 dark:from-red-950/30 dark:to-red-900/30 rounded-3xl p-6 border border-red-200/50 dark:border-red-800/50">
                  <div className="flex items-center gap-3 mb-3">
                    <TrendingDown className="w-6 h-6 text-red-500" />
                    <div className="text-3xl font-bold text-red-600 dark:text-red-400">73%</div>
                  </div>
                  <div className="text-sm text-red-700 dark:text-red-300 font-medium">Leave before you know they're unhappy</div>
                </div>
                <div className="bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-950/30 dark:to-orange-900/30 rounded-3xl p-6 border border-orange-200/50 dark:border-orange-800/50">
                  <div className="flex items-center gap-3 mb-3">
                    <AlertTriangle className="w-6 h-6 text-orange-500" />
                    <div className="text-3xl font-bold text-orange-600 dark:text-orange-400">43%</div>
                  </div>
                  <div className="text-sm text-orange-700 dark:text-orange-300 font-medium">Are silently dissatisfied</div>
                </div>
                <div className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950/30 dark:to-green-900/30 rounded-3xl p-6 border border-green-200/50 dark:border-green-800/50">
                  <div className="flex items-center gap-3 mb-3">
                    <TrendingUp className="w-6 h-6 text-green-500" />
                    <div className="text-3xl font-bold text-green-600 dark:text-green-400">89%</div>
                  </div>
                  <div className="text-sm text-green-700 dark:text-green-300 font-medium">Can be saved with early intervention</div>
                </div>
              </div>
            </motion.div>

            {/* What You'll Learn Card */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-950/50 dark:to-purple-950/50 rounded-3xl p-8 border border-blue-200/50 dark:border-blue-800/50 shadow-xl"
            >
              <div className="flex items-start gap-6">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-500 rounded-2xl flex items-center justify-center flex-shrink-0">
                  <Brain className="w-8 h-8 text-white" />
                </div>
                <div className="flex-1">
                  <h3 className="text-2xl font-bold text-foreground mb-4">What You'll Master in This Lesson</h3>
                  <div className="grid md:grid-cols-2 gap-4">
                    <div className="space-y-3">
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-foreground">Why 73% of customers leave without complaining</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-foreground">The 4 early warning signals that predict churn</span>
                      </div>
                    </div>
                    <div className="space-y-3">
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-foreground">Automated retention sequences that save 89% of at-risk customers</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-foreground">Case study: £89k saved annually with early warning system</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Main Content - Why Customers Really Leave */}
      <section className="pb-16">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-4xl mx-auto space-y-12">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="bg-background/80 backdrop-blur-sm rounded-3xl p-8 border border-border/50 shadow-xl"
            >
              <div className="flex items-start gap-6 mb-8">
                <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-orange-500 rounded-xl flex items-center justify-center flex-shrink-0">
                  <AlertTriangle className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h2 className="text-3xl font-bold text-foreground mb-4">The Hidden Reasons Customers Leave</h2>
                  <p className="text-lg text-muted-foreground leading-relaxed">
                    Smart business owners discovered that customers rarely tell you why they're leaving. Here are the real reasons behind customer churn and how to spot them early.
                  </p>
                </div>
              </div>

              <div className="space-y-6">
                {churnReasons.map((reason, index) => (
                  <div key={index} className={`bg-gradient-to-r ${
                    reason.color === 'red' ? 'from-red-50 to-red-100 dark:from-red-950/30 dark:to-red-900/30' :
                    reason.color === 'orange' ? 'from-orange-50 to-orange-100 dark:from-orange-950/30 dark:to-orange-900/30' :
                    reason.color === 'yellow' ? 'from-yellow-50 to-yellow-100 dark:from-yellow-950/30 dark:to-yellow-900/30' :
                    'from-blue-50 to-blue-100 dark:from-blue-950/30 dark:to-blue-900/30'
                  } rounded-2xl p-6 border ${
                    reason.color === 'red' ? 'border-red-200/50 dark:border-red-800/50' :
                    reason.color === 'orange' ? 'border-orange-200/50 dark:border-orange-800/50' :
                    reason.color === 'yellow' ? 'border-yellow-200/50 dark:border-yellow-800/50' :
                    'border-blue-200/50 dark:border-blue-800/50'
                  }`}>
                    <div className="flex items-start gap-4 mb-6">
                      <div className={`w-16 h-16 rounded-xl flex items-center justify-center flex-shrink-0 ${
                        reason.color === 'red' ? 'bg-red-500' :
                        reason.color === 'orange' ? 'bg-orange-500' :
                        reason.color === 'yellow' ? 'bg-yellow-500' :
                        'bg-blue-500'
                      }`}>
                        <span className="text-white font-bold text-lg">{reason.percentage}</span>
                      </div>
                      <div className="flex-1">
                        <h4 className={`text-xl font-bold mb-2 ${
                          reason.color === 'red' ? 'text-red-700 dark:text-red-300' :
                          reason.color === 'orange' ? 'text-orange-700 dark:text-orange-300' :
                          reason.color === 'yellow' ? 'text-yellow-700 dark:text-yellow-300' :
                          'text-blue-700 dark:text-blue-300'
                        }`}>
                          {reason.reason}
                        </h4>
                        <p className={`mb-4 ${
                          reason.color === 'red' ? 'text-red-600 dark:text-red-400' :
                          reason.color === 'orange' ? 'text-orange-600 dark:text-orange-400' :
                          reason.color === 'yellow' ? 'text-yellow-600 dark:text-yellow-400' :
                          'text-blue-600 dark:text-blue-400'
                        }`}>
                          {reason.description}
                        </p>
                      </div>
                    </div>

                    <div className="grid md:grid-cols-2 gap-4">
                      <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
                        <h5 className={`font-bold mb-2 ${
                          reason.color === 'red' ? 'text-red-600 dark:text-red-400' :
                          reason.color === 'orange' ? 'text-orange-600 dark:text-orange-400' :
                          reason.color === 'yellow' ? 'text-yellow-600 dark:text-yellow-400' :
                          'text-blue-600 dark:text-blue-400'
                        }`}>Warning Signs:</h5>
                        <ul className="space-y-1 text-sm">
                          {reason.warning_signs.map((sign, signIndex) => (
                            <li key={signIndex} className={`${
                              reason.color === 'red' ? 'text-red-600 dark:text-red-400' :
                              reason.color === 'orange' ? 'text-orange-600 dark:text-orange-400' :
                              reason.color === 'yellow' ? 'text-yellow-600 dark:text-yellow-400' :
                              'text-blue-600 dark:text-blue-400'
                            }`}>
                              • {sign}
                            </li>
                          ))}
                        </ul>
                      </div>
                      <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
                        <h5 className={`font-bold mb-2 ${
                          reason.color === 'red' ? 'text-red-600 dark:text-red-400' :
                          reason.color === 'orange' ? 'text-orange-600 dark:text-orange-400' :
                          reason.color === 'yellow' ? 'text-yellow-600 dark:text-yellow-400' :
                          'text-blue-600 dark:text-blue-400'
                        }`}>Prevention Strategy:</h5>
                        <p className={`text-sm ${
                          reason.color === 'red' ? 'text-red-600 dark:text-red-400' :
                          reason.color === 'orange' ? 'text-orange-600 dark:text-orange-400' :
                          reason.color === 'yellow' ? 'text-yellow-600 dark:text-yellow-400' :
                          'text-blue-600 dark:text-blue-400'
                        }`}>
                          {reason.prevention}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>

            {/* Early Warning System */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="bg-background/80 backdrop-blur-sm rounded-3xl p-8 border border-border/50 shadow-xl"
            >
              <div className="flex items-start gap-6 mb-8">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-xl flex items-center justify-center flex-shrink-0">
                  <Bell className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h2 className="text-3xl font-bold text-foreground mb-4">The Early Warning System That Saves 89% of At-Risk Customers</h2>
                  <p className="text-lg text-muted-foreground leading-relaxed">
                    Smart businesses discovered that customer churn follows predictable patterns. Here's the automated early warning system that detects problems before customers complain.
                  </p>
                </div>
              </div>

              <div className="space-y-8">
                {earlyWarningSignals.map((signal, index) => (
                  <div key={index} className={`bg-gradient-to-r ${
                    signal.color === 'red' ? 'from-red-50 to-red-100 dark:from-red-950/30 dark:to-red-900/30' :
                    signal.color === 'orange' ? 'from-orange-50 to-orange-100 dark:from-orange-950/30 dark:to-orange-900/30' :
                    signal.color === 'yellow' ? 'from-yellow-50 to-yellow-100 dark:from-yellow-950/30 dark:to-yellow-900/30' :
                    'from-blue-50 to-blue-100 dark:from-blue-950/30 dark:to-blue-900/30'
                  } rounded-2xl p-6 border ${
                    signal.color === 'red' ? 'border-red-200/50 dark:border-red-800/50' :
                    signal.color === 'orange' ? 'border-orange-200/50 dark:border-orange-800/50' :
                    signal.color === 'yellow' ? 'border-yellow-200/50 dark:border-yellow-800/50' :
                    'border-blue-200/50 dark:border-blue-800/50'
                  }`}>
                    <div className="flex items-start gap-4 mb-6">
                      <div className={`w-12 h-12 rounded-xl flex items-center justify-center flex-shrink-0 ${
                        signal.color === 'red' ? 'bg-red-500' :
                        signal.color === 'orange' ? 'bg-orange-500' :
                        signal.color === 'yellow' ? 'bg-yellow-500' :
                        'bg-blue-500'
                      }`}>
                        <span className="text-white font-bold text-lg">{index + 1}</span>
                      </div>
                      <div className="flex-1">
                        <h4 className={`text-xl font-bold mb-2 ${
                          signal.color === 'red' ? 'text-red-700 dark:text-red-300' :
                          signal.color === 'orange' ? 'text-orange-700 dark:text-orange-300' :
                          signal.color === 'yellow' ? 'text-yellow-700 dark:text-yellow-300' :
                          'text-blue-700 dark:text-blue-300'
                        }`}>
                          {signal.signal}
                        </h4>
                        <p className={`mb-4 ${
                          signal.color === 'red' ? 'text-red-600 dark:text-red-400' :
                          signal.color === 'orange' ? 'text-orange-600 dark:text-orange-400' :
                          signal.color === 'yellow' ? 'text-yellow-600 dark:text-yellow-400' :
                          'text-blue-600 dark:text-blue-400'
                        }`}>
                          {signal.description}
                        </p>
                      </div>
                      <div className="text-center">
                        <div className={`text-3xl font-bold ${
                          signal.color === 'red' ? 'text-red-600 dark:text-red-400' :
                          signal.color === 'orange' ? 'text-orange-600 dark:text-orange-400' :
                          signal.color === 'yellow' ? 'text-yellow-600 dark:text-yellow-400' :
                          'text-blue-600 dark:text-blue-400'
                        }`}>{signal.success_rate}</div>
                        <div className="text-xs text-muted-foreground">Save Rate</div>
                      </div>
                    </div>

                    <div className="grid md:grid-cols-3 gap-4">
                      <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
                        <h5 className={`font-bold mb-2 ${
                          signal.color === 'red' ? 'text-red-600 dark:text-red-400' :
                          signal.color === 'orange' ? 'text-orange-600 dark:text-orange-400' :
                          signal.color === 'yellow' ? 'text-yellow-600 dark:text-yellow-400' :
                          'text-blue-600 dark:text-blue-400'
                        }`}>Detection Time:</h5>
                        <p className={`text-sm ${
                          signal.color === 'red' ? 'text-red-600 dark:text-red-400' :
                          signal.color === 'orange' ? 'text-orange-600 dark:text-orange-400' :
                          signal.color === 'yellow' ? 'text-yellow-600 dark:text-yellow-400' :
                          'text-blue-600 dark:text-blue-400'
                        }`}>
                          {signal.timeframe}
                        </p>
                      </div>
                      <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
                        <h5 className={`font-bold mb-2 ${
                          signal.color === 'red' ? 'text-red-600 dark:text-red-400' :
                          signal.color === 'orange' ? 'text-orange-600 dark:text-orange-400' :
                          signal.color === 'yellow' ? 'text-yellow-600 dark:text-yellow-400' :
                          'text-blue-600 dark:text-blue-400'
                        }`}>Immediate Action:</h5>
                        <p className={`text-sm ${
                          signal.color === 'red' ? 'text-red-600 dark:text-red-400' :
                          signal.color === 'orange' ? 'text-orange-600 dark:text-orange-400' :
                          signal.color === 'yellow' ? 'text-yellow-600 dark:text-yellow-400' :
                          'text-blue-600 dark:text-blue-400'
                        }`}>
                          {signal.action}
                        </p>
                      </div>
                      <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
                        <h5 className={`font-bold mb-2 ${
                          signal.color === 'red' ? 'text-red-600 dark:text-red-400' :
                          signal.color === 'orange' ? 'text-orange-600 dark:text-orange-400' :
                          signal.color === 'yellow' ? 'text-yellow-600 dark:text-yellow-400' :
                          'text-blue-600 dark:text-blue-400'
                        }`}>Automation:</h5>
                        <p className={`text-sm ${
                          signal.color === 'red' ? 'text-red-600 dark:text-red-400' :
                          signal.color === 'orange' ? 'text-orange-600 dark:text-orange-400' :
                          signal.color === 'yellow' ? 'text-yellow-600 dark:text-yellow-400' :
                          'text-blue-600 dark:text-blue-400'
                        }`}>
                          {signal.automation}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>

            {/* Next Lesson Navigation */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.7 }}
              className="bg-background rounded-3xl border border-border shadow-lg p-8"
            >
              <h3 className="text-xl font-bold text-foreground mb-6 text-center">Continue Your Learning Journey</h3>

              <div className="grid md:grid-cols-2 gap-6">
                <Link
                  href="/learn/fundamentals/scale-breakthrough"
                  className="group bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950/30 dark:to-green-900/30 rounded-2xl p-6 border border-green-200/50 dark:border-green-800/50 hover:shadow-lg transition-all duration-300"
                >
                  <div className="flex items-center gap-4 mb-4">
                    <div className="w-12 h-12 bg-green-100 dark:bg-green-900/50 rounded-xl flex items-center justify-center">
                      <ArrowRight className="w-6 h-6 text-green-600 group-hover:translate-x-1 transition-transform" />
                    </div>
                    <div>
                      <h4 className="font-bold text-foreground">Final Lesson</h4>
                      <p className="text-sm text-muted-foreground">Lesson 15 of 15</p>
                    </div>
                  </div>
                  <h5 className="font-semibold text-foreground mb-2">The Scale Breakthrough</h5>
                  <p className="text-sm text-muted-foreground">
                    How to break through every scaling bottleneck and build a business that runs without you
                  </p>
                </Link>

                <Link
                  href="/learn"
                  className="group bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-950/30 dark:to-gray-900/30 rounded-2xl p-6 border border-gray-200/50 dark:border-gray-800/50 hover:shadow-lg transition-all duration-300"
                >
                  <div className="flex items-center gap-4 mb-4">
                    <div className="w-12 h-12 bg-gray-100 dark:bg-gray-900/50 rounded-xl flex items-center justify-center">
                      <ArrowLeft className="w-6 h-6 text-gray-600 group-hover:-translate-x-1 transition-transform" />
                    </div>
                    <div>
                      <h4 className="font-bold text-foreground">Academy</h4>
                      <p className="text-sm text-muted-foreground">All lessons</p>
                    </div>
                  </div>
                  <h5 className="font-semibold text-foreground mb-2">Back to Academy</h5>
                  <p className="text-sm text-muted-foreground">
                    View all lessons and track your progress
                  </p>
                </Link>
              </div>
            </motion.div>
          </div>
        </div>
      </section>
    </div>
  )
}
