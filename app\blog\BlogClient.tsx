'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { CustomButton } from '@/components/ui/custom-button'
import {
  Search,
  Calendar,
  Clock,
  User,
  ArrowRight,
  BookOpen,
  TrendingUp,
  Heart,
  Eye,
  Filter
} from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'
import { BlogPost, Category } from './types'
import BlogCard from '../../components/blog/BlogCard'
import RSSSubscribe from '../../components/blog/RSSSubscribe'

interface BlogClientProps {
  posts: BlogPost[]
  categories: Category[]
}

export default function BlogClient({ posts, categories }: BlogClientProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [visiblePosts, setVisiblePosts] = useState(6)
  const postsPerPage = 6

  const featuredPosts = posts.filter(post => post.featured)
  const regularPosts = posts.filter(post => !post.featured)

  // Filter posts based on search and category
  const filteredPosts = regularPosts.filter(post => {
    const matchesSearch = post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         post.excerpt.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         post.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
    
    const matchesCategory = selectedCategory === 'all' || 
                           post.category.toLowerCase().replace(' ', '-') === selectedCategory

    return matchesSearch && matchesCategory
  })

  // Load More functionality
  const displayedPosts = filteredPosts.slice(0, visiblePosts)
  const hasMorePosts = visiblePosts < filteredPosts.length

  const loadMorePosts = () => {
    setVisiblePosts(prev => prev + postsPerPage)
  }

  // Reset visible posts when filters change
  const resetPagination = () => {
    setVisiblePosts(postsPerPage)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    })
  }

  return (
    <div className="bg-background transition-colors duration-300">
      {/* Hero Section */}
      <section className="relative header-spacing pb-20 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-primary-50/30 via-background to-accent-50/20 dark:from-primary-950/20 dark:via-background dark:to-accent-950/10"></div>
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-primary-200/20 rounded-full blur-3xl dark:bg-primary-800/10"></div>
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-accent-200/20 rounded-full blur-3xl dark:bg-accent-800/10"></div>

        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, ease: 'easeOut' }}
            >
              <div className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-primary-100 to-accent-100 text-primary-800 text-sm font-semibold tracking-wide mb-8 dark:from-primary-900/30 dark:to-accent-900/30 dark:text-primary-400 border border-primary-200/50 dark:border-primary-800/50">
                <BookOpen className="w-4 h-4 mr-2" />
                Business Automation Insights
              </div>

              <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-foreground mb-8 leading-[1.1]">
                Stop working late,{' '}
                <span className="relative">
                  <span className="text-primary-700 dark:text-primary-400">start living</span>
                  <div className="absolute -bottom-2 left-0 right-0 h-3 bg-gradient-to-r from-primary-200 to-accent-200 dark:from-primary-800 dark:to-accent-800 rounded-full opacity-30"></div>
                </span>
              </h1>

              <p className="text-xl text-muted-foreground mb-12 leading-relaxed max-w-3xl mx-auto">
                Real stories from UK business owners who've reclaimed their evenings, weekends, and sanity. 
                <strong className="text-foreground"> Discover how automation transforms businesses and lives.</strong>
              </p>

              {/* Search and Filter */}
              <div className="flex flex-col sm:flex-row gap-4 max-w-2xl mx-auto mb-8">
                <div className="relative flex-1">
                  <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-muted-foreground" />
                  <input
                    type="text"
                    placeholder="Search articles..."
                    value={searchQuery}
                    onChange={(e) => {
                      setSearchQuery(e.target.value)
                      resetPagination()
                    }}
                    className="w-full pl-12 pr-4 py-3 rounded-2xl border border-border bg-background/50 backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-300"
                  />
                </div>
                <select
                  value={selectedCategory}
                  onChange={(e) => {
                    setSelectedCategory(e.target.value)
                    resetPagination()
                  }}
                  className="px-6 py-3 rounded-2xl border border-border bg-background/50 backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-300"
                >
                  {categories.map((category) => (
                    <option key={category.slug} value={category.slug}>
                      {category.name} ({category.count})
                    </option>
                  ))}
                </select>
              </div>

              {/* Stats */}
              <div className="flex flex-col sm:flex-row items-center justify-center gap-6 text-sm text-muted-foreground">
                <div className="flex items-center">
                  <TrendingUp className="w-4 h-4 mr-2 text-primary-600" />
                  <span><strong className="text-foreground">500+</strong> UK businesses transformed</span>
                </div>
                <div className="flex items-center">
                  <BookOpen className="w-4 h-4 mr-2 text-accent-600" />
                  <span><strong className="text-foreground">{posts.length}</strong> practical guides</span>
                </div>
                <div className="flex items-center">
                  <Heart className="w-4 h-4 mr-2 text-red-500" />
                  <span><strong className="text-foreground">Real stories</strong> from real business owners</span>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Featured Articles */}
      {featuredPosts.length > 0 && (
        <section className="py-20 bg-gradient-to-b from-background to-muted/30">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, ease: 'easeOut' }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">Featured Stories</h2>
              <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                The most impactful transformations from UK business owners who've taken back control
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {featuredPosts.map((post, index) => (
                <BlogCard
                  key={post.id}
                  post={post}
                  index={index}
                  featured={true}
                />
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Regular Articles */}
      <section className="py-20 bg-gradient-to-b from-muted/30 to-background">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: 'easeOut' }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">Latest Insights</h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Practical advice to help you automate your business and reclaim your time
            </p>
          </motion.div>

          {displayedPosts.length > 0 ? (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
                {displayedPosts.map((post, index) => (
                  <BlogCard
                    key={post.id}
                    post={post}
                    index={index}
                    featured={false}
                  />
                ))}
              </div>

              {/* Load More Button */}
              {hasMorePosts && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                  viewport={{ once: true }}
                  className="flex flex-col items-center gap-4"
                >
                  <div className="text-center mb-4">
                    <p className="text-muted-foreground text-sm">
                      Showing {displayedPosts.length} of {filteredPosts.length} articles
                    </p>
                  </div>

                  <CustomButton
                    onClick={loadMorePosts}
                    variant="outline"
                    size="lg"
                    className="group px-8 py-4 bg-background hover:bg-primary-50 dark:hover:bg-primary-950/30 border-2 border-primary-200 dark:border-primary-800 hover:border-primary-400 dark:hover:border-primary-600 transition-all duration-300"
                  >
                    <span className="mr-2">Load More Articles</span>
                    <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
                  </CustomButton>

                  <div className="text-center">
                    <p className="text-xs text-muted-foreground">
                      {filteredPosts.length - displayedPosts.length} more articles available
                    </p>
                  </div>
                </motion.div>
              )}
            </>
          ) : (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center py-16"
            >
              <div className="w-24 h-24 bg-gradient-to-br from-primary-100 to-accent-100 dark:from-primary-900 dark:to-accent-900 rounded-full flex items-center justify-center mx-auto mb-6">
                <Search className="w-12 h-12 text-primary-600 dark:text-primary-400" />
              </div>
              <h3 className="text-2xl font-bold text-foreground mb-4">No articles found</h3>
              <p className="text-muted-foreground mb-8 max-w-md mx-auto">
                Try adjusting your search terms or browse all categories to find what you're looking for.
              </p>
              <CustomButton
                variant="outline"
                onClick={() => {
                  setSearchQuery('')
                  setSelectedCategory('all')
                  resetPagination()
                }}
              >
                Clear Filters
              </CustomButton>
            </motion.div>
          )}
        </div>
      </section>

      {/* RSS Subscribe Section */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <RSSSubscribe />
      </div>

      {/* Newsletter CTA */}
      <section className="py-20 bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800 relative overflow-hidden">
        <div className="absolute inset-0 opacity-20">
          <div className="w-full h-full bg-white/5 bg-[radial-gradient(circle_at_50%_50%,rgba(255,255,255,0.1),transparent)]"></div>
        </div>

        <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: 'easeOut' }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6 leading-tight">
              Never miss a{' '}
              <span className="relative">
                <span className="text-accent-300">game-changing insight</span>
                <div className="absolute -bottom-2 left-0 right-0 h-3 bg-white/30 rounded-full"></div>
              </span>
            </h2>
            <p className="text-xl text-primary-100 mb-8 leading-relaxed max-w-2xl mx-auto">
              <strong className="text-white">Join 2,000+ UK business owners</strong> who get our weekly automation insights.
              Real strategies, real results, delivered every Tuesday morning.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center max-w-md mx-auto mb-8">
              <input
                type="email"
                placeholder="Enter your email address"
                className="flex-1 px-6 py-4 rounded-2xl border-0 bg-white/10 backdrop-blur-sm text-white placeholder-primary-200 focus:outline-none focus:ring-2 focus:ring-white/50 transition-all duration-300"
              />
              <CustomButton
                variant="secondary"
                size="lg"
                className="bg-white text-primary-700 hover:bg-primary-50 shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300"
              >
                Get Weekly Insights
              </CustomButton>
            </div>

            <div className="flex flex-col sm:flex-row items-center justify-center gap-6 text-primary-100 text-sm">
              <div className="flex items-center">
                <BookOpen className="w-4 h-4 mr-2 text-accent-300" />
                <span><strong className="text-white">Weekly</strong> practical guides</span>
              </div>
              <div className="flex items-center">
                <TrendingUp className="w-4 h-4 mr-2 text-green-300" />
                <span><strong className="text-white">Real</strong> business transformations</span>
              </div>
              <div className="flex items-center">
                <Heart className="w-4 h-4 mr-2 text-red-300" />
                <span><strong className="text-white">No spam</strong> - unsubscribe anytime</span>
              </div>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  )
}
