const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Check if ImageMagick is installed
function checkImageMagick() {
  try {
    execSync('magick -version', { stdio: 'ignore' });
    return true;
  } catch (error) {
    console.log('ImageMagick not found. Trying convert command...');
    try {
      execSync('convert -version', { stdio: 'ignore' });
      return true;
    } catch (error) {
      return false;
    }
  }
}

// Convert SVG to WebP using ImageMagick
function convertSvgToWebp(inputPath, outputPath, customSize = '1200x630') {
  try {
    // Try with magick command first (newer ImageMagick)
    try {
      execSync(`magick "${inputPath}" -resize ${customSize}! -quality 90 "${outputPath}"`, { stdio: 'inherit' });
    } catch (error) {
      // Fallback to convert command (older ImageMagick)
      execSync(`convert "${inputPath}" -resize ${customSize}! -quality 90 "${outputPath}"`, { stdio: 'inherit' });
    }
    console.log(`✅ Converted: ${path.basename(inputPath)} → ${path.basename(outputPath)} (${customSize})`);
    return true;
  } catch (error) {
    console.error(`❌ Failed to convert ${inputPath}:`, error.message);
    return false;
  }
}

// Alternative conversion using sharp (if ImageMagick is not available)
async function convertWithSharp(inputPath, outputPath, customSize = '1200x630') {
  try {
    const sharp = require('sharp');

    // Parse custom size
    const [width, height] = customSize.split('x').map(Number);

    // Read SVG and convert to WebP
    await sharp(inputPath)
      .resize(width, height)
      .webp({ quality: 90 })
      .toFile(outputPath);

    console.log(`✅ Converted with Sharp: ${path.basename(inputPath)} → ${path.basename(outputPath)} (${customSize})`);
    return true;
  } catch (error) {
    console.error(`❌ Sharp conversion failed for ${inputPath}:`, error.message);
    return false;
  }
}

// Main conversion function
async function convertAllSvgs() {
  const publicDir = path.join(__dirname, 'public');
  const blogImagesDir = path.join(publicDir, 'blog-images');
  const academyImagesDir = path.join(publicDir, 'academy-images');

  // List of all SVG files to convert
  const svgFiles = [
    // Main OG images
    { input: path.join(publicDir, 'og-image.svg'), output: path.join(publicDir, 'og-image.webp') },
    { input: path.join(publicDir, 'og-blog.svg'), output: path.join(publicDir, 'og-blog.webp') },
    { input: path.join(publicDir, 'og-contact.svg'), output: path.join(publicDir, 'og-contact.webp') },
    { input: path.join(publicDir, 'og-demo.svg'), output: path.join(publicDir, 'og-demo.webp') },
    { input: path.join(publicDir, 'og-about.svg'), output: path.join(publicDir, 'og-about.webp') },
    { input: path.join(publicDir, 'og-pricing.svg'), output: path.join(publicDir, 'og-pricing.webp') },
    { input: path.join(publicDir, 'og-website-development.svg'), output: path.join(publicDir, 'og-website-development.webp') },
    { input: path.join(publicDir, 'og-business-automation.svg'), output: path.join(publicDir, 'og-business-automation.webp') },

    // Academy OG images
    { input: path.join(academyImagesDir, 'og-academy.svg'), output: path.join(academyImagesDir, 'og-academy.webp') },
    { input: path.join(academyImagesDir, 'og-why-follow-ups-fail.svg'), output: path.join(academyImagesDir, 'og-why-follow-ups-fail.webp') },
    { input: path.join(academyImagesDir, 'og-three-touch-rule.svg'), output: path.join(academyImagesDir, 'og-three-touch-rule.webp') },
    { input: path.join(academyImagesDir, 'og-automation-underground-movement.svg'), output: path.join(academyImagesDir, 'og-automation-underground-movement.webp') },
    { input: path.join(academyImagesDir, 'og-chaos-to-control.svg'), output: path.join(academyImagesDir, 'og-chaos-to-control.webp') },
    { input: path.join(academyImagesDir, 'og-silent-revolution.svg'), output: path.join(academyImagesDir, 'og-silent-revolution.webp') },

    // New Academy Lessons (6-15)
    { input: path.join(academyImagesDir, 'og-psychology-of-timing.svg'), output: path.join(academyImagesDir, 'og-psychology-of-timing.webp') },
    { input: path.join(academyImagesDir, 'og-invisible-influence.svg'), output: path.join(academyImagesDir, 'og-invisible-influence.webp') },
    { input: path.join(academyImagesDir, 'og-multi-channel-revolution.svg'), output: path.join(academyImagesDir, 'og-multi-channel-revolution.webp') },
    { input: path.join(academyImagesDir, 'og-delegation-dilemma.svg'), output: path.join(academyImagesDir, 'og-delegation-dilemma.webp') },
    { input: path.join(academyImagesDir, 'og-revenue-leak-detector.svg'), output: path.join(academyImagesDir, 'og-revenue-leak-detector.webp') },
    { input: path.join(academyImagesDir, 'og-client-onboarding-machine.svg'), output: path.join(academyImagesDir, 'og-client-onboarding-machine.webp') },
    { input: path.join(academyImagesDir, 'og-referral-engine.svg'), output: path.join(academyImagesDir, 'og-referral-engine.webp') },
    { input: path.join(academyImagesDir, 'og-pricing-psychology-revolution.svg'), output: path.join(academyImagesDir, 'og-pricing-psychology-revolution.webp') },
    { input: path.join(academyImagesDir, 'og-retention-secret.svg'), output: path.join(academyImagesDir, 'og-retention-secret.webp') },
    { input: path.join(academyImagesDir, 'og-scale-breakthrough.svg'), output: path.join(academyImagesDir, 'og-scale-breakthrough.webp') },
    
    // Blog post images
    { input: path.join(blogImagesDir, 'convert-89-percent-prospects-customers.svg'), output: path.join(blogImagesDir, 'convert-89-percent-prospects-customers.webp') },
    { input: path.join(blogImagesDir, 'automation-for-customer-satisfaction.svg'), output: path.join(blogImagesDir, 'automation-for-customer-satisfaction.webp') },
    { input: path.join(blogImagesDir, 'signs-your-business-needs-automation.svg'), output: path.join(blogImagesDir, 'signs-your-business-needs-automation.webp') },
    { input: path.join(blogImagesDir, 'hidden-struggles-local-businesses.svg'), output: path.join(blogImagesDir, 'hidden-struggles-local-businesses.webp') },
    { input: path.join(blogImagesDir, 'stop-working-late-automation-guide.svg'), output: path.join(blogImagesDir, 'stop-working-late-automation-guide.webp') },
    { input: path.join(blogImagesDir, 'payment-chasing-nightmare.svg'), output: path.join(blogImagesDir, 'payment-chasing-nightmare.webp') },
    { input: path.join(blogImagesDir, 'social-media-time-trap.svg'), output: path.join(blogImagesDir, 'social-media-time-trap.webp') },
    { input: path.join(blogImagesDir, 'customer-service-automation.svg'), output: path.join(blogImagesDir, 'customer-service-automation.webp') },
    { input: path.join(blogImagesDir, 'review-generation-system.svg'), output: path.join(blogImagesDir, 'review-generation-system.webp') },
    { input: path.join(blogImagesDir, 'no-show-nightmare-solution.svg'), output: path.join(blogImagesDir, 'no-show-nightmare-solution.webp') },
    { input: path.join(blogImagesDir, 'weekend-warrior-to-family-time.svg'), output: path.join(blogImagesDir, 'weekend-warrior-to-family-time.webp') },
    { input: path.join(blogImagesDir, 'nextjs-website-development.svg'), output: path.join(blogImagesDir, 'nextjs-website-development.webp') },
    { input: path.join(blogImagesDir, 'losing-customers-competitors.svg'), output: path.join(blogImagesDir, 'losing-customers-competitors.webp') },
    { input: path.join(blogImagesDir, 'successful-business-pattern.svg'), output: path.join(blogImagesDir, 'successful-business-pattern.webp') },
    { input: path.join(blogImagesDir, 'silent-revolution-uk-businesses.svg'), output: path.join(blogImagesDir, 'silent-revolution-uk-businesses.webp') },

    // LinkedIn Cover - Custom dimensions
    { input: path.join(publicDir, 'linkedin-cover.svg'), output: path.join(publicDir, 'linkedin-cover.webp'), customSize: '1584x396' }
  ];

  console.log('🚀 Starting SVG to WebP conversion...\n');

  // Check if ImageMagick is available
  const hasImageMagick = checkImageMagick();
  let useSharp = false;

  if (!hasImageMagick) {
    console.log('⚠️  ImageMagick not found. Trying to use Sharp...');
    try {
      require('sharp');
      useSharp = true;
      console.log('✅ Sharp found and will be used for conversion.\n');
    } catch (error) {
      console.error('❌ Neither ImageMagick nor Sharp found!');
      console.log('\nTo install ImageMagick:');
      console.log('- Windows: Download from https://imagemagick.org/script/download.php#windows');
      console.log('- macOS: brew install imagemagick');
      console.log('- Ubuntu/Debian: sudo apt-get install imagemagick');
      console.log('\nOr install Sharp: npm install sharp');
      process.exit(1);
    }
  } else {
    console.log('✅ ImageMagick found and will be used for conversion.\n');
  }

  let successCount = 0;
  let failCount = 0;

  // Convert each SVG file
  for (const fileConfig of svgFiles) {
    const { input, output, customSize } = fileConfig;

    if (!fs.existsSync(input)) {
      console.log(`⚠️  Skipping ${path.basename(input)} - file not found`);
      continue;
    }

    let success = false;
    if (useSharp) {
      success = await convertWithSharp(input, output, customSize);
    } else {
      success = convertSvgToWebp(input, output, customSize);
    }

    if (success) {
      successCount++;
    } else {
      failCount++;
    }
  }

  console.log('\n🎉 Conversion completed!');
  console.log(`✅ Successfully converted: ${successCount} files`);
  if (failCount > 0) {
    console.log(`❌ Failed conversions: ${failCount} files`);
  }
  
  console.log('\n📝 Next steps:');
  console.log('1. Update your metadata files to use .webp instead of .svg');
  console.log('2. Test the images on social media platforms');
  console.log('3. Consider keeping both SVG and WebP versions for different use cases');
}

// Run the conversion
convertAllSvgs().catch(console.error);
