'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import Link from 'next/link'
import { <PERSON>ie, Shield, ExternalLink, X } from 'lucide-react'

export default function CookieConsent() {
  const [showBanner, setShowBanner] = useState(false)

  useEffect(() => {
    // Check if user has already consented
    const hasConsented = localStorage.getItem('cookieConsent')
    if (!hasConsented) {
      setShowBanner(true)
    }
  }, [])

  const acceptCookies = () => {
    localStorage.setItem('cookieConsent', 'accepted')
    setShowBanner(false)
  }

  const declineCookies = () => {
    localStorage.setItem('cookieConsent', 'declined')
    setShowBanner(false)
  }

  if (!showBanner) return null

  return (
    <AnimatePresence>
      {showBanner && (
        <motion.div
          initial={{ y: 100, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          exit={{ y: 100, opacity: 0 }}
          transition={{ duration: 0.5, ease: 'easeOut' }}
          className="fixed bottom-4 left-4 right-4 z-50"
        >
          <div className="max-w-4xl mx-auto bg-background/95 backdrop-blur-md border border-border rounded-3xl shadow-2xl shadow-black/10 dark:shadow-black/30 p-6">
            <div className="flex flex-col lg:flex-row items-start lg:items-center gap-6">
              {/* Icon and Content */}
              <div className="flex items-start gap-4 flex-1">
                <div className="w-12 h-12 bg-gradient-to-br from-primary-100 to-accent-100 dark:from-primary-900/30 dark:to-accent-900/30 rounded-2xl flex items-center justify-center flex-shrink-0">
                  <Cookie className="w-6 h-6 text-primary-700 dark:text-primary-400" />
                </div>

                <div className="flex-1">
                  <h3 className="font-semibold text-foreground mb-2 flex items-center">
                    <Shield className="w-4 h-4 mr-2 text-green-600" />
                    We respect your privacy
                  </h3>
                  <p className="text-sm text-muted-foreground leading-relaxed mb-3">
                    We use essential cookies to make our website work properly and analytics cookies to understand how you interact with our site.
                    This helps us improve your experience and provide better service.
                  </p>
                  <Link
                    href="/privacy"
                    className="inline-flex items-center text-sm text-primary-700 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 font-medium transition-colors"
                  >
                    Read our Privacy Policy
                    <ExternalLink className="w-3 h-3 ml-1" />
                  </Link>
                </div>
              </div>

              {/* Actions */}
              <div className="flex items-center gap-3 flex-shrink-0 w-full lg:w-auto">
                <button
                  onClick={declineCookies}
                  className="flex-1 lg:flex-none px-6 py-3 text-sm font-medium text-muted-foreground hover:text-foreground border border-border hover:border-border/80 rounded-xl transition-all duration-300 hover:bg-muted/50"
                >
                  Decline
                </button>
                <button
                  onClick={acceptCookies}
                  className="flex-1 lg:flex-none px-6 py-3 text-sm font-medium bg-gradient-to-r from-primary-700 to-primary-800 hover:from-primary-800 hover:to-primary-900 text-white rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl hover:shadow-primary-500/25 transform hover:scale-105"
                >
                  Accept All
                </button>
                <button
                  onClick={declineCookies}
                  className="p-2 text-muted-foreground hover:text-foreground rounded-lg transition-colors"
                  aria-label="Close"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}
