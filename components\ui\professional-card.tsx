'use client'

import { motion } from 'framer-motion'
import { cn } from '@/lib/utils'
import { ReactNode } from 'react'

interface ProfessionalCardProps {
  icon: ReactNode
  title: string
  description: string
  highlight?: string
  variant?: 'default' | 'featured' | 'problem' | 'solution'
  className?: string
}

const variantStyles = {
  default: {
    card: 'bg-background border border-border hover:border-primary-300 dark:bg-gray-800 dark:border-gray-700 dark:hover:border-primary-400',
    iconBg: 'bg-primary-50 text-primary-700 dark:bg-primary-900/30 dark:text-primary-400',
    title: 'text-foreground',
    description: 'text-muted-foreground',
    highlight: 'text-primary-700 dark:text-primary-400 font-semibold',
  },
  featured: {
    card: 'bg-gradient-to-br from-primary-50 to-background border border-primary-200 hover:border-primary-300 dark:from-primary-900/20 dark:to-gray-800 dark:border-primary-700 dark:hover:border-primary-600',
    iconBg: 'bg-primary-100 text-primary-800 dark:bg-primary-800/30 dark:text-primary-300',
    title: 'text-foreground',
    description: 'text-muted-foreground',
    highlight: 'text-primary-800 dark:text-primary-300 font-semibold',
  },
  problem: {
    card: 'bg-gradient-to-br from-red-50 to-background border border-red-200 hover:border-red-300 dark:from-red-900/20 dark:to-gray-800 dark:border-red-700 dark:hover:border-red-600',
    iconBg: 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400',
    title: 'text-foreground',
    description: 'text-muted-foreground',
    highlight: 'text-red-700 dark:text-red-400 font-semibold',
  },
  solution: {
    card: 'bg-gradient-to-br from-success-50 to-background border border-success-200 hover:border-success-300 dark:from-success-900/20 dark:to-gray-800 dark:border-success-700 dark:hover:border-success-600',
    iconBg: 'bg-success-100 text-success-800 dark:bg-success-900/30 dark:text-success-400',
    title: 'text-foreground',
    description: 'text-muted-foreground',
    highlight: 'text-success-800 dark:text-success-400 font-semibold',
  },
}

export function ProfessionalCard({
  icon,
  title,
  description,
  highlight,
  variant = 'default',
  className,
}: ProfessionalCardProps) {
  const styles = variantStyles[variant]

  return (
    <motion.div
      whileHover={{ 
        y: -8, 
        scale: 1.02,
        boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.15)'
      }}
      transition={{ duration: 0.3, ease: 'easeOut' }}
      className={cn(
        "group relative p-8 rounded-2xl transition-all duration-300 shadow-lg hover:shadow-2xl",
        styles.card,
        className
      )}
    >
      {/* Icon */}
      <div className={cn(
        "inline-flex items-center justify-center w-16 h-16 rounded-2xl mb-6 transition-transform group-hover:scale-110",
        styles.iconBg
      )}>
        {icon}
      </div>

      {/* Content */}
      <div className="space-y-4">
        <h3 className={cn(
          "text-xl font-bold leading-tight",
          styles.title
        )}>
          {title}
        </h3>

        <p className={cn(
          "text-base leading-relaxed",
          styles.description
        )}>
          {description}
        </p>

        {highlight && (
          <p className={cn(
            "text-sm",
            styles.highlight
          )}>
            {highlight}
          </p>
        )}
      </div>

      {/* Subtle gradient overlay on hover */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl" />
    </motion.div>
  )
}
