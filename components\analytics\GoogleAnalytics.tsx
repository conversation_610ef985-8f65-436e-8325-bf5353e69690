'use client'

import Script from 'next/script'
import { usePathname, useSearchParams } from 'next/navigation'
import { useEffect } from 'react'

// Google Analytics 4 Configuration
const GA_MEASUREMENT_ID = process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID?.replace(/['"]/g, '')

// Google Tag Manager Configuration
const GTM_ID = process.env.NEXT_PUBLIC_GTM_ID?.replace(/['"]/g, '')

// GA4 Events Interface
interface GAEvent {
  action: string
  category: string
  label?: string
  value?: number
  custom_parameters?: Record<string, any>
}

// GTM DataLayer Interface
interface GTMEvent {
  event: string
  [key: string]: any
}

// Google Analytics Component
export function GoogleAnalytics() {
  const pathname = usePathname()
  const searchParams = useSearchParams()

  useEffect(() => {
    if (!GA_MEASUREMENT_ID) return

    // Track page views
    const url = pathname + searchParams.toString()

    // Ensure gtag is available
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('config', GA_MEASUREMENT_ID, {
        page_path: url,
        custom_map: {
          custom_parameter_1: 'business_type',
          custom_parameter_2: 'form_source'
        }
      })
    }
  }, [pathname, searchParams])

  if (!GA_MEASUREMENT_ID) {
    return null
  }

  return (
    <>
      <Script
        strategy="lazyOnload"
        src={`https://www.googletagmanager.com/gtag/js?id=${GA_MEASUREMENT_ID}`}
      />
      <Script
        id="google-analytics"
        strategy="lazyOnload"
        dangerouslySetInnerHTML={{
          __html: `
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', '${GA_MEASUREMENT_ID}', {
              page_path: window.location.pathname,
              send_page_view: true,
              custom_map: {
                'custom_parameter_1': 'business_type',
                'custom_parameter_2': 'form_source'
              }
            });
          `,
        }}
      />
    </>
  )
}

// Google Tag Manager Component
export function GoogleTagManager() {
  if (!GTM_ID) {
    return null
  }

  return (
    <>
      <Script
        id="google-tag-manager"
        strategy="lazyOnload"
        dangerouslySetInnerHTML={{
          __html: `
            (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
            'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
            })(window,document,'script','dataLayer','${GTM_ID}');
          `,
        }}
      />
      <noscript>
        <iframe
          src={`https://www.googletagmanager.com/ns.html?id=${GTM_ID}`}
          height="0"
          width="0"
          style={{ display: 'none', visibility: 'hidden' }}
        />
      </noscript>
    </>
  )
}

// Analytics Event Tracking Functions
export const trackEvent = (event: GAEvent) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', event.action, {
      event_category: event.category,
      event_label: event.label,
      value: event.value,
      ...event.custom_parameters
    })
  }
}

// GTM Event Tracking
export const trackGTMEvent = (event: GTMEvent) => {
  if (typeof window !== 'undefined' && window.dataLayer) {
    window.dataLayer.push(event)
  }
}

// Conversion Tracking Functions
export const trackDemoBooking = (businessType: string, businessName: string) => {
  // GA4 Event
  trackEvent({
    action: 'demo_booking_submitted',
    category: 'conversions',
    label: businessType,
    value: 1,
    custom_parameters: {
      business_type: businessType,
      business_name: businessName,
      conversion_type: 'demo_booking',
      form_source: 'demo_page'
    }
  })

  // GTM Event
  trackGTMEvent({
    event: 'demo_booking',
    business_type: businessType,
    business_name: businessName,
    conversion_value: 588, // Average demo value
    currency: 'GBP'
  })
}

export const trackContactSubmission = (subject: string, name: string) => {
  // GA4 Event
  trackEvent({
    action: 'contact_form_submitted',
    category: 'conversions',
    label: subject,
    value: 1,
    custom_parameters: {
      contact_subject: subject,
      contact_name: name,
      conversion_type: 'contact_form',
      form_source: 'contact_page'
    }
  })

  // GTM Event
  trackGTMEvent({
    event: 'contact_submission',
    contact_subject: subject,
    contact_name: name,
    conversion_value: subject === 'website' ? 2500 : 588,
    currency: 'GBP'
  })
}

export const trackWebsiteQuote = (name: string) => {
  // GA4 Event
  trackEvent({
    action: 'website_quote_requested',
    category: 'conversions',
    label: 'website_development',
    value: 2500,
    custom_parameters: {
      service_type: 'website_development',
      contact_name: name,
      conversion_type: 'website_quote',
      form_source: 'contact_page'
    }
  })

  // GTM Event
  trackGTMEvent({
    event: 'website_quote_request',
    service_type: 'website_development',
    contact_name: name,
    conversion_value: 2500,
    currency: 'GBP'
  })
}

// Page View Tracking
export const trackPageView = (pagePath: string, pageTitle: string) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('config', GA_MEASUREMENT_ID, {
      page_path: pagePath,
      page_title: pageTitle
    })
  }
}

// Button Click Tracking
export const trackButtonClick = (buttonName: string, location: string) => {
  trackEvent({
    action: 'button_click',
    category: 'engagement',
    label: buttonName,
    custom_parameters: {
      button_name: buttonName,
      click_location: location
    }
  })
}

// Scroll Depth Tracking
export const trackScrollDepth = (percentage: number) => {
  trackEvent({
    action: 'scroll_depth',
    category: 'engagement',
    label: `${percentage}%`,
    value: percentage
  })
}

// File Download Tracking
export const trackFileDownload = (fileName: string, fileType: string) => {
  trackEvent({
    action: 'file_download',
    category: 'engagement',
    label: fileName,
    custom_parameters: {
      file_name: fileName,
      file_type: fileType
    }
  })
}

// External Link Tracking
export const trackExternalLink = (url: string, linkText: string) => {
  trackEvent({
    action: 'external_link_click',
    category: 'engagement',
    label: url,
    custom_parameters: {
      link_url: url,
      link_text: linkText
    }
  })
}

// Declare global gtag and dataLayer for TypeScript
declare global {
  interface Window {
    gtag: (...args: any[]) => void
    dataLayer: any[]
  }
}
