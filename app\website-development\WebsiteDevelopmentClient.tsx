'use client'

import { CustomButton } from '@/components/ui/custom-button'
import { motion } from 'framer-motion'
import { useState, useEffect } from 'react'
import {
  ArrowRight,
  Code,
  Phone,
  CheckCircle,
  Users,

  AlertTriangle,
  Smartphone,
  Search,
  Clock,
  Zap,
  Shield,
  TrendingUp,
  Gauge,
  ChevronDown,
  ChevronUp,
  Timer,
  PoundSterling,
  Target,
  Award
} from 'lucide-react'

export default function WebsiteDevelopmentClient() {
  // Dynamic availability calculation
  const [availability, setAvailability] = useState({ spotsLeft: 2, month: 'August', year: 2025 })

  useEffect(() => {
    const calculateAvailability = () => {
      const now = new Date()
      const currentMonth = now.toLocaleString('en-GB', { month: 'long' })
      const currentYear = now.getFullYear()

      // Simulate dynamic spots based on month (you can connect this to a real system later)
      const monthlySpots = {
        'January': 1, 'February': 3, 'March': 2, 'April': 1,
        'May': 2, 'June': 3, 'July': 1, 'August': 2,
        'September': 3, 'October': 1, 'November': 2, 'December': 1
      }

      const spotsLeft = monthlySpots[currentMonth as keyof typeof monthlySpots] || 2

      setAvailability({
        spotsLeft,
        month: currentMonth,
        year: currentYear
      })
    }

    calculateAvailability()

    // Update at the start of each month
    const interval = setInterval(calculateAvailability, 24 * 60 * 60 * 1000) // Check daily

    return () => clearInterval(interval)
  }, [])

  return (
    <div className="bg-background transition-colors duration-300">
      {/* Hero Section */}
      <section className="relative header-spacing pb-20 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-primary-50/30 via-background to-accent-50/20 dark:from-primary-950/20 dark:via-background dark:to-accent-950/10"></div>
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-primary-200/20 rounded-full blur-3xl dark:bg-primary-800/10"></div>
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-accent-200/20 rounded-full blur-3xl dark:bg-accent-800/10"></div>

        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center min-h-[80vh]">
            {/* Left Column - Content */}
            <div className="text-left">
              <div className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-primary-100 to-accent-100 text-primary-800 text-sm font-semibold tracking-wide mb-8 dark:from-primary-900/30 dark:to-accent-900/30 dark:text-primary-400 border border-primary-200/50 dark:border-primary-800/50">
                <Code className="w-4 h-4 mr-2" />
                Next.js Website Development
              </div>

              <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-foreground mb-8 leading-[1.1]">
                Stop losing customers to your{' '}
                <span className="relative">
                  <span className="text-red-600 dark:text-red-400">broken WordPress</span>
                  <div className="absolute -bottom-2 left-0 right-0 h-3 bg-red-200 dark:bg-red-800 rounded-full opacity-30"></div>
                </span>
                {' '}website
              </h1>

              <p className="text-xl text-muted-foreground mb-8 leading-relaxed max-w-xl">
                You know the feeling: another plugin update broke your site, a customer couldn't book on mobile, or you got that dreaded call at 9pm because your website crashed.
                <strong className="text-foreground"> We build websites that actually work.</strong>
              </p>

              <div className="flex flex-col sm:flex-row gap-4 mb-12">
                <CustomButton
                  variant="primary"
                  size="lg"
                  href="/contact"
                  icon={<ArrowRight className="w-5 h-5" />}
                  iconPosition="right"
                  className="shadow-xl hover:shadow-2xl hover:shadow-primary-500/25 transform hover:scale-105 transition-all duration-300"
                >
                  Contact Sales Team
                </CustomButton>

                <CustomButton
                  variant="outline"
                  size="lg"
                  href="/demo"
                  className="border-2 hover:bg-primary-50 dark:hover:bg-primary-900/20"
                >
                  See Our Work
                </CustomButton>
              </div>

              {/* Trust indicators */}
              <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4 text-sm text-muted-foreground">
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                  <span><strong className="text-foreground">Save 15+ hours</strong> weekly on website issues</span>
                </div>
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                  <span><strong className="text-foreground">67% higher</strong> mobile conversion rates</span>
                </div>
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-purple-500 rounded-full mr-3"></div>
                  <span><strong className="text-foreground">£0</strong> monthly maintenance costs</span>
                </div>
              </div>
            </div>

            {/* Right Column - Visual */}
            <div className="relative">
              <div className="relative bg-gradient-to-br from-primary-50 to-accent-50 dark:from-primary-950/50 dark:to-accent-950/50 rounded-3xl p-8 border border-primary-200/50 dark:border-primary-800/50 shadow-2xl">
                {/* Performance Comparison */}
                <div className="space-y-6">
                  <div className="text-center mb-8">
                    <h3 className="text-2xl font-bold text-foreground mb-2">Your Business Impact: Before vs After</h3>
                    <p className="text-muted-foreground">Real results from UK businesses like yours</p>
                  </div>

                  {/* WordPress */}
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.6 }}
                    className="bg-red-50 dark:bg-red-950/30 rounded-2xl p-6 border border-red-200 dark:border-red-800"
                  >
                    <div className="flex items-center mb-4">
                      <div className="w-3 h-3 bg-red-500 rounded-full mr-3"></div>
                      <span className="font-semibold text-red-800 dark:text-red-300">Your Current WordPress Reality</span>
                    </div>
                    <div className="space-y-2 text-sm text-red-700 dark:text-red-400">
                      <div>� Lost £5,000 client - couldn't book on mobile</div>
                      <div>� Emergency calls when site crashes during peak hours</div>
                      <div>⏰ 3+ hours weekly fixing plugin conflicts</div>
                      <div>📉 Losing Google rankings due to slow loading</div>
                    </div>
                  </motion.div>

                  {/* Next.js */}
                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.6, delay: 0.2 }}
                    className="bg-green-50 dark:bg-green-950/30 rounded-2xl p-6 border border-green-200 dark:border-green-800"
                  >
                    <div className="flex items-center mb-4">
                      <div className="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
                      <span className="font-semibold text-green-800 dark:text-green-300">Your New Business Reality</span>
                    </div>
                    <div className="space-y-2 text-sm text-green-700 dark:text-green-400">
                      <div>💰 30% more mobile bookings from day one</div>
                      <div>� Sleep peacefully - your website never breaks</div>
                      <div>🏠 Home by 6pm - zero website maintenance</div>
                      <div>� Top 3 Google rankings in your area</div>
                    </div>
                  </motion.div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Problems Section */}
      <section className="py-24 bg-gradient-to-b from-background to-muted/30">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-red-100 text-red-800 text-sm font-semibold tracking-wide mb-8 dark:bg-red-900/30 dark:text-red-400 border border-red-200/50 dark:border-red-800/50">
              The WordPress Problem
            </div>
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-foreground mb-6 leading-tight">
              These WordPress nightmares are{' '}
              <span className="text-red-600 dark:text-red-400">costing you money</span>
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              <strong className="text-foreground">Sound familiar?</strong> You're not alone. Every week, UK business owners tell us the same stories about WordPress disasters that cost them customers, sleep, and thousands in lost revenue.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 lg:gap-12">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="bg-background rounded-3xl p-8 shadow-lg border border-primary-200/20 dark:border-primary-800/20 hover:shadow-xl transition-all duration-300 h-full group"
            >
              <div className="flex items-start gap-6">
                <div className="flex-shrink-0 w-16 h-16 bg-gradient-to-br from-red-100 to-red-200 dark:from-red-900/30 dark:to-red-800/30 rounded-2xl flex items-center justify-center text-red-600 dark:text-red-400 group-hover:scale-110 transition-transform duration-300">
                  <Phone className="w-8 h-8" />
                </div>
                <div className="flex-1">
                  <h3 className="text-xl font-bold text-foreground mb-4">The 9pm Emergency Call</h3>
                  <p className="text-muted-foreground mb-4 leading-relaxed">
                    "Your website's down!" It's 9pm on a Friday. You're with family, but now you're frantically calling your web developer who isn't answering. Your biggest client can't access your booking system, and you're losing weekend appointments.
                  </p>
                  <div className="inline-flex items-center px-4 py-2 bg-red-50 dark:bg-red-950/30 rounded-full border border-red-200 dark:border-red-800">
                    <PoundSterling className="w-4 h-4 text-red-600 dark:text-red-400 mr-2" />
                    <span className="text-sm font-medium text-red-700 dark:text-red-300">
                      Average cost: £2,400 in lost weekend bookings
                    </span>
                  </div>
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
              className="bg-background rounded-3xl p-8 shadow-lg border border-primary-200/20 dark:border-primary-800/20 hover:shadow-xl transition-all duration-300 h-full group"
            >
              <div className="flex items-start gap-6">
                <div className="flex-shrink-0 w-16 h-16 bg-gradient-to-br from-red-100 to-red-200 dark:from-red-900/30 dark:to-red-800/30 rounded-2xl flex items-center justify-center text-red-600 dark:text-red-400 group-hover:scale-110 transition-transform duration-300">
                  <Smartphone className="w-8 h-8" />
                </div>
                <div className="flex-1">
                  <h3 className="text-xl font-bold text-foreground mb-4">The £5,000 Mobile Disaster</h3>
                  <p className="text-muted-foreground mb-4 leading-relaxed">
                    Your biggest client tried to book a £5,000 project on their phone. Your booking form wouldn't load, buttons were cut off, and they couldn't scroll properly. They went to your competitor instead. You found out three weeks later.
                  </p>
                  <div className="inline-flex items-center px-4 py-2 bg-red-50 dark:bg-red-950/30 rounded-full border border-red-200 dark:border-red-800">
                    <Target className="w-4 h-4 text-red-600 dark:text-red-400 mr-2" />
                    <span className="text-sm font-medium text-red-700 dark:text-red-300">
                      78% of your customers browse on mobile first
                    </span>
                  </div>
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
              className="bg-background rounded-3xl p-8 shadow-lg border border-primary-200/20 dark:border-primary-800/20 hover:shadow-xl transition-all duration-300 h-full group"
            >
              <div className="flex items-start gap-6">
                <div className="flex-shrink-0 w-16 h-16 bg-gradient-to-br from-red-100 to-red-200 dark:from-red-900/30 dark:to-red-800/30 rounded-2xl flex items-center justify-center text-red-600 dark:text-red-400 group-hover:scale-110 transition-transform duration-300">
                  <Search className="w-8 h-8" />
                </div>
                <div className="flex-1">
                  <h3 className="text-xl font-bold text-foreground mb-4">Invisible on Google</h3>
                  <p className="text-muted-foreground mb-4 leading-relaxed">
                    You used to be on page 1 for "your service + your city." Now you're on page 3 because Google hates slow websites. Your competitor with the faster site is getting all the calls you used to get.
                  </p>
                  <div className="inline-flex items-center px-4 py-2 bg-red-50 dark:bg-red-950/30 rounded-full border border-red-200 dark:border-red-800">
                    <TrendingUp className="w-4 h-4 text-red-600 dark:text-red-400 mr-2" />
                    <span className="text-sm font-medium text-red-700 dark:text-red-300">
                      Slow sites lose 67% of potential Google traffic
                    </span>
                  </div>
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              viewport={{ once: true }}
              className="bg-background rounded-3xl p-8 shadow-lg border border-primary-200/20 dark:border-primary-800/20 hover:shadow-xl transition-all duration-300 h-full group"
            >
              <div className="flex items-start gap-6">
                <div className="flex-shrink-0 w-16 h-16 bg-gradient-to-br from-red-100 to-red-200 dark:from-red-900/30 dark:to-red-800/30 rounded-2xl flex items-center justify-center text-red-600 dark:text-red-400 group-hover:scale-110 transition-transform duration-300">
                  <Timer className="w-8 h-8" />
                </div>
                <div className="flex-1">
                  <h3 className="text-xl font-bold text-foreground mb-4">The Weekend Website Warrior</h3>
                  <p className="text-muted-foreground mb-4 leading-relaxed">
                    Every Saturday morning: "The contact form isn't working," "The booking page is broken," "I can't update the prices." You spend your weekends being an unpaid IT support person instead of enjoying time with family.
                  </p>
                  <div className="inline-flex items-center px-4 py-2 bg-red-50 dark:bg-red-950/30 rounded-full border border-red-200 dark:border-red-800">
                    <Clock className="w-4 h-4 text-red-600 dark:text-red-400 mr-2" />
                    <span className="text-sm font-medium text-red-700 dark:text-red-300">
                      Average: 15+ hours monthly on website issues
                    </span>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>

          {/* Emotional Quote */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
            className="text-center mt-16"
          >
            <div className="bg-gradient-to-br from-primary-50 to-accent-50 dark:from-primary-950/50 dark:to-accent-950/50 rounded-3xl p-12 max-w-4xl mx-auto border border-primary-200/50 dark:border-primary-800/50 shadow-xl">
              <div className="text-3xl mb-6">😤</div>
              <blockquote className="text-2xl font-medium text-foreground mb-6 leading-relaxed italic">
                "I was losing sleep over my website. Every week something broke. I felt like I was running an IT company instead of a business.
                The final straw was when my biggest client couldn't book because my site crashed during their lunch break."
              </blockquote>
              <cite className="text-muted-foreground font-medium">— Sarah, Restaurant Owner, Manchester</cite>
              <div className="mt-4 text-sm text-muted-foreground">
                <strong className="text-foreground">Sarah's new Next.js site:</strong> Zero downtime in 8 months, 45% more mobile bookings
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Solutions Section */}
      <section className="py-24 bg-gradient-to-b from-muted/30 to-background">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-green-100 text-green-800 text-sm font-semibold tracking-wide mb-8 dark:bg-green-900/30 dark:text-green-400 border border-green-200/50 dark:border-green-800/50">
              The Next.js Solution
            </div>
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-foreground mb-6 leading-tight">
              Imagine never worrying about your{' '}
              <span className="text-primary-700 dark:text-primary-400">website again</span>
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              <strong className="text-foreground">What if your website just worked?</strong> No more emergency calls, no more lost customers, no more weekend maintenance.
              Just a beautiful, fast website that brings you business while you sleep.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="bg-background rounded-3xl p-8 border border-border shadow-lg hover:shadow-xl transition-all duration-300 group"
            >
              <div className="w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                <PoundSterling className="w-8 h-8" />
              </div>
              <h3 className="text-xl font-bold text-foreground mb-4">Turn Speed Into Sales</h3>
              <p className="text-muted-foreground mb-6 leading-relaxed">
                When your site loads in under 1 second, customers don't have time to change their mind. They book, they buy, they stay.
                Every second you save equals more money in your pocket.
              </p>
              <div className="bg-green-50 dark:bg-green-950/30 rounded-xl p-4 border border-green-200 dark:border-green-800">
                <div className="text-green-800 dark:text-green-300 font-semibold text-sm">Real Result:</div>
                <div className="text-green-600 dark:text-green-400 font-bold">30% more conversions from faster loading</div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
              className="bg-background rounded-3xl p-8 border border-border shadow-lg hover:shadow-xl transition-all duration-300 group"
            >
              <div className="w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                <Smartphone className="w-8 h-8" />
              </div>
              <h3 className="text-xl font-bold text-foreground mb-4">Never Lose Another Mobile Customer</h3>
              <p className="text-muted-foreground mb-6 leading-relaxed">
                78% of your customers browse on mobile first. Your new site will look perfect and work flawlessly on every device.
                No more "sorry, our website doesn't work on phones" embarrassment.
              </p>
              <div className="bg-green-50 dark:bg-green-950/30 rounded-xl p-4 border border-green-200 dark:border-green-800">
                <div className="text-green-800 dark:text-green-300 font-semibold text-sm">Real Result:</div>
                <div className="text-green-600 dark:text-green-400 font-bold">67% increase in mobile bookings</div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
              className="bg-background rounded-3xl p-8 border border-border shadow-lg hover:shadow-xl transition-all duration-300 group"
            >
              <div className="w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                <Shield className="w-8 h-8" />
              </div>
              <h3 className="text-xl font-bold text-foreground mb-4">Sleep Peacefully Again</h3>
              <p className="text-muted-foreground mb-6 leading-relaxed">
                No more 9pm emergency calls. No more weekend website fixes. No more security scares.
                Your website becomes the most reliable part of your business - it just works, 24/7.
              </p>
              <div className="bg-green-50 dark:bg-green-950/30 rounded-xl p-4 border border-green-200 dark:border-green-800">
                <div className="text-green-800 dark:text-green-300 font-semibold text-sm">Real Result:</div>
                <div className="text-green-600 dark:text-green-400 font-bold">Zero downtime, zero maintenance stress</div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              viewport={{ once: true }}
              className="bg-background rounded-3xl p-8 border border-border shadow-lg hover:shadow-xl transition-all duration-300 group"
            >
              <div className="w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                <TrendingUp className="w-8 h-8" />
              </div>
              <h3 className="text-xl font-bold text-foreground mb-4">Reclaim Your Weekends</h3>
              <p className="text-muted-foreground mb-6 leading-relaxed">
                Spend Saturday mornings with your family, not fixing your website. Your new site handles bookings,
                inquiries, and payments automatically while you focus on what you love.
              </p>
              <div className="bg-green-50 dark:bg-green-950/30 rounded-xl p-4 border border-green-200 dark:border-green-800">
                <div className="text-green-800 dark:text-green-300 font-semibold text-sm">Real Result:</div>
                <div className="text-green-600 dark:text-green-400 font-bold">Save 15+ hours weekly on website issues</div>
              </div>
            </motion.div>
          </div>

          {/* Enhanced Features Section */}
          <div className="mt-16">
            <div className="text-center mb-12">
              <h3 className="text-2xl font-bold text-foreground mb-4">Why Next.js Websites Outperform WordPress</h3>
              <p className="text-muted-foreground max-w-2xl mx-auto">The technical advantages that translate to real business results</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <motion.div
                initial={{ opacity: 0, x: -30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
                className="group"
              >
                <div className="relative bg-gradient-to-br from-blue-50 to-primary-50 dark:from-blue-950/30 dark:to-primary-950/30 rounded-3xl p-8 border border-blue-200/50 dark:border-blue-800/50 hover:shadow-xl transition-all duration-300 overflow-hidden">
                  <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-200/20 to-primary-200/20 rounded-full blur-2xl transform translate-x-16 -translate-y-16"></div>
                  <div className="relative z-10">
                    <div className="flex items-center gap-4 mb-6">
                      <div className="w-14 h-14 bg-gradient-to-br from-blue-500 to-primary-600 rounded-2xl flex items-center justify-center text-white group-hover:scale-110 transition-transform duration-300">
                        <Code className="w-7 h-7" />
                      </div>
                      <div>
                        <h4 className="text-lg font-bold text-foreground">Enterprise-Grade Technology</h4>
                        <p className="text-sm text-blue-600 dark:text-blue-400 font-medium">Same tech as Netflix & TikTok</p>
                      </div>
                    </div>
                    <p className="text-muted-foreground leading-relaxed">
                      Built with Next.js - the bulletproof technology that handles Netflix's 230 million users and TikTok's billions of videos.
                      <strong className="text-foreground"> Your website runs on enterprise infrastructure that never fails.</strong>
                    </p>
                  </div>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
                viewport={{ once: true }}
                className="group"
              >
                <div className="relative bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-950/30 dark:to-emerald-950/30 rounded-3xl p-8 border border-green-200/50 dark:border-green-800/50 hover:shadow-xl transition-all duration-300 overflow-hidden">
                  <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-green-200/20 to-emerald-200/20 rounded-full blur-2xl transform translate-x-16 -translate-y-16"></div>
                  <div className="relative z-10">
                    <div className="flex items-center gap-4 mb-6">
                      <div className="w-14 h-14 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center text-white group-hover:scale-110 transition-transform duration-300">
                        <Gauge className="w-7 h-7" />
                      </div>
                      <div>
                        <h4 className="text-lg font-bold text-foreground">Google Loves Your Site</h4>
                        <p className="text-sm text-green-600 dark:text-green-400 font-medium">Perfect 100/100 PageSpeed scores</p>
                      </div>
                    </div>
                    <p className="text-muted-foreground leading-relaxed">
                      Perfect performance scores mean Google ranks you higher than your slow competitors.
                      <strong className="text-foreground"> Watch your search rankings climb within weeks of launch.</strong>
                    </p>
                  </div>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: -30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                viewport={{ once: true }}
                className="group"
              >
                <div className="relative bg-gradient-to-br from-purple-50 to-violet-50 dark:from-purple-950/30 dark:to-violet-950/30 rounded-3xl p-8 border border-purple-200/50 dark:border-purple-800/50 hover:shadow-xl transition-all duration-300 overflow-hidden">
                  <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-purple-200/20 to-violet-200/20 rounded-full blur-2xl transform translate-x-16 -translate-y-16"></div>
                  <div className="relative z-10">
                    <div className="flex items-center gap-4 mb-6">
                      <div className="w-14 h-14 bg-gradient-to-br from-purple-500 to-violet-600 rounded-2xl flex items-center justify-center text-white group-hover:scale-110 transition-transform duration-300">
                        <Search className="w-7 h-7" />
                      </div>
                      <div>
                        <h4 className="text-lg font-bold text-foreground">Dominate Local Search</h4>
                        <p className="text-sm text-purple-600 dark:text-purple-400 font-medium">Built-in SEO optimization</p>
                      </div>
                    </div>
                    <p className="text-muted-foreground leading-relaxed">
                      Advanced SEO gets you found when customers search "[your service] near me."
                      <strong className="text-foreground"> Be the first result they see, not your competitor.</strong>
                    </p>
                  </div>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                viewport={{ once: true }}
                className="group"
              >
                <div className="relative bg-gradient-to-br from-orange-50 to-amber-50 dark:from-orange-950/30 dark:to-amber-950/30 rounded-3xl p-8 border border-orange-200/50 dark:border-orange-800/50 hover:shadow-xl transition-all duration-300 overflow-hidden">
                  <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-orange-200/20 to-amber-200/20 rounded-full blur-2xl transform translate-x-16 -translate-y-16"></div>
                  <div className="relative z-10">
                    <div className="flex items-center gap-4 mb-6">
                      <div className="w-14 h-14 bg-gradient-to-br from-orange-500 to-amber-600 rounded-2xl flex items-center justify-center text-white group-hover:scale-110 transition-transform duration-300">
                        <CheckCircle className="w-7 h-7" />
                      </div>
                      <div>
                        <h4 className="text-lg font-bold text-foreground">Set It and Forget It</h4>
                        <p className="text-sm text-orange-600 dark:text-orange-400 font-medium">Zero maintenance required</p>
                      </div>
                    </div>
                    <p className="text-muted-foreground leading-relaxed">
                      No more "your website needs updating" emails. No security scares. No broken plugins.
                      <strong className="text-foreground"> Your website becomes the most reliable part of your business.</strong>
                    </p>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="py-24 bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800 relative overflow-hidden">
        <div className="absolute inset-0 opacity-20">
          <div className="w-full h-full bg-white/5 bg-[radial-gradient(circle_at_50%_50%,rgba(255,255,255,0.1),transparent)]"></div>
        </div>

        <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div>
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-6 leading-tight">
              Stop losing money to your{' '}
              <span className="relative">
                <span className="text-accent-300">broken website</span>
                <div className="absolute -bottom-2 left-0 right-0 h-3 bg-white/30 rounded-full"></div>
              </span>
            </h2>
            <p className="text-xl text-primary-100 mb-8 leading-relaxed max-w-3xl mx-auto">
              <strong className="text-white">Every day you wait, your competitors get further ahead.</strong>
              While you're fixing WordPress problems, they're winning your customers with websites that actually work.
              Let's change that.
            </p>

            {/* Enhanced Urgency Element */}
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="bg-gradient-to-r from-red-500/20 to-orange-500/20 rounded-3xl p-8 mb-12 max-w-3xl mx-auto backdrop-blur-sm border-2 border-red-400/30 shadow-2xl"
            >
              <div className="text-center">
                <div className="flex items-center justify-center gap-3 mb-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-orange-500 rounded-2xl flex items-center justify-center">
                    <Timer className="w-6 h-6 text-white" />
                  </div>
                  <span className="text-2xl font-bold text-white">Limited Availability</span>
                </div>
                <p className="text-xl text-white mb-4 leading-relaxed">
                  We only take on <strong className="text-yellow-300">3 new website projects per month</strong> to ensure exceptional quality.
                </p>
                <div className={`rounded-2xl p-4 border ${
                  availability.spotsLeft === 1
                    ? 'bg-red-500/20 border-red-300/50'
                    : availability.spotsLeft === 2
                    ? 'bg-orange-500/20 border-orange-300/50'
                    : 'bg-white/20 border-white/30'
                }`}>
                  <div className={`text-3xl font-bold mb-1 ${
                    availability.spotsLeft === 1
                      ? 'text-red-300'
                      : availability.spotsLeft === 2
                      ? 'text-orange-300'
                      : 'text-yellow-300'
                  }`}>
                    Only {availability.spotsLeft} Spot{availability.spotsLeft !== 1 ? 's' : ''} Left
                  </div>
                  <div className="text-white font-medium">for {availability.month} {availability.year}</div>
                  <div className="text-primary-100 text-sm mt-2">
                    {availability.spotsLeft === 1
                      ? 'Last chance - book now!'
                      : "Don't wait - your competitors won't"
                    }
                  </div>
                </div>
              </div>
            </motion.div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
              <CustomButton
                variant="secondary"
                size="lg"
                href="/contact"
                icon={<Phone className="w-5 h-5" />}
                iconPosition="right"
                className="bg-white text-primary-700 hover:bg-primary-50 shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300"
              >
                Get Your Website Quote
              </CustomButton>

              <CustomButton
                variant="outline"
                size="lg"
                href="/demo"
                className="border-2 border-white text-white hover:bg-white hover:text-primary-700 transition-all duration-300"
              >
                See Our Portfolio
              </CustomButton>
            </div>

            {/* Enhanced Trust Indicators - GenLogic Branding */}
            <div className="bg-white/5 backdrop-blur-sm rounded-3xl p-8 border border-white/10 max-w-4xl mx-auto">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.1 }}
                  viewport={{ once: true }}
                  className="text-center group"
                >
                  <div className="w-12 h-12 bg-gradient-to-br from-accent-400 to-accent-500 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                    <Award className="w-6 h-6 text-white" />
                  </div>
                  <div className="text-2xl font-bold text-white mb-1">100%</div>
                  <div className="text-primary-100 font-medium">Client Satisfaction</div>
                  <div className="text-primary-200 text-sm mt-1">Every project delivered perfectly</div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                  viewport={{ once: true }}
                  className="text-center group"
                >
                  <div className="w-12 h-12 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                    <Users className="w-6 h-6 text-white" />
                  </div>
                  <div className="text-2xl font-bold text-white mb-1">500+</div>
                  <div className="text-primary-100 font-medium">UK Businesses Automated</div>
                  <div className="text-primary-200 text-sm mt-1">Same proven GenLogic team</div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.3 }}
                  viewport={{ once: true }}
                  className="text-center group"
                >
                  <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                    <CheckCircle className="w-6 h-6 text-white" />
                  </div>
                  <div className="text-2xl font-bold text-white mb-1">30-Day</div>
                  <div className="text-primary-100 font-medium">Money-Back Guarantee</div>
                  <div className="text-primary-200 text-sm mt-1">Risk-free investment</div>
                </motion.div>
              </div>
            </div>

            {/* Additional Social Proof */}
            <div className="mt-8 text-center">
              <p className="text-primary-200 text-sm mb-2">
                <strong className="text-white">"Best investment I've made for my business."</strong> - David, Home Improvement Company
              </p>
              <p className="text-primary-200 text-sm">
                <strong className="text-white">"Finally, a website that works as hard as I do."</strong> - Lisa, Beauty Salon Owner
              </p>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
