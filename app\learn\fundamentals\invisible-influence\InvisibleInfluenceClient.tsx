'use client'

import { useEffect, useState } from 'react'
import { motion } from 'framer-motion'
import { 
  ArrowLeft, 
  Clock, 
  CheckCircle,
  ArrowRight,
  Brain,
  Zap,
  Eye,
  MessageSquare,
  Copy,
  Check,
  AlertTriangle,
  Lightbulb,
  Target,
  Users,
  Star,
  TrendingUp
} from 'lucide-react'
import Link from 'next/link'

const psychologicalTriggers = [
  {
    trigger: "Social Proof",
    description: "People follow what others do",
    badExample: "We're the best in the area",
    goodExample: "Join 200+ local businesses who've transformed their customer experience",
    psychology: "Reduces decision anxiety by showing others have succeeded"
  },
  {
    trigger: "Scarcity",
    description: "Limited availability increases desire",
    badExample: "Call us anytime",
    goodExample: "We only take on 3 new clients per month to ensure quality",
    psychology: "Creates urgency and increases perceived value"
  },
  {
    trigger: "Authority",
    description: "Expertise builds instant trust",
    badExample: "We know what we're doing",
    goodExample: "After helping 500+ UK businesses increase revenue by 40%...",
    psychology: "Transfers credibility and reduces risk perception"
  }
]

const languagePatterns = [
  {
    category: "Trust Builders",
    bad: "Just checking in",
    good: "I noticed something about your industry...",
    why: "Shows you're thinking about their business, not just making contact"
  },
  {
    category: "Value Creators",
    bad: "Do you need our services?",
    good: "I have an idea that could save you 10 hours per week...",
    why: "Leads with value instead of asking for something"
  },
  {
    category: "Curiosity Openers",
    bad: "How's business?",
    good: "Something's happening in your industry that most owners don't see...",
    why: "Creates intrigue and positions you as an insider"
  }
]

export default function InvisibleInfluenceClient() {
  const [copiedIndex, setCopiedIndex] = useState<string | null>(null)

  // Mark lesson as completed when user visits
  useEffect(() => {
    const markAsCompleted = () => {
      const saved = localStorage.getItem('genlogic-academy-progress')
      const completed = saved ? JSON.parse(saved) : []
      if (!completed.includes('invisible-influence')) {
        completed.push('invisible-influence')
        localStorage.setItem('genlogic-academy-progress', JSON.stringify(completed))
        
        // Dispatch custom event to notify other components
        window.dispatchEvent(new CustomEvent('academyProgressUpdate'))
      }
    }
    
    // Mark as completed after user has been on page for 30 seconds
    const timer = setTimeout(markAsCompleted, 30000)
    return () => clearTimeout(timer)
  }, [])

  const copyToClipboard = (text: string, index: string) => {
    navigator.clipboard.writeText(text)
    setCopiedIndex(index)
    setTimeout(() => setCopiedIndex(null), 2000)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-purple-50/30 to-background dark:from-background dark:via-purple-950/30 dark:to-background">
      {/* Navigation */}
      <div className="pt-40 pb-8">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-5xl mx-auto">
            <Link 
              href="/learn" 
              className="inline-flex items-center gap-2 bg-background/80 backdrop-blur-sm border border-border/50 rounded-2xl px-4 py-2 text-primary-600 hover:text-primary-700 font-medium transition-all duration-300 hover:shadow-md"
            >
              <ArrowLeft className="w-4 h-4" />
              Back to Academy
            </Link>
          </div>
        </div>
      </div>

      {/* Hero Section */}
      <section className="pb-16">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-5xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              {/* Lesson Meta */}
              <div className="flex items-center justify-center gap-6 mb-8">
                <div className="flex items-center gap-2 bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 px-4 py-2 rounded-2xl">
                  <Brain className="w-4 h-4" />
                  <span className="font-medium text-sm">Psychology</span>
                </div>
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Clock className="w-4 h-4" />
                  <span className="text-sm">14 min read</span>
                </div>
                <div className="bg-muted px-3 py-1 rounded-full text-xs font-medium text-muted-foreground">
                  Advanced Communication
                </div>
              </div>

              {/* Main Title */}
              <h1 className="text-4xl md:text-6xl font-bold text-foreground mb-6 leading-tight">
                The Invisible 
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-500 to-pink-500"> Influence:</span>
                <br />How Words Shape 
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-500 to-purple-500"> Customer Decisions</span>
              </h1>

              <p className="text-xl md:text-2xl text-muted-foreground mb-8 leading-relaxed max-w-4xl mx-auto">
                There's an invisible force that determines whether prospects trust you or ignore you. 
                <strong className="text-foreground"> It's not what you sell, it's how you say it.</strong>
                Discover the 7 psychological triggers that make prospects say yes, and why "checking in" kills more sales than any other phrase.
              </p>

              {/* Warning Stats */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-12">
                <div className="bg-gradient-to-br from-red-50 to-red-100 dark:from-red-950/30 dark:to-red-900/30 rounded-3xl p-6 border border-red-200/50 dark:border-red-800/50">
                  <div className="flex items-center gap-3 mb-3">
                    <AlertTriangle className="w-6 h-6 text-red-500" />
                    <div className="text-2xl font-bold text-red-600 dark:text-red-400">89%</div>
                  </div>
                  <div className="text-sm text-red-700 dark:text-red-300 font-medium">Of follow-ups use trust-killing language</div>
                </div>
                <div className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950/30 dark:to-green-900/30 rounded-3xl p-6 border border-green-200/50 dark:border-green-800/50">
                  <div className="flex items-center gap-3 mb-3">
                    <TrendingUp className="w-6 h-6 text-green-500" />
                    <div className="text-2xl font-bold text-green-600 dark:text-green-400">340%</div>
                  </div>
                  <div className="text-sm text-green-700 dark:text-green-300 font-medium">Higher response with psychological triggers</div>
                </div>
              </div>
            </motion.div>

            {/* What You'll Learn Card */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-950/50 dark:to-pink-950/50 rounded-3xl p-8 border border-purple-200/50 dark:border-purple-800/50 shadow-xl"
            >
              <div className="flex items-start gap-6">
                <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center flex-shrink-0">
                  <Eye className="w-8 h-8 text-white" />
                </div>
                <div className="flex-1">
                  <h3 className="text-2xl font-bold text-foreground mb-4">What You'll Master in This Lesson</h3>
                  <div className="grid md:grid-cols-2 gap-4">
                    <div className="space-y-3">
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-foreground">The 7 psychological triggers that make prospects say yes</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-foreground">Why "checking in" kills sales (and what to say instead)</span>
                      </div>
                    </div>
                    <div className="space-y-3">
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-foreground">Language patterns that build trust vs. create resistance</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-foreground">Copy-paste scripts that feel personal at scale</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="pb-16">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-4xl mx-auto space-y-12">

            {/* The 7 Psychological Triggers */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="bg-background/80 backdrop-blur-sm rounded-3xl p-8 border border-border/50 shadow-xl"
            >
              <div className="flex items-start gap-6 mb-8">
                <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl flex items-center justify-center flex-shrink-0">
                  <Zap className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h2 className="text-3xl font-bold text-foreground mb-4">The 7 Psychological Triggers That Make Prospects Say Yes</h2>
                  <p className="text-lg text-muted-foreground leading-relaxed">
                    Successful businesses understand that influence isn't about manipulation—it's about communicating in a way that naturally aligns with how the human brain makes decisions.
                  </p>
                </div>
              </div>

              <div className="space-y-6">
                {psychologicalTriggers.map((trigger, index) => (
                  <div key={index} className="bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-950/30 dark:to-pink-950/30 rounded-2xl p-6 border border-purple-200/50 dark:border-purple-800/50">
                    <h3 className="text-xl font-bold text-purple-700 dark:text-purple-300 mb-2">{trigger.trigger}</h3>
                    <p className="text-purple-600 dark:text-purple-400 mb-4">{trigger.description}</p>

                    <div className="grid md:grid-cols-2 gap-4 mb-4">
                      <div className="bg-red-50 dark:bg-red-950/30 rounded-xl p-4 border border-red-200/50 dark:border-red-800/50">
                        <div className="flex items-center gap-2 mb-2">
                          <AlertTriangle className="w-4 h-4 text-red-500" />
                          <span className="text-sm font-medium text-red-700 dark:text-red-300">Kills Trust</span>
                        </div>
                        <p className="text-sm text-red-600 dark:text-red-400">"{trigger.badExample}"</p>
                      </div>
                      <div className="bg-green-50 dark:bg-green-950/30 rounded-xl p-4 border border-green-200/50 dark:border-green-800/50">
                        <div className="flex items-center gap-2 mb-2">
                          <CheckCircle className="w-4 h-4 text-green-500" />
                          <span className="text-sm font-medium text-green-700 dark:text-green-300">Builds Trust</span>
                        </div>
                        <p className="text-sm text-green-600 dark:text-green-400">"{trigger.goodExample}"</p>
                      </div>
                    </div>

                    <div className="bg-blue-50 dark:bg-blue-950/30 rounded-xl p-4 border border-blue-200/50 dark:border-blue-800/50">
                      <div className="flex items-center gap-2 mb-2">
                        <Brain className="w-4 h-4 text-blue-500" />
                        <span className="text-sm font-medium text-blue-700 dark:text-blue-300">Psychology</span>
                      </div>
                      <p className="text-sm text-blue-600 dark:text-blue-400">{trigger.psychology}</p>
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>

            {/* Language Patterns */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="bg-background/80 backdrop-blur-sm rounded-3xl p-8 border border-border/50 shadow-xl"
            >
              <div className="flex items-start gap-6 mb-8">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-xl flex items-center justify-center flex-shrink-0">
                  <MessageSquare className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h2 className="text-3xl font-bold text-foreground mb-4">Language Patterns That Build Trust vs. Create Resistance</h2>
                  <p className="text-lg text-muted-foreground leading-relaxed">
                    The difference between a prospect who responds and one who ignores you often comes down to a few key words.
                  </p>
                </div>
              </div>

              <div className="space-y-6">
                {languagePatterns.map((pattern, index) => (
                  <div key={index} className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/30 dark:to-purple-950/30 rounded-2xl p-6 border border-blue-200/50 dark:border-blue-800/50">
                    <h3 className="text-xl font-bold text-blue-700 dark:text-blue-300 mb-4">{pattern.category}</h3>

                    <div className="grid md:grid-cols-2 gap-4 mb-4">
                      <div className="bg-red-50 dark:bg-red-950/30 rounded-xl p-4 border border-red-200/50 dark:border-red-800/50">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm font-medium text-red-700 dark:text-red-300">❌ Resistance Creator</span>
                        </div>
                        <p className="text-red-600 dark:text-red-400 font-medium">"{pattern.bad}"</p>
                      </div>
                      <div className="bg-green-50 dark:bg-green-950/30 rounded-xl p-4 border border-green-200/50 dark:border-green-800/50">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm font-medium text-green-700 dark:text-green-300">✅ Trust Builder</span>
                          <button
                            onClick={() => copyToClipboard(pattern.good, `pattern-${index}`)}
                            className="text-green-600 hover:text-green-700 transition-colors"
                          >
                            {copiedIndex === `pattern-${index}` ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                          </button>
                        </div>
                        <p className="text-green-600 dark:text-green-400 font-medium">"{pattern.good}"</p>
                      </div>
                    </div>

                    <div className="bg-yellow-50 dark:bg-yellow-950/30 rounded-xl p-4 border border-yellow-200/50 dark:border-yellow-800/50">
                      <div className="flex items-center gap-2 mb-2">
                        <Lightbulb className="w-4 h-4 text-yellow-500" />
                        <span className="text-sm font-medium text-yellow-700 dark:text-yellow-300">Why It Works</span>
                      </div>
                      <p className="text-sm text-yellow-600 dark:text-yellow-400">{pattern.why}</p>
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>

            {/* Real Case Study: The £234k Language Transformation */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              className="bg-background/80 backdrop-blur-sm rounded-3xl p-8 border border-border/50 shadow-xl"
            >
              <div className="flex items-start gap-6 mb-8">
                <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-blue-500 rounded-xl flex items-center justify-center flex-shrink-0">
                  <Star className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h2 className="text-3xl font-bold text-foreground mb-4">Case Study: How Premium Kitchens Generated £234k with Language Changes</h2>
                  <p className="text-lg text-muted-foreground leading-relaxed">
                    Sarah owned a kitchen design company in Leeds, but struggled with low response rates and price objections. Here's how changing 7 words in her follow-ups transformed everything.
                  </p>
                </div>
              </div>

              <div className="bg-gradient-to-r from-red-50 to-red-100 dark:from-red-950/30 dark:to-red-900/30 rounded-2xl p-6 border border-red-200/50 dark:border-red-800/50 mb-8">
                <h3 className="text-xl font-bold text-red-700 dark:text-red-300 mb-4">The Language Problem</h3>
                <div className="grid md:grid-cols-3 gap-4 mb-4">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-red-600 dark:text-red-400 mb-2">12%</div>
                    <div className="text-sm text-red-700 dark:text-red-300">Response rate to follow-ups</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-red-600 dark:text-red-400 mb-2">67%</div>
                    <div className="text-sm text-red-700 dark:text-red-300">Price objections</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-red-600 dark:text-red-400 mb-2">£89k</div>
                    <div className="text-sm text-red-700 dark:text-red-300">Lost opportunities annually</div>
                  </div>
                </div>
                <p className="text-red-600 dark:text-red-400 text-sm">
                  "I was using all the 'professional' language I thought I should use. 'Just checking in', 'following up', 'wondering if you've had a chance'.
                  Prospects either ignored me or said they were 'still thinking about it'."
                </p>
              </div>

              <div className="space-y-8">
                {/* Language Transformation Examples */}
                <div className="bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-950/30 dark:to-blue-900/30 rounded-2xl p-6 border border-blue-200/50 dark:border-blue-800/50">
                  <h4 className="text-lg font-bold text-blue-700 dark:text-blue-300 mb-4">The 7-Word Transformation</h4>

                  <div className="space-y-6">
                    <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
                      <h5 className="font-bold text-red-600 dark:text-red-400 mb-2">❌ BEFORE (Trust-Killing Language):</h5>
                      <div className="bg-red-50 dark:bg-red-900/30 rounded-lg p-3 mb-3">
                        <p className="text-red-600 dark:text-red-400 italic">
                          "Hi Sarah, just checking in to see if you've had a chance to think about the kitchen design we discussed.
                          Let me know if you have any questions or if you'd like to move forward. Thanks!"
                        </p>
                      </div>
                      <div className="text-sm text-red-600 dark:text-red-400">
                        <strong>Problems:</strong> Passive, puts pressure on prospect, sounds like every other salesperson
                      </div>
                    </div>

                    <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
                      <h5 className="font-bold text-green-600 dark:text-green-400 mb-2">✅ AFTER (Trust-Building Language):</h5>
                      <div className="bg-green-50 dark:bg-green-900/30 rounded-lg p-3 mb-3">
                        <p className="text-green-600 dark:text-green-400 italic">
                          "Hi Sarah, I was reviewing your project and realized something important about your corner space that could save you £2,000.
                          Most people miss this detail. Would you like me to show you what I discovered?"
                        </p>
                      </div>
                      <div className="text-sm text-green-600 dark:text-green-400">
                        <strong>Psychology:</strong> Creates curiosity, offers value first, positions as expert, creates urgency
                      </div>
                    </div>
                  </div>

                  <div className="bg-green-100 dark:bg-green-900/30 rounded-xl p-4 mt-6">
                    <h6 className="font-bold text-green-700 dark:text-green-300 mb-2">Immediate Results:</h6>
                    <div className="grid md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <div className="text-green-600 dark:text-green-400">✓ Response rate: 12% → 67%</div>
                        <div className="text-green-600 dark:text-green-400">✓ Price objections: 67% → 23%</div>
                      </div>
                      <div>
                        <div className="text-green-600 dark:text-green-400">✓ Conversion rate: +340%</div>
                        <div className="text-green-600 dark:text-green-400">✓ Average project value: +45%</div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* The 7 Psychological Triggers Implementation */}
                <div className="bg-gradient-to-r from-purple-50 to-purple-100 dark:from-purple-950/30 dark:to-purple-900/30 rounded-2xl p-6 border border-purple-200/50 dark:border-purple-800/50">
                  <h4 className="text-lg font-bold text-purple-700 dark:text-purple-300 mb-4">The 7 Psychological Triggers Sarah Used</h4>

                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
                        <h6 className="font-bold text-purple-600 dark:text-purple-400 mb-2">1. Authority Positioning</h6>
                        <p className="text-sm text-purple-600 dark:text-purple-400 mb-2">
                          <strong>Before:</strong> "I think this would work well"
                        </p>
                        <p className="text-sm text-purple-600 dark:text-purple-400">
                          <strong>After:</strong> "In my 12 years designing 200+ kitchens, I've discovered..."
                        </p>
                      </div>

                      <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
                        <h6 className="font-bold text-purple-600 dark:text-purple-400 mb-2">2. Scarcity Creation</h6>
                        <p className="text-sm text-purple-600 dark:text-purple-400 mb-2">
                          <strong>Before:</strong> "We can start whenever you're ready"
                        </p>
                        <p className="text-sm text-purple-600 dark:text-purple-400">
                          <strong>After:</strong> "I have one slot left in March before my 6-week waiting list"
                        </p>
                      </div>

                      <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
                        <h6 className="font-bold text-purple-600 dark:text-purple-400 mb-2">3. Social Proof</h6>
                        <p className="text-sm text-purple-600 dark:text-purple-400 mb-2">
                          <strong>Before:</strong> "We do great work"
                        </p>
                        <p className="text-sm text-purple-600 dark:text-purple-400">
                          <strong>After:</strong> "Like the Johnsons in Harrogate, who saved £3k with this approach"
                        </p>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
                        <h6 className="font-bold text-purple-600 dark:text-purple-400 mb-2">4. Loss Aversion</h6>
                        <p className="text-sm text-purple-600 dark:text-purple-400 mb-2">
                          <strong>Before:</strong> "This will add value to your home"
                        </p>
                        <p className="text-sm text-purple-600 dark:text-purple-400">
                          <strong>After:</strong> "Without this, you'll lose £8k in home value and regret it daily"
                        </p>
                      </div>

                      <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
                        <h6 className="font-bold text-purple-600 dark:text-purple-400 mb-2">5. Curiosity Gap</h6>
                        <p className="text-sm text-purple-600 dark:text-purple-400 mb-2">
                          <strong>Before:</strong> "Here's our proposal"
                        </p>
                        <p className="text-sm text-purple-600 dark:text-purple-400">
                          <strong>After:</strong> "I discovered something about your space that changes everything"
                        </p>
                      </div>

                      <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
                        <h6 className="font-bold text-purple-600 dark:text-purple-400 mb-2">6. Reciprocity</h6>
                        <p className="text-sm text-purple-600 dark:text-purple-400 mb-2">
                          <strong>Before:</strong> "Let me know if you're interested"
                        </p>
                        <p className="text-sm text-purple-600 dark:text-purple-400">
                          <strong>After:</strong> "I've prepared a cost-saving analysis just for you"
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-950/30 dark:to-blue-950/30 rounded-2xl p-6 border border-green-200/50 dark:border-green-800/50 mt-8">
                <h4 className="text-xl font-bold text-green-700 dark:text-green-300 mb-4">Total Transformation: £234,000 Additional Revenue</h4>
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h5 className="font-bold text-green-600 dark:text-green-400 mb-2">Language Impact:</h5>
                    <ul className="space-y-1 text-sm text-green-600 dark:text-green-400">
                      <li>• Response rate: 12% → 67% (+458%)</li>
                      <li>• Conversion rate: 23% → 78% (+340%)</li>
                      <li>• Average project value: £8.5k → £12.3k</li>
                      <li><strong>• Additional annual revenue: £234k</strong></li>
                    </ul>
                  </div>
                  <div>
                    <h5 className="font-bold text-blue-600 dark:text-blue-400 mb-2">Business Impact:</h5>
                    <ul className="space-y-1 text-sm text-blue-600 dark:text-blue-400">
                      <li>• Price objections: 67% → 23%</li>
                      <li>• Sales cycle: 6 weeks → 3 weeks</li>
                      <li>• Referral rate: +156%</li>
                      <li><strong>• Sarah's confidence: Transformed</strong></li>
                    </ul>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Language Impact Calculator */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
              className="bg-background/80 backdrop-blur-sm rounded-3xl p-8 border border-border/50 shadow-xl"
            >
              <div className="flex items-start gap-6 mb-8">
                <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl flex items-center justify-center flex-shrink-0">
                  <Target className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h2 className="text-3xl font-bold text-foreground mb-4">Language Impact Calculator</h2>
                  <p className="text-lg text-muted-foreground leading-relaxed">
                    Calculate exactly how much revenue you could gain by implementing psychological triggers in your communication.
                  </p>
                </div>
              </div>

              <div className="bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-950/30 dark:to-pink-950/30 rounded-2xl p-6 border border-purple-200/50 dark:border-purple-800/50">
                <h3 className="text-xl font-bold text-purple-700 dark:text-purple-300 mb-6">Interactive Language ROI Calculator</h3>

                <div className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
                      <h4 className="font-bold text-purple-600 dark:text-purple-400 mb-3">Current Communication Metrics</h4>
                      <div className="space-y-3 text-sm">
                        <div className="flex items-center gap-2">
                          <label className="min-w-0 flex-1">Monthly prospects contacted:</label>
                          <input
                            type="number"
                            className="w-20 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-foreground"
                            placeholder="50"
                          />
                        </div>
                        <div className="flex items-center gap-2">
                          <label className="min-w-0 flex-1">Current response rate (%):</label>
                          <input
                            type="number"
                            className="w-20 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-foreground"
                            placeholder="15"
                          />
                        </div>
                        <div className="flex items-center gap-2">
                          <label className="min-w-0 flex-1">Current conversion rate (%):</label>
                          <input
                            type="number"
                            className="w-20 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-foreground"
                            placeholder="25"
                          />
                        </div>
                        <div className="flex items-center gap-2">
                          <label className="min-w-0 flex-1">Average deal value (£):</label>
                          <input
                            type="number"
                            className="w-20 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-foreground"
                            placeholder="2500"
                          />
                        </div>
                      </div>
                    </div>

                    <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
                      <h4 className="font-bold text-purple-600 dark:text-purple-400 mb-3">With Psychological Triggers</h4>
                      <div className="space-y-3 text-sm">
                        <div className="flex items-center gap-2">
                          <label className="min-w-0 flex-1">Improved response rate (%):</label>
                          <input
                            type="number"
                            className="w-20 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-foreground"
                            placeholder="45"
                          />
                        </div>
                        <div className="flex items-center gap-2">
                          <label className="min-w-0 flex-1">Improved conversion rate (%):</label>
                          <input
                            type="number"
                            className="w-20 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-foreground"
                            placeholder="65"
                          />
                        </div>
                        <div className="flex items-center gap-2">
                          <label className="min-w-0 flex-1">Improved deal value (£):</label>
                          <input
                            type="number"
                            className="w-20 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-foreground"
                            placeholder="3200"
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-green-100 to-blue-100 dark:from-green-900/30 dark:to-blue-900/30 rounded-xl p-6 text-center border-2 border-green-300 dark:border-green-700">
                    <h4 className="text-2xl font-bold text-green-700 dark:text-green-300 mb-3">Annual Revenue Impact</h4>
                    <div className="grid md:grid-cols-3 gap-4 mb-4">
                      <div>
                        <div className="text-lg font-bold text-blue-600 dark:text-blue-400">Current Annual Revenue</div>
                        <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">£46,875</div>
                        <div className="text-xs text-blue-500">Based on current metrics</div>
                      </div>
                      <div>
                        <div className="text-lg font-bold text-green-600 dark:text-green-400">Improved Annual Revenue</div>
                        <div className="text-2xl font-bold text-green-600 dark:text-green-400">£281,250</div>
                        <div className="text-xs text-green-500">With psychological triggers</div>
                      </div>
                      <div>
                        <div className="text-lg font-bold text-purple-600 dark:text-purple-400">Additional Revenue</div>
                        <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">£234,375</div>
                        <div className="text-xs text-purple-500">Annual increase</div>
                      </div>
                    </div>
                    <div className="border-t pt-4">
                      <div className="text-3xl font-bold text-green-600 dark:text-green-400 mb-2">Revenue Increase</div>
                      <div className="text-5xl font-bold text-green-600 dark:text-green-400 mb-2">+500%</div>
                      <div className="text-lg font-bold text-green-600 dark:text-green-400">Just from changing your language</div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Implementation Guide */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.7 }}
              className="bg-background/80 backdrop-blur-sm rounded-3xl p-8 border border-border/50 shadow-xl"
            >
              <div className="flex items-start gap-6 mb-8">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-green-500 rounded-xl flex items-center justify-center flex-shrink-0">
                  <Lightbulb className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h2 className="text-3xl font-bold text-foreground mb-4">Your 30-Day Language Transformation Plan</h2>
                  <p className="text-lg text-muted-foreground leading-relaxed">
                    Follow this proven roadmap to implement psychological triggers and transform your communication within 30 days.
                  </p>
                </div>
              </div>

              <div className="space-y-6">
                <div className="bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-950/30 dark:to-blue-900/30 rounded-2xl p-6 border border-blue-200/50 dark:border-blue-800/50">
                  <h3 className="text-xl font-bold text-blue-700 dark:text-blue-300 mb-4">Week 1: Audit & Replace Trust-Killers</h3>
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-bold text-blue-600 dark:text-blue-400 mb-2">Day 1-3: Language Audit</h4>
                      <ul className="space-y-1 text-sm text-blue-600 dark:text-blue-400">
                        <li>• Review last 20 follow-up messages</li>
                        <li>• Identify trust-killing phrases</li>
                        <li>• Count "just checking in" usage</li>
                        <li>• Note response rates by message type</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-bold text-blue-600 dark:text-blue-400 mb-2">Day 4-7: Replace & Test</h4>
                      <ul className="space-y-1 text-sm text-blue-600 dark:text-blue-400">
                        <li>• Replace with curiosity-driven openers</li>
                        <li>• Add specific value propositions</li>
                        <li>• Test 3 different approaches</li>
                        <li>• Track response rate changes</li>
                      </ul>
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-green-50 to-green-100 dark:from-green-950/30 dark:to-green-900/30 rounded-2xl p-6 border border-green-200/50 dark:border-green-800/50">
                  <h3 className="text-xl font-bold text-green-700 dark:text-green-300 mb-4">Week 2-3: Implement Psychological Triggers</h3>
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-bold text-green-600 dark:text-green-400 mb-2">Authority & Social Proof</h4>
                      <ul className="space-y-1 text-sm text-green-600 dark:text-green-400">
                        <li>• Add credentials to email signatures</li>
                        <li>• Include specific client results</li>
                        <li>• Reference industry experience</li>
                        <li>• Use case study examples</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-bold text-green-600 dark:text-green-400 mb-2">Scarcity & Urgency</h4>
                      <ul className="space-y-1 text-sm text-green-600 dark:text-green-400">
                        <li>• Mention limited availability</li>
                        <li>• Create deadline-driven offers</li>
                        <li>• Reference waiting lists</li>
                        <li>• Highlight time-sensitive benefits</li>
                      </ul>
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-purple-50 to-purple-100 dark:from-purple-950/30 dark:to-purple-900/30 rounded-2xl p-6 border border-purple-200/50 dark:border-purple-800/50">
                  <h3 className="text-xl font-bold text-purple-700 dark:text-purple-300 mb-4">Week 4: Optimize & Scale</h3>
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-bold text-purple-600 dark:text-purple-400 mb-2">Performance Analysis</h4>
                      <ul className="space-y-1 text-sm text-purple-600 dark:text-purple-400">
                        <li>• Compare response rates by trigger type</li>
                        <li>• Identify highest-converting messages</li>
                        <li>• Calculate ROI improvement</li>
                        <li>• Document winning formulas</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-bold text-purple-600 dark:text-purple-400 mb-2">Scale Implementation</h4>
                      <ul className="space-y-1 text-sm text-purple-600 dark:text-purple-400">
                        <li>• Create message templates</li>
                        <li>• Train team on new language</li>
                        <li>• Automate winning sequences</li>
                        <li>• Set up performance tracking</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-950/30 dark:to-blue-950/30 rounded-2xl p-6 border border-green-200/50 dark:border-green-800/50 mt-8">
                <h3 className="text-xl font-bold text-green-700 dark:text-green-300 mb-4">30-Day Transformation Guarantee</h3>
                <div className="grid md:grid-cols-3 gap-4 text-center">
                  <div>
                    <div className="text-3xl font-bold text-green-600 dark:text-green-400 mb-2">Week 1</div>
                    <div className="text-sm text-green-600 dark:text-green-400">50% better response rates</div>
                  </div>
                  <div>
                    <div className="text-3xl font-bold text-green-600 dark:text-green-400 mb-2">Week 2-3</div>
                    <div className="text-sm text-green-600 dark:text-green-400">200% conversion improvement</div>
                  </div>
                  <div>
                    <div className="text-3xl font-bold text-green-600 dark:text-green-400 mb-2">Week 4</div>
                    <div className="text-sm text-green-600 dark:text-green-400">340% overall language impact</div>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Call to Action */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.8 }}
              className="bg-gradient-to-r from-purple-500 to-pink-500 rounded-3xl p-8 text-white text-center"
            >
              <h2 className="text-3xl font-bold mb-4">Something's Changing</h2>
              <p className="text-xl mb-6 opacity-90">
                Smart business owners are quietly discovering what successful companies already know about psychological influence. The shift is happening whether you participate or not.
              </p>
              <Link
                href="/demo"
                className="inline-flex items-center gap-2 bg-white text-purple-600 px-8 py-4 rounded-2xl font-bold text-lg hover:bg-gray-100 transition-all duration-300 shadow-lg hover:shadow-xl"
              >
                See What They Know
                <ArrowRight className="w-5 h-5" />
              </Link>
              <p className="text-sm mt-4 opacity-75">
                Join the business owners who've already discovered the language revolution
              </p>
            </motion.div>

            {/* Next Lesson Navigation */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.9 }}
              className="bg-background rounded-3xl border border-border shadow-lg p-8"
            >
              <h3 className="text-xl font-bold text-foreground mb-6 text-center">Continue Your Learning Journey</h3>

              <div className="grid md:grid-cols-2 gap-6">
                <Link
                  href="/learn/fundamentals/multi-channel-revolution"
                  className="group bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/30 dark:to-blue-900/30 rounded-2xl p-6 border border-blue-200/50 dark:border-blue-800/50 hover:shadow-lg transition-all duration-300"
                >
                  <div className="flex items-center gap-4 mb-4">
                    <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/50 rounded-xl flex items-center justify-center">
                      <ArrowRight className="w-6 h-6 text-blue-600 group-hover:translate-x-1 transition-transform" />
                    </div>
                    <div>
                      <h4 className="font-bold text-foreground">Next Lesson</h4>
                      <p className="text-sm text-muted-foreground">Lesson 8 of 15</p>
                    </div>
                  </div>
                  <h5 className="font-semibold text-foreground mb-2">The Multi-Channel Revolution</h5>
                  <p className="text-sm text-muted-foreground">
                    Why single-channel follow-up is killing your conversion rates and the multi-touch system that works
                  </p>
                </Link>

                <Link
                  href="/learn"
                  className="group bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-950/30 dark:to-gray-900/30 rounded-2xl p-6 border border-gray-200/50 dark:border-gray-800/50 hover:shadow-lg transition-all duration-300"
                >
                  <div className="flex items-center gap-4 mb-4">
                    <div className="w-12 h-12 bg-gray-100 dark:bg-gray-900/50 rounded-xl flex items-center justify-center">
                      <ArrowLeft className="w-6 h-6 text-gray-600 group-hover:-translate-x-1 transition-transform" />
                    </div>
                    <div>
                      <h4 className="font-bold text-foreground">Academy</h4>
                      <p className="text-sm text-muted-foreground">All lessons</p>
                    </div>
                  </div>
                  <h5 className="font-semibold text-foreground mb-2">Back to Academy</h5>
                  <p className="text-sm text-muted-foreground">
                    View all lessons and track your progress
                  </p>
                </Link>
              </div>
            </motion.div>
          </div>
        </div>
      </section>
    </div>
  )
}
