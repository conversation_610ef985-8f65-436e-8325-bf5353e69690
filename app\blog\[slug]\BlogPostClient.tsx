'use client'

import { useState, useEffect } from 'react'
import { usePostFavorites } from '../../../hooks/useFavorites'
import { useFavoritesContext } from '../../../contexts/FavoritesContext'
import { motion } from 'framer-motion'
import { CustomButton } from '@/components/ui/custom-button'
import {
  Calendar,
  Clock,
  User,
  ArrowLeft,
  Share2,
  BookOpen,
  Heart,
  Eye,
  MessageCircle,
  ArrowRight,
  Twitter,
  Linkedin,
  Facebook,
  Link as LinkIcon,
  CheckCircle
} from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'
import { getBlogPostBySlug, getRelatedPosts } from '../posts'
import { BlogPost } from '../types'
import BlogCard from '../../../components/blog/BlogCard'

interface Author {
  name: string
  role: string
  avatar: string
  bio: string
}



interface BlogPostClientProps {
  post: BlogPost
  relatedPosts: BlogPost[]
}

export default function BlogPostClient({ post, relatedPosts }: BlogPostClientProps) {
  const [showShareMenu, setShowShareMenu] = useState(false)
  const [copied, setCopied] = useState(false)

  // Use the new favorites hook
  const { isLiked, currentLikes, loading: likesLoading, handleLike } = usePostFavorites(post.slug)

  // Use context for views
  const { getViewCount, incrementView } = useFavoritesContext()
  const currentViews = getViewCount(post.slug) // Remove fallback to post.views to avoid conflicts

  // Handle view counting
  useEffect(() => {
    const handleViewIncrement = async () => {
      // Check if user has already viewed this post
      const viewedPosts = JSON.parse(localStorage.getItem('viewedPosts') || '[]')
      if (!viewedPosts.includes(post.slug)) {
        // Increment view count via context
        await incrementView(post.slug)

        // Mark as viewed
        localStorage.setItem('viewedPosts', JSON.stringify([...viewedPosts, post.slug]))
      }
    }

    handleViewIncrement()
  }, [post.slug, incrementView])

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    })
  }

  const handleShare = (platform: string) => {
    const url = encodeURIComponent(window.location.href)
    const title = encodeURIComponent(post.title)
    const text = encodeURIComponent(post.excerpt)

    let shareUrl = ''
    
    switch (platform) {
      case 'twitter':
        shareUrl = `https://twitter.com/intent/tweet?url=${url}&text=${title}`
        break
      case 'linkedin':
        shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${url}`
        break
      case 'facebook':
        shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${url}`
        break
      case 'copy':
        navigator.clipboard.writeText(window.location.href)
        setCopied(true)
        setTimeout(() => setCopied(false), 2000)
        return
    }

    if (shareUrl) {
      window.open(shareUrl, '_blank', 'width=600,height=400')
    }
    setShowShareMenu(false)
  }




  return (
    <div className="bg-background transition-colors duration-300">
      {/* Header */}
      <section className="relative header-spacing pb-12">
        <div className="absolute inset-0 bg-gradient-to-br from-primary-50/30 via-background to-accent-50/20 dark:from-primary-950/20 dark:via-background dark:to-accent-950/10"></div>
        
        <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: 'easeOut' }}
          >
            {/* Back to Blog */}
            <Link 
              href="/blog"
              className="inline-flex items-center text-muted-foreground hover:text-primary-600 transition-colors duration-300 mb-8"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Blog
            </Link>

            {/* Article Meta */}
            <div className="flex flex-wrap items-center gap-4 mb-6 text-sm text-muted-foreground">
              <span className="px-3 py-1 bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-400 rounded-full font-medium">
                {post.category}
              </span>
              <div className="flex items-center">
                <Calendar className="w-4 h-4 mr-1" />
                {formatDate(post.publishedAt)}
              </div>
              <div className="flex items-center">
                <Clock className="w-4 h-4 mr-1" />
                {post.readingTime}
              </div>
              <div className="flex items-center">
                <Eye className="w-4 h-4 mr-1" />
                {currentViews.toLocaleString()} views
              </div>
            </div>

            {/* Title */}
            <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-foreground mb-6 leading-[1.1]">
              {post.title}
            </h1>

            {/* Excerpt */}
            <p className="text-xl text-muted-foreground mb-8 leading-relaxed">
              {post.excerpt}
            </p>

            {/* Author and Actions */}
            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-6 pb-8 border-b border-border">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-gradient-to-br from-primary-100 to-accent-100 dark:from-primary-900 dark:to-accent-900 rounded-full flex items-center justify-center">
                  <User className="w-6 h-6 text-primary-600 dark:text-primary-400" />
                </div>
                <div>
                  <div className="font-semibold text-foreground">{post.author.name}</div>
                  <div className="text-sm text-muted-foreground">{post.author.role}</div>
                </div>
              </div>

              <div className="flex items-center gap-4">
                <button
                  onClick={handleLike}
                  disabled={likesLoading}
                  className={`flex items-center gap-2 px-4 py-2 rounded-xl border transition-all duration-300 ${
                    isLiked
                      ? 'border-red-500 bg-red-50 dark:bg-red-950/30 text-red-600 dark:text-red-400'
                      : 'border-border bg-background hover:bg-muted'
                  } ${likesLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                >
                  <Heart className={`w-4 h-4 ${isLiked ? 'fill-current' : ''}`} />
                  {likesLoading ? '...' : currentLikes}
                </button>

                <div className="relative">
                  <button
                    onClick={() => setShowShareMenu(!showShareMenu)}
                    className="flex items-center gap-2 px-4 py-2 rounded-xl border border-border bg-background hover:bg-muted transition-all duration-300"
                  >
                    <Share2 className="w-4 h-4" />
                    Share
                  </button>

                  {showShareMenu && (
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: 10 }}
                      className="absolute top-full right-0 mt-2 bg-background border border-border rounded-xl shadow-lg p-2 z-50 min-w-[160px]"
                    >
                      <button
                        onClick={() => handleShare('twitter')}
                        className="flex items-center gap-3 w-full px-3 py-2 text-left hover:bg-muted rounded-lg transition-colors duration-200"
                      >
                        <Twitter className="w-4 h-4 text-blue-500" />
                        Twitter
                      </button>
                      <button
                        onClick={() => handleShare('linkedin')}
                        className="flex items-center gap-3 w-full px-3 py-2 text-left hover:bg-muted rounded-lg transition-colors duration-200"
                      >
                        <Linkedin className="w-4 h-4 text-blue-600" />
                        LinkedIn
                      </button>
                      <button
                        onClick={() => handleShare('facebook')}
                        className="flex items-center gap-3 w-full px-3 py-2 text-left hover:bg-muted rounded-lg transition-colors duration-200"
                      >
                        <Facebook className="w-4 h-4 text-blue-700" />
                        Facebook
                      </button>
                      <button
                        onClick={() => handleShare('copy')}
                        className="flex items-center gap-3 w-full px-3 py-2 text-left hover:bg-muted rounded-lg transition-colors duration-200"
                      >
                        {copied ? (
                          <>
                            <CheckCircle className="w-4 h-4 text-green-500" />
                            Copied!
                          </>
                        ) : (
                          <>
                            <LinkIcon className="w-4 h-4" />
                            Copy Link
                          </>
                        )}
                      </button>
                    </motion.div>
                  )}
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Article Content */}
      <section className="py-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.article
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="prose prose-lg dark:prose-invert max-w-none article-content"
            style={{
              '--tw-prose-body': 'var(--foreground)',
              '--tw-prose-headings': 'var(--foreground)',
              '--tw-prose-lead': 'var(--muted-foreground)',
              '--tw-prose-links': 'var(--primary)',
              '--tw-prose-bold': 'var(--foreground)',
              '--tw-prose-counters': 'var(--muted-foreground)',
              '--tw-prose-bullets': 'var(--muted-foreground)',
              '--tw-prose-hr': 'var(--border)',
              '--tw-prose-quotes': 'var(--muted-foreground)',
              '--tw-prose-quote-borders': 'var(--primary)',
              '--tw-prose-captions': 'var(--muted-foreground)',
              '--tw-prose-code': 'var(--foreground)',
              '--tw-prose-pre-code': 'var(--foreground)',
              '--tw-prose-pre-bg': 'var(--muted)',
              '--tw-prose-th-borders': 'var(--border)',
              '--tw-prose-td-borders': 'var(--border)',
            } as React.CSSProperties}
          >
            <style jsx>{`
              .clean-article {
                max-width: none;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                line-height: 1.7;
                color: hsl(var(--foreground));
              }

              .clean-article h2 {
                font-size: 2.25rem;
                font-weight: 700;
                color: hsl(var(--foreground));
                margin: 3rem 0 1.5rem 0;
                line-height: 1.2;
              }

              .clean-article h3 {
                font-size: 1.5rem;
                font-weight: 600;
                color: hsl(var(--foreground));
                margin: 2rem 0 1rem 0;
                line-height: 1.3;
              }

              .clean-article p {
                font-size: 1.125rem;
                line-height: 1.7;
                margin-bottom: 1.5rem;
                color: hsl(var(--foreground));
              }

              .clean-article ul {
                margin: 1rem 0;
                padding-left: 1.5rem;
              }

              .clean-article li {
                margin-bottom: 0.5rem;
                font-size: 1.125rem;
                line-height: 1.6;
              }

              /* Opening Section */
              .opening-section {
                background: linear-gradient(135deg, hsl(var(--primary-50)) 0%, hsl(var(--accent-50)) 100%);
                border-radius: 1.5rem;
                padding: 2.5rem;
                margin-bottom: 3rem;
                border: 1px solid hsl(var(--primary-200));
              }

              .dark .opening-section {
                background: linear-gradient(135deg, hsl(var(--primary-950)) 0%, hsl(var(--accent-950)) 100%);
                border: 1px solid hsl(var(--primary-800));
              }

              /* Reality Grid */
              .reality-grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 2rem;
                margin: 2rem 0;
              }

              @media (max-width: 768px) {
                .reality-grid {
                  grid-template-columns: 1fr;
                }
              }

              .reality-card {
                padding: 2rem;
                border-radius: 1.5rem;
                border: 1px solid hsl(var(--border));
                background: hsl(var(--background));
              }

              .reality-card.negative {
                border-color: hsl(var(--red-200));
                background: linear-gradient(135deg, hsl(var(--red-50)) 0%, hsl(var(--background)) 100%);
              }

              .dark .reality-card.negative {
                border-color: hsl(var(--red-800));
                background: linear-gradient(135deg, hsl(var(--red-950)) 0%, hsl(var(--background)) 100%);
              }

              .reality-card.positive {
                border-color: hsl(var(--green-200));
                background: linear-gradient(135deg, hsl(var(--green-50)) 0%, hsl(var(--background)) 100%);
              }

              .dark .reality-card.positive {
                border-color: hsl(var(--green-800));
                background: linear-gradient(135deg, hsl(var(--green-950)) 0%, hsl(var(--background)) 100%);
              }

              /* Success Highlight */
              .success-highlight {
                background: linear-gradient(135deg, hsl(var(--primary-50)) 0%, hsl(var(--accent-50)) 100%);
                border-radius: 1.5rem;
                padding: 2.5rem;
                margin: 2rem 0;
                text-align: center;
                border: 1px solid hsl(var(--primary-200));
              }

              .dark .success-highlight {
                background: linear-gradient(135deg, hsl(var(--primary-950)) 0%, hsl(var(--accent-950)) 100%);
                border: 1px solid hsl(var(--primary-800));
              }

              .success-stat {
                display: flex;
                flex-direction: column;
                align-items: center;
                margin-bottom: 2rem;
              }

              .big-number {
                font-size: 3.5rem;
                font-weight: 900;
                color: hsl(var(--primary-600));
                line-height: 1;
              }

              .stat-label {
                font-size: 1.25rem;
                font-weight: 600;
                color: hsl(var(--foreground));
                margin-top: 0.5rem;
              }

              .success-benefits {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 1rem;
              }

              .benefit-item {
                background: hsl(var(--background));
                padding: 1rem;
                border-radius: 1rem;
                font-weight: 600;
                border: 1px solid hsl(var(--border));
              }

              /* Method Steps */
              .method-steps {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
                gap: 2rem;
                margin: 2rem 0;
              }

              .step-card {
                background: hsl(var(--background));
                border-radius: 1.5rem;
                padding: 2rem;
                border: 1px solid hsl(var(--border));
                text-align: center;
                transition: all 0.3s ease;
              }

              .step-card:hover {
                transform: translateY(-4px);
                box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
              }

              .step-number {
                width: 3rem;
                height: 3rem;
                background: linear-gradient(135deg, hsl(var(--primary-500)) 0%, hsl(var(--primary-600)) 100%);
                color: white;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: 800;
                font-size: 1.25rem;
                margin: 0 auto 1.5rem auto;
              }

              .step-content h3 {
                margin: 0 0 1rem 0;
                font-size: 1.25rem;
                font-weight: 700;
                color: hsl(var(--foreground));
              }

              .step-content p {
                margin: 0;
                font-size: 1rem;
                color: hsl(var(--muted-foreground));
                font-weight: 500;
              }

              /* Time Thieves Grid */
              .time-thieves-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 2rem;
                margin: 2rem 0;
              }

              .thief-card {
                background: hsl(var(--background));
                border: 1px solid hsl(var(--red-200));
                border-radius: 1.5rem;
                padding: 2rem;
                text-align: center;
                transition: all 0.3s ease;
              }

              .dark .thief-card {
                border-color: hsl(var(--red-800));
              }

              .thief-card:hover {
                transform: translateY(-4px);
                box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
              }

              .thief-icon {
                font-size: 3rem;
                margin-bottom: 1rem;
                display: block;
              }

              .thief-card h3 {
                font-size: 1.25rem;
                font-weight: 700;
                color: hsl(var(--foreground));
                margin-bottom: 1rem;
              }

              .thief-card p {
                color: hsl(var(--muted-foreground));
                margin-bottom: 1.5rem;
                font-size: 1rem;
                line-height: 1.5;
              }

              .damage-stat {
                background: linear-gradient(135deg, hsl(var(--red-100)) 0%, hsl(var(--red-200)) 100%);
                color: hsl(var(--red-800));
                padding: 0.5rem 1rem;
                border-radius: 1rem;
                font-size: 0.875rem;
                font-weight: 600;
                display: inline-block;
                border: 1px solid hsl(var(--red-300));
              }

              .dark .damage-stat {
                background: linear-gradient(135deg, hsl(var(--red-900)) 0%, hsl(var(--red-800)) 100%);
                color: hsl(var(--red-300));
                border-color: hsl(var(--red-700));
              }

              /* Golden Rule Box */
              .golden-rule-box {
                background: linear-gradient(135deg, hsl(var(--yellow-50)) 0%, hsl(var(--orange-50)) 100%);
                border: 2px solid hsl(var(--yellow-300));
                border-radius: 1.5rem;
                padding: 2.5rem;
                margin: 2rem 0;
                text-align: center;
              }

              .dark .golden-rule-box {
                background: linear-gradient(135deg, hsl(var(--yellow-950)) 0%, hsl(var(--orange-950)) 100%);
                border-color: hsl(var(--yellow-700));
              }

              .golden-rule-box h3 {
                margin-top: 0;
                margin-bottom: 1.5rem;
                font-size: 1.75rem;
                color: hsl(var(--foreground));
              }

              .rule-emphasis {
                font-size: 2rem;
                font-weight: 900;
                color: hsl(var(--orange-600));
                margin: 1rem 0;
              }

              /* Transformation Grid */
              .transformation-grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 2rem;
                margin: 2rem 0;
              }

              @media (max-width: 768px) {
                .transformation-grid {
                  grid-template-columns: 1fr;
                }
              }

              .before-card, .after-card {
                padding: 2rem;
                border-radius: 1.5rem;
                text-align: center;
                border: 1px solid hsl(var(--border));
              }

              .before-card {
                background: linear-gradient(135deg, hsl(var(--red-50)) 0%, hsl(var(--background)) 100%);
                border-color: hsl(var(--red-200));
              }

              .dark .before-card {
                background: linear-gradient(135deg, hsl(var(--red-950)) 0%, hsl(var(--background)) 100%);
                border-color: hsl(var(--red-800));
              }

              .after-card {
                background: linear-gradient(135deg, hsl(var(--green-50)) 0%, hsl(var(--background)) 100%);
                border-color: hsl(var(--green-200));
              }

              .dark .after-card {
                background: linear-gradient(135deg, hsl(var(--green-950)) 0%, hsl(var(--background)) 100%);
                border-color: hsl(var(--green-800));
              }

              .before-card h4, .after-card h4 {
                margin-top: 0;
                margin-bottom: 1rem;
                font-size: 1.125rem;
                font-weight: 700;
                text-transform: uppercase;
                letter-spacing: 1px;
              }

              .big-stat {
                font-size: 2.5rem;
                font-weight: 900;
                color: hsl(var(--foreground));
                line-height: 1;
                margin-bottom: 0.5rem;
              }

              .stat-context {
                font-size: 1rem;
                color: hsl(var(--muted-foreground));
                margin-bottom: 1.5rem;
                font-weight: 600;
              }

              /* Testimonial Box */
              .testimonial-box {
                background: linear-gradient(135deg, hsl(var(--primary-50)) 0%, hsl(var(--accent-50)) 100%);
                border: 1px solid hsl(var(--primary-200));
                border-radius: 1.5rem;
                padding: 2rem;
                margin: 2rem 0;
                text-align: center;
              }

              .dark .testimonial-box {
                background: linear-gradient(135deg, hsl(var(--primary-950)) 0%, hsl(var(--accent-950)) 100%);
                border: 1px solid hsl(var(--primary-800));
              }

              .testimonial-box blockquote {
                font-size: 1.25rem;
                font-style: italic;
                color: hsl(var(--foreground));
                margin-bottom: 1rem;
                line-height: 1.6;
                font-weight: 500;
              }

              .testimonial-box cite {
                font-style: normal;
                font-weight: 600;
                color: hsl(var(--primary-600));
                font-size: 1rem;
              }

              /* Priority List */
              .priority-list {
                display: flex;
                flex-direction: column;
                gap: 2rem;
                margin: 2rem 0;
              }

              .priority-item {
                background: hsl(var(--background));
                border: 1px solid hsl(var(--border));
                border-radius: 1.5rem;
                padding: 2rem;
                position: relative;
              }

              .priority-badge {
                position: absolute;
                top: -0.75rem;
                left: 2rem;
                padding: 0.5rem 1rem;
                border-radius: 1rem;
                font-size: 0.875rem;
                font-weight: 700;
                color: white;
              }

              .priority-badge.level-1 {
                background: linear-gradient(135deg, hsl(var(--green-500)) 0%, hsl(var(--green-600)) 100%);
              }

              .priority-badge.level-2 {
                background: linear-gradient(135deg, hsl(var(--blue-500)) 0%, hsl(var(--blue-600)) 100%);
              }

              .priority-badge.level-3 {
                background: linear-gradient(135deg, hsl(var(--purple-500)) 0%, hsl(var(--purple-600)) 100%);
              }

              .priority-item h3 {
                margin-top: 0.5rem;
                margin-bottom: 1rem;
                font-size: 1.25rem;
                font-weight: 700;
                color: hsl(var(--foreground));
              }

              .impact-stat {
                font-size: 1.125rem;
                font-weight: 600;
                color: hsl(var(--primary-600));
                margin-bottom: 0.5rem;
              }

              /* Results Grid */
              .results-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 2rem;
                margin: 2rem 0;
              }

              .result-card {
                background: hsl(var(--background));
                border: 1px solid hsl(var(--border));
                border-radius: 1.5rem;
                padding: 2rem;
                transition: all 0.3s ease;
              }

              .result-card:hover {
                transform: translateY(-4px);
                box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
              }

              .result-card h3 {
                margin: 0 0 1.5rem 0;
                color: hsl(var(--foreground));
                font-size: 1.125rem;
                font-weight: 700;
              }

              .result-comparison {
                display: flex;
                flex-direction: column;
                gap: 0.5rem;
                margin-bottom: 1rem;
              }

              @media (min-width: 640px) {
                .result-comparison {
                  flex-direction: row;
                  align-items: center;
                  gap: 1rem;
                }
              }

              .result-comparison .before {
                color: hsl(var(--red-600));
                font-weight: 500;
                font-size: 0.95rem;
              }

              .result-comparison .arrow {
                color: hsl(var(--primary-600));
                font-weight: 900;
                font-size: 1.25rem;
              }

              .result-comparison .after {
                color: hsl(var(--green-600));
                font-weight: 600;
                font-size: 0.95rem;
              }

              .result-card .bonus {
                font-size: 0.875rem;
                color: hsl(var(--muted-foreground));
                font-style: italic;
                margin: 0;
              }

              /* Week Plan */
              .week-plan {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 2rem;
                margin: 2rem 0;
              }

              .week {
                background: hsl(var(--background));
                border: 1px solid hsl(var(--border));
                border-radius: 1.5rem;
                padding: 2rem;
                text-align: center;
                transition: all 0.3s ease;
              }

              .week:hover {
                transform: translateY(-4px);
                box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
              }

              .week-num {
                display: inline-block;
                background: linear-gradient(135deg, hsl(var(--primary-500)) 0%, hsl(var(--primary-600)) 100%);
                color: white;
                padding: 0.5rem 1rem;
                border-radius: 1rem;
                font-size: 0.875rem;
                font-weight: 700;
                margin-bottom: 1rem;
              }

              .week h3 {
                margin: 0 0 1rem 0;
                color: hsl(var(--foreground));
                font-size: 1.125rem;
                font-weight: 700;
              }

              .week p {
                margin: 0 0 1rem 0;
                font-size: 1rem;
                color: hsl(var(--muted-foreground));
                line-height: 1.5;
              }

              .pro-tip {
                font-size: 0.875rem;
                color: hsl(var(--primary-600));
                font-style: italic;
                margin: 0;
              }

              /* Final Sections */
              .final-thoughts {
                background: linear-gradient(135deg, hsl(var(--primary-50)) 0%, hsl(var(--accent-50)) 100%);
                border: 1px solid hsl(var(--primary-200));
                border-radius: 1.5rem;
                padding: 2rem;
                margin: 2rem 0;
              }

              .dark .final-thoughts {
                background: linear-gradient(135deg, hsl(var(--primary-950)) 0%, hsl(var(--accent-950)) 100%);
                border: 1px solid hsl(var(--primary-800));
              }

              .final-question {
                text-align: center;
                margin: 2rem 0;
              }

              .final-question h3 {
                font-size: 1.375rem;
                color: hsl(var(--foreground));
                margin-bottom: 1rem;
                line-height: 1.4;
              }











            `}</style>
            <div>
              {typeof post.content === 'string' ? (
                <div dangerouslySetInnerHTML={{ __html: post.content }} />
              ) : (
                post.content
              )}
            </div>
          </motion.article>
        </div>
      </section>

      {/* Author Bio */}
      <section className="py-16 bg-gradient-to-b from-background to-muted/30">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="bg-background rounded-3xl p-8 border border-primary-200/20 dark:border-primary-800/20 shadow-lg"
          >
            <div className="flex flex-col sm:flex-row items-start gap-6">
              <div className="w-20 h-20 bg-gradient-to-br from-primary-100 to-accent-100 dark:from-primary-900 dark:to-accent-900 rounded-full flex items-center justify-center flex-shrink-0">
                <User className="w-10 h-10 text-primary-600 dark:text-primary-400" />
              </div>
              <div className="flex-1">
                <h3 className="text-xl font-bold text-foreground mb-2">About {post.author.name}</h3>
                <p className="text-muted-foreground mb-4 leading-relaxed">{post.author.bio}</p>
                <div className="text-sm text-muted-foreground">
                  <strong className="text-foreground">{post.author.role}</strong> at GenLogic
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Related Articles */}
      {relatedPosts.length > 0 && (
        <section className="py-20 bg-gradient-to-b from-muted/30 to-background">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">Related Articles</h2>
              <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                More insights to help you automate your business and reclaim your time
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {relatedPosts.map((relatedPost, index) => (
                <BlogCard
                  key={relatedPost.id}
                  post={relatedPost}
                  index={index}
                  featured={false}
                />
              ))}
            </div>
          </div>
        </section>
      )}

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800 relative overflow-hidden">
        <div className="absolute inset-0 opacity-20">
          <div className="w-full h-full bg-white/5 bg-[radial-gradient(circle_at_50%_50%,rgba(255,255,255,0.1),transparent)]"></div>
        </div>

        <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6 leading-tight">
              Ready to reclaim your{' '}
              <span className="relative">
                <span className="text-accent-300">evenings?</span>
                <div className="absolute -bottom-2 left-0 right-0 h-3 bg-white/30 rounded-full"></div>
              </span>
            </h2>
            <p className="text-xl text-primary-100 mb-8 leading-relaxed max-w-2xl mx-auto">
              <strong className="text-white">Join 500+ UK business owners</strong> who've automated their operations and got their lives back.
              Let's discuss how automation can transform your business.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
              <CustomButton
                variant="secondary"
                size="lg"
                href="/contact"
                icon={<ArrowRight className="w-5 h-5" />}
                iconPosition="right"
                className="bg-white text-primary-700 hover:bg-primary-50 shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300"
              >
                Get Your Free Automation Assessment
              </CustomButton>

              <CustomButton
                variant="outline"
                size="lg"
                href="/demo"
                className="border-2 border-white text-white hover:bg-white hover:text-primary-700 transition-all duration-300"
              >
                See How It Works
              </CustomButton>
            </div>

            <div className="flex flex-col sm:flex-row items-center justify-center gap-6 text-primary-100 text-sm">
              <div className="flex items-center">
                <CheckCircle className="w-4 h-4 mr-2 text-green-300" />
                <span><strong className="text-white">Free</strong> consultation call</span>
              </div>
              <div className="flex items-center">
                <Clock className="w-4 h-4 mr-2 text-blue-300" />
                <span><strong className="text-white">30-minute</strong> assessment</span>
              </div>
              <div className="flex items-center">
                <BookOpen className="w-4 h-4 mr-2 text-yellow-300" />
                <span><strong className="text-white">Custom</strong> automation plan</span>
              </div>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  )
}
