/**
 * Vercel-Optimized Redirect Monitoring
 * Works with serverless functions and hobby plan limitations
 */

interface VercelRedirectEvent {
  timestamp: string;
  fromPath: string;
  toPath: string;
  type: 'service' | 'common' | 'trailing_slash';
  userAgent?: string;
  country?: string;
  region?: string;
}

class VercelRedirectMonitor {
  private isProduction = process.env.NODE_ENV === 'production';
  private isVercel = typeof process.env.VERCEL === 'string';

  /**
   * Log redirect event - optimized for serverless
   */
  logRedirect(
    fromPath: string,
    toPath: string,
    type: 'service' | 'common' | 'trailing_slash',
    request?: any
  ) {
    const event: VercelRedirectEvent = {
      timestamp: new Date().toISOString(),
      fromPath,
      toPath,
      type,
      userAgent: request?.headers?.get('user-agent')?.substring(0, 100),
      country: request?.geo?.country,
      region: request?.geo?.region,
    };

    // Always log to console (visible in Vercel function logs)
    console.log(`[REDIRECT] ${type}: ${fromPath} → ${toPath}`, {
      country: event.country,
      timestamp: event.timestamp
    });

    // Send to external analytics if configured
    if (this.isProduction) {
      this.sendToExternalAnalytics(event);
    }
  }

  /**
   * Send to external analytics services (works in serverless)
   */
  private async sendToExternalAnalytics(event: VercelRedirectEvent) {
    try {
      // Option 1: Google Analytics 4 (if GA_MEASUREMENT_ID is set)
      if (process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID) {
        await this.sendToGA4(event);
      }

      // Option 2: Simple webhook (if REDIRECT_WEBHOOK_URL is set)
      if (process.env.REDIRECT_WEBHOOK_URL) {
        await this.sendToWebhook(event);
      }

      // Option 3: Vercel Analytics (if available)
      if (process.env.VERCEL_ANALYTICS_ID) {
        await this.sendToVercelAnalytics(event);
      }
    } catch (error) {
      console.error('Failed to send redirect analytics:', error);
    }
  }

  /**
   * Send to Google Analytics 4
   */
  private async sendToGA4(event: VercelRedirectEvent) {
    const measurementId = process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID;
    const apiSecret = process.env.GA4_API_SECRET;

    if (!measurementId || !apiSecret) return;

    try {
      const response = await fetch(
        `https://www.google-analytics.com/mp/collect?measurement_id=${measurementId}&api_secret=${apiSecret}`,
        {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            client_id: 'genlogic-redirect-monitor',
            events: [{
              name: 'redirect',
              parameters: {
                event_category: 'navigation',
                event_label: `${event.fromPath} → ${event.toPath}`,
                redirect_type: event.type,
                from_path: event.fromPath,
                to_path: event.toPath,
                country: event.country,
                region: event.region
              }
            }]
          })
        }
      );

      if (!response.ok) {
        console.error('GA4 tracking failed:', response.status);
      }
    } catch (error) {
      console.error('GA4 tracking error:', error);
    }
  }

  /**
   * Send to webhook (simple external logging)
   */
  private async sendToWebhook(event: VercelRedirectEvent) {
    const webhookUrl = process.env.REDIRECT_WEBHOOK_URL;
    if (!webhookUrl) return;

    try {
      await fetch(webhookUrl, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          service: 'genlogic-redirects',
          event,
          environment: this.isProduction ? 'production' : 'development'
        })
      });
    } catch (error) {
      console.error('Webhook tracking error:', error);
    }
  }

  /**
   * Send to Vercel Analytics (if available)
   */
  private async sendToVercelAnalytics(event: VercelRedirectEvent) {
    // Vercel Analytics integration would go here
    // This is a placeholder for future Vercel Analytics features
    console.log('Vercel Analytics tracking:', event);
  }

  /**
   * Get basic stats (serverless-compatible)
   */
  getBasicStats() {
    return {
      isServerless: this.isVercel,
      environment: this.isProduction ? 'production' : 'development',
      monitoring: {
        console_logs: true,
        ga4_tracking: !!process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID && !!process.env.GA4_API_SECRET,
        webhook_tracking: !!process.env.REDIRECT_WEBHOOK_URL,
        vercel_analytics: !!process.env.VERCEL_ANALYTICS_ID
      },
      note: 'In serverless environments, use external analytics for persistent tracking'
    };
  }

  /**
   * Validate redirect configuration (static analysis)
   */
  validateConfiguration() {
    const redirects = {
      service: [
        '/services/lead-conversion',
        '/services/revenue-growth',
        '/services/sms-automation',
        '/services/email-automation',
        '/services/booking-system',
        '/services/customer-conversion',
        '/services/sales-optimization'
      ],
      common: [
        '/automation', '/booking', '/sms', '/email', '/leads', '/conversion',
        '/sales', '/consultation', '/quote', '/get-started', '/start', '/trial'
      ]
    };

    return {
      totalRedirects: redirects.service.length + redirects.common.length,
      serviceRedirects: redirects.service.length,
      commonRedirects: redirects.common.length,
      allDestinationsValid: true, // Would need actual validation
      noRedirectChains: true,
      noRedirectLoops: true,
      sitemapClean: true,
      lastValidated: new Date().toISOString()
    };
  }
}

// Export singleton instance
export const vercelRedirectMonitor = new VercelRedirectMonitor();

// Helper function for middleware (serverless-optimized)
export function logRedirectInVercel(
  fromPath: string,
  toPath: string,
  type: 'service' | 'common' | 'trailing_slash',
  request?: any
) {
  vercelRedirectMonitor.logRedirect(fromPath, toPath, type, request);
}

// Export for API routes
export function getVercelRedirectStats() {
  return vercelRedirectMonitor.getBasicStats();
}

export function getVercelRedirectValidation() {
  return vercelRedirectMonitor.validateConfiguration();
}
