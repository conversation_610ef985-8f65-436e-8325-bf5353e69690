'use client'

import { useEffect } from 'react'

// Extend Window interface for custom properties
declare global {
  interface Window {
    __batchedRead?: (callback: () => void) => void
    __batchedWrite?: (callback: () => void) => void
    __createOptimizedObserver?: (callback: IntersectionObserverCallback, options?: IntersectionObserverInit) => IntersectionObserver
  }
}

// Component to optimize forced reflows and improve performance
export function ReflowOptimizer() {
  useEffect(() => {
    // Batch DOM reads and writes to prevent forced reflows
    const optimizeReflows = () => {
      // Use requestAnimationFrame to batch DOM operations
      let rafId: number
      const pendingReads: (() => void)[] = []
      const pendingWrites: (() => void)[] = []

      const batchedRead = (callback: () => void) => {
        pendingReads.push(callback)
        scheduleFlush()
      }

      const batchedWrite = (callback: () => void) => {
        pendingWrites.push(callback)
        scheduleFlush()
      }

      const scheduleFlush = () => {
        if (rafId) return
        rafId = requestAnimationFrame(() => {
          // Execute all reads first
          while (pendingReads.length) {
            const read = pendingReads.shift()
            if (read) read()
          }
          
          // Then execute all writes
          while (pendingWrites.length) {
            const write = pendingWrites.shift()
            if (write) write()
          }
          
          rafId = 0
        })
      }

      // Expose batching functions globally for other components
      ;(window as any).__batchedRead = batchedRead
      ;(window as any).__batchedWrite = batchedWrite
    }

    // Optimize scroll performance
    const optimizeScrolling = () => {
      let ticking = false

      const updateScrollPosition = () => {
        // Batch scroll-related DOM operations
        if (typeof window !== 'undefined' && window.__batchedRead) {
          window.__batchedRead(() => {
            const scrollY = window.scrollY
            // Store scroll position without triggering layout
            document.documentElement.style.setProperty('--scroll-y', `${scrollY}px`)
          })
        }
        ticking = false
      }

      const onScroll = () => {
        if (!ticking) {
          requestAnimationFrame(updateScrollPosition)
          ticking = true
        }
      }

      window.addEventListener('scroll', onScroll, { passive: true })
      
      return () => {
        window.removeEventListener('scroll', onScroll)
      }
    }

    // Optimize resize performance
    const optimizeResize = () => {
      let resizeTimer: NodeJS.Timeout

      const handleResize = () => {
        clearTimeout(resizeTimer)
        resizeTimer = setTimeout(() => {
          if (typeof window !== 'undefined' && window.__batchedWrite) {
            window.__batchedWrite(() => {
              // Batch resize-related DOM updates
              document.documentElement.style.setProperty('--viewport-width', `${window.innerWidth}px`)
              document.documentElement.style.setProperty('--viewport-height', `${window.innerHeight}px`)
            })
          }
        }, 100)
      }

      window.addEventListener('resize', handleResize, { passive: true })
      
      return () => {
        window.removeEventListener('resize', handleResize)
        clearTimeout(resizeTimer)
      }
    }

    // Initialize optimizations
    optimizeReflows()
    const cleanupScroll = optimizeScrolling()
    const cleanupResize = optimizeResize()

    // Cleanup on unmount
    return () => {
      cleanupScroll()
      cleanupResize()
    }
  }, [])

  useEffect(() => {
    // Optimize intersection observer performance
    const optimizeIntersectionObserver = () => {
      const observers = new Map()

      const createOptimizedObserver = (callback: IntersectionObserverCallback, options?: IntersectionObserverInit) => {
        const key = JSON.stringify(options)
        
        if (!observers.has(key)) {
          const observer = new IntersectionObserver((entries) => {
            // Batch intersection observer callbacks
            if (typeof window !== 'undefined' && window.__batchedRead) {
              window.__batchedRead(() => {
                callback(entries, observer)
              })
            } else {
              callback(entries, observer)
            }
          }, options)
          
          observers.set(key, observer)
        }
        
        return observers.get(key)
      }

      // Expose optimized observer globally
      ;(window as any).__createOptimizedObserver = createOptimizedObserver
    }

    optimizeIntersectionObserver()
  }, [])

  useEffect(() => {
    // Optimize animation performance
    const optimizeAnimations = () => {
      // Use transform and opacity for animations (GPU accelerated)
      const style = document.createElement('style')
      style.textContent = `
        .animate-optimized {
          will-change: transform, opacity;
          transform: translateZ(0);
        }
        
        .animate-optimized:not(.animating) {
          will-change: auto;
        }
        
        /* Optimize common animation patterns */
        .fade-in {
          animation: fadeIn 0.3s ease-out forwards;
        }
        
        .slide-up {
          animation: slideUp 0.4s ease-out forwards;
        }
        
        @keyframes fadeIn {
          from { opacity: 0; }
          to { opacity: 1; }
        }
        
        @keyframes slideUp {
          from { 
            opacity: 0; 
            transform: translateY(20px); 
          }
          to { 
            opacity: 1; 
            transform: translateY(0); 
          }
        }
        
        /* Reduce motion for accessibility */
        @media (prefers-reduced-motion: reduce) {
          .animate-optimized,
          .fade-in,
          .slide-up {
            animation: none !important;
            transition: none !important;
          }
        }
      `
      
      document.head.appendChild(style)
      
      return () => {
        document.head.removeChild(style)
      }
    }

    const cleanupAnimations = optimizeAnimations()
    
    return cleanupAnimations
  }, [])

  // This component doesn't render anything visible
  return null
}

// Performance monitoring hook
export function usePerformanceMonitoring() {
  useEffect(() => {
    if (typeof window === 'undefined') return

    // Monitor Core Web Vitals
    const observePerformance = () => {
      // Largest Contentful Paint (LCP)
      if ('PerformanceObserver' in window) {
        try {
          const lcpObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries()
            const lastEntry = entries[entries.length - 1] as any
            
            if (lastEntry && window.gtag) {
              window.gtag('event', 'web_vitals', {
                event_category: 'performance',
                event_label: 'LCP',
                value: Math.round(lastEntry.startTime),
                custom_parameter_1: 'core_web_vitals'
              })
            }
          })
          
          lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })
        } catch (e) {
          console.warn('LCP observer not supported')
        }

        // Interaction to Next Paint (INP) - Replaced FID in March 2024
        try {
          const inpObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries()
            entries.forEach((entry: any) => {
              if (window.gtag && 'duration' in entry) {
                window.gtag('event', 'web_vitals', {
                  event_category: 'performance',
                  event_label: 'INP',
                  value: Math.round(entry.duration),
                  custom_parameter_1: 'core_web_vitals_2025'
                })
              }
            })
          })

          inpObserver.observe({ entryTypes: ['event'] })
        } catch (e) {
          console.warn('INP observer not supported')
        }

        // First Input Delay (FID) - Keep for backwards compatibility
        try {
          const fidObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries()
            entries.forEach((entry: any) => {
              if (window.gtag) {
                window.gtag('event', 'web_vitals', {
                  event_category: 'performance',
                  event_label: 'FID_LEGACY',
                  value: Math.round(entry.processingStart - entry.startTime),
                  custom_parameter_1: 'core_web_vitals'
                })
              }
            })
          })

          fidObserver.observe({ entryTypes: ['first-input'] })
        } catch (e) {
          console.warn('FID observer not supported')
        }

        // Cumulative Layout Shift (CLS)
        try {
          let clsValue = 0
          const clsObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries()
            entries.forEach((entry: any) => {
              if (!entry.hadRecentInput) {
                clsValue += entry.value
              }
            })
            
            if (window.gtag) {
              window.gtag('event', 'web_vitals', {
                event_category: 'performance',
                event_label: 'CLS',
                value: Math.round(clsValue * 1000),
                custom_parameter_1: 'core_web_vitals'
              })
            }
          })
          
          clsObserver.observe({ entryTypes: ['layout-shift'] })
        } catch (e) {
          console.warn('CLS observer not supported')
        }
      }
    }

    observePerformance()
  }, [])
}
