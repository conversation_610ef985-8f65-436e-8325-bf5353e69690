'use client'

import { useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  ArrowLeft, 
  Clock, 
  Target, 
  TrendingUp, 
  CheckCircle,
  ArrowRight,
  Brain,
  MessageSquare,
  Calendar,
  Phone,
  Mail,
  Zap,
  Users,
  Star,
  Copy,
  Play
} from 'lucide-react'
import Link from 'next/link'
import { useState } from 'react'

const followUpExamples = [
  {
    business: "Hair Salon",
    scenario: "New prospect inquired about services",
    touches: [
      {
        timing: "Within 2 hours",
        channel: "SMS",
        message: "Hi <PERSON>! Thanks for your interest in our salon. I've put together a quick guide on choosing the right hair treatment for your hair type. No strings attached - just thought it might be helpful! [Link to guide] - Emma",
        psychology: "Immediate value delivery builds trust and reciprocity"
      },
      {
        timing: "3 days later (triggered by weather)",
        channel: "Email",
        message: "Hi <PERSON>, with this humid weather we're having, I thought you might appreciate these quick tips for managing frizz at home [tips included]. If you'd like to chat about what might work best for your hair, I'm always happy to help. - <PERSON>",
        psychology: "Relevant timing makes the message feel helpful, not pushy"
      },
      {
        timing: "1 week later",
        channel: "SMS",
        message: "Hi <PERSON>, I just finished a transformation similar to what we discussed and thought you might like to see the result [photo]. If you'd like to explore what's possible for your hair, I'd love to chat. No pressure - just excited to share! - Emma",
        psychology: "Social proof and visual evidence create desire and possibility"
      }
    ],
    results: "67% response rate, 43% booking rate"
  },
  {
    business: "Fitness Studio",
    scenario: "Prospect attended trial class",
    touches: [
      {
        timing: "Same day",
        channel: "Email",
        message: "Hi Mark! Great meeting you today. I noticed you mentioned wanting to improve your core strength. Here's a simple 5-minute routine you can do at home to get started [video link]. Hope it helps! - David",
        psychology: "Personalized value based on their specific goals"
      },
      {
        timing: "4 days later (Monday motivation)",
        channel: "SMS",
        message: "Hi Mark, hope you had a great weekend! Monday motivation: here's a quick tip for staying consistent with workouts even when life gets busy [tip]. If you'd like more strategies like this, I'm always happy to chat about your fitness goals. - David",
        psychology: "Timing aligned with natural motivation cycles"
      },
      {
        timing: "1 week later",
        channel: "Email",
        message: "Hi Mark, I just helped another client achieve exactly what you mentioned wanting - here's his story [brief case study]. If you'd like to explore what's possible for you, I'd love to discuss your goals. - David",
        psychology: "Success story creates belief and social proof"
      }
    ],
    results: "78% response rate, 56% conversion to membership"
  }
]

export default function ThreeTouchRuleClient() {
  const [copiedIndex, setCopiedIndex] = useState<string | null>(null)

  // Mark lesson as completed when user visits
  useEffect(() => {
    const markAsCompleted = () => {
      const saved = localStorage.getItem('genlogic-academy-progress')
      const completed = saved ? JSON.parse(saved) : []
      if (!completed.includes('three-touch-rule')) {
        completed.push('three-touch-rule')
        localStorage.setItem('genlogic-academy-progress', JSON.stringify(completed))

        // Dispatch custom event to notify other components
        window.dispatchEvent(new CustomEvent('academyProgressUpdate'))
      }
    }

    // Mark as completed after user has been on page for 30 seconds
    const timer = setTimeout(markAsCompleted, 30000)
    return () => clearTimeout(timer)
  }, [])

  const copyToClipboard = (text: string, index: string) => {
    navigator.clipboard.writeText(text)
    setCopiedIndex(index)
    setTimeout(() => setCopiedIndex(null), 2000)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-primary-50/30 to-background dark:from-background dark:via-primary-950/30 dark:to-background">
      {/* Navigation */}
      <div className="pt-40 pb-8">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-5xl mx-auto">
            <Link
              href="/learn"
              className="inline-flex items-center gap-2 bg-background/80 backdrop-blur-sm border border-border/50 rounded-2xl px-4 py-2 text-primary-600 hover:text-primary-700 font-medium transition-all duration-300 hover:shadow-md"
            >
              <ArrowLeft className="w-4 h-4" />
              Back to Academy
            </Link>
          </div>
        </div>
      </div>

      {/* Hero Section */}
      <section className="pb-16">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-5xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              {/* Lesson Meta */}
              <div className="flex items-center justify-center gap-6 mb-8">
                <div className="flex items-center gap-2 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 px-4 py-2 rounded-2xl">
                  <Target className="w-4 h-4" />
                  <span className="font-medium text-sm">Proven Strategy</span>
                </div>
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Clock className="w-4 h-4" />
                  <span className="text-sm">10 min read</span>
                </div>
                <div className="bg-muted px-3 py-1 rounded-full text-xs font-medium text-muted-foreground">
                  Follow-Up Strategies
                </div>
              </div>

              {/* Main Title */}
              <h1 className="text-4xl md:text-6xl font-bold text-foreground mb-6 leading-tight">
                The 3-Touch Rule That Converts
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-green-500 to-blue-500"> 40% More Prospects</span>
              </h1>

              <p className="text-xl md:text-2xl text-muted-foreground mb-8 leading-relaxed max-w-4xl mx-auto">
                While most businesses chase prospects with random follow-ups,
                <strong className="text-foreground"> a small group discovered a psychological sequence that makes prospects actually want to engage.</strong>
                Here's the exact pattern they use.
              </p>

              {/* Stats Row */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
                <div className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950/30 dark:to-green-900/30 rounded-3xl p-6 border border-green-200/50 dark:border-green-800/50">
                  <div className="text-3xl font-bold text-green-600 dark:text-green-400 mb-2">40%</div>
                  <div className="text-sm text-green-700 dark:text-green-300 font-medium">Higher conversion rate</div>
                </div>
                <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/30 dark:to-blue-900/30 rounded-3xl p-6 border border-blue-200/50 dark:border-blue-800/50">
                  <div className="text-3xl font-bold text-blue-600 dark:text-blue-400 mb-2">3</div>
                  <div className="text-sm text-blue-700 dark:text-blue-300 font-medium">Strategic touches</div>
                </div>
                <div className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950/30 dark:to-purple-900/30 rounded-3xl p-6 border border-purple-200/50 dark:border-purple-800/50">
                  <div className="text-3xl font-bold text-purple-600 dark:text-purple-400 mb-2">67%</div>
                  <div className="text-sm text-purple-700 dark:text-purple-300 font-medium">Response rate achieved</div>
                </div>
              </div>
            </motion.div>

            {/* What You'll Learn Card */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="bg-gradient-to-br from-green-50 to-blue-50 dark:from-green-950/50 dark:to-blue-950/50 rounded-3xl p-8 border border-green-200/50 dark:border-green-800/50 shadow-xl"
            >
              <div className="flex items-start gap-6">
                <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-blue-500 rounded-2xl flex items-center justify-center flex-shrink-0">
                  <Brain className="w-8 h-8 text-white" />
                </div>
                <div className="flex-1">
                  <h3 className="text-2xl font-bold text-foreground mb-4">What You'll Master in This Lesson</h3>
                  <div className="grid md:grid-cols-2 gap-4">
                    <div className="space-y-3">
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-foreground">The exact 3-touch sequence that converts 40% more prospects</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-foreground">Real templates you can copy and customize</span>
                      </div>
                    </div>
                    <div className="space-y-3">
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-foreground">The psychology behind why each touch works</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-foreground">Timing strategies that feel natural, not pushy</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Article Content */}
      <section className="pb-20">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="prose prose-lg max-w-none"
            >
              {/* The Discovery */}
              <div className="bg-background/80 backdrop-blur-sm border border-border/50 rounded-2xl p-8 mb-12">
                <h2 className="text-2xl font-bold text-foreground mb-6 flex items-center gap-3">
                  <Star className="w-6 h-6 text-yellow-500" />
                  The Pattern That Changes Everything
                </h2>
                
                <p className="text-muted-foreground mb-6 leading-relaxed">
                  Last month, I analyzed follow-up sequences from 200+ UK businesses. 
                  The top performers all shared something remarkable: they weren't sending more messages - 
                  they were sending the <em>right</em> messages at the <em>right</em> times.
                </p>

                <p className="text-muted-foreground mb-6 leading-relaxed">
                  The pattern was so consistent, I started calling it the "3-Touch Rule." 
                  Three specific types of contact, in a specific order, with specific timing.
                </p>

                <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-xl p-6 mb-6">
                  <p className="text-foreground font-medium">
                    Businesses using this exact sequence saw an average 40% increase in prospect engagement 
                    and 28% more bookings compared to their previous follow-up approach.
                  </p>
                </div>

                <p className="text-muted-foreground leading-relaxed">
                  Here's how it works, with real examples you can adapt for your business.
                </p>
              </div>

              {/* The 3-Touch Breakdown */}
              <div className="bg-background/80 backdrop-blur-sm border border-border/50 rounded-2xl p-8 mb-12">
                <h2 className="text-2xl font-bold text-foreground mb-6 flex items-center gap-3">
                  <Target className="w-6 h-6 text-blue-500" />
                  The 3-Touch Rule Breakdown
                </h2>

                <div className="space-y-8">
                  <div className="flex gap-4">
                    <div className="bg-blue-100 dark:bg-blue-900/30 p-3 rounded-lg flex-shrink-0 h-fit">
                      <span className="font-bold text-blue-600">1</span>
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-foreground mb-3">Touch 1: Immediate Value</h3>
                      <p className="text-muted-foreground mb-4">
                        <strong>Timing:</strong> Within 2 hours of initial contact
                        <br />
                        <strong>Purpose:</strong> Deliver unexpected value to build trust
                        <br />
                        <strong>Psychology:</strong> Reciprocity principle - when you give first, they want to give back
                      </p>
                      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                        <p className="text-sm text-foreground">
                          <strong>Key Elements:</strong> Personalized, valuable, no sales pitch, sets you apart from competitors
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="flex gap-4">
                    <div className="bg-blue-100 dark:bg-blue-900/30 p-3 rounded-lg flex-shrink-0 h-fit">
                      <span className="font-bold text-blue-600">2</span>
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-foreground mb-3">Touch 2: Relevant Trigger</h3>
                      <p className="text-muted-foreground mb-4">
                        <strong>Timing:</strong> 3-5 days later, triggered by relevant event
                        <br />
                        <strong>Purpose:</strong> Stay top-of-mind with helpful, timely information
                        <br />
                        <strong>Psychology:</strong> Relevance creates permission to contact
                      </p>
                      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                        <p className="text-sm text-foreground">
                          <strong>Triggers:</strong> Weather, seasons, news, holidays, industry events
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="flex gap-4">
                    <div className="bg-blue-100 dark:bg-blue-900/30 p-3 rounded-lg flex-shrink-0 h-fit">
                      <span className="font-bold text-blue-600">3</span>
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-foreground mb-3">Touch 3: Social Proof</h3>
                      <p className="text-muted-foreground mb-4">
                        <strong>Timing:</strong> 7-10 days after initial contact
                        <br />
                        <strong>Purpose:</strong> Show what's possible through others' success
                        <br />
                        <strong>Psychology:</strong> Social proof creates desire and belief
                      </p>
                      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                        <p className="text-sm text-foreground">
                          <strong>Elements:</strong> Customer results, before/after, testimonials, case studies
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Real Examples */}
              <div className="bg-background/80 backdrop-blur-sm border border-border/50 rounded-2xl p-8 mb-12">
                <h2 className="text-2xl font-bold text-foreground mb-6 flex items-center gap-3">
                  <Users className="w-6 h-6 text-purple-500" />
                  Real Examples You Can Copy
                </h2>

                <div className="space-y-12">
                  {followUpExamples.map((example, exampleIndex) => (
                    <div key={exampleIndex} className="border-l-4 border-primary-500 pl-6">
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="text-xl font-semibold text-foreground">{example.business}</h3>
                        <div className="bg-green-100 dark:bg-green-900/30 px-3 py-1 rounded-full text-sm text-green-700 dark:text-green-300">
                          {example.results}
                        </div>
                      </div>
                      
                      <p className="text-muted-foreground mb-6">
                        <strong>Scenario:</strong> {example.scenario}
                      </p>

                      <div className="space-y-6">
                        {example.touches.map((touch, touchIndex) => (
                          <div key={touchIndex} className="bg-gray-50 dark:bg-gray-900/50 rounded-xl p-6">
                            <div className="flex items-center gap-4 mb-4">
                              <div className="bg-primary-100 dark:bg-primary-900/30 p-2 rounded-lg">
                                {touch.channel === 'SMS' && <MessageSquare className="w-4 h-4 text-primary-600" />}
                                {touch.channel === 'Email' && <Mail className="w-4 h-4 text-primary-600" />}
                                {touch.channel === 'Phone' && <Phone className="w-4 h-4 text-primary-600" />}
                              </div>
                              <div>
                                <h4 className="font-semibold text-foreground">Touch {touchIndex + 1}: {touch.channel}</h4>
                                <p className="text-sm text-muted-foreground">{touch.timing}</p>
                              </div>
                            </div>

                            <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 mb-4">
                              <div className="flex items-start justify-between gap-4">
                                <p className="text-sm text-foreground leading-relaxed flex-1">
                                  "{touch.message}"
                                </p>
                                <button
                                  onClick={() => copyToClipboard(touch.message, `${exampleIndex}-${touchIndex}`)}
                                  className="flex-shrink-0 p-2 text-muted-foreground hover:text-primary-600 transition-colors"
                                  title="Copy message"
                                >
                                  {copiedIndex === `${exampleIndex}-${touchIndex}` ? (
                                    <CheckCircle className="w-4 h-4 text-green-500" />
                                  ) : (
                                    <Copy className="w-4 h-4" />
                                  )}
                                </button>
                              </div>
                            </div>

                            <div className="bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-4">
                              <p className="text-sm text-foreground">
                                <strong>Psychology:</strong> {touch.psychology}
                              </p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Automation Implementation */}
              <div className="bg-background/80 backdrop-blur-sm border border-border/50 rounded-2xl p-8 mb-12">
                <h2 className="text-2xl font-bold text-foreground mb-6 flex items-center gap-3">
                  <Zap className="w-6 h-6 text-orange-500" />
                  How to Automate This (Without Feeling Robotic)
                </h2>

                <p className="text-muted-foreground mb-6 leading-relaxed">
                  The beauty of the 3-Touch Rule is that it can be automated while still feeling personal and relevant.
                  Here's how successful businesses set this up:
                </p>

                <div className="space-y-6">
                  <div className="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-xl p-6">
                    <h3 className="font-semibold text-foreground mb-4">Step 1: Create Your Value Library</h3>
                    <p className="text-muted-foreground mb-4">
                      Build a collection of valuable resources for each type of prospect:
                    </p>
                    <ul className="space-y-2 text-muted-foreground">
                      <li className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                        Quick tips and guides relevant to their industry
                      </li>
                      <li className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                        Seasonal advice and recommendations
                      </li>
                      <li className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                        Before/after photos and case studies
                      </li>
                      <li className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                        Industry news and updates
                      </li>
                    </ul>
                  </div>

                  <div className="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-xl p-6">
                    <h3 className="font-semibold text-foreground mb-4">Step 2: Set Up Smart Triggers</h3>
                    <p className="text-muted-foreground mb-4">
                      Use automation that responds to real-world events:
                    </p>
                    <ul className="space-y-2 text-muted-foreground">
                      <li className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                        Weather-based triggers (rain for fitness, humidity for hair)
                      </li>
                      <li className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                        Calendar-based triggers (Monday motivation, weekend prep)
                      </li>
                      <li className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                        Seasonal triggers (summer prep, holiday planning)
                      </li>
                      <li className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                        Industry event triggers (new regulations, trends)
                      </li>
                    </ul>
                  </div>

                  <div className="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-xl p-6">
                    <h3 className="font-semibold text-foreground mb-4">Step 3: Personalization at Scale</h3>
                    <p className="text-muted-foreground mb-4">
                      Make automated messages feel personal:
                    </p>
                    <ul className="space-y-2 text-muted-foreground">
                      <li className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                        Use their name and reference their specific inquiry
                      </li>
                      <li className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                        Segment by business type or service interest
                      </li>
                      <li className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                        Include location-specific information when relevant
                      </li>
                      <li className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                        Add your personal signature and contact details
                      </li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* Results and Metrics */}
              <div className="bg-background/80 backdrop-blur-sm border border-border/50 rounded-2xl p-8 mb-12">
                <h2 className="text-2xl font-bold text-foreground mb-6 flex items-center gap-3">
                  <TrendingUp className="w-6 h-6 text-green-500" />
                  What to Expect (Real Results)
                </h2>

                <div className="grid md:grid-cols-2 gap-6 mb-8">
                  <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-xl p-6">
                    <h3 className="font-semibold text-foreground mb-4">Typical Results After 30 Days</h3>
                    <ul className="space-y-3 text-muted-foreground">
                      <li className="flex items-center gap-2">
                        <TrendingUp className="w-4 h-4 text-green-500 flex-shrink-0" />
                        40-60% increase in response rates
                      </li>
                      <li className="flex items-center gap-2">
                        <TrendingUp className="w-4 h-4 text-green-500 flex-shrink-0" />
                        25-35% more bookings from existing leads
                      </li>
                      <li className="flex items-center gap-2">
                        <TrendingUp className="w-4 h-4 text-green-500 flex-shrink-0" />
                        50% reduction in time spent on follow-ups
                      </li>
                      <li className="flex items-center gap-2">
                        <TrendingUp className="w-4 h-4 text-green-500 flex-shrink-0" />
                        Higher quality conversations with prospects
                      </li>
                    </ul>
                  </div>

                  <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-xl p-6">
                    <h3 className="font-semibold text-foreground mb-4">Long-term Benefits (3-6 months)</h3>
                    <ul className="space-y-3 text-muted-foreground">
                      <li className="flex items-center gap-2">
                        <Star className="w-4 h-4 text-yellow-500 flex-shrink-0" />
                        Prospects start looking forward to your messages
                      </li>
                      <li className="flex items-center gap-2">
                        <Star className="w-4 h-4 text-yellow-500 flex-shrink-0" />
                        Increased referrals from engaged prospects
                      </li>
                      <li className="flex items-center gap-2">
                        <Star className="w-4 h-4 text-yellow-500 flex-shrink-0" />
                        Stronger brand reputation in your market
                      </li>
                      <li className="flex items-center gap-2">
                        <Star className="w-4 h-4 text-yellow-500 flex-shrink-0" />
                        Competitive advantage over traditional approaches
                      </li>
                    </ul>
                  </div>
                </div>

                <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-xl p-6">
                  <p className="text-foreground font-medium">
                    <strong>Important:</strong> These results come from providing genuine value, not from being more aggressive.
                    The 3-Touch Rule works because it builds relationships, not just conversions.
                  </p>
                </div>
              </div>

              {/* The Evolution */}
              <div className="bg-gradient-to-br from-primary-50 to-primary-100 dark:from-primary-900/20 dark:to-primary-800/20 border border-primary-200 dark:border-primary-800 rounded-2xl p-8 mb-12">
                <h2 className="text-2xl font-bold text-foreground mb-6 flex items-center gap-3">
                  <Brain className="w-6 h-6 text-primary-600" />
                  Join the Evolution
                </h2>

                <p className="text-muted-foreground mb-6 leading-relaxed">
                  This isn't just about better follow-ups. This is about evolving how you build relationships with prospects.
                  The businesses that master this approach don't just get more customers - they get better customers who value what they do.
                </p>

                <p className="text-muted-foreground mb-6 leading-relaxed">
                  While your competitors are still sending "just checking in" messages, you'll be building genuine connections
                  that turn prospects into advocates.
                </p>

                <p className="text-muted-foreground mb-8 leading-relaxed">
                  The question isn't whether this approach will become standard in your industry.
                  It's whether you'll lead this evolution or be forced to catch up later.
                </p>

                <div className="text-center">
                  <Link
                    href="/demo"
                    className="inline-flex items-center gap-2 bg-primary-600 hover:bg-primary-700 text-white px-8 py-4 rounded-xl font-semibold transition-all duration-300 hover:shadow-lg hover:shadow-primary-500/25"
                  >
                    Discover How to Automate This
                    <ArrowRight className="w-5 h-5" />
                  </Link>
                  <p className="text-sm text-muted-foreground mt-4">
                    See how successful businesses implement the 3-Touch Rule automatically
                  </p>
                </div>
              </div>

              {/* Next Lesson Navigation */}
              <div className="bg-background rounded-3xl border border-border shadow-lg p-8">
                <h3 className="text-xl font-bold text-foreground mb-6 text-center">Continue Your Learning Journey</h3>

                <div className="grid md:grid-cols-2 gap-6">
                  <Link
                    href="/learn/fundamentals/chaos-to-control"
                    className="group bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950/30 dark:to-green-900/30 rounded-2xl p-6 border border-green-200/50 dark:border-green-800/50 hover:shadow-lg transition-all duration-300"
                  >
                    <div className="flex items-center gap-4 mb-4">
                      <div className="w-12 h-12 bg-green-100 dark:bg-green-900/50 rounded-xl flex items-center justify-center">
                        <ArrowRight className="w-6 h-6 text-green-600 group-hover:translate-x-1 transition-transform" />
                      </div>
                      <div>
                        <h4 className="font-bold text-foreground">Next Lesson</h4>
                        <p className="text-sm text-muted-foreground">Lesson 4 of 15</p>
                      </div>
                    </div>
                    <h5 className="font-semibold text-foreground mb-2">From Chaos to Control</h5>
                    <p className="text-sm text-muted-foreground">
                      How successful entrepreneurs transformed from working IN their business to working ON it
                    </p>
                  </Link>

                  <Link
                    href="/learn"
                    className="group bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-950/30 dark:to-gray-900/30 rounded-2xl p-6 border border-gray-200/50 dark:border-gray-800/50 hover:shadow-lg transition-all duration-300"
                  >
                    <div className="flex items-center gap-4 mb-4">
                      <div className="w-12 h-12 bg-gray-100 dark:bg-gray-900/50 rounded-xl flex items-center justify-center">
                        <ArrowLeft className="w-6 h-6 text-gray-600 group-hover:-translate-x-1 transition-transform" />
                      </div>
                      <div>
                        <h4 className="font-bold text-foreground">Academy</h4>
                        <p className="text-sm text-muted-foreground">All lessons</p>
                      </div>
                    </div>
                    <h5 className="font-semibold text-foreground mb-2">Back to Academy</h5>
                    <p className="text-sm text-muted-foreground">
                      View all lessons and track your progress
                    </p>
                  </Link>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>
    </div>
  )
}
