import React from 'react'
import { BlogPost } from '../types'

const BlogContent = () => {
  return (
    <div className="max-w-4xl mx-auto space-y-12">
      
      {/* Opening Hook */}
      <div className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 rounded-3xl p-8 border border-blue-200/50 dark:border-blue-800/30">
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6 text-center">
          "What time do you close?"
        </h2>
        <p className="text-lg text-gray-700 dark:text-gray-300 mb-4 text-center">
          You've answered this question 847 times this year. Along with "Do you have availability?" and "How much does it cost?" 
          The same questions. Every. Single. Day.
        </p>
        <p className="text-lg text-gray-700 dark:text-gray-300 text-center font-medium">
          What if you never had to answer them again?
        </p>
      </div>

      {/* The Problem */}
      <div>
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
          The Repetitive Question Nightmare
        </h2>
        <p className="text-lg text-gray-700 dark:text-gray-300 mb-8">
          Research shows that 73% of customer inquiries to UK small businesses are repetitive questions that could be answered automatically. Yet business owners spend 2-3 hours daily answering the same questions over and over.
        </p>

        <div className="bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800/30 rounded-2xl p-8 mb-8">
          <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6 text-center">
            The Top 10 Questions You're Tired of Answering
          </h3>
          
          <div className="grid md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <span className="text-gray-700 dark:text-gray-300">"What time do you open/close?"</span>
                  <span className="text-red-600 dark:text-red-400 font-bold">Daily</span>
                </div>
              </div>
              <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <span className="text-gray-700 dark:text-gray-300">"Do you have availability?"</span>
                  <span className="text-red-600 dark:text-red-400 font-bold">Daily</span>
                </div>
              </div>
              <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <span className="text-gray-700 dark:text-gray-300">"How much does [service] cost?"</span>
                  <span className="text-red-600 dark:text-red-400 font-bold">Daily</span>
                </div>
              </div>
              <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <span className="text-gray-700 dark:text-gray-300">"Where are you located?"</span>
                  <span className="text-orange-600 dark:text-orange-400 font-bold">Weekly</span>
                </div>
              </div>
              <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <span className="text-gray-700 dark:text-gray-300">"Do you offer [specific service]?"</span>
                  <span className="text-orange-600 dark:text-orange-400 font-bold">Weekly</span>
                </div>
              </div>
            </div>
            
            <div className="space-y-3">
              <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <span className="text-gray-700 dark:text-gray-300">"How long does it take?"</span>
                  <span className="text-orange-600 dark:text-orange-400 font-bold">Weekly</span>
                </div>
              </div>
              <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <span className="text-gray-700 dark:text-gray-300">"Do you take card payments?"</span>
                  <span className="text-yellow-600 dark:text-yellow-400 font-bold">Monthly</span>
                </div>
              </div>
              <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <span className="text-gray-700 dark:text-gray-300">"What's your cancellation policy?"</span>
                  <span className="text-yellow-600 dark:text-yellow-400 font-bold">Monthly</span>
                </div>
              </div>
              <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <span className="text-gray-700 dark:text-gray-300">"Do you have parking?"</span>
                  <span className="text-yellow-600 dark:text-yellow-400 font-bold">Monthly</span>
                </div>
              </div>
              <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <span className="text-gray-700 dark:text-gray-300">"Can I reschedule my appointment?"</span>
                  <span className="text-yellow-600 dark:text-yellow-400 font-bold">Monthly</span>
                </div>
              </div>
            </div>
          </div>
          
          <div className="mt-6 p-4 bg-red-100 dark:bg-red-900/30 rounded-xl text-center">
            <p className="text-red-800 dark:text-red-300 font-bold text-lg">
              Time spent answering these: 2-3 hours daily = 15+ hours weekly
            </p>
          </div>
        </div>
      </div>

      {/* The Solution */}
      <div>
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
          The Smart Customer Service System
        </h2>
        <p className="text-lg text-gray-700 dark:text-gray-300 mb-8">
          Here's how to automate 90% of customer inquiries while actually improving customer satisfaction.
        </p>

        <div className="grid md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 text-center hover:shadow-lg transition-shadow">
            <div className="w-12 h-12 bg-blue-600 text-white rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4">1</div>
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3">Smart FAQ System</h3>
            <p className="text-gray-600 dark:text-gray-400">Answers 80% of questions instantly</p>
          </div>
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 text-center hover:shadow-lg transition-shadow">
            <div className="w-12 h-12 bg-blue-600 text-white rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4">2</div>
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3">Chatbot Assistant</h3>
            <p className="text-gray-600 dark:text-gray-400">24/7 availability and booking</p>
          </div>
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 text-center hover:shadow-lg transition-shadow">
            <div className="w-12 h-12 bg-blue-600 text-white rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4">3</div>
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3">Auto-Responses</h3>
            <p className="text-gray-600 dark:text-gray-400">Instant replies to common queries</p>
          </div>
        </div>
      </div>

      {/* Implementation Guide */}
      <div>
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
          Step-by-Step Implementation Guide
        </h2>

        <div className="space-y-6">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700">
            <div className="flex items-start space-x-4">
              <div className="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0 mt-1">1</div>
              <div>
                <h4 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3">Create Your FAQ Database</h4>
                <p className="text-gray-700 dark:text-gray-300 mb-4">
                  Start by documenting every question you've been asked in the past month.
                </p>
                <div className="bg-green-50 dark:bg-green-950/20 rounded-xl p-4">
                  <h5 className="font-bold text-green-800 dark:text-green-300 mb-3">Essential FAQ Categories:</h5>
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <h6 className="font-semibold text-green-700 dark:text-green-400 mb-2">Basic Information:</h6>
                      <ul className="text-green-600 dark:text-green-500 text-sm space-y-1">
                        <li>• Opening hours</li>
                        <li>• Location and directions</li>
                        <li>• Contact information</li>
                        <li>• Parking availability</li>
                      </ul>
                    </div>
                    <div>
                      <h6 className="font-semibold text-green-700 dark:text-green-400 mb-2">Services & Pricing:</h6>
                      <ul className="text-green-600 dark:text-green-500 text-sm space-y-1">
                        <li>• Service descriptions</li>
                        <li>• Pricing information</li>
                        <li>• Duration of services</li>
                        <li>• Package deals</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700">
            <div className="flex items-start space-x-4">
              <div className="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0 mt-1">2</div>
              <div>
                <h4 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3">Set Up Automated Responses</h4>
                <p className="text-gray-700 dark:text-gray-300 mb-4">
                  Create instant responses for the most common questions.
                </p>
                <div className="grid md:grid-cols-2 gap-4">
                  <div className="bg-blue-50 dark:bg-blue-950/20 rounded-xl p-4">
                    <h5 className="font-bold text-blue-800 dark:text-blue-300 mb-2">📱 SMS Auto-Replies:</h5>
                    <div className="text-blue-700 dark:text-blue-400 text-sm space-y-2">
                      <p><strong>Trigger:</strong> "opening hours"</p>
                      <p><strong>Response:</strong> "We're open Mon-Fri 9am-6pm, Sat 9am-4pm. Book online: [link]"</p>
                    </div>
                  </div>
                  <div className="bg-purple-50 dark:bg-purple-950/20 rounded-xl p-4">
                    <h5 className="font-bold text-purple-800 dark:text-purple-300 mb-2">📧 Email Auto-Replies:</h5>
                    <div className="text-purple-700 dark:text-purple-400 text-sm space-y-2">
                      <p><strong>Trigger:</strong> "availability"</p>
                      <p><strong>Response:</strong> "Check real-time availability and book instantly: [booking link]"</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700">
            <div className="flex items-start space-x-4">
              <div className="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0 mt-1">3</div>
              <div>
                <h4 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3">Deploy Website Chatbot</h4>
                <p className="text-gray-700 dark:text-gray-300 mb-4">
                  Add a chatbot to your website that handles inquiries 24/7.
                </p>
                <div className="bg-yellow-50 dark:bg-yellow-950/20 rounded-xl p-4">
                  <h5 className="font-bold text-yellow-800 dark:text-yellow-300 mb-3">Chatbot Conversation Flow:</h5>
                  <div className="space-y-3">
                    <div className="flex items-start space-x-3">
                      <div className="w-6 h-6 bg-yellow-600 text-white rounded-full flex items-center justify-center text-xs font-bold flex-shrink-0 mt-1">1</div>
                      <p className="text-yellow-700 dark:text-yellow-400 text-sm">
                        <strong>Greeting:</strong> "Hi! I'm here to help. What can I assist you with today?"
                      </p>
                    </div>
                    <div className="flex items-start space-x-3">
                      <div className="w-6 h-6 bg-yellow-600 text-white rounded-full flex items-center justify-center text-xs font-bold flex-shrink-0 mt-1">2</div>
                      <p className="text-yellow-700 dark:text-yellow-400 text-sm">
                        <strong>Options:</strong> "Book Appointment | Check Prices | Opening Hours | Contact Us"
                      </p>
                    </div>
                    <div className="flex items-start space-x-3">
                      <div className="w-6 h-6 bg-yellow-600 text-white rounded-full flex items-center justify-center text-xs font-bold flex-shrink-0 mt-1">3</div>
                      <p className="text-yellow-700 dark:text-yellow-400 text-sm">
                        <strong>Fallback:</strong> "I'll connect you with our team. They'll respond within 2 hours."
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Ready-to-Use Templates */}
      <div>
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
          Ready-to-Use Response Templates
        </h2>
        <p className="text-lg text-gray-700 dark:text-gray-300 mb-8">
          Copy and paste these proven response templates into your automation system.
        </p>

        <div className="grid md:grid-cols-2 gap-6">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700">
            <h4 className="text-lg font-bold text-gray-900 dark:text-gray-100 mb-4">🕐 Opening Hours Template</h4>
            <div className="bg-gray-50 dark:bg-gray-900 rounded-xl p-4">
              <p className="text-gray-700 dark:text-gray-300 text-sm font-mono">
                "Thanks for your message! 🕐<br/><br/>
                Our opening hours are:<br/>
                Monday-Friday: 9am-6pm<br/>
                Saturday: 9am-4pm<br/>
                Sunday: Closed<br/><br/>
                Book your appointment online 24/7: [booking-link]<br/><br/>
                Need urgent help? Call us during opening hours: [phone]"
              </p>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700">
            <h4 className="text-lg font-bold text-gray-900 dark:text-gray-100 mb-4">💰 Pricing Template</h4>
            <div className="bg-gray-50 dark:bg-gray-900 rounded-xl p-4">
              <p className="text-gray-700 dark:text-gray-300 text-sm font-mono">
                "Hi! Thanks for your interest in our services! 💰<br/><br/>
                Our current prices are:<br/>
                • [Service 1]: £[price]<br/>
                • [Service 2]: £[price]<br/>
                • [Service 3]: £[price]<br/><br/>
                View full price list: [website-link]<br/>
                Book online: [booking-link]<br/><br/>
                Questions? We're here to help!"
              </p>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700">
            <h4 className="text-lg font-bold text-gray-900 dark:text-gray-100 mb-4">📍 Location Template</h4>
            <div className="bg-gray-50 dark:bg-gray-900 rounded-xl p-4">
              <p className="text-gray-700 dark:text-gray-300 text-sm font-mono">
                "We're located at: 📍<br/><br/>
                [Full Address]<br/>
                [City, Postcode]<br/><br/>
                🚗 Free parking available<br/>
                🚌 Bus routes: [numbers]<br/>
                🚇 Nearest station: [station name]<br/><br/>
                Get directions: [google-maps-link]<br/>
                Book your visit: [booking-link]"
              </p>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700">
            <h4 className="text-lg font-bold text-gray-900 dark:text-gray-100 mb-4">📅 Availability Template</h4>
            <div className="bg-gray-50 dark:bg-gray-900 rounded-xl p-4">
              <p className="text-gray-700 dark:text-gray-300 text-sm font-mono">
                "Great question! 📅<br/><br/>
                Check our real-time availability and book instantly:<br/>
                👉 [booking-link]<br/><br/>
                You'll see all available slots for the next 4 weeks.<br/><br/>
                Prefer to call? Ring us during opening hours:<br/>
                📞 [phone-number]<br/><br/>
                We typically have same-day availability!"
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Results */}
      <div>
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
          The Results: What You'll Achieve
        </h2>
        
        <div className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-950/20 dark:to-emerald-950/20 rounded-3xl p-8 border border-green-200/50 dark:border-green-800/30 text-center mb-8">
          <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6">
            After Implementing Customer Service Automation:
          </h3>
          
          <div className="grid md:grid-cols-3 gap-6 mb-6">
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
              <div className="text-4xl font-bold text-green-600 dark:text-green-400 mb-2">90%</div>
              <div className="text-gray-700 dark:text-gray-300">Questions answered automatically</div>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
              <div className="text-4xl font-bold text-green-600 dark:text-green-400 mb-2">15 hrs</div>
              <div className="text-gray-700 dark:text-gray-300">Weekly time saved</div>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
              <div className="text-4xl font-bold text-green-600 dark:text-green-400 mb-2">24/7</div>
              <div className="text-gray-700 dark:text-gray-300">Customer support availability</div>
            </div>
          </div>
          
          <p className="text-xl font-bold text-gray-900 dark:text-gray-100">
            Never answer "What time do you close?" again.
          </p>
        </div>

        <div className="text-center">
          <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4">
            Your Customers Get Better Service. You Get Your Life Back.
          </h3>
          <p className="text-lg text-gray-700 dark:text-gray-300">
            Instant responses, 24/7 availability, and consistent information - while you focus on what actually grows your business.
          </p>
        </div>
      </div>

    </div>
  )
}

export const customerServicePost: BlogPost = {
  id: '6',
  slug: 'customer-service-automation',
  title: 'Never Answer "What Time Do You Close?" Again: The Complete Customer Service Automation Guide',
  excerpt: '73% of customer inquiries are repetitive questions. Here\'s how to automate 90% of customer service while improving satisfaction and saving 15+ hours weekly.',
  content: <BlogContent />,
  author: {
    name: 'Chedi Rach',
    role: 'Business Automation Specialist',
    avatar: '/logo/genlogic-icon-only.svg',
    bio: 'Chedi helps UK businesses automate their customer service to provide 24/7 support while reducing workload.'
  },
  publishedAt: '2025-07-04',
  readingTime: '8 min read',
  category: 'Customer Service',
  tags: ['customer-service', 'automation', 'chatbots', 'efficiency'],
  featured: false,
  image: '/blog-images/customer-service-automation.webp',
  views: 2156,
  likes: 94
}
