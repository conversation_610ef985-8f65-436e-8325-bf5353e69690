# Vercel Hobby Plan - Redirect Monitoring Setup

## 🎯 **What Works on Vercel Hobby Plan**

### ✅ **Fully Compatible**
- **Redirect Functionality**: All redirects work perfectly
- **Console Logging**: Visible in Vercel function logs
- **Validation Scripts**: Run locally for pre-deployment checks
- **Basic Health Monitoring**: API endpoints for status checks

### ⚠️ **Limited (Serverless Constraints)**
- **In-Memory Stats**: Reset on each function invocation
- **Persistent Logging**: Requires external services

## 🛠️ **Setup Options**

### **Option 1: Basic Monitoring (Free)**
Works out of the box with Vercel hobby plan:

```bash
# Check redirect health
curl https://your-domain.vercel.app/api/redirect-monitor?type=health

# View redirect stats
curl https://your-domain.vercel.app/api/redirect-monitor?type=stats

# Validate configuration
curl https://your-domain.vercel.app/api/redirect-monitor?type=validation
```

**Console Logs**: View redirect activity in Vercel dashboard → Functions → View Function Logs

### **Option 2: Google Analytics 4 Integration (Free)**
Track redirects in Google Analytics:

1. **Get GA4 Measurement ID** (you already have this)
2. **Create GA4 API Secret**:
   - Go to GA4 → Admin → Data Streams → Your Stream
   - Measurement Protocol API secrets → Create
3. **Add to Vercel Environment Variables**:
   ```
   NEXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX
   GA4_API_SECRET=your_api_secret_here
   ```

### **Option 3: Webhook Integration (Free)**
Send redirect data to any webhook service:

1. **Choose a service** (examples):
   - [Webhook.site](https://webhook.site) (testing)
   - [Discord Webhook](https://discord.com/developers/docs/resources/webhook) (free)
   - [Slack Webhook](https://api.slack.com/messaging/webhooks) (free)
   - [Zapier Webhook](https://zapier.com/apps/webhook/integrations) (free tier)

2. **Add webhook URL to Vercel**:
   ```
   REDIRECT_WEBHOOK_URL=https://your-webhook-url.com/endpoint
   ```

### **Option 4: Advanced Monitoring (Paid Services)**
For production monitoring, consider:
- [Sentry](https://sentry.io) - Error tracking with custom events
- [LogRocket](https://logrocket.com) - Session replay with custom events
- [Mixpanel](https://mixpanel.com) - Event analytics
- [PostHog](https://posthog.com) - Product analytics

## 📊 **Monitoring Capabilities**

### **What You Can Monitor**

1. **Redirect Activity**:
   ```bash
   # View in Vercel function logs
   [REDIRECT] SERVICE: /services/lead-conversion → /services/business-automation
   [REDIRECT] COMMON: /automation → /services/business-automation
   [REDIRECT] TRAILING_SLASH: /about/ → /about
   ```

2. **Health Status**:
   ```json
   {
     "status": "healthy",
     "environment": "serverless",
     "totalRedirects": 19,
     "monitoring": {
       "console_logs": true,
       "ga4_tracking": true,
       "webhook_tracking": false
     }
   }
   ```

3. **Geographic Data** (if using GA4):
   - Country/region of redirect requests
   - User agent information
   - Timestamp data

### **What You Can't Monitor (Serverless Limitations)**
- Persistent redirect counts across function invocations
- Historical trend analysis (without external service)
- Real-time dashboards (without external service)

## 🚀 **Recommended Setup for Hobby Plan**

### **Minimal Setup (5 minutes)**
1. Deploy your current code - monitoring works out of the box
2. Check health: `curl https://your-domain.vercel.app/api/redirect-monitor?type=health`
3. View logs in Vercel dashboard when redirects occur

### **Enhanced Setup (15 minutes)**
1. Set up GA4 integration (if you want analytics)
2. Add environment variables to Vercel
3. Test with a redirect and check GA4 events

### **Production Setup (30 minutes)**
1. Choose external monitoring service
2. Set up webhook or API integration
3. Create alerts/dashboards in external service

## 🔧 **Testing Your Setup**

### **Test Redirects**
```bash
# Test service redirect
curl -I https://your-domain.vercel.app/services/lead-conversion

# Test common redirect  
curl -I https://your-domain.vercel.app/automation

# Test trailing slash
curl -I https://your-domain.vercel.app/about/
```

### **Check Monitoring**
```bash
# Health check
curl https://your-domain.vercel.app/api/redirect-monitor?type=health

# Stats
curl https://your-domain.vercel.app/api/redirect-monitor?type=stats

# Validation
curl https://your-domain.vercel.app/api/redirect-monitor?type=validation
```

## 📈 **Expected Results**

### **Immediate Benefits**
- All redirects work correctly on Vercel
- Console logs show redirect activity
- Health monitoring confirms system status
- Google Search Console errors should decrease

### **With GA4 Integration**
- Redirect events tracked in Google Analytics
- Geographic distribution of redirects
- User agent analysis
- Custom redirect reports

### **With Webhook Integration**
- Real-time notifications of redirect activity
- Integration with existing monitoring tools
- Custom alerting based on redirect patterns

## 🆘 **Troubleshooting**

### **Common Issues**
1. **No logs appearing**: Check Vercel function logs, not build logs
2. **GA4 not tracking**: Verify API secret and measurement ID
3. **Webhook not receiving**: Test webhook URL independently

### **Debugging Commands**
```bash
# Local validation
npm run validate:redirects

# Check environment variables
vercel env ls

# View function logs
vercel logs --follow
```

## 💡 **Pro Tips**

1. **Use validation script** before every deployment
2. **Monitor Vercel function logs** for redirect patterns
3. **Set up GA4** for free analytics if you're already using Google Analytics
4. **Consider webhook integration** for real-time monitoring
5. **Check Google Search Console** weekly for redirect error trends

Your redirect system is fully functional on Vercel hobby plan - the monitoring is just a bonus for insights and debugging!
