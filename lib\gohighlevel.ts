// GoHighLevel API Integration for GenLogic
// Handles form submissions with proper error handling and GDPR compliance

interface GHLContactData {
  firstName: string
  lastName?: string
  email: string
  phone?: string
  companyName?: string
  source: 'contact_form' | 'demo_booking' | 'newsletter'
  tags: string[]
  customFields: Record<string, any>
  notes?: string
}

interface GHLResponse {
  success: boolean
  contactId?: string
  error?: string
  message?: string
}

class GoHighLevelService {
  private apiKey: string
  private locationId: string
  private baseUrl: string

  constructor() {
    this.apiKey = process.env.GHL_API_KEY || ''
    this.locationId = process.env.GHL_LOCATION_ID || ''
    this.baseUrl = 'https://rest.gohighlevel.com/v1'

    if (!this.apiKey || !this.locationId) {
      console.warn('GoHighLevel API credentials not configured')
    }
  }

  /**
   * Create or update contact in GoHighLevel
   */
  async createContact(data: GHLContactData): Promise<GHLResponse> {
    try {
      if (!this.apiKey || !this.locationId) {
        throw new Error('GHL API credentials not configured')
      }

      // Prepare contact payload for GHL API
      const contactPayload = {
        firstName: data.firstName,
        lastName: data.lastName || '',
        email: data.email,
        phone: data.phone || '',
        companyName: data.companyName || '',
        source: data.source || 'website',
        tags: data.tags || [],
        customFields: data.customFields || {}
      }

      // Add notes if provided
      if (data.notes) {
        (contactPayload.customFields as any).form_message = data.notes
      }



      // Use GoHighLevel API 1.0 - correct endpoint and format
      const response = await fetch(`${this.baseUrl}/contacts/`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(contactPayload)
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.message || `GHL API Error: ${response.status} ${response.statusText}`)
      }

      // Trigger automation workflows based on source
      if (result.contact?.id) {
        await this.triggerWorkflow(result.contact.id, data.source)
      }

      return {
        success: true,
        contactId: result.contact?.id,
        message: 'Contact created successfully'
      }

    } catch (error) {
      console.error('GHL API Error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      }
    }
  }

  /**
   * Trigger specific automation workflows based on form source
   */
  private async triggerWorkflow(contactId: string, source: string): Promise<void> {
    try {
      const workflowIds = {
        contact_form: process.env.GHL_CONTACT_WORKFLOW_ID,
        demo_booking: process.env.GHL_DEMO_WORKFLOW_ID,
        newsletter: process.env.GHL_NEWSLETTER_WORKFLOW_ID
      }

      const workflowId = workflowIds[source as keyof typeof workflowIds]
      
      if (!workflowId) {
        return
      }

      await fetch(`${this.baseUrl}/workflows/${workflowId}/subscribe`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'Version': '2021-07-28'
        },
        body: JSON.stringify({
          contactId: contactId
        })
      })

    } catch (error) {
      console.error('Failed to trigger workflow:', error)
      // Don't throw here - contact creation was successful
    }
  }

  /**
   * Add tags to existing contact
   */
  async addTags(contactId: string, tags: string[]): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/contacts/${contactId}/tags`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'Version': '2021-07-28'
        },
        body: JSON.stringify({ tags })
      })

      return response.ok
    } catch (error) {
      console.error('Failed to add tags:', error)
      return false
    }
  }

  /**
   * Create opportunity/deal for demo bookings
   */
  async createOpportunity(contactId: string, data: {
    title: string
    value: number
    source: string
    stage: string
  }): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/opportunities/`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'Version': '2021-07-28'
        },
        body: JSON.stringify({
          contactId,
          locationId: this.locationId,
          name: data.title,
          monetaryValue: data.value,
          source: data.source,
          status: data.stage
        })
      })

      return response.ok
    } catch (error) {
      console.error('Failed to create opportunity:', error)
      return false
    }
  }
}

// Export singleton instance
export const ghlService = new GoHighLevelService()

// Tag generation utilities
export function generateFormSourceTag(source: 'contact_form' | 'demo_booking'): string {
  const sourceMap = {
    'contact_form': 'contact_page',
    'demo_booking': 'demo_page'
  }
  return sourceMap[source]
}

export function generateSubjectTag(subject: string): string {
  if (!subject) return 'subject_unknown'

  // Normalize subject to tag format
  const normalized = subject.toLowerCase()
    .replace(/[^a-z0-9\s]/g, '') // Remove special characters
    .replace(/\s+/g, '_') // Replace spaces with underscores
    .substring(0, 30) // Limit length

  return `subject_${normalized}`
}

export function generateBusinessTypeTag(businessType: string): string {
  if (!businessType) return 'business_unknown'

  // Normalize business type to tag format
  const normalized = businessType.toLowerCase()
    .replace(/[^a-z0-9\s]/g, '') // Remove special characters
    .replace(/\s+/g, '_') // Replace spaces with underscores
    .substring(0, 30) // Limit length

  return `business_${normalized}`
}

export function generateIntentTag(source: 'contact_form' | 'demo_booking'): string[] {
  if (source === 'demo_booking') {
    return ['high_intent', 'demo_qualified', 'sales_ready']
  } else {
    return ['general_inquiry', 'nurture_required']
  }
}

export function generateLeadScoreTag(score: number): string {
  if (score >= 80) return 'score_hot'
  if (score >= 60) return 'score_warm'
  if (score >= 40) return 'score_qualified'
  return 'score_cold'
}

// Helper function to format form data for GHL with enhanced tagging
export function formatContactData(formData: any, source: 'contact_form' | 'demo_booking'): GHLContactData {
  const [firstName, ...lastNameParts] = (formData.name || '').split(' ')
  const lastName = lastNameParts.join(' ')

  // Calculate lead score based on form type and selections
  let leadScore = source === 'demo_booking' ? 80 : 40

  // Boost score for specific business types (high-value segments)
  const highValueBusinessTypes = ['healthcare', 'professional', 'automotive']
  if (highValueBusinessTypes.includes(formData.businessType)) {
    leadScore += 10
  }

  // Generate dynamic tags
  const dynamicTags = [
    'genlogic_website',
    generateFormSourceTag(source),
    ...generateIntentTag(source),
    generateLeadScoreTag(leadScore)
  ]

  // Add subject-specific tags for contact form
  if (source === 'contact_form' && formData.subject) {
    dynamicTags.push(generateSubjectTag(formData.subject))
  }

  // Add business type tags for demo form
  if (source === 'demo_booking' && formData.businessType) {
    dynamicTags.push(generateBusinessTypeTag(formData.businessType))
  }

  // Add urgency and conversion intent tags based on message content
  if (formData.message) {
    const urgentKeywords = ['urgent', 'asap', 'immediately', 'today', 'emergency']
    const conversionKeywords = ['sales', 'revenue', 'conversion', 'customers', 'prospects', 'grow', 'increase']
    const consultationKeywords = ['consultation', 'strategy', 'advice', 'help', 'optimize']

    const hasUrgentKeywords = urgentKeywords.some(keyword =>
      formData.message.toLowerCase().includes(keyword)
    )
    const hasConversionKeywords = conversionKeywords.some(keyword =>
      formData.message.toLowerCase().includes(keyword)
    )
    const hasConsultationKeywords = consultationKeywords.some(keyword =>
      formData.message.toLowerCase().includes(keyword)
    )

    if (hasUrgentKeywords) {
      dynamicTags.push('urgent_inquiry')
      leadScore += 15
    }
    if (hasConversionKeywords) {
      dynamicTags.push('conversion_focused', 'sales_intent')
      leadScore += 10
    }
    if (hasConsultationKeywords) {
      dynamicTags.push('consultation_request', 'advice_seeking')
      leadScore += 8
    }
  }

  const baseData: GHLContactData = {
    firstName: firstName || 'Unknown',
    lastName: lastName || '',
    email: formData.email || '',
    phone: formData.phone || '',
    companyName: formData.business || '',
    source,
    tags: dynamicTags,
    customFields: {
      businessType: formData.businessType || '',
      challenges: formData.message || '',
      subject: formData.subject || '',
      leadMagnet: source === 'demo_booking' ? 'Free Demo' : 'Contact Form',
      formSource: generateFormSourceTag(source),
      leadScore: leadScore,
      submissionDate: new Date().toISOString(),
      utm_source: 'website',
      utm_medium: 'organic',
      utm_campaign: 'genlogic_website'
    }
  }

  // Add source-specific custom fields
  if (source === 'demo_booking') {
    baseData.customFields.demo_requested = true
    baseData.customFields.opportunity_value = 588 // £49/month * 12 months
    baseData.customFields.sales_stage = 'demo_requested'
  } else {
    baseData.customFields.inquiry_type = formData.subject || 'general'
    baseData.customFields.sales_stage = 'inquiry_received'
  }

  // Add notes with structured information
  if (formData.message) {
    const notesParts = [
      `Form submission from GenLogic website (${generateFormSourceTag(source)})`,
      '',
      `Business: ${formData.business || 'Not specified'}`,
      `Type: ${formData.businessType || 'Not specified'}`,
      source === 'contact_form' ? `Subject: ${formData.subject || 'Not specified'}` : '',
      '',
      'Message:',
      formData.message
    ].filter(Boolean)

    baseData.notes = notesParts.join('\n')
  }

  return baseData
}

// GDPR compliance helper
export function validateGDPRConsent(formData: any): boolean {
  // Ensure GDPR consent is provided for UK businesses
  // Handle both boolean and string values
  const gdprConsent = formData.gdprConsent === true || formData.gdprConsent === 'true'
  const marketingConsent = formData.marketingConsent === true || formData.marketingConsent === 'true'

  return gdprConsent || marketingConsent
}
