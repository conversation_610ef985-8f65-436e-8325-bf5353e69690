'use client'

import { motion } from 'framer-motion'
import { Eye, Heart, Calendar, Clock } from 'lucide-react'
import Link from 'next/link'
import { BlogPost } from '@/app/blog/types'
import { useFavoritesContext } from '../../contexts/FavoritesContext'

interface BlogCardProps {
  post: BlogPost
  index?: number
  featured?: boolean
}

export default function BlogCard({ post, index = 0, featured = false }: BlogCardProps) {
  const { getFavoriteCount, getViewCount, loading } = useFavoritesContext()

  const currentLikes = getFavoriteCount(post.slug)
  const currentViews = getViewCount(post.slug) // Remove fallback to post.views to avoid conflicts

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    })
  }

  const formatViews = (views: number) => {
    return views > 1000 ? `${(views / 1000).toFixed(1)}k` : views.toString()
  }

  return (
    <motion.article
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: index * 0.1 }}
      viewport={{ once: true }}
      className="group"
    >
      <Link href={`/blog/${post.slug}`}>
        <div className="bg-background rounded-3xl overflow-hidden shadow-lg border border-primary-200/20 dark:border-primary-800/20 hover:shadow-xl transition-all duration-300 h-full">
          <div className="relative h-48 overflow-hidden">
            {featured && (
              <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent z-10"></div>
            )}
            <div className="w-full h-full bg-gradient-to-br from-primary-100 to-accent-100 dark:from-primary-900 dark:to-accent-900 flex items-center justify-center">
              <div className="text-center p-6">
                <div className="w-16 h-16 mx-auto mb-4 bg-primary-500 rounded-2xl flex items-center justify-center">
                  <Heart className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-lg font-bold text-primary-900 dark:text-primary-100 mb-2">
                  {post.category}
                </h3>
                <p className="text-sm text-primary-700 dark:text-primary-300">
                  {post.readingTime}
                </p>
              </div>
            </div>
            
            {featured && (
              <div className="absolute top-4 left-4 z-20">
                <span className="px-3 py-1 bg-accent-500 text-white text-xs font-semibold rounded-full">
                  Featured
                </span>
              </div>
            )}
          </div>

          <div className="p-6">
            <div className="flex items-center gap-2 mb-3">
              <span className="px-2 py-1 bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-400 rounded-md text-xs font-medium">
                {post.category}
              </span>
              <div className="flex items-center text-xs text-muted-foreground">
                <Calendar className="w-3 h-3 mr-1" />
                {formatDate(post.publishedAt)}
              </div>
            </div>

            <h3 className="text-xl font-bold text-foreground mb-3 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors duration-300 line-clamp-2">
              {post.title}
            </h3>

            <p className="text-muted-foreground text-sm mb-4 line-clamp-3">
              {post.excerpt}
            </p>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="w-8 h-8 rounded-full bg-primary-100 dark:bg-primary-900/30 flex items-center justify-center">
                  <span className="text-xs font-semibold text-primary-700 dark:text-primary-400">
                    {post.author.name.split(' ').map(n => n[0]).join('')}
                  </span>
                </div>
                <div>
                  <p className="text-xs font-medium text-foreground">{post.author.name}</p>
                  <p className="text-xs text-muted-foreground">{post.author.role}</p>
                </div>
              </div>

              <div className="flex items-center gap-3 text-xs text-muted-foreground">
                <div className="flex items-center">
                  <Eye className="w-3 h-3 mr-1" />
                  {formatViews(currentViews)}
                </div>
                <div className="flex items-center">
                  <Heart className="w-3 h-3 mr-1" />
                  {loading ? '...' : currentLikes}
                </div>
              </div>
            </div>
          </div>
        </div>
      </Link>
    </motion.article>
  )
}
