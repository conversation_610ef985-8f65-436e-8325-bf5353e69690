import { Metadata } from 'next'
import { SchemaMarkup, BreadcrumbSchema } from '@/components/seo/SchemaMarkup'
import SolutionsClient from './SolutionsClient'

export const metadata: Metadata = {
  title: 'GoHighLevel Automation Solutions for UK Local Businesses - GenLogic',
  description: 'Discover how successful UK businesses are quietly transforming their operations with white-label GoHighLevel automation. Join 500+ businesses converting 89% of prospects into customers.',
  keywords: 'GoHighLevel automation UK, business automation solutions, local business automation, customer conversion system, booking automation, SMS automation, email follow-up',
  openGraph: {
    title: 'GoHighLevel Automation Solutions for UK Local Businesses - GenLogic',
    description: 'Discover how successful UK businesses are quietly transforming their operations with white-label GoHighLevel automation.',
    url: 'https://genlogic.io/solutions',
    type: 'website',
    images: [
      {
        url: 'https://genlogic.io/og-solutions.jpg',
        width: 1200,
        height: 630,
        alt: 'GenLogic GoHighLevel Automation Solutions',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'GoHighLevel Automation Solutions for UK Local Businesses - GenLogic',
    description: 'Discover how successful UK businesses are quietly transforming their operations with white-label GoHighLevel automation.',
    images: ['https://genlogic.io/og-solutions.jpg'],
  },
  alternates: {
    canonical: 'https://genlogic.io/solutions',
  },
}

const solutionsServiceData = {
  name: "GoHighLevel Automation Solutions",
  description: "White-label GoHighLevel automation solutions for UK local businesses. Booking automation, customer conversion, and revenue optimization.",
  serviceType: "Business Automation",
  offers: {
    priceCurrency: "GBP",
    description: "Complete automation solutions with GoHighLevel technology for UK local businesses"
  }
}

const breadcrumbItems = [
  { name: 'Home', url: 'https://genlogic.io' },
  { name: 'Solutions', url: 'https://genlogic.io/solutions' }
]

export default function SolutionsPage() {
  return (
    <>
      <SchemaMarkup
        type="service"
        data={solutionsServiceData}
      />
      <SchemaMarkup type="softwareApplication" />
      <SchemaMarkup type="faq" data={{
        faqs: [
          {
            question: "What automation solutions does GenLogic offer?",
            answer: "GenLogic provides comprehensive GoHighLevel automation solutions including smart booking automation, intelligent follow-up sequences, no-show prevention systems, customer retention automation, revenue analytics, and reputation management systems."
          },
          {
            question: "How do these automation solutions help UK businesses?",
            answer: "Our automation solutions help UK businesses convert 89% of prospects into customers, reduce no-shows by 85%, save 20+ hours weekly, and increase revenue by 25%. Businesses reclaim their evenings and weekends while growing their customer base."
          },
          {
            question: "Are these solutions suitable for local UK businesses?",
            answer: "Yes, our solutions are specifically designed for UK local businesses including window installation, home improvement, healthcare clinics, hair salons, fitness studios, automotive services, and professional services. Over 500+ UK businesses already use our automation."
          },
          {
            question: "How quickly can automation solutions be implemented?",
            answer: "Most UK businesses are fully automated within 2-3 days. We handle the complete setup including GoHighLevel configuration, automation workflows, SMS/email templates, and staff training to ensure smooth implementation."
          },
          {
            question: "What makes GenLogic's automation different from competitors?",
            answer: "GenLogic uses white-label GoHighLevel technology with psychological engineering principles to create automation that feels personal and human. Our solutions are specifically designed for UK business owners who want to stop working late and reclaim their time."
          }
        ]
      }} />
      <BreadcrumbSchema items={breadcrumbItems} />

      <SolutionsClient />
    </>
  )
}
      benefit: 'Capture 40% more bookings that would otherwise go to competitors',
      psychologyHook: 'Never lose another customer to timing',
      features: ['24/7 online booking', 'Instant confirmations', 'Calendar sync', 'Automated reminders']
    },
    {
      icon: <MessageSquare className="w-8 h-8" />,
      title: 'Intelligent Follow-Up Sequences',
      description: 'Smart automation nurtures every prospect until they\'re ready to buy. Turn cold leads into hot customers without lifting a finger.',
      benefit: 'Convert 89% of prospects who would normally slip away',
      psychologyHook: 'What successful businesses do differently',
      features: ['SMS automation', 'Email sequences', 'Personalized messaging', 'Behavioral triggers']
    },
    {
      icon: <Shield className="w-8 h-8" />,
      title: 'No-Show Prevention System',
      description: 'Automated reminder sequences reduce no-shows by 85%. Your time slots stay filled, your revenue stays protected.',
      benefit: 'Save £2,400+ monthly from eliminated no-shows',
      psychologyHook: 'Stop losing money to empty appointments',
      features: ['Multi-channel reminders', 'Confirmation requests', 'Rescheduling automation', 'Last-minute notifications']
    },
    {
      icon: <Users className="w-8 h-8" />,
      title: 'Customer Retention Automation',
      description: 'Turn one-time buyers into loyal customers. Automated thank you messages, review requests, and re-booking invitations build lasting relationships.',
      benefit: 'Increase repeat business by 40% and customer lifetime value',
      psychologyHook: 'The secret to effortless customer loyalty',
      features: ['Thank you automation', 'Review generation', 'Loyalty programs', 'Win-back campaigns']
    },
    {
      icon: <BarChart3 className="w-8 h-8" />,
      title: 'Revenue Analytics & Insights',
      description: 'See exactly how many prospects convert into customers. Identify revenue opportunities and optimize your sales process for maximum growth.',
      benefit: 'Optimize conversion rates and increase revenue by 25%',
      psychologyHook: 'Know what successful owners track',
      features: ['Conversion tracking', 'Revenue reporting', 'Customer insights', 'Performance optimization']
    },
    {
      icon: <Target className="w-8 h-8" />,
      title: 'Reputation Management System',
      description: 'Automatically collect positive reviews and manage your online reputation. Build trust that converts more prospects into customers.',
      benefit: 'Increase online credibility and attract 30% more customers',
      psychologyHook: 'How thriving businesses build unstoppable reputations',
      features: ['Review automation', 'Reputation monitoring', 'Response management', 'Rating optimization']
    }
  ]

  return (
    <>
      <SchemaMarkup
        type="service"
        data={{
          name: 'GoHighLevel Automation Solutions',
          description: 'White-label GoHighLevel automation solutions for UK local businesses. Booking automation, customer conversion, and revenue optimization.'
        }}
      />
      <SchemaMarkup type="softwareApplication" />
      <SchemaMarkup type="faq" data={{
        faqs: [
          {
            question: "What automation solutions does GenLogic offer?",
            answer: "GenLogic provides comprehensive GoHighLevel automation solutions including smart booking automation, intelligent follow-up sequences, no-show prevention systems, customer retention automation, revenue analytics, and reputation management systems."
          },
          {
            question: "How do these automation solutions help UK businesses?",
            answer: "Our automation solutions help UK businesses convert 89% of prospects into customers, reduce no-shows by 85%, save 20+ hours weekly, and increase revenue by 25%. Businesses reclaim their evenings and weekends while growing their customer base."
          },
          {
            question: "Are these solutions suitable for local UK businesses?",
            answer: "Yes, our solutions are specifically designed for UK local businesses including window installation, home improvement, healthcare clinics, hair salons, fitness studios, automotive services, and professional services. Over 500+ UK businesses already use our automation."
          },
          {
            question: "How quickly can automation solutions be implemented?",
            answer: "Most UK businesses are fully automated within 2-3 days. We handle the complete setup including GoHighLevel configuration, automation workflows, SMS/email templates, and staff training to ensure smooth implementation."
          },
          {
            question: "What makes GenLogic's automation different from competitors?",
            answer: "GenLogic uses white-label GoHighLevel technology with psychological engineering principles to create automation that feels personal and human. Our solutions are specifically designed for UK business owners who want to stop working late and reclaim their time."
          }
        ]
      }} />
      <BreadcrumbSchema items={breadcrumbItems} />

      <div className="relative w-full overflow-hidden">
        {/* Hero Section - Psychological Engineering Framework */}
        <section className="header-spacing py-20 relative">
          <div className="container relative z-10 mx-auto px-4 md:px-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, ease: 'easeOut' }}
              className="mx-auto max-w-4xl text-center"
            >
              <div className="bg-background border border-primary-200/20 dark:border-primary-800/20 rounded-3xl p-12 shadow-lg backdrop-blur-sm">
                {/* Curiosity Hook */}
                <div className="bg-primary-50 dark:bg-primary-950/30 border border-primary-200/30 dark:border-primary-800/30 rounded-2xl p-4 mb-8">
                  <p className="text-primary-700 dark:text-primary-400 font-medium">
                    <Lightbulb className="w-5 h-5 inline mr-2" />
                    Something's changing across UK businesses...
                  </p>
                </div>

                <h1 className="text-4xl font-bold text-foreground sm:text-5xl md:text-6xl mb-6">
                  The <span className="text-primary-700 dark:text-primary-400">Automation Revolution</span> Smart Business Owners Are Quietly Joining
                </h1>

                <p className="text-xl text-muted-foreground leading-relaxed mb-6">
                  <strong className="text-foreground">While you were working late, something shifted.</strong> Forward-thinking UK business owners discovered how to automate their operations and convert 89% of prospects into customers. The question isn't IF you'll join them—it's WHEN.
                </p>
                
                {/* Social Proof */}
                <div className="grid md:grid-cols-3 gap-6 mb-8">
                  <div className="bg-green-50 dark:bg-green-950/30 border border-green-200/30 dark:border-green-800/30 rounded-2xl p-4">
                    <div className="text-2xl font-bold text-green-700 dark:text-green-400 mb-1">
                      500+
                    </div>
                    <p className="text-sm text-muted-foreground">UK businesses already transformed</p>
                  </div>

                  <div className="bg-green-50 dark:bg-green-950/30 border border-green-200/30 dark:border-green-800/30 rounded-2xl p-4">
                    <div className="text-2xl font-bold text-green-700 dark:text-green-400 mb-1">
                      89%
                    </div>
                    <p className="text-sm text-muted-foreground">Prospect conversion rate achieved</p>
                  </div>

                  <div className="bg-green-50 dark:bg-green-950/30 border border-green-200/30 dark:border-green-800/30 rounded-2xl p-4">
                    <div className="text-2xl font-bold text-green-700 dark:text-green-400 mb-1">
                      20+
                    </div>
                    <p className="text-sm text-muted-foreground">Hours saved weekly per business</p>
                  </div>
                </div>
                
                {/* Tribal Identity Building */}
                <div className="bg-accent-50 dark:bg-accent-950/30 border border-accent-200/30 dark:border-accent-800/30 rounded-2xl p-6 mb-8">
                  <p className="text-foreground font-medium text-lg">
                    <strong>The businesses that thrive all share one thing:</strong> They stopped doing manually what technology can do automatically. They joined the automation evolution while their competitors stayed stuck in the past.
                  </p>
                </div>
                
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Link 
                    href="/demo" 
                    className="inline-flex items-center justify-center px-8 py-4 bg-primary-700 hover:bg-primary-800 dark:bg-primary-600 dark:hover:bg-primary-700 text-white rounded-2xl transition-all duration-300 font-medium shadow-lg hover:shadow-xl text-lg"
                  >
                    Join the Evolution
                    <Rocket className="w-5 h-5 ml-2" />
                  </Link>
                  
                  <Link 
                    href="#solutions" 
                    className="inline-flex items-center justify-center px-8 py-4 border-2 border-primary-200 dark:border-primary-800 text-primary-700 dark:text-primary-400 rounded-2xl hover:border-primary-300 dark:hover:border-primary-700 hover:bg-primary-50 dark:hover:bg-primary-900/20 transition-all duration-300 font-medium text-lg"
                  >
                    Discover What They Know
                  </Link>
                </div>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Pattern Recognition Section - Framework Step 2 */}
        <section className="py-20" id="solutions">
          <div className="container mx-auto px-4 md:px-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, ease: 'easeOut' }}
              viewport={{ once: true }}
              className="text-center mb-12"
            >
              <div className="bg-primary-50 dark:bg-primary-950/30 border border-primary-200/30 dark:border-primary-800/30 rounded-2xl p-4 mb-6 max-w-2xl mx-auto">
                <p className="text-primary-700 dark:text-primary-400 font-medium">
                  A pattern is emerging among successful owners...
                </p>
              </div>

              <h2 className="text-3xl font-bold text-foreground sm:text-4xl mb-6">
                What <span className="text-primary-700 dark:text-primary-400">Thriving Businesses</span> Do Differently
              </h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                <strong className="text-foreground">The companies that seem effortless all share the same automation systems.</strong> While struggling businesses work harder, successful ones work smarter with these proven solutions.
              </p>
            </motion.div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
              {automationSolutions.map((solution, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="bg-background border border-primary-200/20 dark:border-primary-800/20 rounded-3xl p-8 shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  {/* Psychology Hook */}
                  <div className="bg-accent-50 dark:bg-accent-950/30 border border-accent-200/30 dark:border-accent-800/30 rounded-xl p-3 mb-6">
                    <p className="text-accent-700 dark:text-accent-400 font-medium text-sm">
                      {solution.psychologyHook}
                    </p>
                  </div>

                  <div className="flex items-start gap-4 mb-6">
                    <div className="flex-shrink-0 w-12 h-12 bg-primary-100 dark:bg-primary-900/30 rounded-2xl flex items-center justify-center text-primary-700 dark:text-primary-400">
                      {solution.icon}
                    </div>
                    <div className="flex-1">
                      <h3 className="text-xl font-semibold text-foreground mb-3">
                        {solution.title}
                      </h3>
                      <p className="text-muted-foreground mb-4 leading-relaxed">
                        {solution.description}
                      </p>
                    </div>
                  </div>

                  {/* Features List */}
                  <div className="space-y-2 mb-6">
                    {solution.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-primary-700 dark:text-primary-400 flex-shrink-0" />
                        <span className="text-sm text-muted-foreground">{feature}</span>
                      </div>
                    ))}
                  </div>

                  {/* Benefit */}
                  <div className="bg-primary-50 dark:bg-primary-950/30 border border-primary-200/30 dark:border-primary-800/30 rounded-xl p-4">
                    <p className="text-sm text-foreground font-medium">
                      <TrendingUp className="w-4 h-4 text-primary-700 dark:text-primary-400 inline mr-2" />
                      {solution.benefit}
                    </p>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Transformation Stories - Social Proof */}
        <section className="py-20">
          <div className="container mx-auto px-4 md:px-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, ease: 'easeOut' }}
              viewport={{ once: true }}
              className="text-center mb-12"
            >
              <div className="bg-green-50 dark:bg-green-950/30 border border-green-200/30 dark:border-green-800/30 rounded-2xl p-4 mb-6 max-w-2xl mx-auto">
                <p className="text-green-700 dark:text-green-400 font-medium">
                  <Star className="w-5 h-5 inline mr-2" />
                  Real transformations from UK business owners
                </p>
              </div>

              <h2 className="text-3xl font-bold text-foreground sm:text-4xl mb-6">
                From <span className="text-red-600 dark:text-red-400">Working Late</span> to <span className="text-green-700 dark:text-green-400">Working Smart</span>
              </h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                <strong className="text-foreground">These UK business owners made the shift.</strong> See how automation transformed their operations and gave them their lives back.
              </p>
            </motion.div>

            <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
              {[
                {
                  industry: 'Window Installation',
                  challenge: 'Working until 9pm calling no-show customers',
                  solution: 'Automated reminders and follow-ups',
                  result: '85% reduction in no-shows, 3 hours saved daily',
                  quote: 'I finally have my evenings back. The automation handles everything.',
                  name: 'Anthony',
                  company: 'Universal Windows'
                },
                {
                  industry: 'Home Improvement',
                  challenge: 'Losing prospects to competitors during busy periods',
                  solution: '24/7 booking automation and instant responses',
                  result: '40% more bookings, £15,000 additional monthly revenue',
                  quote: 'Customers book themselves even when I\'m on-site with other clients.',
                  name: 'Danny',
                  company: 'First Impression Driveways'
                },
                {
                  industry: 'Thermal Insulation',
                  challenge: 'Manual appointment reminders taking hours daily',
                  solution: 'Smart reminder sequences and confirmation automation',
                  result: '20 hours saved weekly, 95% appointment attendance',
                  quote: 'The system runs itself. I focus on customers, not admin work.',
                  name: 'Shaun',
                  company: 'Barrier Therm'
                }
              ].map((story, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="bg-background border border-primary-200/20 dark:border-primary-800/20 rounded-3xl p-8 shadow-lg"
                >
                  <div className="text-center mb-6">
                    <div className="bg-primary-100 dark:bg-primary-900/30 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                      <Users className="w-8 h-8 text-primary-700 dark:text-primary-400" />
                    </div>
                    <h3 className="text-lg font-semibold text-foreground mb-2">{story.industry}</h3>
                    <p className="text-sm text-muted-foreground">{story.name} - {story.company}</p>
                  </div>

                  <div className="space-y-4">
                    <div className="bg-red-50 dark:bg-red-950/30 border border-red-200/30 dark:border-red-800/30 rounded-xl p-4">
                      <p className="text-sm text-red-700 dark:text-red-400 font-medium mb-1">Before:</p>
                      <p className="text-sm text-muted-foreground">{story.challenge}</p>
                    </div>

                    <div className="bg-blue-50 dark:bg-blue-950/30 border border-blue-200/30 dark:border-blue-800/30 rounded-xl p-4">
                      <p className="text-sm text-blue-700 dark:text-blue-400 font-medium mb-1">Solution:</p>
                      <p className="text-sm text-muted-foreground">{story.solution}</p>
                    </div>

                    <div className="bg-green-50 dark:bg-green-950/30 border border-green-200/30 dark:border-green-800/30 rounded-xl p-4">
                      <p className="text-sm text-green-700 dark:text-green-400 font-medium mb-1">Result:</p>
                      <p className="text-sm text-muted-foreground">{story.result}</p>
                    </div>

                    <blockquote className="bg-primary-50 dark:bg-primary-950/30 border border-primary-200/30 dark:border-primary-800/30 rounded-xl p-4 italic">
                      <p className="text-sm text-foreground">"{story.quote}"</p>
                    </blockquote>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Inevitability Section - Framework Step 3 */}
        <section className="py-20">
          <div className="container mx-auto px-4 md:px-6">
            <div className="bg-navy-900 dark:bg-navy-950 text-white rounded-3xl p-12 shadow-2xl max-w-4xl mx-auto text-center">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, ease: 'easeOut' }}
                viewport={{ once: true }}
              >
                <div className="bg-accent-500/20 border border-accent-400/30 rounded-2xl p-4 mb-8">
                  <p className="text-accent-300 font-medium">
                    <Clock className="w-5 h-5 inline mr-2" />
                    This isn't about technology—it's about evolution
                  </p>
                </div>

                <h2 className="text-3xl font-bold sm:text-4xl mb-6">
                  The Shift Is <span className="text-accent-400">Happening</span> Whether You Participate or Not
                </h2>

                <p className="text-xl text-gray-300 leading-relaxed mb-8">
                  <strong className="text-white">Automation isn't the future—it's the present.</strong> Your competitors are already implementing these systems. You can lead this change or be forced to follow. The choice is yours, but the timeline isn't.
                </p>

                <div className="grid md:grid-cols-2 gap-6 mb-8">
                  <div className="bg-red-900/30 border border-red-700/30 rounded-2xl p-6">
                    <h3 className="text-lg font-semibold text-red-300 mb-3">Businesses That Wait</h3>
                    <ul className="space-y-2 text-sm text-gray-300">
                      <li>• Continue working late every night</li>
                      <li>• Lose customers to automated competitors</li>
                      <li>• Struggle with manual processes</li>
                      <li>• Watch revenue stagnate</li>
                    </ul>
                  </div>

                  <div className="bg-green-900/30 border border-green-700/30 rounded-2xl p-6">
                    <h3 className="text-lg font-semibold text-green-300 mb-3">Businesses That Act Now</h3>
                    <ul className="space-y-2 text-sm text-gray-300">
                      <li>• Reclaim their evenings and weekends</li>
                      <li>• Convert 89% of prospects automatically</li>
                      <li>• Scale without hiring more staff</li>
                      <li>• Increase revenue by 25%+</li>
                    </ul>
                  </div>
                </div>

                <div className="bg-accent-500/10 border border-accent-400/20 rounded-2xl p-6 mb-8">
                  <p className="text-accent-200 font-medium text-lg">
                    <strong>The question isn't IF automation will transform your industry.</strong><br />
                    The question is: Will you be leading the transformation or scrambling to catch up?
                  </p>
                </div>

                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Link
                    href="/demo"
                    className="inline-flex items-center justify-center px-8 py-4 bg-accent-600 hover:bg-accent-700 text-white rounded-2xl transition-all duration-300 font-medium shadow-lg hover:shadow-xl text-lg"
                  >
                    Lead the Change
                    <ArrowRight className="w-5 h-5 ml-2" />
                  </Link>

                  <Link
                    href="/pricing"
                    className="inline-flex items-center justify-center px-8 py-4 border-2 border-gray-400 text-gray-300 rounded-2xl hover:border-gray-300 hover:bg-white/10 transition-all duration-300 font-medium text-lg"
                  >
                    View Investment Options
                  </Link>
                </div>
              </motion.div>
            </div>
          </div>
        </section>

        {/* Business Types Section */}
        <section className="py-20">
          <div className="container mx-auto px-4 md:px-6">
            <div className="bg-primary-50 dark:bg-primary-950/30 border border-primary-200/30 dark:border-primary-800/30 rounded-3xl p-12 shadow-lg max-w-5xl mx-auto text-center">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, ease: 'easeOut' }}
                viewport={{ once: true }}
              >
                <h2 className="text-3xl font-bold text-foreground mb-6">
                  Perfect for UK <span className="text-primary-700 dark:text-primary-400">Local Businesses</span>
                </h2>
                <p className="text-xl text-muted-foreground mb-8">
                  <strong className="text-foreground">Join 500+ UK businesses</strong> across multiple industries who've already made the shift to automation.
                </p>

                <div className="grid md:grid-cols-3 lg:grid-cols-4 gap-4 mb-8">
                  {[
                    'Window Installation',
                    'Home Improvement',
                    'Healthcare Clinics',
                    'Hair Salons & Beauty',
                    'Fitness Studios',
                    'Automotive Services',
                    'Professional Services',
                    'Thermal Insulation',
                    'Roofing Services',
                    'Plumbing & Heating',
                    'Electrical Services',
                    'Landscaping'
                  ].map((type, index) => (
                    <div key={index} className="bg-background border border-primary-200/20 dark:border-primary-800/20 rounded-2xl p-4">
                      <CheckCircle className="w-5 h-5 text-primary-700 dark:text-primary-400 mx-auto mb-2" />
                      <p className="text-foreground font-medium text-sm">{type}</p>
                    </div>
                  ))}
                </div>

                <div className="bg-accent-50 dark:bg-accent-950/30 border border-accent-200/30 dark:border-accent-800/30 rounded-2xl p-6 mb-8">
                  <p className="text-foreground font-medium text-lg">
                    <strong>If you serve customers with appointments, bookings, or consultations,</strong> these automation solutions will transform your business operations and increase your revenue.
                  </p>
                </div>

                <Link
                  href="/demo"
                  className="inline-flex items-center justify-center px-8 py-4 bg-primary-700 hover:bg-primary-800 dark:bg-primary-600 dark:hover:bg-primary-700 text-white rounded-2xl transition-all duration-300 font-medium shadow-lg hover:shadow-xl text-lg"
                >
                  Discover Your Automation Potential
                  <Rocket className="w-5 h-5 ml-2" />
                </Link>
              </motion.div>
            </div>
          </div>
        </section>

        {/* Final CTA - Natural Conclusion */}
        <section className="py-20">
          <div className="container mx-auto px-4 md:px-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, ease: 'easeOut' }}
              viewport={{ once: true }}
              className="text-center max-w-3xl mx-auto"
            >
              <div className="bg-background border border-primary-200/20 dark:border-primary-800/20 rounded-3xl p-12 shadow-lg">
                <h2 className="text-3xl font-bold text-foreground sm:text-4xl mb-6">
                  Ready to Join the <span className="text-primary-700 dark:text-primary-400">Successful Minority</span>?
                </h2>

                <p className="text-xl text-muted-foreground leading-relaxed mb-8">
                  <strong className="text-foreground">The businesses that thrive tomorrow are the ones that automate today.</strong> Don't wait until your competitors force you to change. Lead the evolution in your industry.
                </p>

                <div className="bg-primary-50 dark:bg-primary-950/30 border border-primary-200/30 dark:border-primary-800/30 rounded-2xl p-6 mb-8">
                  <p className="text-foreground font-medium">
                    <strong>Free Demo Available:</strong> See exactly how these automation solutions will work for your specific business. No obligation, just insights.
                  </p>
                </div>

                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Link
                    href="/demo"
                    className="inline-flex items-center justify-center px-8 py-4 bg-primary-700 hover:bg-primary-800 dark:bg-primary-600 dark:hover:bg-primary-700 text-white rounded-2xl transition-all duration-300 font-medium shadow-lg hover:shadow-xl text-lg"
                  >
                    Book Your Free Demo
                    <Calendar className="w-5 h-5 ml-2" />
                  </Link>

                  <Link
                    href="/contact"
                    className="inline-flex items-center justify-center px-8 py-4 border-2 border-primary-200 dark:border-primary-800 text-primary-700 dark:text-primary-400 rounded-2xl hover:border-primary-300 dark:hover:border-primary-700 hover:bg-primary-50 dark:hover:bg-primary-900/20 transition-all duration-300 font-medium text-lg"
                  >
                    Speak to an Automation Expert
                  </Link>
                </div>
              </div>
            </motion.div>
          </div>
        </section>
      </div>
    </>
  )
}
