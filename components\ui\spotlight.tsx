'use client'

import { cn } from '@/lib/utils'

interface SpotlightProps {
  className?: string
  gradientFirst?: string
  gradientSecond?: string
  gradientThird?: string
}

export function Spotlight({
  className,
  gradientFirst = "radial-gradient(68.54% 68.72% at 55.02% 31.46%, hsla(336, 100%, 50%, 0.08) 0, hsla(341, 100%, 55%, 0.04) 50%, hsla(336, 100%, 45%, 0) 80%)",
  gradientSecond = "radial-gradient(50% 50% at 50% 50%, hsla(333, 100%, 85%, 0.08) 0, hsla(335, 100%, 55%, 0.04) 80%, transparent 100%)",
  gradientThird = "radial-gradient(50% 50% at 50% 50%, hsla(332, 100%, 85%, 0.06) 0, hsla(327, 100%, 85%, 0.06) 80%, transparent 100%)"
}: SpotlightProps) {
  return (
    <div className={cn("absolute inset-0 overflow-hidden", className)}>
      {/* First gradient */}
      <div
        className="absolute -top-40 -right-40 w-96 h-96 opacity-60"
        style={{
          background: gradientFirst,
          filter: 'blur(40px)',
        }}
      />
      
      {/* Second gradient */}
      <div
        className="absolute -bottom-40 -left-40 w-96 h-96 opacity-40"
        style={{
          background: gradientSecond,
          filter: 'blur(40px)',
        }}
      />
      
      {/* Third gradient */}
      <div
        className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] opacity-30"
        style={{
          background: gradientThird,
          filter: 'blur(60px)',
        }}
      />
    </div>
  )
}
