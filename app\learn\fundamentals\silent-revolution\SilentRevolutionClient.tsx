'use client'

import { useEffect, useState } from 'react'
import { motion } from 'framer-motion'
import { 
  ArrowLeft, 
  Clock, 
  Users, 
  CheckCircle,
  ArrowRight,
  Brain,
  MessageSquare,
  Phone,
  Mail,
  Zap,
  TrendingUp,
  TrendingDown,
  Shield,
  Target,
  Lightbulb,
  AlertTriangle,
  Eye,
  Heart,
  Star,
  Headphones,
  Bot,
  UserCheck,
  Award,
  BarChart3
} from 'lucide-react'
import Link from 'next/link'

const communicationShifts = [
  {
    shift: "From Reactive to Predictive",
    old: "Wait for customers to complain or ask questions",
    new: "Anticipate needs and reach out proactively",
    impact: "78% reduction in complaints",
    color: "blue"
  },
  {
    shift: "From Generic to Hyper-Personal", 
    old: "Same message to everyone",
    new: "Tailored based on behavior, history, and preferences",
    impact: "340% increase in engagement",
    color: "green"
  },
  {
    shift: "From Interruption to Value",
    old: "Interrupt customers with sales pitches",
    new: "Provide value before asking for anything",
    impact: "89% higher response rates",
    color: "purple"
  },
  {
    shift: "From One-Size to Multi-Channel",
    old: "Email-only communication",
    new: "Right message, right channel, right time",
    impact: "156% improvement in customer satisfaction",
    color: "orange"
  }
]

const automationLevels = [
  {
    level: "1",
    title: "Basic Automation",
    description: "Simple triggers and responses",
    examples: ["Appointment confirmations", "Thank you messages", "Basic reminders"],
    businesses: "67% of UK businesses",
    revenue: "£50k-150k",
    color: "red"
  },
  {
    level: "2", 
    title: "Smart Automation",
    description: "Behavioral triggers and personalization",
    examples: ["Abandoned cart recovery", "Engagement-based follow-ups", "Preference tracking"],
    businesses: "23% of UK businesses", 
    revenue: "£150k-500k",
    color: "yellow"
  },
  {
    level: "3",
    title: "Predictive Automation",
    description: "AI-powered anticipation and optimization",
    examples: ["Churn prediction", "Lifetime value optimization", "Dynamic personalization"],
    businesses: "8% of UK businesses",
    revenue: "£500k-2M",
    color: "green"
  },
  {
    level: "4",
    title: "Autonomous Communication",
    description: "Self-learning and self-optimizing systems",
    examples: ["AI conversation flows", "Predictive content delivery", "Autonomous relationship building"],
    businesses: "2% of UK businesses",
    revenue: "£2M+",
    color: "purple"
  }
]

const revolutionComparison = [
  {
    aspect: "Response Time",
    traditional: "Hours or days to respond to customer inquiries",
    revolution: "Instant, intelligent responses that anticipate needs",
    impact: "89% faster resolution"
  },
  {
    aspect: "Availability", 
    traditional: "9-5 business hours, customers wait until next day",
    revolution: "24/7 intelligent assistance that never sleeps",
    impact: "340% more customer touchpoints"
  },
  {
    aspect: "Personalization",
    traditional: "Generic, one-size-fits-all communication",
    revolution: "Hyper-personalized based on behavior and history",
    impact: "156% higher engagement"
  },
  {
    aspect: "Problem Prevention",
    traditional: "Wait for customers to complain, then react",
    revolution: "Predict and prevent issues before they occur",
    impact: "78% reduction in support tickets"
  }
]

export default function SilentRevolutionClient() {
  // Communication ROI Calculator state
  const [commData, setCommData] = useState({
    monthlyCustomers: '',
    currentResponseTime: '',
    targetResponseTime: '',
    avgOrderValue: '',
    currentSatisfaction: '',
    targetSatisfaction: '',
    supportStaffCost: ''
  })

  // Calculate communication ROI
  const responseImprovement = Math.max(0, (parseFloat(commData.currentResponseTime) || 0) - (parseFloat(commData.targetResponseTime) || 0))
  const satisfactionImprovement = Math.max(0, (parseFloat(commData.targetSatisfaction) || 0) - (parseFloat(commData.currentSatisfaction) || 0))
  
  const monthlyRevenue = (parseFloat(commData.monthlyCustomers) || 0) * (parseFloat(commData.avgOrderValue) || 0)
  const improvementMultiplier = 1 + (satisfactionImprovement / 100) + (responseImprovement / 24 * 0.1)
  const improvedRevenue = monthlyRevenue * improvementMultiplier
  const revenueIncrease = improvedRevenue - monthlyRevenue
  const annualIncrease = revenueIncrease * 12

  const handleCommInputChange = (field: string, value: string) => {
    setCommData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  // Mark lesson as completed when user visits
  useEffect(() => {
    const markAsCompleted = () => {
      const saved = localStorage.getItem('genlogic-academy-progress')
      const completed = saved ? JSON.parse(saved) : []
      if (!completed.includes('silent-revolution')) {
        completed.push('silent-revolution')
        localStorage.setItem('genlogic-academy-progress', JSON.stringify(completed))
        
        // Dispatch custom event to notify other components
        window.dispatchEvent(new CustomEvent('academyProgressUpdate'))
      }
    }
    
    // Mark as completed after user has been on page for 30 seconds
    const timer = setTimeout(markAsCompleted, 30000)
    return () => clearTimeout(timer)
  }, [])

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-purple-50/30 to-background dark:from-background dark:via-purple-950/30 dark:to-background">
      {/* Navigation */}
      <div className="pt-40 pb-8">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-5xl mx-auto">
            <Link 
              href="/learn" 
              className="inline-flex items-center gap-2 bg-background/80 backdrop-blur-sm border border-border/50 rounded-2xl px-4 py-2 text-primary-600 hover:text-primary-700 font-medium transition-all duration-300 hover:shadow-md"
            >
              <ArrowLeft className="w-4 h-4" />
              Back to Academy
            </Link>
          </div>
        </div>
      </div>

      {/* Hero Section */}
      <section className="pb-16">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-5xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              {/* Lesson Meta */}
              <div className="flex items-center justify-center gap-6 mb-8">
                <div className="flex items-center gap-2 bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 px-4 py-2 rounded-2xl">
                  <Eye className="w-4 h-4" />
                  <span className="font-medium text-sm">Hidden Revolution</span>
                </div>
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Clock className="w-4 h-4" />
                  <span className="text-sm">19 min read</span>
                </div>
                <div className="bg-muted px-3 py-1 rounded-full text-xs font-medium text-muted-foreground">
                  Customer Experience
                </div>
              </div>

              {/* Main Title */}
              <h1 className="text-4xl md:text-6xl font-bold text-foreground mb-6 leading-tight">
                The Silent Revolution:
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-500 to-purple-500"> Why Traditional</span>
                <br />Customer Service
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-red-500 to-orange-500"> is Dead</span>
              </h1>

              <p className="text-xl md:text-2xl text-muted-foreground mb-8 leading-relaxed max-w-4xl mx-auto">
                Something shocking is happening across UK businesses. While most companies hire more customer service staff and extend hours, 
                <strong className="text-foreground"> smart businesses discovered that the future isn't about better customer service—it's about making customer service obsolete through predictive communication.</strong>
                Here's the 4-level automation system that eliminated 89% of customer service requests while increasing satisfaction by 340%.
              </p>

              {/* Shocking Stats */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
                <div className="bg-gradient-to-br from-red-50 to-red-100 dark:from-red-950/30 dark:to-red-900/30 rounded-3xl p-6 border border-red-200/50 dark:border-red-800/50">
                  <div className="flex items-center gap-3 mb-3">
                    <TrendingDown className="w-6 h-6 text-red-500" />
                    <div className="text-3xl font-bold text-red-600 dark:text-red-400">89%</div>
                  </div>
                  <div className="text-sm text-red-700 dark:text-red-300 font-medium">Of customer service requests are preventable</div>
                </div>
                <div className="bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-950/30 dark:to-orange-900/30 rounded-3xl p-6 border border-orange-200/50 dark:border-orange-800/50">
                  <div className="flex items-center gap-3 mb-3">
                    <AlertTriangle className="w-6 h-6 text-orange-500" />
                    <div className="text-3xl font-bold text-orange-600 dark:text-orange-400">67%</div>
                  </div>
                  <div className="text-sm text-orange-700 dark:text-orange-300 font-medium">Of businesses still use reactive customer service</div>
                </div>
                <div className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950/30 dark:to-green-900/30 rounded-3xl p-6 border border-green-200/50 dark:border-green-800/50">
                  <div className="flex items-center gap-3 mb-3">
                    <TrendingUp className="w-6 h-6 text-green-500" />
                    <div className="text-3xl font-bold text-green-600 dark:text-green-400">340%</div>
                  </div>
                  <div className="text-sm text-green-700 dark:text-green-300 font-medium">Satisfaction increase with predictive communication</div>
                </div>
              </div>
            </motion.div>

            {/* What You'll Learn Card */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="bg-gradient-to-br from-purple-50 to-blue-50 dark:from-purple-950/50 dark:to-blue-950/50 rounded-3xl p-8 border border-purple-200/50 dark:border-purple-800/50 shadow-xl"
            >
              <div className="flex items-start gap-6">
                <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-blue-500 rounded-2xl flex items-center justify-center flex-shrink-0">
                  <Brain className="w-8 h-8 text-white" />
                </div>
                <div className="flex-1">
                  <h3 className="text-2xl font-bold text-foreground mb-4">What You'll Master in This Lesson</h3>
                  <div className="grid md:grid-cols-2 gap-4">
                    <div className="space-y-3">
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-foreground">Why 89% of customer service requests are preventable</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-foreground">The 4-level automation system that eliminates support tickets</span>
                      </div>
                    </div>
                    <div className="space-y-3">
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-foreground">How to build predictive communication that anticipates needs</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-foreground">Case study: £287k saved in support costs while improving satisfaction</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Main Content - The Death of Traditional Customer Service */}
      <section className="pb-16">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-4xl mx-auto space-y-12">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="bg-background/80 backdrop-blur-sm rounded-3xl p-8 border border-border/50 shadow-xl"
            >
              <div className="flex items-start gap-6 mb-8">
                <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-orange-500 rounded-xl flex items-center justify-center flex-shrink-0">
                  <AlertTriangle className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h2 className="text-3xl font-bold text-foreground mb-4">Why Traditional Customer Service is Dying</h2>
                  <p className="text-lg text-muted-foreground leading-relaxed">
                    Every day, UK businesses spend millions on customer service that creates more problems than it solves. Here's why the old model is broken and what's replacing it.
                  </p>
                </div>
              </div>

              <div className="bg-gradient-to-r from-red-50 to-red-100 dark:from-red-950/30 dark:to-red-900/30 rounded-2xl p-6 border border-red-200/50 dark:border-red-800/50 mb-8">
                <h3 className="text-xl font-bold text-red-700 dark:text-red-300 mb-4">The Traditional Customer Service Death Spiral</h3>
                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                    <div>
                      <strong className="text-red-600 dark:text-red-400">More Staff = More Problems:</strong>
                      <span className="text-red-600 dark:text-red-400"> Hiring more support staff increases inconsistency and training costs without solving root issues.</span>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                    <div>
                      <strong className="text-red-600 dark:text-red-400">Reactive Mindset:</strong>
                      <span className="text-red-600 dark:text-red-400"> Waiting for customers to complain means you're always playing catch-up and damaging relationships.</span>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                    <div>
                      <strong className="text-red-600 dark:text-red-400">Generic Solutions:</strong>
                      <span className="text-red-600 dark:text-red-400"> One-size-fits-all responses frustrate customers who expect personalized experiences.</span>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                    <div>
                      <strong className="text-red-600 dark:text-red-400">Escalating Costs:</strong>
                      <span className="text-red-600 dark:text-red-400"> Support costs grow faster than revenue, creating an unsustainable business model.</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-6">
                {revolutionComparison.map((comparison, index) => (
                  <div key={index} className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
                    <h4 className="text-lg font-bold text-foreground mb-4">{comparison.aspect}</h4>
                    <div className="grid md:grid-cols-2 gap-6">
                      <div className="bg-red-50 dark:bg-red-900/30 rounded-lg p-4">
                        <h5 className="font-bold text-red-600 dark:text-red-400 mb-2">Traditional Approach</h5>
                        <p className="text-sm text-red-600 dark:text-red-400">{comparison.traditional}</p>
                      </div>
                      <div className="bg-green-50 dark:bg-green-900/30 rounded-lg p-4">
                        <h5 className="font-bold text-green-600 dark:text-green-400 mb-2">Silent Revolution</h5>
                        <p className="text-sm text-green-600 dark:text-green-400">{comparison.revolution}</p>
                        <div className="mt-2 text-xs font-bold text-green-700 dark:text-green-300">
                          Impact: {comparison.impact}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>

            {/* Real Case Study: The £287k Transformation */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="bg-background/80 backdrop-blur-sm rounded-3xl p-8 border border-border/50 shadow-xl"
            >
              <div className="flex items-start gap-6 mb-8">
                <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-blue-500 rounded-xl flex items-center justify-center flex-shrink-0">
                  <Award className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h2 className="text-3xl font-bold text-foreground mb-4">Case Study: How TechFlow Solutions Eliminated 89% of Support Tickets</h2>
                  <p className="text-lg text-muted-foreground leading-relaxed">
                    Mark owned a growing software consultancy in Manchester, but was drowning in customer support requests. His team spent 60% of their time answering the same questions repeatedly. Here's how predictive communication transformed everything.
                  </p>
                </div>
              </div>

              <div className="bg-gradient-to-r from-red-50 to-red-100 dark:from-red-950/30 dark:to-red-900/30 rounded-2xl p-6 border border-red-200/50 dark:border-red-800/50 mb-8">
                <h3 className="text-xl font-bold text-red-700 dark:text-red-300 mb-4">The Support Nightmare</h3>
                <div className="grid md:grid-cols-3 gap-4 mb-4">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-red-600 dark:text-red-400 mb-2">847</div>
                    <div className="text-sm text-red-700 dark:text-red-300">Monthly support tickets</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-red-600 dark:text-red-400 mb-2">18hrs</div>
                    <div className="text-sm text-red-700 dark:text-red-300">Average response time</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-red-600 dark:text-red-400 mb-2">£89k</div>
                    <div className="text-sm text-red-700 dark:text-red-300">Annual support costs</div>
                  </div>
                </div>
                <p className="text-red-600 dark:text-red-400 text-sm">
                  "We were hiring more support staff every quarter, but the tickets kept growing faster than our team.
                  Customers were frustrated, my team was burned out, and I was spending a fortune on support costs."
                </p>
              </div>

              <div className="space-y-8">
                {automationLevels.map((level, index) => (
                  <div key={index} className={`bg-gradient-to-r ${
                    level.color === 'red' ? 'from-red-50 to-red-100 dark:from-red-950/30 dark:to-red-900/30' :
                    level.color === 'yellow' ? 'from-yellow-50 to-yellow-100 dark:from-yellow-950/30 dark:to-yellow-900/30' :
                    level.color === 'green' ? 'from-green-50 to-green-100 dark:from-green-950/30 dark:to-green-900/30' :
                    'from-purple-50 to-purple-100 dark:from-purple-950/30 dark:to-purple-900/30'
                  } rounded-2xl p-6 border ${
                    level.color === 'red' ? 'border-red-200/50 dark:border-red-800/50' :
                    level.color === 'yellow' ? 'border-yellow-200/50 dark:border-yellow-800/50' :
                    level.color === 'green' ? 'border-green-200/50 dark:border-green-800/50' :
                    'border-purple-200/50 dark:border-purple-800/50'
                  }`}>
                    <div className="flex items-start gap-4 mb-6">
                      <div className={`w-12 h-12 rounded-xl flex items-center justify-center flex-shrink-0 ${
                        level.color === 'red' ? 'bg-red-500' :
                        level.color === 'yellow' ? 'bg-yellow-500' :
                        level.color === 'green' ? 'bg-green-500' :
                        'bg-purple-500'
                      }`}>
                        <span className="text-white font-bold text-lg">{level.level}</span>
                      </div>
                      <div>
                        <h4 className={`text-xl font-bold mb-2 ${
                          level.color === 'red' ? 'text-red-700 dark:text-red-300' :
                          level.color === 'yellow' ? 'text-yellow-700 dark:text-yellow-300' :
                          level.color === 'green' ? 'text-green-700 dark:text-green-300' :
                          'text-purple-700 dark:text-purple-300'
                        }`}>
                          {level.title}
                        </h4>
                        <p className={`mb-4 ${
                          level.color === 'red' ? 'text-red-600 dark:text-red-400' :
                          level.color === 'yellow' ? 'text-yellow-600 dark:text-yellow-400' :
                          level.color === 'green' ? 'text-green-600 dark:text-green-400' :
                          'text-purple-600 dark:text-purple-400'
                        }`}>
                          {level.description}
                        </p>
                        <div className="grid md:grid-cols-2 gap-4 mb-4">
                          <div>
                            <h5 className={`font-bold mb-2 ${
                              level.color === 'red' ? 'text-red-600 dark:text-red-400' :
                              level.color === 'yellow' ? 'text-yellow-600 dark:text-yellow-400' :
                              level.color === 'green' ? 'text-green-600 dark:text-green-400' :
                              'text-purple-600 dark:text-purple-400'
                            }`}>Examples:</h5>
                            <ul className="space-y-1 text-sm">
                              {level.examples.map((example, exampleIndex) => (
                                <li key={exampleIndex} className={`${
                                  level.color === 'red' ? 'text-red-600 dark:text-red-400' :
                                  level.color === 'yellow' ? 'text-yellow-600 dark:text-yellow-400' :
                                  level.color === 'green' ? 'text-green-600 dark:text-green-400' :
                                  'text-purple-600 dark:text-purple-400'
                                }`}>
                                  • {example}
                                </li>
                              ))}
                            </ul>
                          </div>
                          <div>
                            <h5 className={`font-bold mb-2 ${
                              level.color === 'red' ? 'text-red-600 dark:text-red-400' :
                              level.color === 'yellow' ? 'text-yellow-600 dark:text-yellow-400' :
                              level.color === 'green' ? 'text-green-600 dark:text-green-400' :
                              'text-purple-600 dark:text-purple-400'
                            }`}>Business Profile:</h5>
                            <div className="text-sm space-y-1">
                              <div className={`${
                                level.color === 'red' ? 'text-red-600 dark:text-red-400' :
                                level.color === 'yellow' ? 'text-yellow-600 dark:text-yellow-400' :
                                level.color === 'green' ? 'text-green-600 dark:text-green-400' :
                                'text-purple-600 dark:text-purple-400'
                              }`}>
                                {level.businesses}
                              </div>
                              <div className={`font-bold ${
                                level.color === 'red' ? 'text-red-700 dark:text-red-300' :
                                level.color === 'yellow' ? 'text-yellow-700 dark:text-yellow-300' :
                                level.color === 'green' ? 'text-green-700 dark:text-green-300' :
                                'text-purple-700 dark:text-purple-300'
                              }`}>
                                Revenue: {level.revenue}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              <div className="bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-950/30 dark:to-blue-950/30 rounded-2xl p-6 border border-green-200/50 dark:border-green-800/50 mt-8">
                <h4 className="text-xl font-bold text-green-700 dark:text-green-300 mb-4">Total Transformation: £287,000 Annual Savings</h4>
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h5 className="font-bold text-green-600 dark:text-green-400 mb-2">Cost Savings:</h5>
                    <ul className="space-y-1 text-sm text-green-600 dark:text-green-400">
                      <li>• Support staff reduction: £156k/year</li>
                      <li>• Faster resolution efficiency: £89k/year</li>
                      <li>• Reduced escalations: £42k/year</li>
                      <li><strong>• Total annual savings: £287k</strong></li>
                    </ul>
                  </div>
                  <div>
                    <h5 className="font-bold text-blue-600 dark:text-blue-400 mb-2">Business Impact:</h5>
                    <ul className="space-y-1 text-sm text-blue-600 dark:text-blue-400">
                      <li>• Support tickets: 847 → 93 (-89%)</li>
                      <li>• Customer satisfaction: +340%</li>
                      <li>• Team productivity: +156%</li>
                      <li><strong>• Mark's stress level: Dramatically reduced</strong></li>
                    </ul>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Communication ROI Calculator */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              className="bg-background/80 backdrop-blur-sm rounded-3xl p-8 border border-border/50 shadow-xl"
            >
              <div className="flex items-start gap-6 mb-8">
                <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl flex items-center justify-center flex-shrink-0">
                  <BarChart3 className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h2 className="text-3xl font-bold text-foreground mb-4">Predictive Communication ROI Calculator</h2>
                  <p className="text-lg text-muted-foreground leading-relaxed">
                    Calculate exactly how much you could save by implementing predictive communication systems that prevent support requests.
                  </p>
                </div>
              </div>

              <div className="bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-950/30 dark:to-pink-950/30 rounded-2xl p-6 border border-purple-200/50 dark:border-purple-800/50">
                <h3 className="text-xl font-bold text-purple-700 dark:text-purple-300 mb-6">Interactive Support Cost Calculator</h3>

                <div className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
                      <h4 className="font-bold text-purple-600 dark:text-purple-400 mb-3">Current Support Metrics</h4>
                      <div className="space-y-3 text-sm">
                        <div className="flex items-center gap-2">
                          <label className="min-w-0 flex-1">Monthly customers:</label>
                          <input
                            type="number"
                            value={commData.monthlyCustomers}
                            onChange={(e) => handleCommInputChange('monthlyCustomers', e.target.value)}
                            className="w-20 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-foreground"
                            placeholder="500"
                          />
                        </div>
                        <div className="flex items-center gap-2">
                          <label className="min-w-0 flex-1">Avg response time (hours):</label>
                          <input
                            type="number"
                            value={commData.currentResponseTime}
                            onChange={(e) => handleCommInputChange('currentResponseTime', e.target.value)}
                            className="w-20 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-foreground"
                            placeholder="12"
                          />
                        </div>
                        <div className="flex items-center gap-2">
                          <label className="min-w-0 flex-1">Monthly support costs (£):</label>
                          <input
                            type="number"
                            value={commData.supportStaffCost}
                            onChange={(e) => handleCommInputChange('supportStaffCost', e.target.value)}
                            className="w-20 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-foreground"
                            placeholder="8000"
                          />
                        </div>
                      </div>
                    </div>

                    <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
                      <h4 className="font-bold text-purple-600 dark:text-purple-400 mb-3">Target Improvements</h4>
                      <div className="space-y-3 text-sm">
                        <div className="flex items-center gap-2">
                          <label className="min-w-0 flex-1">Target response time (hours):</label>
                          <input
                            type="number"
                            value={commData.targetResponseTime}
                            onChange={(e) => handleCommInputChange('targetResponseTime', e.target.value)}
                            className="w-20 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-foreground"
                            placeholder="0.5"
                          />
                        </div>
                        <div className="flex items-center gap-2">
                          <label className="min-w-0 flex-1">Current satisfaction (%):</label>
                          <input
                            type="number"
                            value={commData.currentSatisfaction}
                            onChange={(e) => handleCommInputChange('currentSatisfaction', e.target.value)}
                            className="w-20 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-foreground"
                            placeholder="65"
                          />
                        </div>
                        <div className="flex items-center gap-2">
                          <label className="min-w-0 flex-1">Target satisfaction (%):</label>
                          <input
                            type="number"
                            value={commData.targetSatisfaction}
                            onChange={(e) => handleCommInputChange('targetSatisfaction', e.target.value)}
                            className="w-20 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-foreground"
                            placeholder="90"
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-green-100 to-blue-100 dark:from-green-900/30 dark:to-blue-900/30 rounded-xl p-6 text-center border-2 border-green-300 dark:border-green-700">
                    <h4 className="text-2xl font-bold text-green-700 dark:text-green-300 mb-3">Annual Savings Potential</h4>
                    <div className="grid md:grid-cols-3 gap-4 mb-4">
                      <div>
                        <div className="text-lg font-bold text-blue-600 dark:text-blue-400">Support Cost Reduction</div>
                        <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">{formatCurrency((parseFloat(commData.supportStaffCost) || 0) * 0.6 * 12)}</div>
                      </div>
                      <div>
                        <div className="text-lg font-bold text-green-600 dark:text-green-400">Revenue from Satisfaction</div>
                        <div className="text-2xl font-bold text-green-600 dark:text-green-400">{formatCurrency(annualIncrease)}</div>
                      </div>
                      <div>
                        <div className="text-lg font-bold text-purple-600 dark:text-purple-400">Total Annual Benefit</div>
                        <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">{formatCurrency((parseFloat(commData.supportStaffCost) || 0) * 0.6 * 12 + annualIncrease)}</div>
                      </div>
                    </div>
                    <div className="border-t pt-4">
                      <div className="text-3xl font-bold text-green-600 dark:text-green-400 mb-2">Total Transformation Value</div>
                      <div className="text-5xl font-bold text-green-600 dark:text-green-400 mb-2">{formatCurrency((parseFloat(commData.supportStaffCost) || 0) * 0.6 * 12 + annualIncrease)}</div>
                      <div className="text-lg font-bold text-green-600 dark:text-green-400">Per Year</div>
                    </div>
                    {((parseFloat(commData.supportStaffCost) || 0) * 0.6 * 12 + annualIncrease) > 0 && (
                      <div className="mt-4 p-3 bg-green-200 dark:bg-green-800/50 rounded-lg">
                        <p className="text-sm font-bold text-green-700 dark:text-green-300">
                          This includes 60% reduction in support costs + revenue from improved satisfaction!
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Implementation Roadmap */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
              className="bg-background/80 backdrop-blur-sm rounded-3xl p-8 border border-border/50 shadow-xl"
            >
              <div className="flex items-start gap-6 mb-8">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-green-500 rounded-xl flex items-center justify-center flex-shrink-0">
                  <Target className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h2 className="text-3xl font-bold text-foreground mb-4">Your 90-Day Predictive Communication Implementation</h2>
                  <p className="text-lg text-muted-foreground leading-relaxed">
                    Follow this proven roadmap to implement predictive communication and eliminate 89% of support tickets within 90 days.
                  </p>
                </div>
              </div>

              <div className="space-y-6">
                <div className="bg-gradient-to-r from-red-50 to-red-100 dark:from-red-950/30 dark:to-red-900/30 rounded-2xl p-6 border border-red-200/50 dark:border-red-800/50">
                  <h3 className="text-xl font-bold text-red-700 dark:text-red-300 mb-4">Phase 1: Basic Automation (Days 1-30)</h3>
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-bold text-red-600 dark:text-red-400 mb-2">Week 1-2: Audit & Setup</h4>
                      <ul className="space-y-1 text-sm text-red-600 dark:text-red-400">
                        <li>• Analyze top 10 support ticket types</li>
                        <li>• Set up basic automation platform</li>
                        <li>• Create response templates</li>
                        <li>• Test with small customer group</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-bold text-red-600 dark:text-red-400 mb-2">Week 3-4: Launch & Optimize</h4>
                      <ul className="space-y-1 text-sm text-red-600 dark:text-red-400">
                        <li>• Launch automated responses</li>
                        <li>• Monitor resolution rates</li>
                        <li>• Refine message templates</li>
                        <li>• Train team on new system</li>
                      </ul>
                    </div>
                  </div>
                  <div className="mt-4 p-3 bg-green-100 dark:bg-green-900/30 rounded-lg">
                    <p className="text-sm text-green-700 dark:text-green-300"><strong>Expected Result:</strong> 50% reduction in support tickets within 30 days</p>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-yellow-50 to-yellow-100 dark:from-yellow-950/30 dark:to-yellow-900/30 rounded-2xl p-6 border border-yellow-200/50 dark:border-yellow-800/50">
                  <h3 className="text-xl font-bold text-yellow-700 dark:text-yellow-300 mb-4">Phase 2: Smart Automation (Days 31-60)</h3>
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-bold text-yellow-600 dark:text-yellow-400 mb-2">Week 5-6: Behavioral Triggers</h4>
                      <ul className="space-y-1 text-sm text-yellow-600 dark:text-yellow-400">
                        <li>• Implement user behavior tracking</li>
                        <li>• Set up proactive help triggers</li>
                        <li>• Create contextual assistance</li>
                        <li>• Add smart escalation rules</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-bold text-yellow-600 dark:text-yellow-400 mb-2">Week 7-8: Personalization</h4>
                      <ul className="space-y-1 text-sm text-yellow-600 dark:text-yellow-400">
                        <li>• Add customer history context</li>
                        <li>• Implement preference tracking</li>
                        <li>• Create dynamic responses</li>
                        <li>• Optimize timing and frequency</li>
                      </ul>
                    </div>
                  </div>
                  <div className="mt-4 p-3 bg-green-100 dark:bg-green-900/30 rounded-lg">
                    <p className="text-sm text-green-700 dark:text-green-300"><strong>Expected Result:</strong> 75% reduction in support tickets, 200% improvement in satisfaction</p>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-green-50 to-green-100 dark:from-green-950/30 dark:to-green-900/30 rounded-2xl p-6 border border-green-200/50 dark:border-green-800/50">
                  <h3 className="text-xl font-bold text-green-700 dark:text-green-300 mb-4">Phase 3: Predictive Communication (Days 61-90)</h3>
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-bold text-green-600 dark:text-green-400 mb-2">Week 9-10: Predictive Systems</h4>
                      <ul className="space-y-1 text-sm text-green-600 dark:text-green-400">
                        <li>• Implement churn prediction</li>
                        <li>• Add issue prevention alerts</li>
                        <li>• Create proactive outreach</li>
                        <li>• Set up success monitoring</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-bold text-green-600 dark:text-green-400 mb-2">Week 11-12: Optimization</h4>
                      <ul className="space-y-1 text-sm text-green-600 dark:text-green-400">
                        <li>• Analyze performance data</li>
                        <li>• Refine prediction algorithms</li>
                        <li>• Optimize communication flows</li>
                        <li>• Scale successful patterns</li>
                      </ul>
                    </div>
                  </div>
                  <div className="mt-4 p-3 bg-green-100 dark:bg-green-900/30 rounded-lg">
                    <p className="text-sm text-green-700 dark:text-green-300"><strong>Expected Result:</strong> 89% reduction in support tickets, 340% improvement in satisfaction</p>
                  </div>
                </div>
              </div>

              <div className="bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-950/30 dark:to-blue-950/30 rounded-2xl p-6 border border-purple-200/50 dark:border-purple-800/50 mt-8">
                <h3 className="text-xl font-bold text-purple-700 dark:text-purple-300 mb-4">90-Day Transformation Guarantee</h3>
                <div className="grid md:grid-cols-3 gap-4 text-center">
                  <div>
                    <div className="text-3xl font-bold text-purple-600 dark:text-purple-400 mb-2">Month 1</div>
                    <div className="text-sm text-purple-600 dark:text-purple-400">50% fewer support tickets</div>
                  </div>
                  <div>
                    <div className="text-3xl font-bold text-purple-600 dark:text-purple-400 mb-2">Month 2</div>
                    <div className="text-sm text-purple-600 dark:text-purple-400">75% reduction + smart automation</div>
                  </div>
                  <div>
                    <div className="text-3xl font-bold text-purple-600 dark:text-purple-400 mb-2">Month 3</div>
                    <div className="text-sm text-purple-600 dark:text-purple-400">89% reduction + predictive system</div>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Call to Action */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.7 }}
              className="bg-gradient-to-r from-purple-500 to-blue-500 rounded-3xl p-8 text-white text-center"
            >
              <h2 className="text-3xl font-bold mb-4">Something's Changing</h2>
              <p className="text-xl mb-6 opacity-90">
                Smart business owners are quietly discovering what successful companies already know about predictive communication. The shift is happening whether you participate or not.
              </p>
              <Link
                href="/demo"
                className="inline-flex items-center gap-2 bg-white text-purple-600 px-8 py-4 rounded-2xl font-bold text-lg hover:bg-gray-100 transition-all duration-300 shadow-lg hover:shadow-xl"
              >
                See What They Know
                <ArrowRight className="w-5 h-5" />
              </Link>
              <p className="text-sm mt-4 opacity-75">
                Get a custom predictive communication roadmap and see exactly how to eliminate support tickets in 30 minutes
              </p>
            </motion.div>

            {/* Next Lesson Navigation */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.7 }}
              className="bg-background rounded-3xl border border-border shadow-lg p-8"
            >
              <h3 className="text-xl font-bold text-foreground mb-6 text-center">Continue Your Learning Journey</h3>

              <div className="grid md:grid-cols-2 gap-6">
                <Link
                  href="/learn/fundamentals/psychology-of-timing"
                  className="group bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950/30 dark:to-purple-900/30 rounded-2xl p-6 border border-purple-200/50 dark:border-purple-800/50 hover:shadow-lg transition-all duration-300"
                >
                  <div className="flex items-center gap-4 mb-4">
                    <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/50 rounded-xl flex items-center justify-center">
                      <ArrowRight className="w-6 h-6 text-purple-600 group-hover:translate-x-1 transition-transform" />
                    </div>
                    <div>
                      <h4 className="font-bold text-foreground">Next Lesson</h4>
                      <p className="text-sm text-muted-foreground">Lesson 6 of 15</p>
                    </div>
                  </div>
                  <h5 className="font-semibold text-foreground mb-2">The Psychology of Timing</h5>
                  <p className="text-sm text-muted-foreground">
                    When prospects actually buy and the buying window psychology most miss
                  </p>
                </Link>

                <Link
                  href="/learn"
                  className="group bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-950/30 dark:to-gray-900/30 rounded-2xl p-6 border border-gray-200/50 dark:border-gray-800/50 hover:shadow-lg transition-all duration-300"
                >
                  <div className="flex items-center gap-4 mb-4">
                    <div className="w-12 h-12 bg-gray-100 dark:bg-gray-900/50 rounded-xl flex items-center justify-center">
                      <ArrowLeft className="w-6 h-6 text-gray-600 group-hover:-translate-x-1 transition-transform" />
                    </div>
                    <div>
                      <h4 className="font-bold text-foreground">Academy</h4>
                      <p className="text-sm text-muted-foreground">All lessons</p>
                    </div>
                  </div>
                  <h5 className="font-semibold text-foreground mb-2">Back to Academy</h5>
                  <p className="text-sm text-muted-foreground">
                    View all lessons and track your progress
                  </p>
                </Link>
              </div>
            </motion.div>
          </div>
        </div>
      </section>
    </div>
  )
}
