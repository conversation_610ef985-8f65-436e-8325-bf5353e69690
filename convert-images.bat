@echo off
echo Converting SVG images to WebP format...
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

REM Run the conversion script
node convert-svg-to-webp.js

echo.
echo Conversion completed! Check the output above for results.
pause
