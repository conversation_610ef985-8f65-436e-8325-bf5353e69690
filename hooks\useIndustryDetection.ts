'use client'

import { useState, useEffect } from 'react'

// Industry detection based on various signals
export function useIndustryDetection() {
  const [detectedIndustry, setDetectedIndustry] = useState<string>('')
  const [confidence, setConfidence] = useState<number>(0)

  useEffect(() => {
    if (typeof window === 'undefined') return

    // Check localStorage first
    const savedIndustry = localStorage.getItem('user-industry')
    if (savedIndustry) {
      setDetectedIndustry(savedIndustry)
      setConfidence(100)
      return
    }

    // Industry detection signals
    const signals: { keyword: string; industry: string; weight: number }[] = [
      // URL parameters
      { keyword: 'salon', industry: 'hair salons', weight: 90 },
      { keyword: 'beauty', industry: 'beauty clinics', weight: 90 },
      { keyword: 'fitness', industry: 'fitness studios', weight: 90 },
      { keyword: 'dental', industry: 'dental practices', weight: 90 },
      { keyword: 'medical', industry: 'medical clinics', weight: 90 },
      { keyword: 'consulting', industry: 'consulting firms', weight: 90 },
      { keyword: 'automotive', industry: 'automotive services', weight: 90 },
      { keyword: 'wellness', industry: 'wellness centers', weight: 90 },
      
      // Referrer detection
      { keyword: 'booking', industry: 'service businesses', weight: 60 },
      { keyword: 'appointment', industry: 'healthcare providers', weight: 70 },
      { keyword: 'schedule', industry: 'professional services', weight: 60 },
      
      // Time-based patterns (business hours detection)
      { keyword: 'business_hours', industry: 'professional services', weight: 40 },
    ]

    let bestMatch = { industry: 'service businesses', confidence: 30 }

    // Check URL parameters
    const urlParams = new URLSearchParams(window.location.search)
    const industry = urlParams.get('industry')
    if (industry) {
      const matchedSignal = signals.find(s => s.keyword === industry.toLowerCase())
      if (matchedSignal) {
        bestMatch = { industry: matchedSignal.industry, confidence: matchedSignal.weight }
      }
    }

    // Check referrer
    if (document.referrer) {
      const referrerUrl = document.referrer.toLowerCase()
      signals.forEach(signal => {
        if (referrerUrl.includes(signal.keyword)) {
          if (signal.weight > bestMatch.confidence) {
            bestMatch = { industry: signal.industry, confidence: signal.weight }
          }
        }
      })
    }

    // Check current page URL
    const currentUrl = window.location.pathname.toLowerCase()
    signals.forEach(signal => {
      if (currentUrl.includes(signal.keyword)) {
        if (signal.weight > bestMatch.confidence) {
          bestMatch = { industry: signal.industry, confidence: signal.weight }
        }
      }
    })

    // Time-based detection (business hours)
    const hour = new Date().getHours()
    if (hour >= 9 && hour <= 17) {
      // Business hours - likely professional services
      if (bestMatch.confidence < 50) {
        bestMatch = { industry: 'professional services', confidence: 45 }
      }
    } else if (hour >= 18 && hour <= 22) {
      // Evening hours - likely service businesses with evening hours
      if (bestMatch.confidence < 50) {
        bestMatch = { industry: 'service businesses', confidence: 40 }
      }
    }

    setDetectedIndustry(bestMatch.industry)
    setConfidence(bestMatch.confidence)

    // Save to localStorage if confidence is high enough
    if (bestMatch.confidence >= 70) {
      localStorage.setItem('user-industry', bestMatch.industry)
    }
  }, [])

  // Function to manually set industry (from form interactions, etc.)
  const setIndustry = (industry: string) => {
    setDetectedIndustry(industry)
    setConfidence(100)
    localStorage.setItem('user-industry', industry)
  }

  // Function to get industry-specific keywords
  const getIndustryKeywords = () => {
    const industryKeywords: Record<string, string[]> = {
      'hair salons': ['hair styling', 'salon booking', 'hair appointments', 'beauty services'],
      'beauty clinics': ['beauty treatments', 'aesthetic services', 'skincare', 'cosmetic procedures'],
      'fitness studios': ['fitness classes', 'personal training', 'gym membership', 'workout sessions'],
      'dental practices': ['dental appointments', 'oral health', 'dental care', 'teeth cleaning'],
      'medical clinics': ['medical appointments', 'healthcare services', 'patient care', 'medical consultations'],
      'consulting firms': ['business consulting', 'professional advice', 'strategic planning', 'expert consultation'],
      'automotive services': ['car maintenance', 'auto repair', 'vehicle servicing', 'automotive care'],
      'wellness centers': ['wellness programs', 'holistic health', 'therapeutic services', 'wellness treatments'],
      'professional services': ['professional appointments', 'business services', 'expert consultation', 'service delivery'],
      'service businesses': ['service appointments', 'customer bookings', 'service delivery', 'business automation']
    }

    return industryKeywords[detectedIndustry] || industryKeywords['service businesses']
  }

  // Function to get industry-specific pain points
  const getIndustryPainPoints = () => {
    const painPoints: Record<string, string[]> = {
      'hair salons': ['last-minute cancellations', 'double bookings', 'no-show clients', 'scheduling conflicts'],
      'beauty clinics': ['treatment scheduling', 'client follow-ups', 'appointment reminders', 'service coordination'],
      'fitness studios': ['class capacity management', 'membership renewals', 'trainer scheduling', 'equipment booking'],
      'dental practices': ['patient no-shows', 'appointment confirmations', 'treatment follow-ups', 'emergency scheduling'],
      'medical clinics': ['patient scheduling', 'appointment reminders', 'follow-up care', 'waiting list management'],
      'consulting firms': ['client meeting coordination', 'project scheduling', 'follow-up meetings', 'time management'],
      'automotive services': ['service appointments', 'maintenance reminders', 'customer follow-ups', 'parts scheduling'],
      'wellness centers': ['treatment bookings', 'client progress tracking', 'session reminders', 'package management'],
      'professional services': ['client appointments', 'service delivery', 'follow-up meetings', 'project coordination'],
      'service businesses': ['customer bookings', 'service scheduling', 'client communication', 'appointment management']
    }

    return painPoints[detectedIndustry] || painPoints['service businesses']
  }

  return {
    detectedIndustry,
    confidence,
    setIndustry,
    getIndustryKeywords,
    getIndustryPainPoints,
    isHighConfidence: confidence >= 70,
    isDetected: detectedIndustry !== ''
  }
}
