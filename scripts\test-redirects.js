#!/usr/bin/env node

/**
 * Redirect Chain Testing Script
 * Tests all redirects defined in middleware.ts to ensure no chains or loops exist
 */

const https = require('https');
const http = require('http');

// All redirects from middleware.ts
const redirectsToTest = {
  // Service redirects
  '/services/lead-conversion': '/services/business-automation',
  '/services/revenue-growth': '/services/business-automation',
  '/services/sms-automation': '/services/business-automation',
  '/services/email-automation': '/services/business-automation',
  '/services/booking-system': '/services/business-automation',
  '/services/customer-conversion': '/services/business-automation',
  '/services/sales-optimization': '/services/business-automation',
  
  // Common redirects
  '/automation': '/services/business-automation',
  '/booking': '/services/business-automation',
  '/sms': '/services/business-automation',
  '/email': '/services/business-automation',
  '/leads': '/services/business-automation',
  '/conversion': '/services/business-automation',
  '/sales': '/contact',
  '/consultation': '/contact',
  '/quote': '/contact',
  '/get-started': '/demo',
  '/start': '/demo',
  '/trial': '/demo',
};

// Test URLs
const testUrls = [
  'https://genlogic.io',
  'http://localhost:3000'
];

async function testRedirect(baseUrl, fromPath, expectedToPath) {
  return new Promise((resolve) => {
    const url = new URL(fromPath, baseUrl);
    const client = url.protocol === 'https:' ? https : http;
    
    const options = {
      method: 'HEAD',
      hostname: url.hostname,
      port: url.port,
      path: url.pathname,
      headers: {
        'User-Agent': 'GenLogic-Redirect-Tester/1.0'
      }
    };

    const req = client.request(options, (res) => {
      const result = {
        fromPath,
        expectedToPath,
        statusCode: res.statusCode,
        location: res.headers.location,
        success: false,
        error: null,
        redirectChain: [fromPath]
      };

      if (res.statusCode === 301 || res.statusCode === 302) {
        if (res.headers.location) {
          const redirectUrl = new URL(res.headers.location, baseUrl);
          const actualToPath = redirectUrl.pathname;
          result.actualToPath = actualToPath;
          result.redirectChain.push(actualToPath);
          
          if (actualToPath === expectedToPath) {
            result.success = true;
          } else {
            result.error = `Expected redirect to ${expectedToPath}, but got ${actualToPath}`;
          }
        } else {
          result.error = 'Redirect response missing Location header';
        }
      } else if (res.statusCode === 200) {
        result.error = `Expected redirect (301/302) but got 200. Page might not be redirecting.`;
      } else {
        result.error = `Unexpected status code: ${res.statusCode}`;
      }

      resolve(result);
    });

    req.on('error', (err) => {
      resolve({
        fromPath,
        expectedToPath,
        success: false,
        error: `Request failed: ${err.message}`,
        redirectChain: [fromPath]
      });
    });

    req.setTimeout(5000, () => {
      req.destroy();
      resolve({
        fromPath,
        expectedToPath,
        success: false,
        error: 'Request timeout',
        redirectChain: [fromPath]
      });
    });

    req.end();
  });
}

async function testRedirectChain(baseUrl, startPath, maxDepth = 10) {
  return new Promise((resolve) => {
    let currentPath = startPath;
    let redirectCount = 0;
    const chain = [startPath];
    
    function followRedirect() {
      if (redirectCount >= maxDepth) {
        resolve({
          startPath,
          chain,
          error: `Too many redirects (${redirectCount}). Possible redirect loop.`,
          success: false
        });
        return;
      }

      const url = new URL(currentPath, baseUrl);
      const client = url.protocol === 'https:' ? https : http;
      
      const options = {
        method: 'HEAD',
        hostname: url.hostname,
        port: url.port,
        path: url.pathname,
        headers: {
          'User-Agent': 'GenLogic-Chain-Tester/1.0'
        }
      };

      const req = client.request(options, (res) => {
        if (res.statusCode === 301 || res.statusCode === 302) {
          if (res.headers.location) {
            const redirectUrl = new URL(res.headers.location, baseUrl);
            const nextPath = redirectUrl.pathname;
            
            if (chain.includes(nextPath)) {
              resolve({
                startPath,
                chain: [...chain, nextPath],
                error: `Redirect loop detected: ${nextPath} already in chain`,
                success: false
              });
              return;
            }
            
            chain.push(nextPath);
            currentPath = nextPath;
            redirectCount++;
            
            // Continue following the chain
            setTimeout(followRedirect, 100);
          } else {
            resolve({
              startPath,
              chain,
              error: 'Redirect response missing Location header',
              success: false
            });
          }
        } else if (res.statusCode === 200) {
          resolve({
            startPath,
            chain,
            finalPath: currentPath,
            redirectCount,
            success: true
          });
        } else {
          resolve({
            startPath,
            chain,
            error: `Final status code: ${res.statusCode}`,
            success: false
          });
        }
      });

      req.on('error', (err) => {
        resolve({
          startPath,
          chain,
          error: `Request failed: ${err.message}`,
          success: false
        });
      });

      req.setTimeout(5000, () => {
        req.destroy();
        resolve({
          startPath,
          chain,
          error: 'Request timeout',
          success: false
        });
      });

      req.end();
    }

    followRedirect();
  });
}

async function runTests() {
  console.log('🔍 Testing GenLogic Redirects for Chains and Loops\n');
  
  for (const baseUrl of testUrls) {
    console.log(`\n📍 Testing against: ${baseUrl}`);
    console.log('=' .repeat(50));
    
    let successCount = 0;
    let totalTests = 0;
    
    // Test individual redirects
    console.log('\n1️⃣ Testing Individual Redirects:');
    for (const [fromPath, expectedToPath] of Object.entries(redirectsToTest)) {
      totalTests++;
      const result = await testRedirect(baseUrl, fromPath, expectedToPath);
      
      if (result.success) {
        console.log(`✅ ${fromPath} → ${expectedToPath}`);
        successCount++;
      } else {
        console.log(`❌ ${fromPath} → ${expectedToPath}`);
        console.log(`   Error: ${result.error}`);
        if (result.actualToPath) {
          console.log(`   Actual: ${result.actualToPath}`);
        }
      }
    }
    
    // Test for redirect chains
    console.log('\n2️⃣ Testing for Redirect Chains:');
    const uniquePaths = [...new Set(Object.keys(redirectsToTest))];
    
    for (const path of uniquePaths.slice(0, 5)) { // Test first 5 to avoid overwhelming
      const chainResult = await testRedirectChain(baseUrl, path);
      
      if (chainResult.success) {
        if (chainResult.redirectCount === 0) {
          console.log(`✅ ${path} → Direct (no redirect)`);
        } else if (chainResult.redirectCount === 1) {
          console.log(`✅ ${path} → ${chainResult.finalPath} (1 redirect)`);
        } else {
          console.log(`⚠️  ${path} → ${chainResult.finalPath} (${chainResult.redirectCount} redirects)`);
          console.log(`   Chain: ${chainResult.chain.join(' → ')}`);
        }
      } else {
        console.log(`❌ ${path}`);
        console.log(`   Error: ${chainResult.error}`);
        console.log(`   Chain: ${chainResult.chain.join(' → ')}`);
      }
    }
    
    console.log(`\n📊 Results: ${successCount}/${totalTests} redirects working correctly`);
  }
  
  console.log('\n🏁 Redirect testing complete!');
  console.log('\n💡 Recommendations:');
  console.log('   - All redirects should be single-hop (1 redirect max)');
  console.log('   - No redirect chains longer than 1');
  console.log('   - No redirect loops');
  console.log('   - All destination URLs should return 200 status');
}

// Run the tests
runTests().catch(console.error);
