import { Metadata } from 'next'
import ThreeTouchRuleClient from './ThreeTouchRuleClient'

export const metadata: Metadata = {
  title: 'The 3-Touch Rule That Converts 40% More Prospects | GenLogic Academy',
  description: 'Discover the psychological sequence that makes prospects want to buy from you. Learn the proven 3-touch follow-up strategy used by successful UK businesses.',
  keywords: 'follow-up sequence, conversion strategy, business automation, prospect nurturing, UK business growth',
  openGraph: {
    title: 'The 3-Touch Rule That Converts 40% More Prospects',
    description: 'Discover the psychological sequence that makes prospects want to buy from you. Learn the proven 3-touch follow-up strategy used by successful UK businesses.',
    type: 'article',
    url: 'https://genlogic.io/learn/fundamentals/three-touch-rule',
    images: [
      {
        url: '/academy-images/og-three-touch-rule.webp',
        width: 1200,
        height: 630,
        alt: 'The 3-Touch Rule That Converts 40% More Prospects',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'The 3-Touch Rule That Converts 40% More Prospects',
    description: 'Discover the psychological sequence that makes prospects want to buy from you. Learn the proven 3-touch follow-up strategy used by successful UK businesses.',
    images: ['/academy-images/og-three-touch-rule.webp'],
  },
}

export default function ThreeTouchRulePage() {
  return <ThreeTouchRuleClient />
}
