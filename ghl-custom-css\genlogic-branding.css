/* ===== GENLOGIC BRANDING FOR GOHIGHLEVEL ===== */
/* Custom CSS to match GenLogic.io website branding */
/* Apply this CSS in GoHighLevel Dashboard > Settings > Custom CSS */

/* Import GenLogic Font */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* ===== GENLOGIC COLOR VARIABLES ===== */
:root {
  /* Primary Brand Colors */
  --genlogic-primary: #a855f7;        /* Purple primary */
  --genlogic-primary-light: #c084fc;  /* Light purple */
  --genlogic-primary-dark: #7c3aed;   /* Dark purple */
  
  /* Secondary Colors */
  --genlogic-accent: #f97316;         /* Orange accent */
  --genlogic-success: #059669;        /* Green success */
  --genlogic-navy: #0f172a;           /* Navy dark */
  
  /* Neutral Colors */
  --genlogic-gray-50: #f8fafc;
  --genlogic-gray-100: #f1f5f9;
  --genlogic-gray-200: #e2e8f0;
  --genlogic-gray-300: #cbd5e1;
  --genlogic-gray-400: #94a3b8;
  --genlogic-gray-500: #64748b;
  --genlogic-gray-600: #475569;
  --genlogic-gray-700: #334155;
  --genlogic-gray-800: #1e293b;
  --genlogic-gray-900: #0f172a;
  
  /* Background Gradients */
  --genlogic-gradient-primary: linear-gradient(135deg, #a855f7 0%, #7c3aed 100%);
  --genlogic-gradient-accent: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
  --genlogic-gradient-subtle: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

/* ===== GLOBAL STYLING ===== */
* {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

body {
  background: var(--genlogic-gray-50) !important;
  color: var(--genlogic-gray-900) !important;
}

/* ===== BUTTONS ===== */
/* GoHighLevel Primary Buttons */
button[type="submit"],
.btn,
.btn-primary,
.button,
.submit-btn,
.form-submit,
[class*="submit"],
[class*="button"],
input[type="submit"] {
  background: var(--genlogic-gradient-primary) !important;
  border: none !important;
  color: white !important;
  font-weight: 600 !important;
  padding: 12px 24px !important;
  border-radius: 12px !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 4px 6px -1px rgba(168, 85, 247, 0.2) !important;
  cursor: pointer !important;
  font-family: 'Inter', sans-serif !important;
}

button[type="submit"]:hover,
.btn:hover,
.btn-primary:hover,
.button:hover,
.submit-btn:hover,
.form-submit:hover,
[class*="submit"]:hover,
[class*="button"]:hover,
input[type="submit"]:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 10px 15px -3px rgba(168, 85, 247, 0.3) !important;
  background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%) !important;
}

/* Secondary/Outline Buttons */
.btn-secondary,
.btn-outline,
.button-secondary,
[class*="outline"] {
  background: white !important;
  border: 2px solid var(--genlogic-primary) !important;
  color: var(--genlogic-primary) !important;
  font-weight: 600 !important;
  padding: 10px 22px !important;
  border-radius: 12px !important;
  transition: all 0.3s ease !important;
}

.btn-secondary:hover,
.btn-outline:hover,
.button-secondary:hover,
[class*="outline"]:hover {
  background: var(--genlogic-primary) !important;
  color: white !important;
  transform: translateY(-1px) !important;
}

/* ===== FORM ELEMENTS ===== */
/* GoHighLevel Form Containers */
.menu-field-wrap,
.form-field,
.field-wrapper,
[class*="field"],
.form-group {
  margin-bottom: 20px !important;
}

/* GoHighLevel Input Fields */
input[type="text"],
input[type="email"],
input[type="tel"],
input[type="password"],
input[type="number"],
input[type="url"],
textarea,
select,
.form-control,
.input,
[class*="input"],
.field input,
.menu-field-wrap input,
.menu-field-wrap textarea,
.menu-field-wrap select {
  border: 2px solid var(--genlogic-gray-200) !important;
  border-radius: 12px !important;
  padding: 12px 16px !important;
  font-size: 14px !important;
  transition: all 0.3s ease !important;
  background: white !important;
  width: 100% !important;
  font-family: 'Inter', sans-serif !important;
  box-sizing: border-box !important;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="tel"]:focus,
input[type="password"]:focus,
input[type="number"]:focus,
input[type="url"]:focus,
textarea:focus,
select:focus,
.form-control:focus,
.input:focus,
[class*="input"]:focus,
.field input:focus,
.menu-field-wrap input:focus,
.menu-field-wrap textarea:focus,
.menu-field-wrap select:focus {
  border-color: var(--genlogic-primary) !important;
  box-shadow: 0 0 0 3px rgba(168, 85, 247, 0.1) !important;
  outline: none !important;
}

/* GoHighLevel Labels */
label,
.label,
.field-label,
.menu-field-wrap label,
[class*="label"] {
  color: var(--genlogic-gray-700) !important;
  font-weight: 500 !important;
  margin-bottom: 6px !important;
  display: block !important;
  font-family: 'Inter', sans-serif !important;
}

/* ===== CARDS & CONTAINERS ===== */
.card,
.container,
.form-container,
.content-box {
  background: white !important;
  border-radius: 16px !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
  border: 1px solid var(--genlogic-gray-200) !important;
  padding: 24px !important;
}

/* ===== HEADERS & NAVIGATION ===== */
h1, h2, h3, h4, h5, h6 {
  color: var(--genlogic-gray-900) !important;
  font-weight: 700 !important;
  line-height: 1.2 !important;
}

h1 { font-size: 2.5rem !important; }
h2 { font-size: 2rem !important; }
h3 { font-size: 1.5rem !important; }
h4 { font-size: 1.25rem !important; }

/* Navigation Links */
.nav-link,
.menu-item,
a {
  color: var(--genlogic-gray-600) !important;
  text-decoration: none !important;
  font-weight: 500 !important;
  transition: color 0.3s ease !important;
}

.nav-link:hover,
.menu-item:hover,
a:hover {
  color: var(--genlogic-primary) !important;
}

/* ===== ALERTS & NOTIFICATIONS ===== */
.alert-success,
.success-message {
  background: rgba(5, 150, 105, 0.1) !important;
  border: 1px solid var(--genlogic-success) !important;
  color: var(--genlogic-success) !important;
  border-radius: 12px !important;
  padding: 12px 16px !important;
}

.alert-error,
.error-message {
  background: rgba(239, 68, 68, 0.1) !important;
  border: 1px solid #ef4444 !important;
  color: #dc2626 !important;
  border-radius: 12px !important;
  padding: 12px 16px !important;
}

.alert-warning,
.warning-message {
  background: rgba(249, 115, 22, 0.1) !important;
  border: 1px solid var(--genlogic-accent) !important;
  color: var(--genlogic-accent) !important;
  border-radius: 12px !important;
  padding: 12px 16px !important;
}

/* ===== TABLES ===== */
table {
  border-collapse: collapse !important;
  width: 100% !important;
  background: white !important;
  border-radius: 12px !important;
  overflow: hidden !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

th {
  background: var(--genlogic-gray-50) !important;
  color: var(--genlogic-gray-700) !important;
  font-weight: 600 !important;
  padding: 12px 16px !important;
  text-align: left !important;
  border-bottom: 1px solid var(--genlogic-gray-200) !important;
}

td {
  padding: 12px 16px !important;
  border-bottom: 1px solid var(--genlogic-gray-100) !important;
  color: var(--genlogic-gray-600) !important;
}

tr:hover {
  background: var(--genlogic-gray-50) !important;
}

/* ===== BADGES & TAGS ===== */
.badge,
.tag,
.status-badge {
  display: inline-block !important;
  padding: 4px 12px !important;
  border-radius: 20px !important;
  font-size: 12px !important;
  font-weight: 600 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
}

.badge-primary,
.tag-primary {
  background: rgba(168, 85, 247, 0.1) !important;
  color: var(--genlogic-primary) !important;
}

.badge-success,
.tag-success {
  background: rgba(5, 150, 105, 0.1) !important;
  color: var(--genlogic-success) !important;
}

.badge-warning,
.tag-warning {
  background: rgba(249, 115, 22, 0.1) !important;
  color: var(--genlogic-accent) !important;
}

/* ===== LOADING & PROGRESS ===== */
.loading-spinner {
  border: 3px solid var(--genlogic-gray-200) !important;
  border-top: 3px solid var(--genlogic-primary) !important;
  border-radius: 50% !important;
  animation: spin 1s linear infinite !important;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.progress-bar {
  background: var(--genlogic-gray-200) !important;
  border-radius: 10px !important;
  overflow: hidden !important;
}

.progress-fill {
  background: var(--genlogic-gradient-primary) !important;
  height: 8px !important;
  border-radius: 10px !important;
  transition: width 0.3s ease !important;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .card,
  .container,
  .form-container {
    padding: 16px !important;
    margin: 8px !important;
  }
  
  h1 { font-size: 2rem !important; }
  h2 { font-size: 1.75rem !important; }
  h3 { font-size: 1.25rem !important; }
}

/* ===== CUSTOM GENLOGIC CLASSES ===== */
.genlogic-brand-primary {
  color: var(--genlogic-primary) !important;
}

.genlogic-bg-primary {
  background: var(--genlogic-gradient-primary) !important;
  color: white !important;
}

.genlogic-border-primary {
  border-color: var(--genlogic-primary) !important;
}

.genlogic-shadow {
  box-shadow: 0 10px 15px -3px rgba(168, 85, 247, 0.2) !important;
}

.genlogic-rounded {
  border-radius: 16px !important;
}

/* ===== END GENLOGIC BRANDING ===== */
