import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import { SchemaMarkup, BreadcrumbSchema, ArticleSchema } from '@/components/seo/SchemaMarkup'
import BlogPostClient from './BlogPostClient'
import { getBlogPostBySlug, getRelatedPosts, getAllSlugs } from '../posts'

interface BlogPostPageProps {
  params: {
    slug: string
  }
}

// Generate static params for all blog posts
export async function generateStaticParams() {
  const slugs = getAllSlugs()
  return slugs.map((slug) => ({
    slug,
  }))
}

// Generate metadata for each blog post
export async function generateMetadata({ params }: BlogPostPageProps): Promise<Metadata> {
  const post = getBlogPostBySlug(params.slug)

  if (!post) {
    return {
      title: 'Blog Post Not Found | GenLogic',
      description: 'The requested blog post could not be found.',
    }
  }

  return {
    title: `${post.title} | GenLogic Blog`,
    description: post.excerpt,
    keywords: `${post.tags.join(', ')}, business automation UK, ${post.category.toLowerCase()}`,
    other: {
      'article:published_time': post.publishedAt,
      'article:author': post.author.name,
      'article:section': post.category,
      'article:tag': post.tags.join(','),
    },
    openGraph: {
      title: post.title,
      description: post.excerpt,
      url: `https://genlogic.io/blog/${post.slug}`,
      type: 'article',
      publishedTime: post.publishedAt,
      authors: [post.author.name],
      tags: post.tags,
      images: [
        {
          url: post.image ? `https://genlogic.io${post.image}` : 'https://genlogic.io/og-image.webp',
          width: 1200,
          height: 630,
          alt: post.title,
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: post.title,
      description: post.excerpt,
      images: [post.image ? `https://genlogic.io${post.image}` : 'https://genlogic.io/og-image.webp'],
    },
    alternates: {
      canonical: `https://genlogic.io/blog/${post.slug}`,
    },
  }
}

export default function BlogPostPage({ params }: BlogPostPageProps) {
  const post = getBlogPostBySlug(params.slug)

  if (!post) {
    notFound()
  }

  // Get related posts
  const relatedPosts = getRelatedPosts(post, 3)

  const breadcrumbItems = [
    { name: 'Home', url: 'https://genlogic.io' },
    { name: 'Blog', url: 'https://genlogic.io/blog' },
    { name: post.title, url: `https://genlogic.io/blog/${post.slug}` }
  ]

  return (
    <>
      <ArticleSchema
        title={post.title}
        description={post.excerpt}
        author={post.author.name}
        datePublished={post.publishedAt}
        dateModified={post.publishedAt}
        url={`https://genlogic.io/blog/${post.slug}`}
        image={post.image}
      />
      <BreadcrumbSchema items={breadcrumbItems} />
      <BlogPostClient post={post} relatedPosts={relatedPosts} />
    </>
  )
}
