'use client'

import { useCallback } from 'react'
import { 
  trackEvent, 
  trackGTMEvent, 
  trackDemoBooking, 
  trackContactSubmission,
  trackWebsiteQuote,
  trackButtonClick,
  trackScrollDepth,
  trackFileDownload,
  trackExternalLink
} from '@/components/analytics/GoogleAnalytics'

// Custom Analytics Hook
export const useAnalytics = () => {
  // Demo Booking Tracking
  const trackDemo = useCallback((businessType: string, businessName: string) => {
    trackDemoBooking(businessType, businessName)
  }, [])

  // Contact Form Tracking
  const trackContact = useCallback((subject: string, name: string) => {
    if (subject === 'website') {
      trackWebsiteQuote(name)
    } else {
      trackContactSubmission(subject, name)
    }
  }, [])

  // Button Click Tracking
  const trackClick = useCallback((buttonName: string, location: string) => {
    trackButtonClick(buttonName, location)
  }, [])

  // CTA Tracking (Call-to-Action)
  const trackCTA = useCallback((ctaName: string, page: string, position: string) => {
    trackEvent({
      action: 'cta_click',
      category: 'conversions',
      label: ctaName,
      custom_parameters: {
        cta_name: ctaName,
        page_location: page,
        cta_position: position
      }
    })

    trackGTMEvent({
      event: 'cta_click',
      cta_name: ctaName,
      page_location: page,
      cta_position: position
    })
  }, [])

  // Navigation Tracking
  const trackNavigation = useCallback((linkName: string, destination: string) => {
    trackEvent({
      action: 'navigation_click',
      category: 'engagement',
      label: linkName,
      custom_parameters: {
        link_name: linkName,
        destination: destination
      }
    })
  }, [])

  // Form Interaction Tracking
  const trackFormStart = useCallback((formName: string) => {
    trackEvent({
      action: 'form_start',
      category: 'engagement',
      label: formName,
      custom_parameters: {
        form_name: formName
      }
    })

    trackGTMEvent({
      event: 'form_start',
      form_name: formName
    })
  }, [])

  const trackFormError = useCallback((formName: string, errorField: string) => {
    trackEvent({
      action: 'form_error',
      category: 'engagement',
      label: formName,
      custom_parameters: {
        form_name: formName,
        error_field: errorField
      }
    })
  }, [])

  // Pricing Interaction Tracking
  const trackPricingView = useCallback((planName: string) => {
    trackEvent({
      action: 'pricing_plan_view',
      category: 'engagement',
      label: planName,
      custom_parameters: {
        plan_name: planName
      }
    })
  }, [])

  const trackPricingClick = useCallback((planName: string, action: string) => {
    trackEvent({
      action: 'pricing_plan_click',
      category: 'conversions',
      label: planName,
      custom_parameters: {
        plan_name: planName,
        pricing_action: action
      }
    })

    trackGTMEvent({
      event: 'pricing_interaction',
      plan_name: planName,
      pricing_action: action
    })
  }, [])

  // Blog Engagement Tracking
  const trackBlogRead = useCallback((articleTitle: string, readTime: number) => {
    trackEvent({
      action: 'blog_article_read',
      category: 'engagement',
      label: articleTitle,
      value: readTime,
      custom_parameters: {
        article_title: articleTitle,
        read_time_seconds: readTime
      }
    })
  }, [])

  const trackBlogShare = useCallback((articleTitle: string, platform: string) => {
    trackEvent({
      action: 'blog_article_share',
      category: 'engagement',
      label: articleTitle,
      custom_parameters: {
        article_title: articleTitle,
        share_platform: platform
      }
    })
  }, [])

  // Video Tracking
  const trackVideoPlay = useCallback((videoTitle: string, videoLocation: string) => {
    trackEvent({
      action: 'video_play',
      category: 'engagement',
      label: videoTitle,
      custom_parameters: {
        video_title: videoTitle,
        video_location: videoLocation
      }
    })

    trackGTMEvent({
      event: 'video_play',
      video_title: videoTitle,
      video_location: videoLocation
    })
  }, [])

  const trackVideoComplete = useCallback((videoTitle: string, duration: number) => {
    trackEvent({
      action: 'video_complete',
      category: 'engagement',
      label: videoTitle,
      value: duration,
      custom_parameters: {
        video_title: videoTitle,
        video_duration: duration
      }
    })
  }, [])

  // Search Tracking
  const trackSearch = useCallback((searchTerm: string, resultsCount: number) => {
    trackEvent({
      action: 'site_search',
      category: 'engagement',
      label: searchTerm,
      value: resultsCount,
      custom_parameters: {
        search_term: searchTerm,
        results_count: resultsCount
      }
    })
  }, [])

  // Exit Intent Tracking
  const trackExitIntent = useCallback((page: string, timeOnPage: number) => {
    trackEvent({
      action: 'exit_intent',
      category: 'engagement',
      label: page,
      value: timeOnPage,
      custom_parameters: {
        page_location: page,
        time_on_page: timeOnPage
      }
    })
  }, [])

  // Popup/Modal Tracking
  const trackPopupView = useCallback((popupName: string, trigger: string) => {
    trackEvent({
      action: 'popup_view',
      category: 'engagement',
      label: popupName,
      custom_parameters: {
        popup_name: popupName,
        popup_trigger: trigger
      }
    })
  }, [])

  const trackPopupClose = useCallback((popupName: string, method: string) => {
    trackEvent({
      action: 'popup_close',
      category: 'engagement',
      label: popupName,
      custom_parameters: {
        popup_name: popupName,
        close_method: method
      }
    })
  }, [])

  // Error Tracking
  const trackError = useCallback((errorType: string, errorMessage: string, page: string) => {
    trackEvent({
      action: 'error_occurred',
      category: 'errors',
      label: errorType,
      custom_parameters: {
        error_type: errorType,
        error_message: errorMessage,
        page_location: page
      }
    })
  }, [])

  // Performance Tracking
  const trackPageLoad = useCallback((page: string, loadTime: number) => {
    trackEvent({
      action: 'page_load_time',
      category: 'performance',
      label: page,
      value: loadTime,
      custom_parameters: {
        page_location: page,
        load_time_ms: loadTime
      }
    })
  }, [])

  return {
    // Conversion Tracking
    trackDemo,
    trackContact,
    trackCTA,
    
    // Engagement Tracking
    trackClick,
    trackNavigation,
    trackFormStart,
    trackFormError,
    trackPricingView,
    trackPricingClick,
    trackBlogRead,
    trackBlogShare,
    trackVideoPlay,
    trackVideoComplete,
    trackSearch,
    trackExitIntent,
    trackPopupView,
    trackPopupClose,
    
    // Technical Tracking
    trackError,
    trackPageLoad,
    
    // Direct access to base functions
    trackScrollDepth,
    trackFileDownload,
    trackExternalLink
  }
}
