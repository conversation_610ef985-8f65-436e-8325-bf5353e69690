import { Metadata } from 'next'
import { SchemaMarkup, BreadcrumbSchema } from '@/components/seo/SchemaMarkup'
import BlogClient from './BlogClient'
import { getAllBlogPosts, getCategories } from './posts'

export const metadata: Metadata = {
  title: 'Customer Conversion Insights | GenLogic Blog for UK Business Owners',
  description: 'Discover how UK business owners are converting 89% of prospects into customers. Practical advice on sales optimization, conversion rates, and revenue growth.',
  keywords: 'customer conversion UK, sales optimization, conversion rate tips, revenue growth, UK business advice, lead conversion strategies, sales automation',
  openGraph: {
    title: 'Customer Conversion Insights | GenLogic Blog for UK Business Owners',
    description: 'Discover how UK business owners are converting 89% of prospects into customers. Practical advice on sales optimization and revenue growth.',
    url: 'https://genlogic.io/blog',
    type: 'website',
    images: [
      {
        url: 'https://genlogic.io/og-blog.webp',
        width: 1200,
        height: 630,
        alt: 'GenLogic Blog - Business Automation Insights for UK Business Owners',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Business Automation Insights | GenLogic Blog for UK Business Owners',
    description: 'Discover how UK business owners are reclaiming their evenings with automation. Practical advice on productivity, growth, and getting your life back.',
    images: ['https://genlogic.io/og-blog.webp'],
  },
  alternates: {
    canonical: 'https://genlogic.io/blog',
  },
}

const blogSchema = {
  name: "GenLogic Business Automation Blog",
  description: "Practical business automation advice for UK business owners",
  url: "https://genlogic.io/blog",
  publisher: {
    name: "GenLogic",
    url: "https://genlogic.io"
  },
  mainEntityOfPage: {
    type: "Blog",
    name: "Business Automation Insights"
  }
}

const breadcrumbItems = [
  { name: 'Home', url: '/' },
  { name: 'Blog', url: '/blog' }
]





export default function BlogPage() {
  const posts = getAllBlogPosts()
  const categories = getCategories()

  return (
    <>
      <SchemaMarkup type="organization" />
      <BreadcrumbSchema items={breadcrumbItems} />
      <BlogClient posts={posts} categories={categories} />
    </>
  )
}
