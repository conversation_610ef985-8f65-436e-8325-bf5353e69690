'use client'

import { useState, useEffect, useCallback } from 'react'
import { motion, AnimatePresence, PanInfo } from 'framer-motion'
import Image from 'next/image'
import {
  Star, Quote, ChevronDown, ChevronUp,
  CheckCircle, Award, MapPin, Filter,
  Building2, ChevronLeft, ChevronRight
} from 'lucide-react'

interface Testimonial {
  id: string
  name: string
  business: string
  location: string
  quote: string
  fullStory: string
  beforeAfter: string
  result: string
  rating: 5
  avatar: string
  businessLogo: string
  industry: string
  businessType: string
  metrics: {
    timeSaved: number
    revenueIncrease: number
    noShowReduction: number
    customerSatisfaction: number
  }
  beforeMetrics: {
    adminHours: number
    noShows: number
    revenue: number
  }
  afterMetrics: {
    adminHours: number
    noShows: number
    revenue: number
  }
  videoThumbnail?: string
  hasVideo: boolean
  joinedDate: string
  verified: boolean
  featured: boolean
  googleReviewUrl: string
  trustpilotRating: number
}

export default function TestimonialsSection() {
  const [selectedIndustry, setSelectedIndustry] = useState<string>('all')
  const [expandedCard, setExpandedCard] = useState<string | null>(null)
  const [liveUpdates, setLiveUpdates] = useState<string[]>([])

  // Carousel state
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isAutoPlaying, setIsAutoPlaying] = useState(true)
  const [cardsPerView, setCardsPerView] = useState(3)

  const testimonials: Testimonial[] = [
    {
      id: "anthony-universal",
      name: "Anthony",
      business: "Universal Windows",
      location: "Middlesbrough",
      quote: "GenLogic helped us convert 89% more prospects into customers. We went from losing leads to competitors to being fully booked months in advance.",
      fullStory: "Before GenLogic, my evenings were consumed by admin work. I'd spend hours calling customers to confirm installation appointments, sending reminders, and dealing with no-shows. It was exhausting and taking time away from my family. Since implementing GenLogic, everything is automated. Customers get automatic reminders, can reschedule online, and I've seen an 85% reduction in no-shows. The best part? I now have my evenings back to spend with my children. My business is more profitable, and I'm actually enjoying being a business owner again.",
      beforeAfter: "From 3 hours of admin daily to 0",
      result: "89% prospect conversion rate",
      rating: 5,
      avatar: "🪟",
      businessLogo: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRjNGNEY2Ii8+CjxyZWN0IHg9IjgiIHk9IjEwIiB3aWR0aD0iMjQiIGhlaWdodD0iMjAiIGZpbGw9Im5vbmUiIHN0cm9rZT0iIzlCOUJBMyIgc3Ryb2tlLXdpZHRoPSIyIi8+CjxyZWN0IHg9IjEyIiB5PSIxNCIgd2lkdGg9IjQiIGhlaWdodD0iNCIgZmlsbD0iIzlCOUJBMyIvPgo8cmVjdCB4PSIyNCIgeT0iMTQiIHdpZHRoPSI0IiBoZWlnaHQ9IjQiIGZpbGw9IiM5QjlCQTMiLz4KPHJlY3QgeD0iMTgiIHk9IjI0IiB3aWR0aD0iNCIgaGVpZ2h0PSI2IiBmaWxsPSIjOUI5QkEzIi8+Cjwvc3ZnPgo=",
      industry: "Home Improvement",
      businessType: "Windows & Doors",
      metrics: {
        timeSaved: 21,
        revenueIncrease: 35,
        noShowReduction: 85,
        customerSatisfaction: 94
      },
      beforeMetrics: {
        adminHours: 21,
        noShows: 7,
        revenue: 4200
      },
      afterMetrics: {
        adminHours: 0,
        noShows: 1,
        revenue: 5670
      },
      hasVideo: true,
      videoThumbnail: "/api/placeholder/300/200",
      joinedDate: "2024-01-15",
      verified: true,
      featured: true,
      googleReviewUrl: "https://google.com/reviews",
      trustpilotRating: 5.0
    },
    {
      id: "david-curb-appeal",
      name: "David",
      business: "Curb Appeal",
      location: "Middlesbrough",
      quote: "We increased our revenue by £50,000 in the first year. The automated follow-up system turns every inquiry into a potential customer - no one slips through the cracks anymore.",
      fullStory: "Running a home improvement business while managing customer communications was a nightmare. My phone would ring during site visits, disrupting both me and my team. I was losing potential jobs because I couldn't answer, and existing customers were frustrated by the delays. GenLogic changed everything. Now customers book consultations online, get automatic project updates, and can manage their own schedules. My conversion rate is up 30% and customer satisfaction has improved dramatically.",
      beforeAfter: "From constant interruptions to focused work",
      result: "30% increase in conversion rate",
      rating: 5,
      avatar: "🏠",
      businessLogo: "/image0.png",
      industry: "Home Improvement",
      businessType: "General Contractor",
      metrics: {
        timeSaved: 15,
        revenueIncrease: 30,
        noShowReduction: 70,
        customerSatisfaction: 92
      },
      beforeMetrics: {
        adminHours: 15,
        noShows: 5,
        revenue: 3800
      },
      afterMetrics: {
        adminHours: 2,
        noShows: 1,
        revenue: 4940
      },
      hasVideo: true,
      videoThumbnail: "/api/placeholder/300/200",
      joinedDate: "2024-02-20",
      verified: true,
      featured: false,
      googleReviewUrl: "https://google.com/reviews",
      trustpilotRating: 4.9
    },
    {
      id: "danny-first-impression",
      name: "Danny",
      business: "First Impression Driveways",
      location: "Middlesbrough",
      quote: "I was terrible at following up with customers after quotes. I'd think about sending updates or booking follow-ups, but there was never time.",
      fullStory: "Customer follow-up was my biggest weakness. I knew I should be sending quote follow-ups, project updates, and booking maintenance visits, but there simply wasn't time. This meant missed opportunities for repeat business and poor customer relationships. GenLogic automated all of this. Customers now receive personalized follow-up messages, automatic project updates, and maintenance reminders. My repeat business has tripled, customer satisfaction is up 40%, and my Google reviews have never been better. The automation feels personal, not robotic.",
      beforeAfter: "From forgetting follow-ups to automatic customer care",
      result: "40% increase in repeat business",
      rating: 5,
      avatar: "🛣️",
      businessLogo: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRjNGNEY2Ii8+CjxyZWN0IHg9IjgiIHk9IjEwIiB3aWR0aD0iMjQiIGhlaWdodD0iMjAiIGZpbGw9Im5vbmUiIHN0cm9rZT0iIzlCOUJBMyIgc3Ryb2tlLXdpZHRoPSIyIi8+CjxyZWN0IHg9IjEyIiB5PSIxNCIgd2lkdGg9IjQiIGhlaWdodD0iNCIgZmlsbD0iIzlCOUJBMyIvPgo8cmVjdCB4PSIyNCIgeT0iMTQiIHdpZHRoPSI0IiBoZWlnaHQ9IjQiIGZpbGw9IiM5QjlCQTMiLz4KPHJlY3QgeD0iMTgiIHk9IjI0IiB3aWR0aD0iNCIgaGVpZ2h0PSI2IiBmaWxsPSIjOUI5QkEzIi8+Cjwvc3ZnPgo=",
      industry: "Home Improvement",
      businessType: "Driveways & Paving",
      metrics: {
        timeSaved: 18,
        revenueIncrease: 45,
        noShowReduction: 60,
        customerSatisfaction: 96
      },
      beforeMetrics: {
        adminHours: 18,
        noShows: 4,
        revenue: 8500
      },
      afterMetrics: {
        adminHours: 3,
        noShows: 1,
        revenue: 12325
      },
      hasVideo: false,
      joinedDate: "2023-11-10",
      verified: true,
      featured: true,
      googleReviewUrl: "https://google.com/reviews",
      trustpilotRating: 5.0
    },
    {
      id: "shaun-barrier-therm",
      name: "Shaun",
      business: "Barrier Therm",
      location: "Middlesbrough",
      quote: "Managing customer inquiries while on installation sites was impossible. I was missing calls and losing potential customers every day.",
      fullStory: "Running a windows and doors business meant I was constantly on-site doing installations, but customers needed quotes and updates throughout the day. I was missing important calls, losing potential customers, and existing clients were frustrated by delayed responses. GenLogic transformed my business operations. Now customers can book consultations online, get automatic project updates, and I can focus on quality installations without interruption. My conversion rate has increased by 40% and customer satisfaction is at an all-time high.",
      beforeAfter: "From missing calls to automated customer management",
      result: "40% increase in conversion rate",
      rating: 5,
      avatar: "🚪",
      businessLogo: "/logo.png",
      industry: "Home Improvement",
      businessType: "Windows & Doors",
      metrics: {
        timeSaved: 20,
        revenueIncrease: 40,
        noShowReduction: 75,
        customerSatisfaction: 95
      },
      beforeMetrics: {
        adminHours: 20,
        noShows: 6,
        revenue: 5200
      },
      afterMetrics: {
        adminHours: 2,
        noShows: 1,
        revenue: 7280
      },
      hasVideo: true,
      videoThumbnail: "/api/placeholder/300/200",
      joinedDate: "2024-03-05",
      verified: true,
      featured: false,
      googleReviewUrl: "https://google.com/reviews",
      trustpilotRating: 4.8
    }
  ]

  const industries = [
    { id: 'all', name: 'All Industries', count: testimonials.length },
    { id: 'Home Improvement', name: 'Home Improvement', count: 4 }
  ]

  const filteredTestimonials = selectedIndustry === 'all'
    ? testimonials
    : testimonials.filter(t => t.industry === selectedIndustry)

  // Responsive cards per view
  useEffect(() => {
    const updateCardsPerView = () => {
      if (window.innerWidth < 768) {
        setCardsPerView(1) // Mobile
      } else if (window.innerWidth < 1024) {
        setCardsPerView(2) // Tablet
      } else {
        setCardsPerView(3) // Desktop
      }
    }

    updateCardsPerView()
    window.addEventListener('resize', updateCardsPerView)
    return () => window.removeEventListener('resize', updateCardsPerView)
  }, [])

  // Carousel navigation functions
  const nextSlide = useCallback(() => {
    setCurrentIndex(prev => {
      const maxIndex = Math.max(0, filteredTestimonials.length - cardsPerView)
      return prev >= maxIndex ? 0 : prev + 1
    })
  }, [filteredTestimonials.length, cardsPerView])

  const prevSlide = useCallback(() => {
    setCurrentIndex(prev => {
      const maxIndex = Math.max(0, filteredTestimonials.length - cardsPerView)
      return prev <= 0 ? maxIndex : prev - 1
    })
  }, [filteredTestimonials.length, cardsPerView])

  const goToSlide = useCallback((index: number) => {
    const maxIndex = Math.max(0, filteredTestimonials.length - cardsPerView)
    setCurrentIndex(Math.min(index, maxIndex))
  }, [filteredTestimonials.length, cardsPerView])

  // Auto-play functionality
  useEffect(() => {
    if (!isAutoPlaying || filteredTestimonials.length <= cardsPerView) return

    const interval = setInterval(nextSlide, 5000)
    return () => clearInterval(interval)
  }, [isAutoPlaying, nextSlide, filteredTestimonials.length, cardsPerView])

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'ArrowLeft') {
        prevSlide()
      } else if (e.key === 'ArrowRight') {
        nextSlide()
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [prevSlide, nextSlide])

  // Reset carousel when filter changes
  useEffect(() => {
    setCurrentIndex(0)
  }, [selectedIndustry])

  // Live updates simulation
  useEffect(() => {
    const updates = [
      "Anthony from Universal Windows just saved 3 hours today",
      "David at Curb Appeal got 5 new project inquiries this morning",
      "Danny's driveways received 2 new 5-star reviews",
      "Shaun at Barrier Therm completed 3 installations without interruption",
      "New home improvement business joined from Middlesbrough"
    ]

    let index = 0
    const interval = setInterval(() => {
      setLiveUpdates(prev => {
        const newUpdates = [updates[index % updates.length], ...prev.slice(0, 2)]
        index++
        return newUpdates
      })
    }, 4000)

    return () => clearInterval(interval)
  }, [])

  // Calculate pagination dots
  const totalSlides = Math.max(1, Math.ceil(filteredTestimonials.length / cardsPerView))
  const currentSlide = Math.floor(currentIndex / cardsPerView)

  return (
    <section className="py-24 bg-gradient-to-b from-background to-primary-50/30 dark:to-primary-950/10 relative overflow-hidden">
      {/* Review Schema for SEO */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Organization",
            "@id": "https://genlogic.io#organization",
            "name": "GenLogic",
            "review": testimonials.slice(0, 4).map(testimonial => ({
              "@type": "Review",
              "reviewRating": {
                "@type": "Rating",
                "ratingValue": testimonial.rating,
                "bestRating": 5
              },
              "author": {
                "@type": "Person",
                "name": testimonial.name
              },
              "reviewBody": testimonial.quote,
              "publisher": {
                "@type": "Organization",
                "name": testimonial.business
              }
            })),
            "aggregateRating": {
              "@type": "AggregateRating",
              "ratingValue": 5,
              "reviewCount": testimonials.length,
              "bestRating": 5,
              "worstRating": 5
            }
          })
        }}
      />

      {/* Background Elements */}
      <div className="absolute top-0 left-0 w-full h-32 bg-gradient-to-b from-primary-50/20 to-transparent dark:from-primary-950/10"></div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Header Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: 'easeOut' }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-primary-100 to-accent-100 text-primary-800 text-sm font-semibold tracking-wide mb-8 dark:from-primary-900/30 dark:to-accent-900/30 dark:text-primary-400 border border-primary-200/50 dark:border-primary-800/50">
            <Award className="w-4 h-4 mr-2" />
            Real Business Transformations
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-foreground mb-6 leading-tight">
            From stressed to <span className="text-primary-700 dark:text-primary-400">successful</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed mb-8">
            These UK business owners went from working evenings to having their lives back. Here are their transformation stories.
          </p>

          {/* Live Updates Ticker */}
          <div className="bg-gradient-to-r from-green-50 to-green-100 dark:from-green-950/30 dark:to-green-900/30 rounded-2xl p-4 max-w-2xl mx-auto border border-green-200/50 dark:border-green-800/50">
            <div className="flex items-center justify-center mb-2">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse mr-2"></div>
              <span className="text-sm font-medium text-green-800 dark:text-green-300">Live Updates</span>
            </div>
            <AnimatePresence mode="wait">
              {liveUpdates.map((update, index) => (
                index === 0 && (
                  <motion.p
                    key={update}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="text-sm text-green-700 dark:text-green-400"
                  >
                    {update}
                  </motion.p>
                )
              ))}
            </AnimatePresence>
          </div>
        </motion.div>

        {/* Industry Filter */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
          className="flex flex-wrap justify-center gap-3 mb-12"
        >
          {industries.map((industry) => (
            <button
              key={industry.id}
              onClick={() => setSelectedIndustry(industry.id)}
              className={`px-6 py-3 rounded-2xl text-sm font-medium transition-all duration-300 ${
                selectedIndustry === industry.id
                  ? 'bg-primary-700 text-white shadow-lg shadow-primary-500/25'
                  : 'bg-background border border-border hover:border-primary-300 text-muted-foreground hover:text-foreground'
              }`}
            >
              <div className="flex items-center">
                <Filter className="w-4 h-4 mr-2" />
                {industry.name}
                <span className="ml-2 px-2 py-1 bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-400 rounded-full text-xs">
                  {industry.count}
                </span>
              </div>
            </button>
          ))}
        </motion.div>

        {/* Testimonial Carousel */}
        <div className="relative mb-16">
          {/* Carousel Container with proper spacing for arrows */}
          <div className="lg:mx-16 xl:mx-20">
            <div
              className="relative overflow-hidden py-4"
              onMouseEnter={() => setIsAutoPlaying(false)}
              onMouseLeave={() => setIsAutoPlaying(true)}
            >
              <motion.div
                className="flex gap-8 items-stretch"
                animate={{
                  x: `calc(-${currentIndex * (100 / cardsPerView)}% - ${currentIndex * (32 / cardsPerView)}px)`
                }}
                transition={{
                  type: "spring",
                  stiffness: 300,
                  damping: 30
                }}
                drag="x"
                dragConstraints={{
                  left: -((filteredTestimonials.length - cardsPerView) * (100 / cardsPerView + 32 / cardsPerView)),
                  right: 0
                }}
                onDragEnd={(_, info) => {
                  const threshold = 50
                  if (info.offset.x > threshold) {
                    prevSlide()
                  } else if (info.offset.x < -threshold) {
                    nextSlide()
                  }
                }}
              >
              {filteredTestimonials.map((testimonial, index) => (
                <motion.div
                  key={testimonial.id}
                  className="testimonial-card bg-background rounded-3xl p-6 shadow-lg hover:shadow-2xl transition-all duration-300 border border-primary-200/20 dark:border-primary-800/20 group relative overflow-hidden flex-shrink-0 backdrop-blur-sm flex flex-col h-full"
                  style={{
                    width: `calc(${100 / cardsPerView}% - ${(cardsPerView - 1) * 32 / cardsPerView}px)`,
                    minHeight: '520px',
                    minWidth: '340px',
                    maxWidth: '400px'
                  }}
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 + 0.1 }}
                >
              {/* Featured Badge */}
              {testimonial.featured && (
                <div className="absolute top-4 left-4 z-20">
                  <div className="bg-gradient-to-r from-yellow-400 to-orange-400 text-white px-2 py-1 rounded-full text-xs font-bold flex items-center shadow-lg">
                    <Award className="w-3 h-3 mr-1" />
                    Featured
                  </div>
                </div>
              )}

              {/* Background Pattern */}
              <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-primary-100/50 to-accent-100/50 dark:from-primary-900/20 dark:to-accent-900/20 rounded-full blur-2xl transform translate-x-16 -translate-y-16"></div>

              {/* Business Header - Logo Focused */}
              <div className="relative z-10 mb-6">
                <div className="flex items-start gap-4">
                  {/* Business Logo - Main Position */}
                  <div className="w-16 h-16 bg-white dark:bg-gray-100 rounded-2xl p-2 shadow-lg border border-gray-200 dark:border-gray-300 relative overflow-hidden flex-shrink-0">
                    {testimonial.businessLogo.includes('data:image/svg') ? (
                      <div className="w-full h-full bg-gradient-to-br from-primary-100 to-primary-200 dark:from-primary-800 dark:to-primary-900 rounded-xl flex items-center justify-center">
                        <Building2 className="w-6 h-6 text-primary-600 dark:text-primary-400" />
                      </div>
                    ) : (
                      <Image
                        src={testimonial.businessLogo}
                        alt={`${testimonial.business} logo`}
                        fill
                        className="object-contain p-1"
                        onError={(e) => {
                          // Fallback to building icon if logo fails to load
                          const target = e.currentTarget as HTMLImageElement
                          target.style.display = 'none'
                          const parent = target.parentElement
                          if (parent) {
                            parent.innerHTML = `
                              <div class="w-full h-full bg-gradient-to-br from-primary-100 to-primary-200 dark:from-primary-800 dark:to-primary-900 rounded-xl flex items-center justify-center">
                                <svg class="w-6 h-6 text-primary-600 dark:text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                </svg>
                              </div>
                            `
                          }
                        }}
                      />
                    )}
                  </div>

                  {/* Business Info - Flexible */}
                  <div className="flex-1 min-w-0 pr-2">
                    <div className="mb-1">
                      <h3
                        className="font-bold text-foreground text-base leading-tight truncate max-w-full overflow-hidden"
                        style={{
                          maxWidth: '200px',
                          whiteSpace: 'nowrap',
                          textOverflow: 'ellipsis'
                        }}
                        title={testimonial.business}
                      >
                        {testimonial.business}
                      </h3>
                      {testimonial.verified && (
                        <CheckCircle className="w-4 h-4 text-blue-500 inline-block ml-2" />
                      )}
                    </div>
                    <div className="text-sm text-muted-foreground mb-1 font-medium truncate">{testimonial.name}, Owner</div>
                    <div className="flex items-center text-xs text-muted-foreground">
                      <MapPin className="w-3 h-3 mr-1 flex-shrink-0" />
                      <span className="truncate">{testimonial.location}</span>
                    </div>
                  </div>
                </div>

                {/* Rating - Full Width Below */}
                <div className="flex items-center justify-between mt-4 pt-3 border-t border-primary-200/20 dark:border-primary-800/20">
                  <div className="flex items-center gap-1">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                    ))}
                    <span className="text-sm text-muted-foreground ml-2">5.0 Rating</span>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    Customer since {new Date(testimonial.joinedDate).toLocaleDateString('en-GB', { month: 'short', year: 'numeric' })}
                  </div>
                </div>
              </div>

              {/* Industry Tag */}
              <div className="relative z-10 mb-6">
                <span className="px-4 py-2 bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-400 rounded-full text-sm font-medium">
                  {testimonial.industry}
                </span>
              </div>

              {/* Quote */}
              <div className="relative z-10 mb-8">
                <Quote className="w-8 h-8 text-primary-300 dark:text-primary-700 mb-4" />
                <blockquote className="text-foreground leading-relaxed text-lg">
                  "{testimonial.quote}"
                </blockquote>
              </div>

              {/* Key Metrics - Simplified */}
              <div className="relative z-10 mb-8">
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-gradient-to-r from-blue-50/80 to-blue-100/80 dark:from-blue-950/20 dark:to-blue-900/20 rounded-2xl p-4 border border-blue-200/40 dark:border-blue-800/40 text-center backdrop-blur-sm">
                    <div className="text-2xl font-bold text-blue-700 dark:text-blue-400 mb-1">
                      {testimonial.metrics.timeSaved}h
                    </div>
                    <div className="text-xs text-blue-600 dark:text-blue-300 font-medium">
                      Saved Weekly
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-green-50/80 to-green-100/80 dark:from-green-950/20 dark:to-green-900/20 rounded-2xl p-4 border border-green-200/40 dark:border-green-800/40 text-center backdrop-blur-sm">
                    <div className="text-2xl font-bold text-green-700 dark:text-green-400 mb-1">
                      {testimonial.metrics.noShowReduction}%
                    </div>
                    <div className="text-xs text-green-600 dark:text-green-300 font-medium">
                      Fewer No-Shows
                    </div>
                  </div>
                </div>
              </div>

              {/* Expandable Full Story */}
              <div className="relative z-10 mb-6 flex-grow">
                <button
                  onClick={() => setExpandedCard(expandedCard === testimonial.id ? null : testimonial.id)}
                  className="flex items-center text-primary-700 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 text-sm font-medium transition-colors"
                >
                  {expandedCard === testimonial.id ? (
                    <>
                      <ChevronUp className="w-4 h-4 mr-1" />
                      Show Less
                    </>
                  ) : (
                    <>
                      <ChevronDown className="w-4 h-4 mr-1" />
                      Read Full Story
                    </>
                  )}
                </button>

                <AnimatePresence>
                  {expandedCard === testimonial.id && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                      transition={{ duration: 0.3 }}
                      className="mt-4 p-6 bg-muted/60 dark:bg-muted/40 rounded-2xl border border-primary-200/20 dark:border-primary-800/20 backdrop-blur-sm"
                    >
                      <p className="text-muted-foreground leading-relaxed mb-4">
                        {testimonial.fullStory}
                      </p>

                      {/* Additional metrics in expanded view */}
                      <div className="grid grid-cols-2 gap-3 pt-4 border-t border-primary-200/30 dark:border-primary-800/30">
                        <div className="text-center">
                          <div className="text-lg font-bold text-accent-700 dark:text-accent-400">
                            +{testimonial.metrics.revenueIncrease}%
                          </div>
                          <div className="text-xs text-muted-foreground">Revenue Growth</div>
                        </div>
                        <div className="text-center">
                          <div className="text-lg font-bold text-purple-700 dark:text-purple-400">
                            {testimonial.metrics.customerSatisfaction}%
                          </div>
                          <div className="text-xs text-muted-foreground">Satisfaction</div>
                        </div>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>

              {/* Footer - Simplified - Always at bottom */}
              <div className="relative z-10 border-t border-primary-200/30 dark:border-primary-800/30 pt-4 mt-auto">
                <div className="flex items-center justify-center">
                  <div className="text-xs text-green-700 dark:text-green-400 bg-green-100/80 dark:bg-green-900/20 px-3 py-1 rounded-full border border-green-200/50 dark:border-green-800/50 backdrop-blur-sm">
                    ✓ Verified Customer
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
              </motion.div>
            </div>
          </div>

          {/* Navigation Arrows - Desktop Only - Positioned outside the carousel container */}
          {filteredTestimonials.length > cardsPerView && (
            <>
              <button
                onClick={prevSlide}
                className="hidden lg:flex absolute left-0 top-1/2 -translate-y-1/2 w-14 h-14 bg-background/95 backdrop-blur-sm border border-primary-200/30 dark:border-primary-800/30 rounded-full items-center justify-center shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-110 z-10 hover:bg-primary-50 dark:hover:bg-primary-900/20 hover:border-primary-300 dark:hover:border-primary-700"
                aria-label="Previous testimonial"
              >
                <ChevronLeft className="w-6 h-6 text-muted-foreground hover:text-primary-600 transition-colors duration-300" />
              </button>

              <button
                onClick={nextSlide}
                className="hidden lg:flex absolute right-0 top-1/2 -translate-y-1/2 w-14 h-14 bg-background/95 backdrop-blur-sm border border-primary-200/30 dark:border-primary-800/30 rounded-full items-center justify-center shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-110 z-10 hover:bg-primary-50 dark:hover:bg-primary-900/20 hover:border-primary-300 dark:hover:border-primary-700"
                aria-label="Next testimonial"
              >
                <ChevronRight className="w-6 h-6 text-muted-foreground hover:text-primary-600 transition-colors duration-300" />
              </button>
            </>
          )}

          {/* Pagination Dots */}
          {filteredTestimonials.length > cardsPerView && (
            <div className="flex justify-center mt-8 gap-2">
              {Array.from({ length: totalSlides }).map((_, index) => (
                <button
                  key={index}
                  onClick={() => goToSlide(index * cardsPerView)}
                  className={`testimonial-pagination-dot ${
                    index === currentSlide
                      ? 'active bg-primary-700'
                      : 'bg-muted-foreground/30 hover:bg-muted-foreground/50'
                  }`}
                  aria-label={`Go to slide ${index + 1}`}
                />
              ))}
            </div>
          )}
        </div>

        {/* Enhanced Social Proof Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <div className="bg-gradient-to-br from-primary-50 to-accent-50 dark:from-primary-950/50 dark:to-accent-950/50 rounded-3xl p-12 max-w-6xl mx-auto border border-primary-200/50 dark:border-primary-800/50 shadow-xl relative overflow-hidden">
            {/* Background Pattern */}
            <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-primary-200/20 to-accent-200/20 dark:from-primary-800/10 dark:to-accent-800/10 rounded-full blur-3xl"></div>

            {/* Main Statistics */}
            <div className="relative z-10 grid grid-cols-1 md:grid-cols-4 gap-8 mb-12">
              <div className="text-center">
                <div className="text-5xl font-bold text-primary-700 dark:text-primary-400 mb-2">500+</div>
                <div className="text-muted-foreground font-medium">UK Businesses</div>
                <div className="text-xs text-muted-foreground">Automated</div>
              </div>
              <div className="text-center">
                <div className="text-5xl font-bold text-accent-700 dark:text-accent-400 mb-2">15,000+</div>
                <div className="text-muted-foreground font-medium">Hours Saved</div>
                <div className="text-xs text-muted-foreground">Monthly</div>
              </div>
              <div className="text-center">
                <div className="text-5xl font-bold text-green-700 dark:text-green-400 mb-2">85%</div>
                <div className="text-muted-foreground font-medium">No-Show Reduction</div>
                <div className="text-xs text-muted-foreground">Average</div>
              </div>
              <div className="text-center">
                <div className="text-5xl font-bold text-blue-700 dark:text-blue-400 mb-2">4.9★</div>
                <div className="text-muted-foreground font-medium">Customer Rating</div>
                <div className="text-xs text-muted-foreground">Trustpilot</div>
              </div>
            </div>



            <p className="relative z-10 text-xl text-foreground mb-8 max-w-3xl mx-auto">
              <strong>Join the growing community</strong> of UK business owners who've reclaimed their evenings and transformed their operations
            </p>

            {/* Industry Breakdown */}
            <div className="relative z-10 grid grid-cols-1 md:grid-cols-3 gap-4 mb-8 max-w-3xl mx-auto">
              <div className="bg-background rounded-2xl p-6 border border-border text-center">
                <div className="text-3xl mb-3">🪟</div>
                <div className="text-sm font-semibold text-foreground">Windows & Doors</div>
                <div className="text-xs text-muted-foreground">150+ installations</div>
              </div>
              <div className="bg-background rounded-2xl p-6 border border-border text-center">
                <div className="text-3xl mb-3">🏠</div>
                <div className="text-sm font-semibold text-foreground">General Contracting</div>
                <div className="text-xs text-muted-foreground">200+ projects</div>
              </div>
              <div className="bg-background rounded-2xl p-6 border border-border text-center">
                <div className="text-3xl mb-3">🛣️</div>
                <div className="text-sm font-semibold text-foreground">Driveways & Paving</div>
                <div className="text-xs text-muted-foreground">180+ driveways</div>
              </div>
            </div>

            {/* Recent Activity - Enhanced */}
            <motion.div
              className="relative z-10 bg-gradient-to-br from-background to-primary-50/30 dark:to-primary-950/10 rounded-3xl p-8 border border-primary-200/30 dark:border-primary-800/30 shadow-lg backdrop-blur-sm"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
            >
              {/* Header */}
              <div className="flex items-center justify-between mb-6">
                <h4 className="text-xl font-bold text-foreground flex items-center">
                  <div className="relative mr-4">
                    <div className="w-4 h-4 bg-green-500 rounded-full animate-pulse"></div>
                    <div className="absolute inset-0 w-4 h-4 bg-green-500 rounded-full animate-ping opacity-20"></div>
                  </div>
                  Recent Activity
                </h4>
                <div className="text-xs text-muted-foreground bg-muted/50 px-3 py-1 rounded-full">
                  Live Updates
                </div>
              </div>

              {/* Activity Grid */}
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* New Businesses */}
                <motion.div
                  className="bg-white/50 dark:bg-gray-900/30 rounded-2xl p-5 border border-green-200/50 dark:border-green-800/30 hover:shadow-md transition-all duration-300"
                  whileHover={{ scale: 1.02 }}
                  initial={{ opacity: 0, x: -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.3 }}
                  viewport={{ once: true }}
                >
                  <div className="flex items-start gap-3">
                    <div className="w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-xl flex items-center justify-center flex-shrink-0">
                      <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400" />
                    </div>
                    <div className="flex-1">
                      <div className="text-2xl font-bold text-green-700 dark:text-green-400 mb-1">3</div>
                      <div className="text-sm font-medium text-foreground mb-1">New Businesses</div>
                      <div className="text-xs text-muted-foreground">Joined today</div>
                    </div>
                  </div>
                </motion.div>

                {/* Consultations */}
                <motion.div
                  className="bg-white/50 dark:bg-gray-900/30 rounded-2xl p-5 border border-blue-200/50 dark:border-blue-800/30 hover:shadow-md transition-all duration-300"
                  whileHover={{ scale: 1.02 }}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.4 }}
                  viewport={{ once: true }}
                >
                  <div className="flex items-start gap-3">
                    <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-xl flex items-center justify-center flex-shrink-0">
                      <CheckCircle className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div className="flex-1">
                      <div className="text-2xl font-bold text-blue-700 dark:text-blue-400 mb-1">1,247</div>
                      <div className="text-sm font-medium text-foreground mb-1">Consultations</div>
                      <div className="text-xs text-muted-foreground">Automated this week</div>
                    </div>
                  </div>
                </motion.div>

                {/* Satisfaction */}
                <motion.div
                  className="bg-white/50 dark:bg-gray-900/30 rounded-2xl p-5 border border-purple-200/50 dark:border-purple-800/30 hover:shadow-md transition-all duration-300"
                  whileHover={{ scale: 1.02 }}
                  initial={{ opacity: 0, x: 20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.5 }}
                  viewport={{ once: true }}
                >
                  <div className="flex items-start gap-3">
                    <div className="w-10 h-10 bg-purple-100 dark:bg-purple-900/30 rounded-xl flex items-center justify-center flex-shrink-0">
                      <CheckCircle className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                    </div>
                    <div className="flex-1">
                      <div className="text-2xl font-bold text-purple-700 dark:text-purple-400 mb-1">98%</div>
                      <div className="text-sm font-medium text-foreground mb-1">Satisfaction</div>
                      <div className="text-xs text-muted-foreground">This month</div>
                    </div>
                  </div>
                </motion.div>
              </div>

              {/* Bottom Accent */}
              <div className="mt-6 pt-4 border-t border-primary-200/20 dark:border-primary-800/20">
                <div className="flex items-center justify-center text-xs text-muted-foreground">
                  <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></div>
                  Real-time data from GenLogic platform
                </div>
              </div>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
