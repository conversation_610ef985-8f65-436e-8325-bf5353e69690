import { Metadata } from 'next'
import ScaleBreakthroughClient from './ScaleBreakthroughClient'

export const metadata: Metadata = {
  title: 'The Scale Breakthrough: From £100k to £1M | GenLogic Academy',
  description: 'Why 89% of businesses plateau at £100k and the automation evolution that breaks through to £1M+. Learn the 4 scaling bottlenecks and systematic breakthrough strategies.',
  keywords: 'business scaling, growth strategy, automation scaling, UK business growth, scaling bottlenecks, business breakthrough',
  openGraph: {
    title: 'The Scale Breakthrough: From £100k to £1M',
    description: 'Why 89% of businesses plateau at £100k and the automation evolution that breaks through to £1M+. Learn the 4 scaling bottlenecks and systematic breakthrough strategies.',
    type: 'article',
    url: 'https://genlogic.io/learn/fundamentals/scale-breakthrough',
    images: [
      {
        url: '/academy-images/og-scale-breakthrough.webp',
        width: 1200,
        height: 630,
        alt: 'The Scale Breakthrough: From £100k to £1M',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'The Scale Breakthrough: From £100k to £1M',
    description: 'Why 89% of businesses plateau at £100k and the automation evolution that breaks through to £1M+. Learn the 4 scaling bottlenecks and systematic breakthrough strategies.',
    images: ['/academy-images/og-scale-breakthrough.webp'],
  },
}

export default function ScaleBreakthroughPage() {
  return <ScaleBreakthroughClient />
}
