'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { useAnalytics } from '@/hooks/useAnalytics'
import { CheckCircle, Clock, Calendar, Star } from 'lucide-react'
import type { Metadata } from 'next'

export default function DemoPage() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    business: '',
    businessType: '',
    message: '',
    gdprConsent: false
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [validationErrors, setValidationErrors] = useState<{[key: string]: string}>({})
  const [submitError, setSubmitError] = useState<string>('')

  // Analytics tracking
  const analytics = useAnalytics()

  // Track form start when user first interacts
  useEffect(() => {
    let formStartTracked = false

    const trackFormStart = () => {
      if (!formStartTracked) {
        analytics.trackFormStart('demo_booking_form')
        formStartTracked = true
      }
    }

    // Add event listeners to form fields
    const formFields = document.querySelectorAll('#demo-form input, #demo-form select, #demo-form textarea')
    formFields.forEach(field => {
      field.addEventListener('focus', trackFormStart, { once: true })
    })

    return () => {
      formFields.forEach(field => {
        field.removeEventListener('focus', trackFormStart)
      })
    }
  }, [])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    setSubmitError('')
    setValidationErrors({})

    try {
      const response = await fetch('/api/forms/submit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          formType: 'demo',
          gdprConsent: Boolean(formData.gdprConsent)
        })
      })

      const result = await response.json()

      if (result.success) {
        setIsSubmitted(true)
        setValidationErrors({})
        setSubmitError('')

        // Track successful demo booking
        analytics.trackDemo(formData.businessType, formData.business)
      } else {
        // Handle validation errors
        console.error('Form submission failed:', result.error)

        // Show detailed validation errors if available
        if (result.details && result.details.length > 0) {
          const errors: {[key: string]: string} = {}
          result.details.forEach((detail: any) => {
            errors[detail.field] = detail.message
            // Track form errors for analytics
            analytics.trackFormError('demo_booking_form', detail.field)
          })
          setValidationErrors(errors)
          setSubmitError('Please fix the errors below and try again.')
        } else {
          setValidationErrors({})
          setSubmitError(result.error || 'Something went wrong. Please try again.')
          // Track general form error
          analytics.trackFormError('demo_booking_form', 'general_error')
        }
      }
    } catch (error) {
      console.error('Network error:', error)
      setSubmitError('Network error. Please check your connection and try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target
    const checked = (e.target as HTMLInputElement).checked

    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    })

    // Clear validation error for this field
    if (validationErrors[name]) {
      setValidationErrors({
        ...validationErrors,
        [name]: ''
      })
    }
  }

  if (isSubmitted) {
    return (
      <div className="min-h-screen flex items-center justify-center header-spacing py-20">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.6 }}
          className="max-w-2xl mx-auto text-center px-4"
        >
          <div className="bg-background border border-primary-200/20 dark:border-primary-800/20 rounded-3xl p-12 shadow-lg">
            <div className="w-20 h-20 bg-primary-100 dark:bg-primary-900/30 rounded-full flex items-center justify-center mx-auto mb-8">
              <CheckCircle className="w-10 h-10 text-primary-700 dark:text-primary-400" />
            </div>
            <h1 className="text-3xl font-bold text-foreground mb-6">
              Demo Request <span className="text-primary-700 dark:text-primary-400">Received!</span>
            </h1>
            <p className="text-xl text-muted-foreground mb-6 leading-relaxed">
              <strong className="text-foreground">We're excited to show you how GenLogic will give you your evenings back.</strong> Our team will contact you within 24 hours with a calendar link to schedule your personalized 15-minute demo.
            </p>

            <div className="grid md:grid-cols-3 gap-6 mb-8">
              <div className="bg-primary-50 dark:bg-primary-950/30 border border-primary-200/30 dark:border-primary-800/30 rounded-2xl p-4">
                <Clock className="w-6 h-6 text-primary-700 dark:text-primary-400 mx-auto mb-2" />
                <p className="text-sm text-foreground font-medium">15 Minutes</p>
                <p className="text-xs text-muted-foreground">Quick & focused</p>
              </div>

              <div className="bg-primary-50 dark:bg-primary-950/30 border border-primary-200/30 dark:border-primary-800/30 rounded-2xl p-4">
                <Calendar className="w-6 h-6 text-primary-700 dark:text-primary-400 mx-auto mb-2" />
                <p className="text-sm text-foreground font-medium">Your Schedule</p>
                <p className="text-xs text-muted-foreground">We work around you</p>
              </div>

              <div className="bg-primary-50 dark:bg-primary-950/30 border border-primary-200/30 dark:border-primary-800/30 rounded-2xl p-4">
                <Star className="w-6 h-6 text-primary-700 dark:text-primary-400 mx-auto mb-2" />
                <p className="text-sm text-foreground font-medium">No Pressure</p>
                <p className="text-xs text-muted-foreground">Just solutions</p>
              </div>
            </div>

            <p className="text-lg text-muted-foreground mb-6">
              <strong className="text-foreground">What to expect:</strong> We'll show you exactly how GenLogic reduces no-shows, saves you 20+ hours weekly, and helps you finish work by 6pm every day.
            </p>

            <div className="bg-primary-50 dark:bg-primary-950/30 border border-primary-200/30 dark:border-primary-800/30 rounded-2xl p-6 mt-6">
              <div className="flex items-center space-x-3 mb-3">
                <div className="w-8 h-8 bg-primary-700 dark:bg-primary-600 text-white rounded-full flex items-center justify-center text-sm font-bold">!</div>
                <h3 className="text-lg font-bold text-primary-900 dark:text-primary-100">Next Steps</h3>
              </div>
              <p className="text-primary-800 dark:text-primary-200 font-medium">
                1. <strong>Check your email</strong> for confirmation details<br/>
                2. <strong>We'll send you a calendar link</strong> within 24 hours to choose your preferred demo time
              </p>
            </div>
          </div>
        </motion.div>
      </div>
    )
  }

  return (
    <div className="relative w-full overflow-hidden">
      {/* Header */}
      <section className="header-spacing py-20 relative">
        <div className="container relative z-10 mx-auto px-4 md:px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: 'easeOut' }}
            className="mx-auto max-w-4xl text-center"
          >
            <div className="bg-background border border-primary-200/20 dark:border-primary-800/20 rounded-3xl p-12 shadow-lg backdrop-blur-sm">
              <h1 className="text-4xl font-bold text-foreground sm:text-5xl md:text-6xl mb-6">
                Stop Working <span className="text-primary-700 dark:text-primary-400">Late Tonight</span>
              </h1>
              <p className="text-xl text-muted-foreground leading-relaxed mb-6">
                <strong className="text-foreground">Book a 15-minute demo and see exactly how you'll get your evenings back.</strong> We'll show you real examples from UK businesses just like yours.
              </p>
              <div className="flex items-center justify-center gap-6 text-sm text-muted-foreground">
                <div className="flex items-center gap-2">
                  <Clock className="w-4 h-4 text-primary-700 dark:text-primary-400" />
                  <span>15 minutes</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-primary-700 dark:text-primary-400" />
                  <span>No pressure</span>
                </div>
                <div className="flex items-center gap-2">
                  <Star className="w-4 h-4 text-yellow-400 fill-current" />
                  <span>500+ happy customers</span>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Demo Form */}
      <section className="py-20">
        <div className="container mx-auto px-4 md:px-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 max-w-6xl mx-auto">

            {/* Form */}
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, ease: 'easeOut' }}
              viewport={{ once: true }}
            >
              <div className="bg-background border border-primary-200/20 dark:border-primary-800/20 rounded-3xl p-8 shadow-lg h-full">
                <h2 className="text-2xl font-bold text-foreground mb-6">
                  Tell Us About Your <span className="text-primary-700 dark:text-primary-400">Challenges</span>
                </h2>
                <p className="text-muted-foreground mb-8 leading-relaxed">
                  <strong className="text-foreground">Help us understand your specific situation</strong> so we can show you exactly how GenLogic will solve your biggest time-wasting problems.
                </p>

                {/* Error Message */}
                {submitError && (
                  <div className="mb-6 p-4 bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800/30 rounded-2xl">
                    <p className="text-red-800 dark:text-red-300 text-sm font-medium">
                      {submitError}
                    </p>
                  </div>
                )}

                <form id="demo-form" onSubmit={handleSubmit} className="space-y-6">
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-foreground mb-2">
                      Full Name *
                    </label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      required
                      value={formData.name}
                      onChange={handleChange}
                      className={`w-full px-4 py-3 border rounded-2xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-background text-foreground transition-all duration-300 ${
                        validationErrors.name
                          ? 'border-red-300 dark:border-red-700'
                          : 'border-primary-200/30 dark:border-primary-800/30'
                      }`}
                      placeholder="Your full name"
                    />
                    {validationErrors.name && (
                      <p className="mt-2 text-sm text-red-600 dark:text-red-400">
                        {validationErrors.name}
                      </p>
                    )}
                  </div>

                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-foreground mb-2">
                      Email Address *
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      required
                      value={formData.email}
                      onChange={handleChange}
                      className={`w-full px-4 py-3 border rounded-2xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-background text-foreground transition-all duration-300 ${
                        validationErrors.email
                          ? 'border-red-300 dark:border-red-700'
                          : 'border-primary-200/30 dark:border-primary-800/30'
                      }`}
                      placeholder="<EMAIL>"
                    />
                    {validationErrors.email && (
                      <p className="mt-2 text-sm text-red-600 dark:text-red-400">
                        {validationErrors.email}
                      </p>
                    )}
                  </div>

                  <div>
                    <label htmlFor="phone" className="block text-sm font-medium text-foreground mb-2">
                      Phone Number
                    </label>
                    <input
                      type="tel"
                      id="phone"
                      name="phone"
                      value={formData.phone}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-primary-200/30 dark:border-primary-800/30 rounded-2xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-background text-foreground transition-all duration-300"
                      placeholder="07xxx xxx xxx"
                    />
                  </div>

                  <div>
                    <label htmlFor="business" className="block text-sm font-medium text-foreground mb-2">
                      Business Name *
                    </label>
                    <input
                      type="text"
                      id="business"
                      name="business"
                      required
                      value={formData.business}
                      onChange={handleChange}
                      className={`w-full px-4 py-3 border rounded-2xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-background text-foreground transition-all duration-300 ${
                        validationErrors.business
                          ? 'border-red-300 dark:border-red-700'
                          : 'border-primary-200/30 dark:border-primary-800/30'
                      }`}
                      placeholder="Your business name"
                    />
                    {validationErrors.business && (
                      <p className="mt-2 text-sm text-red-600 dark:text-red-400">
                        {validationErrors.business}
                      </p>
                    )}
                  </div>

                  <div>
                    <label htmlFor="businessType" className="block text-sm font-medium text-foreground mb-2">
                      Business Type *
                    </label>
                    <select
                      id="businessType"
                      name="businessType"
                      required
                      value={formData.businessType}
                      onChange={handleChange}
                      className={`w-full px-4 py-3 border rounded-2xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-background text-foreground transition-all duration-300 ${
                        validationErrors.businessType
                          ? 'border-red-300 dark:border-red-700'
                          : 'border-primary-200/30 dark:border-primary-800/30'
                      }`}
                    >
                      <option value="">Select your business type</option>
                      <option value="salon">Hair Salon / Beauty</option>
                      <option value="fitness">Gym / Fitness Studio</option>
                      <option value="healthcare">Healthcare / Clinic</option>
                      <option value="automotive">Automotive Services</option>
                      <option value="home-improvement">Home Improvement</option>
                      <option value="professional">Professional Services</option>
                      <option value="other">Other</option>
                    </select>
                    {validationErrors.businessType && (
                      <p className="mt-2 text-sm text-red-600 dark:text-red-400">
                        {validationErrors.businessType}
                      </p>
                    )}
                  </div>

                  <div>
                    <label htmlFor="message" className="block text-sm font-medium text-foreground mb-2">
                      What's your biggest time-wasting challenge?
                    </label>
                    <textarea
                      id="message"
                      name="message"
                      rows={4}
                      value={formData.message}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-primary-200/30 dark:border-primary-800/30 rounded-2xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-background text-foreground transition-all duration-300 resize-none"
                      placeholder="e.g., 'Spending 2 hours daily on booking confirmations and chasing no-shows'"
                    />
                  </div>

                  <div>
                    <div className="flex items-start">
                      <input
                        type="checkbox"
                        id="gdprConsent"
                        name="gdprConsent"
                        required
                        checked={formData.gdprConsent}
                        onChange={handleChange}
                        className="mt-1 h-4 w-4 text-primary-600 focus:ring-primary-500 border-primary-300 rounded"
                      />
                      <label htmlFor="gdprConsent" className="ml-3 text-sm text-muted-foreground">
                        I consent to GenLogic contacting me about my demo request and processing my business data.
                        <strong className="text-foreground"> This consent is required under UK GDPR.</strong>
                        <br />
                        <span className="text-xs">
                          Your data will be processed according to our <a href="/privacy" className="text-primary-700 dark:text-primary-400 hover:underline">Privacy Policy</a>.
                        </span>
                      </label>
                    </div>
                    {validationErrors.gdprConsent && (
                      <p className="mt-2 text-sm text-red-600 dark:text-red-400">
                        {validationErrors.gdprConsent}
                      </p>
                    )}
                  </div>

                  <button
                    type="submit"
                    disabled={isSubmitting || !formData.gdprConsent}
                    className="w-full px-6 py-4 bg-primary-700 hover:bg-primary-800 dark:bg-primary-600 dark:hover:bg-primary-700 text-white rounded-2xl transition-all duration-300 font-medium shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed text-lg"
                  >
                    {isSubmitting ? 'Booking Your Demo...' : 'Book My Free Demo'}
                  </button>
                </form>
              </div>
            </motion.div>

            {/* Benefits */}
            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, ease: 'easeOut' }}
              viewport={{ once: true }}
            >
              <div className="bg-primary-50 dark:bg-primary-950/30 border border-primary-200/30 dark:border-primary-800/30 rounded-3xl p-8 shadow-lg h-full">
                <h3 className="text-2xl font-bold text-foreground mb-6">
                  What You'll See in Your <span className="text-primary-700 dark:text-primary-400">Demo</span>
                </h3>
                <p className="text-muted-foreground mb-8 leading-relaxed">
                  <strong className="text-foreground">This isn't a generic sales pitch.</strong> We'll show you exactly how GenLogic solves your specific challenges.
                </p>

                <div className="space-y-6">
                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-10 h-10 bg-primary-100 dark:bg-primary-900/30 rounded-2xl flex items-center justify-center mr-4">
                      <CheckCircle className="w-5 h-5 text-primary-700 dark:text-primary-400" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-foreground mb-2">Live Automation Demo</h4>
                      <p className="text-muted-foreground">Watch us set up automated reminders for your business type in real-time</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-10 h-10 bg-primary-100 dark:bg-primary-900/30 rounded-2xl flex items-center justify-center mr-4">
                      <Clock className="w-5 h-5 text-primary-700 dark:text-primary-400" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-foreground mb-2">Time Savings Calculator</h4>
                      <p className="text-muted-foreground">See exactly how many hours weekly you'll save (typically 20+ hours)</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-10 h-10 bg-primary-100 dark:bg-primary-900/30 rounded-2xl flex items-center justify-center mr-4">
                      <Star className="w-5 h-5 text-primary-700 dark:text-primary-400" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-foreground mb-2">Real Customer Examples</h4>
                      <p className="text-muted-foreground">See how Sarah from Manchester went from 9pm finishes to 6pm every day</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-10 h-10 bg-primary-100 dark:bg-primary-900/30 rounded-2xl flex items-center justify-center mr-4">
                      <Calendar className="w-5 h-5 text-primary-700 dark:text-primary-400" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-foreground mb-2">Instant Setup Available</h4>
                      <p className="text-muted-foreground">If you love what you see, we can get you started today</p>
                    </div>
                  </div>
                </div>

                <div className="mt-8 p-6 bg-background border border-primary-200/30 dark:border-primary-800/30 rounded-2xl">
                  <div className="text-center">
                    <div className="flex items-center justify-center mb-3">
                      <CheckCircle className="w-5 h-5 text-primary-700 dark:text-primary-400 mr-2" />
                      <span className="text-foreground font-semibold">
                        15-minute demo
                      </span>
                    </div>
                    <p className="text-muted-foreground text-sm">
                      <strong className="text-foreground">No pressure, no sales pitch</strong> - just real solutions to your biggest challenges
                    </p>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>
    </div>
  )
}
