'use client'

import { useEffect, useState } from 'react'
import { motion } from 'framer-motion'
import { 
  ArrowLeft, 
  Clock, 
  Users, 
  CheckCircle,
  ArrowRight,
  Brain,
  MessageSquare,
  Phone,
  Mail,
  Zap,
  TrendingUp,
  TrendingDown,
  Shield,
  Target,
  Lightbulb,
  AlertTriangle,
  Eye,
  Heart,
  Star,
  Headphones,
  Bot,
  UserCheck,
  Award,
  BarChart3,
  Gift,
  Share2,
  DollarSign,
  Calculator
} from 'lucide-react'
import Link from 'next/link'

const pricingMistakes = [
  {
    mistake: "Cost-Plus Pricing",
    traditional: "Add 20-30% markup to costs",
    problem: "Ignores customer value perception",
    impact: "Leaves 40-60% revenue on table",
    color: "red"
  },
  {
    mistake: "Competitor Matching", 
    traditional: "Price same as competitors",
    problem: "Commoditizes your service",
    impact: "Race to bottom, no differentiation",
    color: "orange"
  },
  {
    mistake: "Single Price Point",
    traditional: "One price for everyone",
    problem: "Misses different value segments",
    impact: "Loses 67% of potential customers",
    color: "yellow"
  },
  {
    mistake: "No Value Communication",
    traditional: "List features and price",
    problem: "Customers don't understand ROI",
    impact: "89% focus on price not value",
    color: "red"
  }
]

const valueLadderLevels = [
  {
    level: "Entry",
    title: "Value Demonstration",
    price: "£47-97",
    psychology: "Low risk trial to prove value",
    examples: ["Consultation", "Audit", "Mini-service", "Assessment"],
    conversion: "45%",
    purpose: "Build trust and demonstrate expertise",
    color: "blue"
  },
  {
    level: "Core", 
    title: "Main Solution",
    price: "£297-997",
    psychology: "Solves primary problem",
    examples: ["Main service", "Monthly package", "Core solution", "Standard offering"],
    conversion: "67%",
    purpose: "Deliver primary value and results",
    color: "green"
  },
  {
    level: "Premium",
    title: "Enhanced Experience",
    price: "£1,497-4,997",
    psychology: "Faster, better, more comprehensive",
    examples: ["VIP service", "Done-for-you", "Premium package", "Accelerated program"],
    conversion: "23%",
    purpose: "Maximize value for committed customers",
    color: "purple"
  },
  {
    level: "Elite",
    title: "Transformation",
    price: "£5,000+",
    psychology: "Complete transformation/outcome",
    examples: ["Mastermind", "1-on-1 intensive", "Complete transformation", "Annual retainer"],
    conversion: "8%",
    purpose: "Ultimate outcome for serious buyers",
    color: "gold"
  }
]

export default function PricingPsychologyRevolutionClient() {
  // Pricing ROI Calculator state
  const [pricingData, setPricingData] = useState({
    monthlyCustomers: '',
    currentAvgPrice: '',
    currentConversion: '',
    targetAvgPrice: '',
    targetConversion: '',
    operatingMargin: ''
  })

  // Calculate pricing ROI
  const currentRevenue = (parseFloat(pricingData.monthlyCustomers) || 0) * (parseFloat(pricingData.currentAvgPrice) || 0) * (parseFloat(pricingData.currentConversion) || 0) / 100
  const targetRevenue = (parseFloat(pricingData.monthlyCustomers) || 0) * (parseFloat(pricingData.targetAvgPrice) || 0) * (parseFloat(pricingData.targetConversion) || 0) / 100
  const revenueIncrease = targetRevenue - currentRevenue
  const annualIncrease = revenueIncrease * 12
  const profitIncrease = annualIncrease * ((parseFloat(pricingData.operatingMargin) || 30) / 100)

  const handlePricingInputChange = (field: string, value: string) => {
    setPricingData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  // Mark lesson as completed when user visits
  useEffect(() => {
    const markAsCompleted = () => {
      const saved = localStorage.getItem('genlogic-academy-progress')
      const completed = saved ? JSON.parse(saved) : []
      if (!completed.includes('pricing-psychology-revolution')) {
        completed.push('pricing-psychology-revolution')
        localStorage.setItem('genlogic-academy-progress', JSON.stringify(completed))
        
        // Dispatch custom event to notify other components
        window.dispatchEvent(new CustomEvent('academyProgressUpdate'))
      }
    }
    
    // Mark as completed after user has been on page for 30 seconds
    const timer = setTimeout(markAsCompleted, 30000)
    return () => clearTimeout(timer)
  }, [])

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-purple-50/30 to-background dark:from-background dark:via-purple-950/30 dark:to-background">
      {/* Navigation */}
      <div className="pt-40 pb-8">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-5xl mx-auto">
            <Link 
              href="/learn" 
              className="inline-flex items-center gap-2 bg-background/80 backdrop-blur-sm border border-border/50 rounded-2xl px-4 py-2 text-primary-600 hover:text-primary-700 font-medium transition-all duration-300 hover:shadow-md"
            >
              <ArrowLeft className="w-4 h-4" />
              Back to Academy
            </Link>
          </div>
        </div>
      </div>

      {/* Hero Section */}
      <section className="pb-16">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-5xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              {/* Lesson Meta */}
              <div className="flex items-center justify-center gap-6 mb-8">
                <div className="flex items-center gap-2 bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 px-4 py-2 rounded-2xl">
                  <DollarSign className="w-4 h-4" />
                  <span className="font-medium text-sm">Pricing Strategy</span>
                </div>
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Clock className="w-4 h-4" />
                  <span className="text-sm">20 min read</span>
                </div>
                <div className="bg-muted px-3 py-1 rounded-full text-xs font-medium text-muted-foreground">
                  Growth Strategy
                </div>
              </div>

              {/* Main Title */}
              <h1 className="text-4xl md:text-6xl font-bold text-foreground mb-6 leading-tight">
                The Pricing Psychology
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-500 to-pink-500"> Revolution:</span>
                <br />Stop Leaving Money
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-green-500 to-blue-500"> on the Table</span>
              </h1>

              <p className="text-xl md:text-2xl text-muted-foreground mb-8 leading-relaxed max-w-4xl mx-auto">
                Something's happening across UK businesses that's making traditional pricing obsolete. 
                <strong className="text-foreground"> While most companies still use cost-plus pricing, smart businesses discovered the value ladder psychology that doubles revenue without doubling customers.</strong>
                Here's the pricing revolution that turns price objections into profit opportunities.
              </p>

              {/* Shocking Stats */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
                <div className="bg-gradient-to-br from-red-50 to-red-100 dark:from-red-950/30 dark:to-red-900/30 rounded-3xl p-6 border border-red-200/50 dark:border-red-800/50">
                  <div className="flex items-center gap-3 mb-3">
                    <TrendingDown className="w-6 h-6 text-red-500" />
                    <div className="text-3xl font-bold text-red-600 dark:text-red-400">67%</div>
                  </div>
                  <div className="text-sm text-red-700 dark:text-red-300 font-medium">Of potential customers lost to single pricing</div>
                </div>
                <div className="bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-950/30 dark:to-orange-900/30 rounded-3xl p-6 border border-orange-200/50 dark:border-orange-800/50">
                  <div className="flex items-center gap-3 mb-3">
                    <AlertTriangle className="w-6 h-6 text-orange-500" />
                    <div className="text-3xl font-bold text-orange-600 dark:text-orange-400">89%</div>
                  </div>
                  <div className="text-sm text-orange-700 dark:text-orange-300 font-medium">Focus on price instead of value</div>
                </div>
                <div className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950/30 dark:to-green-900/30 rounded-3xl p-6 border border-green-200/50 dark:border-green-800/50">
                  <div className="flex items-center gap-3 mb-3">
                    <TrendingUp className="w-6 h-6 text-green-500" />
                    <div className="text-3xl font-bold text-green-600 dark:text-green-400">240%</div>
                  </div>
                  <div className="text-sm text-green-700 dark:text-green-300 font-medium">Revenue increase with value ladder</div>
                </div>
              </div>
            </motion.div>

            {/* What You'll Learn Card */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-950/50 dark:to-pink-950/50 rounded-3xl p-8 border border-purple-200/50 dark:border-purple-800/50 shadow-xl"
            >
              <div className="flex items-start gap-6">
                <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center flex-shrink-0">
                  <Brain className="w-8 h-8 text-white" />
                </div>
                <div className="flex-1">
                  <h3 className="text-2xl font-bold text-foreground mb-4">What You'll Master in This Lesson</h3>
                  <div className="grid md:grid-cols-2 gap-4">
                    <div className="space-y-3">
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-foreground">Why cost-plus pricing leaves 60% revenue on the table</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-foreground">The 4-level value ladder that captures every customer segment</span>
                      </div>
                    </div>
                    <div className="space-y-3">
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-foreground">Psychology of pricing that makes customers choose premium</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-foreground">Case study: £340k revenue increase with zero new customers</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Main Content - Pricing Mistakes */}
      <section className="pb-16">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-4xl mx-auto space-y-12">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="bg-background/80 backdrop-blur-sm rounded-3xl p-8 border border-border/50 shadow-xl"
            >
              <div className="flex items-start gap-6 mb-8">
                <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-orange-500 rounded-xl flex items-center justify-center flex-shrink-0">
                  <AlertTriangle className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h2 className="text-3xl font-bold text-foreground mb-4">Why Traditional Pricing Kills Profits</h2>
                  <p className="text-lg text-muted-foreground leading-relaxed">
                    Smart business owners discovered that traditional pricing methods actually repel customers and leave massive revenue on the table. Here's what's really happening with your pricing.
                  </p>
                </div>
              </div>

              <div className="space-y-6">
                {pricingMistakes.map((mistake, index) => (
                  <div key={index} className={`bg-gradient-to-r ${
                    mistake.color === 'red' ? 'from-red-50 to-red-100 dark:from-red-950/30 dark:to-red-900/30' :
                    mistake.color === 'orange' ? 'from-orange-50 to-orange-100 dark:from-orange-950/30 dark:to-orange-900/30' :
                    mistake.color === 'yellow' ? 'from-yellow-50 to-yellow-100 dark:from-yellow-950/30 dark:to-yellow-900/30' :
                    'from-red-50 to-red-100 dark:from-red-950/30 dark:to-red-900/30'
                  } rounded-2xl p-6 border ${
                    mistake.color === 'red' ? 'border-red-200/50 dark:border-red-800/50' :
                    mistake.color === 'orange' ? 'border-orange-200/50 dark:border-orange-800/50' :
                    mistake.color === 'yellow' ? 'border-yellow-200/50 dark:border-yellow-800/50' :
                    'border-red-200/50 dark:border-red-800/50'
                  }`}>
                    <h4 className={`text-xl font-bold mb-4 ${
                      mistake.color === 'red' ? 'text-red-700 dark:text-red-300' :
                      mistake.color === 'orange' ? 'text-orange-700 dark:text-orange-300' :
                      mistake.color === 'yellow' ? 'text-yellow-700 dark:text-yellow-300' :
                      'text-red-700 dark:text-red-300'
                    }`}>Fatal Mistake #{index + 1}: {mistake.mistake}</h4>

                    <div className="grid md:grid-cols-2 gap-6">
                      <div>
                        <h5 className={`font-bold mb-2 ${
                          mistake.color === 'red' ? 'text-red-600 dark:text-red-400' :
                          mistake.color === 'orange' ? 'text-orange-600 dark:text-orange-400' :
                          mistake.color === 'yellow' ? 'text-yellow-600 dark:text-yellow-400' :
                          'text-red-600 dark:text-red-400'
                        }`}>What Most Do:</h5>
                        <p className={`text-sm mb-4 ${
                          mistake.color === 'red' ? 'text-red-600 dark:text-red-400' :
                          mistake.color === 'orange' ? 'text-orange-600 dark:text-orange-400' :
                          mistake.color === 'yellow' ? 'text-yellow-600 dark:text-yellow-400' :
                          'text-red-600 dark:text-red-400'
                        }`}>"{mistake.traditional}"</p>

                        <h5 className={`font-bold mb-2 ${
                          mistake.color === 'red' ? 'text-red-600 dark:text-red-400' :
                          mistake.color === 'orange' ? 'text-orange-600 dark:text-orange-400' :
                          mistake.color === 'yellow' ? 'text-yellow-600 dark:text-yellow-400' :
                          'text-red-600 dark:text-red-400'
                        }`}>The Problem:</h5>
                        <p className={`text-sm ${
                          mistake.color === 'red' ? 'text-red-600 dark:text-red-400' :
                          mistake.color === 'orange' ? 'text-orange-600 dark:text-orange-400' :
                          mistake.color === 'yellow' ? 'text-yellow-600 dark:text-yellow-400' :
                          'text-red-600 dark:text-red-400'
                        }`}>{mistake.problem}</p>
                      </div>

                      <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
                        <h5 className={`font-bold mb-2 ${
                          mistake.color === 'red' ? 'text-red-600 dark:text-red-400' :
                          mistake.color === 'orange' ? 'text-orange-600 dark:text-orange-400' :
                          mistake.color === 'yellow' ? 'text-yellow-600 dark:text-yellow-400' :
                          'text-red-600 dark:text-red-400'
                        }`}>Revenue Impact:</h5>
                        <div className={`text-2xl font-bold mb-2 ${
                          mistake.color === 'red' ? 'text-red-600 dark:text-red-400' :
                          mistake.color === 'orange' ? 'text-orange-600 dark:text-orange-400' :
                          mistake.color === 'yellow' ? 'text-yellow-600 dark:text-yellow-400' :
                          'text-red-600 dark:text-red-400'
                        }`}>{mistake.impact}</div>
                        <p className="text-xs text-muted-foreground">Lost revenue potential</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>

            {/* The Value Ladder Revolution */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="bg-background/80 backdrop-blur-sm rounded-3xl p-8 border border-border/50 shadow-xl"
            >
              <div className="flex items-start gap-6 mb-8">
                <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl flex items-center justify-center flex-shrink-0">
                  <Target className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h2 className="text-3xl font-bold text-foreground mb-4">The Value Ladder That Captures Every Customer</h2>
                  <p className="text-lg text-muted-foreground leading-relaxed">
                    Smart businesses discovered that different customers have different budgets and urgency levels. The value ladder captures all of them instead of losing 67% to "too expensive."
                  </p>
                </div>
              </div>

              <div className="space-y-8">
                {valueLadderLevels.map((level, index) => (
                  <div key={index} className={`bg-gradient-to-r ${
                    level.color === 'blue' ? 'from-blue-50 to-blue-100 dark:from-blue-950/30 dark:to-blue-900/30' :
                    level.color === 'green' ? 'from-green-50 to-green-100 dark:from-green-950/30 dark:to-green-900/30' :
                    level.color === 'purple' ? 'from-purple-50 to-purple-100 dark:from-purple-950/30 dark:to-purple-900/30' :
                    'from-yellow-50 to-yellow-100 dark:from-yellow-950/30 dark:to-yellow-900/30'
                  } rounded-2xl p-6 border ${
                    level.color === 'blue' ? 'border-blue-200/50 dark:border-blue-800/50' :
                    level.color === 'green' ? 'border-green-200/50 dark:border-green-800/50' :
                    level.color === 'purple' ? 'border-purple-200/50 dark:border-purple-800/50' :
                    'border-yellow-200/50 dark:border-yellow-800/50'
                  }`}>
                    <div className="flex items-start gap-4 mb-6">
                      <div className={`w-16 h-16 rounded-xl flex items-center justify-center flex-shrink-0 ${
                        level.color === 'blue' ? 'bg-blue-500' :
                        level.color === 'green' ? 'bg-green-500' :
                        level.color === 'purple' ? 'bg-purple-500' :
                        'bg-yellow-500'
                      }`}>
                        <span className="text-white font-bold text-lg">{level.level}</span>
                      </div>
                      <div className="flex-1">
                        <h4 className={`text-xl font-bold mb-2 ${
                          level.color === 'blue' ? 'text-blue-700 dark:text-blue-300' :
                          level.color === 'green' ? 'text-green-700 dark:text-green-300' :
                          level.color === 'purple' ? 'text-purple-700 dark:text-purple-300' :
                          'text-yellow-700 dark:text-yellow-300'
                        }`}>
                          {level.title}
                        </h4>
                        <div className={`text-2xl font-bold mb-2 ${
                          level.color === 'blue' ? 'text-blue-600 dark:text-blue-400' :
                          level.color === 'green' ? 'text-green-600 dark:text-green-400' :
                          level.color === 'purple' ? 'text-purple-600 dark:text-purple-400' :
                          'text-yellow-600 dark:text-yellow-400'
                        }`}>{level.price}</div>
                        <p className={`mb-4 ${
                          level.color === 'blue' ? 'text-blue-600 dark:text-blue-400' :
                          level.color === 'green' ? 'text-green-600 dark:text-green-400' :
                          level.color === 'purple' ? 'text-purple-600 dark:text-purple-400' :
                          'text-yellow-600 dark:text-yellow-400'
                        }`}>
                          <strong>Psychology:</strong> {level.psychology}
                        </p>
                        <p className={`mb-4 ${
                          level.color === 'blue' ? 'text-blue-600 dark:text-blue-400' :
                          level.color === 'green' ? 'text-green-600 dark:text-green-400' :
                          level.color === 'purple' ? 'text-purple-600 dark:text-purple-400' :
                          'text-yellow-600 dark:text-yellow-400'
                        }`}>
                          <strong>Purpose:</strong> {level.purpose}
                        </p>
                      </div>
                      <div className="text-center">
                        <div className={`text-3xl font-bold ${
                          level.color === 'blue' ? 'text-blue-600 dark:text-blue-400' :
                          level.color === 'green' ? 'text-green-600 dark:text-green-400' :
                          level.color === 'purple' ? 'text-purple-600 dark:text-purple-400' :
                          'text-yellow-600 dark:text-yellow-400'
                        }`}>{level.conversion}</div>
                        <div className="text-xs text-muted-foreground">Conversion Rate</div>
                      </div>
                    </div>

                    <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
                      <h5 className={`font-bold mb-2 ${
                        level.color === 'blue' ? 'text-blue-600 dark:text-blue-400' :
                        level.color === 'green' ? 'text-green-600 dark:text-green-400' :
                        level.color === 'purple' ? 'text-purple-600 dark:text-purple-400' :
                        'text-yellow-600 dark:text-yellow-400'
                      }`}>Examples:</h5>
                      <div className="grid md:grid-cols-4 gap-2">
                        {level.examples.map((example, exampleIndex) => (
                          <div key={exampleIndex} className={`text-sm p-2 rounded text-center ${
                            level.color === 'blue' ? 'bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400' :
                            level.color === 'green' ? 'bg-green-50 dark:bg-green-900/30 text-green-600 dark:text-green-400' :
                            level.color === 'purple' ? 'bg-purple-50 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400' :
                            'bg-yellow-50 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400'
                          }`}>
                            {example}
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>

            {/* Next Lesson Navigation */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.7 }}
              className="bg-background rounded-3xl border border-border shadow-lg p-8"
            >
              <h3 className="text-xl font-bold text-foreground mb-6 text-center">Continue Your Learning Journey</h3>

              <div className="grid md:grid-cols-2 gap-6">
                <Link
                  href="/learn/fundamentals/retention-secret"
                  className="group bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950/30 dark:to-purple-900/30 rounded-2xl p-6 border border-purple-200/50 dark:border-purple-800/50 hover:shadow-lg transition-all duration-300"
                >
                  <div className="flex items-center gap-4 mb-4">
                    <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/50 rounded-xl flex items-center justify-center">
                      <ArrowRight className="w-6 h-6 text-purple-600 group-hover:translate-x-1 transition-transform" />
                    </div>
                    <div>
                      <h4 className="font-bold text-foreground">Next Lesson</h4>
                      <p className="text-sm text-muted-foreground">Lesson 14 of 15</p>
                    </div>
                  </div>
                  <h5 className="font-semibold text-foreground mb-2">The Retention Secret</h5>
                  <p className="text-sm text-muted-foreground">
                    Why 73% of customers leave before you know they're unhappy and the early warning system that prevents it
                  </p>
                </Link>

                <Link
                  href="/learn"
                  className="group bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-950/30 dark:to-gray-900/30 rounded-2xl p-6 border border-gray-200/50 dark:border-gray-800/50 hover:shadow-lg transition-all duration-300"
                >
                  <div className="flex items-center gap-4 mb-4">
                    <div className="w-12 h-12 bg-gray-100 dark:bg-gray-900/50 rounded-xl flex items-center justify-center">
                      <ArrowLeft className="w-6 h-6 text-gray-600 group-hover:-translate-x-1 transition-transform" />
                    </div>
                    <div>
                      <h4 className="font-bold text-foreground">Academy</h4>
                      <p className="text-sm text-muted-foreground">All lessons</p>
                    </div>
                  </div>
                  <h5 className="font-semibold text-foreground mb-2">Back to Academy</h5>
                  <p className="text-sm text-muted-foreground">
                    View all lessons and track your progress
                  </p>
                </Link>
              </div>
            </motion.div>
          </div>
        </div>
      </section>
    </div>
  )
}
