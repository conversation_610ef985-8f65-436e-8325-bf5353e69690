import React from 'react'
import { BlogPost } from '../types'

const BlogContent = () => {
  return (
    <div className="max-w-4xl mx-auto space-y-12">
      
      {/* Opening Hook */}
      <div className="bg-gradient-to-br from-amber-50 to-orange-50 dark:from-amber-950/20 dark:to-orange-950/20 rounded-3xl p-8 border border-amber-200/50 dark:border-amber-800/30">
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6 text-center">
          "I know I need to automate, but where do I even start?"
        </h2>
        <p className="text-lg text-gray-700 dark:text-gray-300 mb-4 text-center">
          That's what <PERSON> from <PERSON> asked me last Tuesday. His window installation business was thriving, but he was drowning in admin work. <strong>Sound familiar?</strong>
        </p>
        <p className="text-lg text-gray-700 dark:text-gray-300 text-center font-medium">
          Here are the 12 warning signs that your business is screaming for automation.
        </p>
      </div>

      {/* The Reality Check */}
      <div>
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
          The Automation Wake-Up Call
        </h2>
        <p className="text-lg text-gray-700 dark:text-gray-300 mb-6">
          Most UK business owners wait too long to automate. They think they need to be "bigger" or "more established" first.
        </p>
        <p className="text-lg text-gray-700 dark:text-gray-300 mb-8">
          The truth? <strong>The best time to automate was yesterday. The second best time is right now.</strong>
        </p>
        
        <div className="bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800/30 rounded-2xl p-6 mb-8">
          <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-4">Why Business Owners Delay Automation</h3>
          <ul className="space-y-3">
            <li className="flex items-start">
              <span className="w-2 h-2 bg-blue-500 rounded-full mr-3 mt-2"></span>
              <span className="text-gray-700 dark:text-gray-300">"I don't have time to set it up" (But you have time to do everything manually?)</span>
            </li>
            <li className="flex items-start">
              <span className="w-2 h-2 bg-blue-500 rounded-full mr-3 mt-2"></span>
              <span className="text-gray-700 dark:text-gray-300">"It's too expensive" (More expensive than working 70-hour weeks?)</span>
            </li>
            <li className="flex items-start">
              <span className="w-2 h-2 bg-blue-500 rounded-full mr-3 mt-2"></span>
              <span className="text-gray-700 dark:text-gray-300">"My customers prefer the personal touch" (They prefer reliable service)</span>
            </li>
            <li className="flex items-start">
              <span className="w-2 h-2 bg-blue-500 rounded-full mr-3 mt-2"></span>
              <span className="text-gray-700 dark:text-gray-300">"I'm not tech-savvy enough" (You don't need to be)</span>
            </li>
          </ul>
        </div>
      </div>

      {/* The 12 Warning Signs */}
      <div>
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6 text-center">
          The 12 Warning Signs Your Business Needs Automation
        </h2>
        <p className="text-lg text-gray-700 dark:text-gray-300 mb-8 text-center">
          If you recognize 3 or more of these signs, automation isn't a luxury—it's a necessity.
        </p>
        
        <div className="space-y-6">
          {/* Sign 1 */}
          <div className="bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800/30 rounded-2xl p-6">
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
              <span className="text-2xl mr-3">🚨</span>
              1. You're Working Past 7pm Every Night
            </h3>
            <p className="text-gray-700 dark:text-gray-300 mb-3">
              If you're still answering emails, confirming appointments, or chasing payments after dinner, you're doing work that should be automated.
            </p>
            <p className="text-sm text-gray-600 dark:text-gray-400 italic">
              "I used to work until 9pm every night. Now I'm home for dinner with my family." - Anthony, Universal Windows
            </p>
          </div>

          {/* Sign 2 */}
          <div className="bg-orange-50 dark:bg-orange-950/20 border border-orange-200 dark:border-orange-800/30 rounded-2xl p-6">
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
              <span className="text-2xl mr-3">📱</span>
              2. Your Phone Never Stops Ringing
            </h3>
            <p className="text-gray-700 dark:text-gray-300 mb-3">
              Constant interruptions for booking confirmations, rescheduling, and basic questions that could be handled automatically.
            </p>
            <p className="text-sm text-gray-600 dark:text-gray-400 italic">
              "My phone used to ring constantly during site visits. Now customers book and manage everything online." - David, Curb Appeal
            </p>
          </div>

          {/* Sign 3 */}
          <div className="bg-yellow-50 dark:bg-yellow-950/20 border border-yellow-200 dark:border-yellow-800/30 rounded-2xl p-6">
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
              <span className="text-2xl mr-3">👻</span>
              3. No-Shows Are Killing Your Schedule
            </h3>
            <p className="text-gray-700 dark:text-gray-300 mb-3">
              If more than 10% of your appointments result in no-shows, you need automated reminders and confirmation systems.
            </p>
            <p className="text-sm text-gray-600 dark:text-gray-400 italic">
              "We went from 40% no-shows to less than 5% with automated reminders." - Danny, First Impression Driveways
            </p>
          </div>

          {/* Sign 4 */}
          <div className="bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800/30 rounded-2xl p-6">
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
              <span className="text-2xl mr-3">💰</span>
              4. You're Chasing Payments Like a Debt Collector
            </h3>
            <p className="text-gray-700 dark:text-gray-300 mb-3">
              Spending hours each week calling customers about overdue invoices instead of focusing on growing your business.
            </p>
            <p className="text-sm text-gray-600 dark:text-gray-400 italic">
              "Automated payment reminders eliminated 90% of my payment chasing." - Shaun, Barrier Therm
            </p>
          </div>

          {/* Sign 5 */}
          <div className="bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800/30 rounded-2xl p-6">
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
              <span className="text-2xl mr-3">📧</span>
              5. You're Drowning in Repetitive Emails
            </h3>
            <p className="text-gray-700 dark:text-gray-300 mb-3">
              Sending the same quotes, confirmations, and follow-ups manually. If you've typed "Thanks for your inquiry" more than 10 times this week, you need automation.
            </p>
          </div>

          {/* Sign 6 */}
          <div className="bg-purple-50 dark:bg-purple-950/20 border border-purple-200 dark:border-purple-800/30 rounded-2xl p-6">
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
              <span className="text-2xl mr-3">📅</span>
              6. Double-Bookings Are Your Nightmare
            </h3>
            <p className="text-gray-700 dark:text-gray-300 mb-3">
              Managing appointments across multiple calendars, notebooks, or systems. If you've ever had to explain to a customer why you can't make their appointment, you need automated scheduling.
            </p>
          </div>

          {/* Sign 7 */}
          <div className="bg-pink-50 dark:bg-pink-950/20 border border-pink-200 dark:border-pink-800/30 rounded-2xl p-6">
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
              <span className="text-2xl mr-3">⭐</span>
              7. You Struggle to Get Reviews
            </h3>
            <p className="text-gray-700 dark:text-gray-300 mb-3">
              You do great work but forget to ask for reviews, or customers promise to leave one but never do. Automated review requests solve this.
            </p>
          </div>

          {/* Sign 8 */}
          <div className="bg-indigo-50 dark:bg-indigo-950/20 border border-indigo-200 dark:border-indigo-800/30 rounded-2xl p-6">
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
              <span className="text-2xl mr-3">🔄</span>
              8. You Answer the Same Questions Daily
            </h3>
            <p className="text-gray-700 dark:text-gray-300 mb-3">
              "What are your prices?" "What time do you close?" "Do you work weekends?" If you're answering these questions multiple times daily, automation can handle them.
            </p>
          </div>

          {/* Sign 9 */}
          <div className="bg-teal-50 dark:bg-teal-950/20 border border-teal-200 dark:border-teal-800/30 rounded-2xl p-6">
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
              <span className="text-2xl mr-3">📊</span>
              9. You Have No Idea Where Your Leads Come From
            </h3>
            <p className="text-gray-700 dark:text-gray-300 mb-3">
              Can't track which marketing efforts are working because everything comes through phone calls and scattered emails.
            </p>
          </div>

          {/* Sign 10 */}
          <div className="bg-rose-50 dark:bg-rose-950/20 border border-rose-200 dark:border-rose-800/30 rounded-2xl p-6">
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
              <span className="text-2xl mr-3">🏃</span>
              10. You Can't Take a Holiday
            </h3>
            <p className="text-gray-700 dark:text-gray-300 mb-3">
              Your business stops when you stop. Customers can't book, questions don't get answered, and everything waits for your return.
            </p>
          </div>

          {/* Sign 11 */}
          <div className="bg-cyan-50 dark:bg-cyan-950/20 border border-cyan-200 dark:border-cyan-800/30 rounded-2xl p-6">
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
              <span className="text-2xl mr-3">💸</span>
              11. You're Losing Money on Admin Time
            </h3>
            <p className="text-gray-700 dark:text-gray-300 mb-3">
              Spending more time on paperwork than billable work. If admin takes up more than 20% of your week, automation will pay for itself immediately.
            </p>
          </div>

          {/* Sign 12 */}
          <div className="bg-emerald-50 dark:bg-emerald-950/20 border border-emerald-200 dark:border-emerald-800/30 rounded-2xl p-6">
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
              <span className="text-2xl mr-3">😴</span>
              12. You're Exhausted But Can't Delegate
            </h3>
            <p className="text-gray-700 dark:text-gray-300 mb-3">
              Everything depends on you because only you know how to handle the bookings, follow-ups, and customer communications. Automation becomes your perfect employee.
            </p>
          </div>
        </div>
      </div>

      {/* The Cost of Waiting */}
      <div className="bg-gradient-to-br from-red-50 to-pink-50 dark:from-red-950/20 dark:to-pink-950/20 rounded-3xl p-8 border border-red-200/50 dark:border-red-800/30">
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6 text-center">
          The Hidden Cost of Waiting "Until Next Year"
        </h2>
        
        <div className="grid md:grid-cols-2 gap-8">
          <div>
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-4">What Waiting Costs You:</h3>
            <ul className="space-y-3">
              <li className="flex items-start">
                <span className="text-red-500 text-xl mr-3">❌</span>
                <span className="text-gray-700 dark:text-gray-300">20+ hours weekly on manual tasks</span>
              </li>
              <li className="flex items-start">
                <span className="text-red-500 text-xl mr-3">❌</span>
                <span className="text-gray-700 dark:text-gray-300">£2,000+ monthly lost to no-shows</span>
              </li>
              <li className="flex items-start">
                <span className="text-red-500 text-xl mr-3">❌</span>
                <span className="text-gray-700 dark:text-gray-300">Missed family time and burnout</span>
              </li>
              <li className="flex items-start">
                <span className="text-red-500 text-xl mr-3">❌</span>
                <span className="text-gray-700 dark:text-gray-300">Competitors gaining advantage</span>
              </li>
            </ul>
          </div>
          
          <div>
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-4">What Automation Gives You:</h3>
            <ul className="space-y-3">
              <li className="flex items-start">
                <span className="text-green-500 text-xl mr-3">✅</span>
                <span className="text-gray-700 dark:text-gray-300">Evenings and weekends back</span>
              </li>
              <li className="flex items-start">
                <span className="text-green-500 text-xl mr-3">✅</span>
                <span className="text-gray-700 dark:text-gray-300">85% reduction in no-shows</span>
              </li>
              <li className="flex items-start">
                <span className="text-green-500 text-xl mr-3">✅</span>
                <span className="text-gray-700 dark:text-gray-300">Professional, reliable service</span>
              </li>
              <li className="flex items-start">
                <span className="text-green-500 text-xl mr-3">✅</span>
                <span className="text-gray-700 dark:text-gray-300">Scalable business growth</span>
              </li>
            </ul>
          </div>
        </div>
      </div>

      {/* The Solution */}
      <div>
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
          The GenLogic Solution: Complete Business Automation
        </h2>
        <p className="text-lg text-gray-700 dark:text-gray-300 mb-8">
          We don't just give you software—we give you a complete <a href="/services/business-automation" className="text-blue-600 dark:text-blue-400 hover:underline">business automation system</a> built on GoHighLevel, customized for UK businesses.
        </p>
        
        <div className="grid md:grid-cols-2 gap-6">
          <div className="bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800/30 rounded-2xl p-6">
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-4">What You Get:</h3>
            <ul className="space-y-2">
              <li className="flex items-center">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                <span className="text-gray-700 dark:text-gray-300">24/7 automated booking system</span>
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                <span className="text-gray-700 dark:text-gray-300">SMS & email reminder sequences</span>
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                <span className="text-gray-700 dark:text-gray-300">Automated payment collection</span>
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                <span className="text-gray-700 dark:text-gray-300">Review generation system</span>
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                <span className="text-gray-700 dark:text-gray-300">Customer communication automation</span>
              </li>
            </ul>
          </div>

          <div className="bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800/30 rounded-2xl p-6">
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-4">Setup Included:</h3>
            <ul className="space-y-2">
              <li className="flex items-center">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                <span className="text-gray-700 dark:text-gray-300">Complete system configuration</span>
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                <span className="text-gray-700 dark:text-gray-300">UK-specific templates</span>
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                <span className="text-gray-700 dark:text-gray-300">GDPR compliance setup</span>
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                <span className="text-gray-700 dark:text-gray-300">Training and support</span>
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                <span className="text-gray-700 dark:text-gray-300">Ongoing optimization</span>
              </li>
            </ul>
          </div>
        </div>
      </div>

      {/* Call to Action */}
      <div className="bg-gradient-to-br from-blue-600 to-purple-600 rounded-3xl p-8 text-white text-center">
        <h2 className="text-3xl font-bold mb-6">
          Ready to Stop Working Late and Start Living?
        </h2>
        <p className="text-xl mb-8 opacity-90">
          Book a free 30-minute automation assessment. We'll identify which of these 12 signs are costing you the most and show you exactly how to fix them.
        </p>
        <div className="space-y-4">
          <p className="text-lg opacity-90">
            ✅ Free automation readiness assessment<br/>
            ✅ Custom solution roadmap<br/>
            ✅ ROI calculation for your business
          </p>
          <div className="pt-4">
            <p className="text-lg font-semibold">
              📞 +44 7401 137621<br/>
              📧 <EMAIL>
            </p>
          </div>
        </div>
      </div>

      {/* Final Thought */}
      <div className="text-center">
        <p className="text-lg text-gray-700 dark:text-gray-300 italic">
          "The best time to automate was when you first noticed these signs. The second best time is right now."
        </p>
        <p className="text-base text-gray-600 dark:text-gray-400 mt-2">
          Don't wait until next year. Your evenings are waiting.
        </p>
      </div>

    </div>
  )
}

export const signsYourBusinessNeedsAutomationPost: BlogPost = {
  id: '10',
  slug: 'signs-your-business-needs-automation',
  title: '12 Warning Signs Your Business Desperately Needs Automation (Before It\'s Too Late)',
  excerpt: 'Working past 7pm every night? Phone never stops ringing? Here are the 12 warning signs that your UK business is screaming for automation—and what to do about it.',
  content: <BlogContent />,
  author: {
    name: 'Chedi Rach',
    role: 'Business Automation Specialist',
    avatar: '/logo/genlogic-icon-only.svg',
    bio: 'Chedi has helped over 500 UK businesses recognize the signs and implement automation before burnout takes over.'
  },
  publishedAt: '2025-07-20',
  readingTime: '10 min read',
  category: 'Business Strategy',
  tags: ['automation-signs', 'business-automation', 'uk-business', 'productivity'],
  featured: true,
  image: '/blog-images/signs-your-business-needs-automation.webp',
  views: 1892,
  likes: 234
}
