'use client'

import { motion } from 'framer-motion'
import { CustomButton } from '@/components/ui/custom-button'
import { ArrowR<PERSON>, Clock, CheckCircle, Users, Star } from 'lucide-react'

interface CTASectionProps {
  variant?: 'primary' | 'secondary' | 'final'
  className?: string
}

export default function CTASection({ variant = 'primary', className }: CTASectionProps) {
  if (variant === 'final') {
    return (
      <section className={`py-20 bg-gray-900 dark:bg-gray-950 text-white relative overflow-hidden ${className}`}>
        {/* Background pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0 bg-gradient-to-r from-primary-600/20 to-accent-600/20"></div>
          <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_50%_50%,rgba(120,119,198,0.1),transparent_50%)]"></div>
        </div>
        
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: 'easeOut' }}
            viewport={{ once: true }}
          >
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-white/10 text-white/90 text-sm font-semibold tracking-wide uppercase mb-8">
              <Clock className="w-4 h-4 mr-2" />
              Stop Wasting Your Evenings
            </div>
            
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-6 leading-tight">
              Ready to get your{' '}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-primary-400 to-accent-400">
                evenings back?
              </span>
            </h2>
            
            <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto leading-relaxed">
              Join over 500 UK business owners who've already reclaimed their time and reduced their stress with GenLogic.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
              <CustomButton 
                variant="primary"
                size="lg"
                href="/demo"
                icon={<ArrowRight className="w-5 h-5" />}
                iconPosition="right"
                className="bg-white text-gray-900 hover:bg-gray-100 shadow-2xl"
              >
                Book Your Free Demo
              </CustomButton>
              
              <CustomButton 
                variant="outline"
                size="lg"
                href="/pricing"
                className="border-gray-600 text-gray-300 hover:bg-gray-800 hover:border-gray-500"
              >
                See Pricing
              </CustomButton>
            </div>
            
            {/* Trust indicators */}
            <div className="flex flex-col sm:flex-row items-center justify-center gap-6 text-sm text-gray-400">
              <div className="flex items-center">
                <CheckCircle className="w-4 h-4 text-green-400 mr-2" />
                <span>14-day free trial</span>
              </div>
              <div className="hidden sm:block w-1 h-1 bg-gray-600 rounded-full"></div>
              <div className="flex items-center">
                <Users className="w-4 h-4 text-blue-400 mr-2" />
                <span>No credit card required</span>
              </div>
              <div className="hidden sm:block w-1 h-1 bg-gray-600 rounded-full"></div>
              <div className="flex items-center">
                <Star className="w-4 h-4 text-yellow-400 mr-2" />
                <span>UK-based support</span>
              </div>
            </div>
          </motion.div>
        </div>
      </section>
    )
  }

  if (variant === 'secondary') {
    return (
      <section className={`py-20 bg-gradient-to-br from-primary-50 to-accent-50 dark:from-gray-800 dark:to-gray-900 relative overflow-hidden ${className}`}>
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Content */}
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, ease: 'easeOut' }}
              viewport={{ once: true }}
            >
              <div className="inline-flex items-center px-4 py-2 rounded-full bg-primary-100 text-primary-800 text-sm font-semibold tracking-wide uppercase mb-6 dark:bg-primary-900/30 dark:text-primary-400">
                Take Back Control
              </div>
              
              <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-6 leading-tight">
                Imagine finishing work at 6pm and actually having time for yourself
              </h2>
              
              <p className="text-lg text-muted-foreground mb-8 leading-relaxed">
                No more spending your evenings chasing customers or sending reminders. 
                GenLogic handles it all automatically, so you can focus on what you love.
              </p>
              
              <div className="space-y-4 mb-8">
                <div className="flex items-center">
                  <CheckCircle className="w-5 h-5 text-success-500 mr-3 flex-shrink-0" />
                  <span className="text-foreground">Save 30+ hours per week on admin tasks</span>
                </div>
                <div className="flex items-center">
                  <CheckCircle className="w-5 h-5 text-success-500 mr-3 flex-shrink-0" />
                  <span className="text-foreground">85% reduction in no-shows guaranteed</span>
                </div>
                <div className="flex items-center">
                  <CheckCircle className="w-5 h-5 text-success-500 mr-3 flex-shrink-0" />
                  <span className="text-foreground">More repeat customers through smart follow-ups</span>
                </div>
              </div>
              
              <CustomButton 
                variant="primary"
                size="lg"
                href="/demo"
                icon={<ArrowRight className="w-5 h-5" />}
                iconPosition="right"
              >
                See How It Works
              </CustomButton>
            </motion.div>

            {/* Visual element */}
            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2, ease: 'easeOut' }}
              viewport={{ once: true }}
              className="relative"
            >
              <div className="bg-background dark:bg-gray-800 rounded-2xl p-8 shadow-2xl border border-border">
                <div className="text-center">
                  <div className="text-4xl font-bold text-primary-700 dark:text-primary-400 mb-2">£49</div>
                  <div className="text-muted-foreground mb-6">per month</div>
                  <div className="space-y-3 text-left mb-8">
                    {[
                      'Unlimited bookings',
                      'SMS & email reminders', 
                      'Auto follow-ups',
                      'UK support team',
                      '14-day free trial'
                    ].map((feature, index) => (
                      <div key={feature} className="flex items-center">
                        <CheckCircle className="w-4 h-4 text-success-500 mr-3" />
                        <span className="text-foreground text-sm">{feature}</span>
                      </div>
                    ))}
                  </div>
                  <CustomButton 
                    variant="primary"
                    size="md"
                    href="/demo"
                    className="w-full"
                  >
                    Start Free Trial
                  </CustomButton>
                  <p className="text-xs text-muted-foreground mt-3">No credit card required</p>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>
    )
  }

  // Primary variant (default)
  return (
    <section className={`py-20 bg-background dark:bg-gray-900 relative overflow-hidden ${className}`}>
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: 'easeOut' }}
          viewport={{ once: true }}
        >
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-accent-100 text-accent-800 text-sm font-semibold tracking-wide uppercase mb-8 dark:bg-accent-900/30 dark:text-accent-400">
            Stop The Admin Nightmare
          </div>
          
          <h2 className="text-3xl sm:text-4xl font-bold text-foreground mb-6 leading-tight">
            Tired of spending your evenings on admin instead of with your family?
          </h2>
          
          <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto leading-relaxed">
            See how GenLogic can automate your bookings, reminders, and follow-ups so you can focus on what matters most.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <CustomButton 
              variant="primary"
              size="lg"
              href="/demo"
              icon={<ArrowRight className="w-5 h-5" />}
              iconPosition="right"
            >
              Book Free Demo
            </CustomButton>
            
            <CustomButton 
              variant="secondary"
              size="lg"
              href="/pricing"
            >
              View Pricing
            </CustomButton>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
