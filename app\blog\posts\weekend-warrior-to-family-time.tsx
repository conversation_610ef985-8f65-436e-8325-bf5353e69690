import React from 'react'
import { BlogPost } from '../types'

const BlogContent = () => {
  return (
    <div className="max-w-4xl mx-auto space-y-12">
      
      {/* Opening Hook */}
      <div className="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-950/20 dark:to-pink-950/20 rounded-3xl p-8 border border-purple-200/50 dark:border-purple-800/30">
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6 text-center">
          Saturday Morning: 8:30am
        </h2>
        <div className="space-y-4 text-lg text-gray-700 dark:text-gray-300 text-center">
          <p>Lisa's phone buzzes. Then again. And again.</p>
          <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-700">
            <p>"Can I change my appointment to 2pm?"</p>
            <p>"I need to cancel today's booking."</p>
            <p>"Do you have anything available this afternoon?"</p>
          </div>
          <p>While other parents are cheering at their kids' football matches, <PERSON> is juggling booking chaos from her car.</p>
          <p className="font-bold text-purple-600 dark:text-purple-400">
            This was her reality for three years. Until everything changed.
          </p>
        </div>
      </div>

      {/* Meet Lisa */}
      <div>
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
          Meet Lisa: The Weekend Warrior
        </h2>
        <p className="text-lg text-gray-700 dark:text-gray-300 mb-8">
          Lisa owns Serenity Beauty Salon in Birmingham. For three years, she was the "Weekend Warrior" - constantly fighting booking battles that never seemed to end.
        </p>

        <div className="grid md:grid-cols-3 gap-6 mb-8">
          <div className="bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800/30 rounded-2xl p-6 text-center">
            <div className="text-4xl mb-4">🕰️</div>
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3">7:00am</h3>
            <p className="text-gray-700 dark:text-gray-300">Check overnight messages (usually 15-20)</p>
          </div>
          <div className="bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800/30 rounded-2xl p-6 text-center">
            <div className="text-4xl mb-4">📞</div>
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3">7:30am</h3>
            <p className="text-gray-700 dark:text-gray-300">Start calling customers about changes</p>
          </div>
          <div className="bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800/30 rounded-2xl p-6 text-center">
            <div className="text-4xl mb-4">💻</div>
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3">8:00am</h3>
            <p className="text-gray-700 dark:text-gray-300">Update the booking system manually</p>
          </div>
        </div>

        <div className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 rounded-2xl p-6 border border-blue-200/50 dark:border-blue-800/30">
          <blockquote className="text-xl italic text-gray-800 dark:text-gray-200 mb-4">
            "I felt like I was running two businesses - the salon and a 24/7 customer service center. My daughter would ask why I was always on the phone during her matches."
          </blockquote>
          <cite className="text-blue-600 dark:text-blue-400 font-semibold">Lisa, Serenity Beauty Salon</cite>
        </div>
      </div>

      {/* Transformation */}
      <div>
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
          The Transformation: Automation Takes Over
        </h2>
        <p className="text-lg text-gray-700 dark:text-gray-300 mb-8">
          Lisa implemented a complete booking automation system. Here's what changed:
        </p>

        <div className="grid md:grid-cols-2 gap-6">
          <div className="bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800/30 rounded-2xl p-6">
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-4">Before Automation</h3>
            <ul className="space-y-2">
              <li className="flex items-center text-gray-700 dark:text-gray-300">
                <span className="w-2 h-2 bg-red-500 rounded-full mr-3"></span>
                <strong>15-20 messages</strong> every Saturday morning
              </li>
              <li className="flex items-center text-gray-700 dark:text-gray-300">
                <span className="w-2 h-2 bg-red-500 rounded-full mr-3"></span>
                <strong>3+ hours</strong> weekly on booking admin
              </li>
              <li className="flex items-center text-gray-700 dark:text-gray-300">
                <span className="w-2 h-2 bg-red-500 rounded-full mr-3"></span>
                <strong>Constant interruptions</strong> during family time
              </li>
            </ul>
          </div>
          
          <div className="bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800/30 rounded-2xl p-6">
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-4">After Automation</h3>
            <ul className="space-y-2">
              <li className="flex items-center text-gray-700 dark:text-gray-300">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                <strong>Zero weekend interruptions</strong> - system handles everything
              </li>
              <li className="flex items-center text-gray-700 dark:text-gray-300">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                <strong>30 minutes weekly</strong> on booking oversight
              </li>
              <li className="flex items-center text-gray-700 dark:text-gray-300">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                <strong>Present for every family moment</strong>
              </li>
            </ul>
          </div>
        </div>
      </div>

      {/* Final CTA */}
      <div>
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
          Your Weekend Freedom Starts Here
        </h2>
        <p className="text-lg text-gray-700 dark:text-gray-300 mb-8">
          Lisa's story proves that you don't have to choose between business success and family time.
        </p>

        <div className="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-950/20 dark:to-pink-950/20 rounded-2xl p-6 border border-purple-200/50 dark:border-purple-800/30 mb-8">
          <p className="text-lg text-gray-700 dark:text-gray-300 text-center">
            The technology exists. The system works. Other business owners are already living this reality.
          </p>
        </div>

        <div className="text-center">
          <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
            How many precious family moments are you willing to sacrifice for booking admin that could be automated?
          </h3>
          <h3 className="text-2xl font-bold text-purple-600 dark:text-purple-400">
            Your weekends are waiting. Your family is waiting.
          </h3>
        </div>
      </div>

    </div>
  )
}

export const weekendWarriorPost: BlogPost = {
  id: '3',
  slug: 'weekend-warrior-to-family-time',
  title: 'From Weekend Warrior to Family Time: A Beauty Salon Owner\'s Transformation',
  excerpt: 'Lisa used to spend every Saturday morning dealing with booking issues. Now she spends it at her daughter\'s football matches. Here\'s her story.',
  content: <BlogContent />,
  author: {
    name: 'Chedi Rach',
    role: 'Business Automation Specialist',
    avatar: '/logo/genlogic-icon-only.svg',
    bio: 'Chedi specializes in helping beauty and wellness businesses automate their operations.'
  },
  publishedAt: '2025-07-05',
  readingTime: '5 min read',
  category: 'Success Stories',
  tags: ['beauty-salon', 'family-time', 'automation'],
  featured: false,
  image: '/blog-images/weekend-warrior-to-family-time.webp',
  views: 1456,
  likes: 67
}
