import React from 'react'
import { BlogPost } from '../types'

const BlogContent = () => {
  return (
    <div className="max-w-4xl mx-auto space-y-12">
      
      {/* Opening Hook */}
      <div className="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-950/20 dark:to-pink-950/20 rounded-3xl p-8 border border-purple-200/50 dark:border-purple-800/30">
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6 text-center">
          "I'm drowning, but everyone thinks I'm swimming."
        </h2>
        <p className="text-lg text-gray-700 dark:text-gray-300 mb-4 text-center">
          That's what <PERSON> from Manchester told me last week. From the outside, her home improvement business looked successful. <strong>Inside, she was barely keeping her head above water.</strong>
        </p>
        <p className="text-lg text-gray-700 dark:text-gray-300 text-center font-medium">
          Sound familiar? You're not alone.
        </p>
      </div>

      {/* The Hidden Reality */}
      <div>
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
          The Hidden Reality Behind "Successful" Local Businesses
        </h2>
        <p className="text-lg text-gray-700 dark:text-gray-300 mb-6">
          Everyone sees the nice van, the busy calendar, the steady stream of customers. What they don't see is what happens behind closed doors.
        </p>
        <p className="text-lg text-gray-700 dark:text-gray-300 mb-8">
          The struggles that keep you awake at 2am, wondering if this is really worth it:
        </p>
        
        <div className="space-y-6">
          <div className="bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800/30 rounded-2xl p-6">
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
              <span className="w-3 h-3 bg-red-500 rounded-full mr-3"></span>
              The Constant Juggling Act
            </h3>
            <p className="text-gray-700 dark:text-gray-300 mb-3">
              You're the CEO, accountant, customer service rep, marketing manager, and janitor all rolled into one. 
            </p>
            <p className="text-gray-700 dark:text-gray-300">
              <strong>The hidden cost:</strong> You're good at your trade, but you're spending 70% of your time on everything except what you're actually good at.
            </p>
          </div>

          <div className="bg-orange-50 dark:bg-orange-950/20 border border-orange-200 dark:border-orange-800/30 rounded-2xl p-6">
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
              <span className="w-3 h-3 bg-orange-500 rounded-full mr-3"></span>
              The Cash Flow Rollercoaster
            </h3>
            <p className="text-gray-700 dark:text-gray-300 mb-3">
              One month you're flush, the next you're chasing payments from three months ago. You've got £15k in outstanding invoices but can't pay your own bills.
            </p>
            <p className="text-gray-700 dark:text-gray-300">
              <strong>The hidden cost:</strong> You're running a successful business that's always one bad month away from disaster.
            </p>
          </div>

          <div className="bg-yellow-50 dark:bg-yellow-950/20 border border-yellow-200 dark:border-yellow-800/30 rounded-2xl p-6">
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
              <span className="w-3 h-3 bg-yellow-500 rounded-full mr-3"></span>
              The No-Show Nightmare
            </h3>
            <p className="text-gray-700 dark:text-gray-300 mb-3">
              You block out your entire Tuesday for the Johnson job. They don't answer their phone. You've lost a day's work, and now you're scrambling to fill the gap.
            </p>
            <p className="text-gray-700 dark:text-gray-300">
              <strong>The hidden cost:</strong> No-shows aren't just lost revenue - they're lost time, wasted fuel, and the stress of explaining to your next customer why you're running late.
            </p>
          </div>

          <div className="bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800/30 rounded-2xl p-6">
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
              <span className="w-3 h-3 bg-blue-500 rounded-full mr-3"></span>
              The Family Sacrifice
            </h3>
            <p className="text-gray-700 dark:text-gray-300 mb-3">
              Your daughter's school play is at 2pm. You've got a customer emergency. Guess which one wins? (Hint: it's never the school play.)
            </p>
            <p className="text-gray-700 dark:text-gray-300">
              <strong>The hidden cost:</strong> You started this business for freedom, but you've got less freedom than when you were employed.
            </p>
          </div>
        </div>
      </div>

      {/* The Breaking Point */}
      <div className="bg-gradient-to-br from-gray-50 to-slate-50 dark:from-gray-950/20 dark:to-slate-950/20 rounded-3xl p-8 border border-gray-200/50 dark:border-gray-800/30">
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6 text-center">
          The Breaking Point Every Business Owner Faces
        </h2>
        <p className="text-lg text-gray-700 dark:text-gray-300 mb-6 text-center">
          There comes a moment when you realize something has to change. For most business owners, it's one of these:
        </p>
        
        <div className="grid md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <span className="text-red-500 text-xl">💔</span>
              <p className="text-gray-700 dark:text-gray-300">Missing your child's birthday because a customer "emergency" couldn't wait</p>
            </div>
            <div className="flex items-start space-x-3">
              <span className="text-red-500 text-xl">😰</span>
              <p className="text-gray-700 dark:text-gray-300">Lying awake at 3am calculating if you can make payroll this month</p>
            </div>
            <div className="flex items-start space-x-3">
              <span className="text-red-500 text-xl">🤯</span>
              <p className="text-gray-700 dark:text-gray-300">Realizing you haven't taken a proper holiday in three years</p>
            </div>
          </div>
          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <span className="text-red-500 text-xl">😡</span>
              <p className="text-gray-700 dark:text-gray-300">Snapping at your family because you're exhausted from chasing payments</p>
            </div>
            <div className="flex items-start space-x-3">
              <span className="text-red-500 text-xl">😵</span>
              <p className="text-gray-700 dark:text-gray-300">Working 70-hour weeks but earning less than your employed friends</p>
            </div>
            <div className="flex items-start space-x-3">
              <span className="text-red-500 text-xl">🔥</span>
              <p className="text-gray-700 dark:text-gray-300">Feeling like you're one bad month away from losing everything</p>
            </div>
          </div>
        </div>
      </div>

      {/* The Solution Revelation */}
      <div>
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
          The Solution That Changes Everything (Without Losing Your Sanity)
        </h2>
        <p className="text-lg text-gray-700 dark:text-gray-300 mb-6">
          Here's what I discovered after working with 500+ UK business owners who felt exactly like you do right now:
        </p>
        <p className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-8 text-center bg-green-50 dark:bg-green-950/20 p-6 rounded-2xl border border-green-200 dark:border-green-800/30">
          The problem isn't your business. The problem is that you're doing manually what should be automated.
        </p>
        
        <div className="space-y-8">
          <div>
            <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4">
              The 4 Pillars of Business Sanity
            </h3>
            <p className="text-lg text-gray-700 dark:text-gray-300 mb-6">
              Every successful local business owner I know has automated these four things. Once you do this, everything changes:
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-6">
            <div className="bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800/30 rounded-2xl p-6">
              <h4 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
                <span className="text-2xl mr-3">📅</span>
                Pillar 1: Customer Booking
              </h4>
              <p className="text-gray-700 dark:text-gray-300 mb-3">
                <strong>The Problem:</strong> You're playing phone tag, double-booking, and manually managing your calendar.
              </p>
              <p className="text-gray-700 dark:text-gray-300">
                <strong>The Solution:</strong> 24/7 automated booking system that handles everything while you sleep.
              </p>
            </div>

            <div className="bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800/30 rounded-2xl p-6">
              <h4 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
                <span className="text-2xl mr-3">💰</span>
                Pillar 2: Payment Collection
              </h4>
              <p className="text-gray-700 dark:text-gray-300 mb-3">
                <strong>The Problem:</strong> You're chasing payments like a debt collector instead of running your business.
              </p>
              <p className="text-gray-700 dark:text-gray-300">
                <strong>The Solution:</strong> Automated payment reminders and collection that gets you paid faster.
              </p>
            </div>

            <div className="bg-purple-50 dark:bg-purple-950/20 border border-purple-200 dark:border-purple-800/30 rounded-2xl p-6">
              <h4 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
                <span className="text-2xl mr-3">📱</span>
                Pillar 3: Customer Communication
              </h4>
              <p className="text-gray-700 dark:text-gray-300 mb-3">
                <strong>The Problem:</strong> You're constantly interrupted by calls, texts, and emails about basic questions.
              </p>
              <p className="text-gray-700 dark:text-gray-300">
                <strong>The Solution:</strong> Automated reminders and responses that keep customers happy without your input.
              </p>
            </div>

            <div className="bg-orange-50 dark:bg-orange-950/20 border border-orange-200 dark:border-orange-800/30 rounded-2xl p-6">
              <h4 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
                <span className="text-2xl mr-3">⭐</span>
                Pillar 4: Review Generation
              </h4>
              <p className="text-gray-700 dark:text-gray-300 mb-3">
                <strong>The Problem:</strong> You do great work but struggle to get reviews, while your competitors seem to get them effortlessly.
              </p>
              <p className="text-gray-700 dark:text-gray-300">
                <strong>The Solution:</strong> Automated review requests that turn happy customers into 5-star reviews.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Real Results */}
      <div className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-950/20 dark:to-emerald-950/20 rounded-3xl p-8 border border-green-200/50 dark:border-green-800/30">
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6 text-center">
          What Happens When You Fix These Hidden Struggles
        </h2>
        
        <div className="grid md:grid-cols-3 gap-6 mb-8">
          <div className="text-center">
            <div className="text-4xl font-black text-green-600 dark:text-green-400 mb-2">85%</div>
            <div className="text-lg font-semibold text-gray-700 dark:text-gray-300">Fewer No-Shows</div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Automated reminders work</div>
          </div>
          <div className="text-center">
            <div className="text-4xl font-black text-green-600 dark:text-green-400 mb-2">20+</div>
            <div className="text-lg font-semibold text-gray-700 dark:text-gray-300">Hours Saved Weekly</div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Time for what matters</div>
          </div>
          <div className="text-center">
            <div className="text-4xl font-black text-green-600 dark:text-green-400 mb-2">£5K+</div>
            <div className="text-lg font-semibold text-gray-700 dark:text-gray-300">Extra Monthly Revenue</div>
            <div className="text-sm text-gray-600 dark:text-gray-400">From better systems</div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700">
          <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-4">Real Stories from Real Business Owners</h3>
          <div className="space-y-4">
            <blockquote className="border-l-4 border-green-500 pl-4">
              <p className="text-gray-700 dark:text-gray-300 italic mb-2">
                "I used to work until 9pm every night. Now I'm home for dinner with my family. The automation handles everything."
              </p>
              <cite className="text-sm font-semibold text-gray-600 dark:text-gray-400">- Anthony, Universal Windows, Middlesbrough</cite>
            </blockquote>
            
            <blockquote className="border-l-4 border-blue-500 pl-4">
              <p className="text-gray-700 dark:text-gray-300 italic mb-2">
                "No more chasing payments. No more no-shows. I actually took a proper holiday for the first time in five years."
              </p>
              <cite className="text-sm font-semibold text-gray-600 dark:text-gray-400">- David, Curb Appeal, Middlesbrough</cite>
            </blockquote>
          </div>
        </div>
      </div>

      {/* The Path Forward */}
      <div>
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
          Your Path to Business Sanity (Without the Overwhelm)
        </h2>
        <p className="text-lg text-gray-700 dark:text-gray-300 mb-8">
          I know what you're thinking: "This sounds great, but I don't have time to learn new systems." Here's the thing - you don't have to.
        </p>
        
        <div className="space-y-6">
          <div className="bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800/30 rounded-2xl p-6">
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3">
              Step 1: Stop the Bleeding (Week 1)
            </h3>
            <p className="text-gray-700 dark:text-gray-300">
              We start with the biggest pain point - usually no-shows or payment chasing. One quick fix that gives you immediate relief.
            </p>
          </div>

          <div className="bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800/30 rounded-2xl p-6">
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3">
              Step 2: Build the Foundation (Week 2-3)
            </h3>
            <p className="text-gray-700 dark:text-gray-300">
              Set up the core systems that handle 80% of your daily admin. You'll start seeing results immediately.
            </p>
          </div>

          <div className="bg-purple-50 dark:bg-purple-950/20 border border-purple-200 dark:border-purple-800/30 rounded-2xl p-6">
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3">
              Step 3: Scale and Optimize (Week 4+)
            </h3>
            <p className="text-gray-700 dark:text-gray-300">
              Fine-tune everything until your business runs like clockwork. This is where you get your life back.
            </p>
          </div>
        </div>
      </div>

      {/* Call to Action */}
      <div className="bg-gradient-to-br from-blue-600 to-purple-600 rounded-3xl p-8 text-white text-center">
        <h2 className="text-3xl font-bold mb-6">
          Ready to Stop Struggling and Start Succeeding?
        </h2>
        <p className="text-xl mb-8 opacity-90">
          Book a free 30-minute call. We'll identify your biggest hidden struggle and show you exactly how to fix it.
        </p>
        <div className="space-y-4">
          <p className="text-lg opacity-90">
            ✅ No sales pitch - just solutions<br/>
            ✅ See your exact automation roadmap<br/>
            ✅ Get actionable steps you can implement today
          </p>
          <div className="pt-4">
            <p className="text-lg font-semibold">
              📞 +44 7401 137621<br/>
              📧 <EMAIL>
            </p>
          </div>
        </div>
      </div>

      {/* Final Thought */}
      <div className="text-center">
        <p className="text-lg text-gray-700 dark:text-gray-300 italic">
          "The best time to plant a tree was 20 years ago. The second best time is now."
        </p>
        <p className="text-base text-gray-600 dark:text-gray-400 mt-2">
          Your business sanity is waiting. The question is: how much longer will you wait?
        </p>
      </div>

    </div>
  )
}

export const hiddenStrugglesPost: BlogPost = {
  id: '9',
  slug: 'hidden-struggles-local-businesses',
  title: 'The Hidden Struggles of Local Businesses (And How to Fix Them Without Losing Your Sanity)',
  excerpt: 'Everyone sees the successful business. Nobody sees the 3am anxiety, the cash flow stress, or the family sacrifices. Here\'s how to fix the hidden struggles that are slowly killing your business.',
  content: <BlogContent />,
  author: {
    name: 'Chedi Rach',
    role: 'Business Automation Specialist',
    avatar: '/logo/genlogic-icon-only.svg',
    bio: 'Chedi has helped over 500 UK businesses overcome their hidden struggles and build systems that work.'
  },
  publishedAt: '2025-07-19',
  readingTime: '12 min read',
  category: 'Business Strategy',
  tags: ['business-struggles', 'automation', 'work-life-balance', 'local-business'],
  featured: true,
  image: '/blog-images/hidden-struggles-local-businesses.webp',
  views: 3247,
  likes: 1500
}
