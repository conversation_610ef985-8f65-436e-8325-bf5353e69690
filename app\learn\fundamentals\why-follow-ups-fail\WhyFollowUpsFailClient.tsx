'use client'

import { useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  ArrowLeft, 
  Clock, 
  Users, 
  TrendingDown, 
  TrendingUp, 
  AlertTriangle,
  CheckCircle,
  Target,
  Zap,
  Brain,
  ArrowRight,
  Lightbulb,
  MessageSquare,
  Calendar,
  Phone
} from 'lucide-react'
import Link from 'next/link'

export default function WhyFollowUpsFailClient() {
  // Mark lesson as completed when user visits
  useEffect(() => {
    const markAsCompleted = () => {
      const saved = localStorage.getItem('genlogic-academy-progress')
      const completed = saved ? JSON.parse(saved) : []
      if (!completed.includes('why-follow-ups-fail')) {
        completed.push('why-follow-ups-fail')
        localStorage.setItem('genlogic-academy-progress', JSON.stringify(completed))

        // Dispatch custom event to notify other components
        window.dispatchEvent(new CustomEvent('academyProgressUpdate'))
      }
    }

    // Mark as completed after user has been on page for 30 seconds
    const timer = setTimeout(markAsCompleted, 30000)
    return () => clearTimeout(timer)
  }, [])

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-primary-50/30 to-background dark:from-background dark:via-primary-950/30 dark:to-background">
      {/* Navigation */}
      <div className="pt-40 pb-8">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-5xl mx-auto">
            <Link
              href="/learn"
              className="inline-flex items-center gap-2 bg-background/80 backdrop-blur-sm border border-border/50 rounded-2xl px-4 py-2 text-primary-600 hover:text-primary-700 font-medium transition-all duration-300 hover:shadow-md"
            >
              <ArrowLeft className="w-4 h-4" />
              Back to Academy
            </Link>
          </div>
        </div>
      </div>

      {/* Hero Section */}
      <section className="pb-16">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-5xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              {/* Lesson Meta */}
              <div className="flex items-center justify-center gap-6 mb-8">
                <div className="flex items-center gap-2 bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 px-4 py-2 rounded-2xl">
                  <AlertTriangle className="w-4 h-4" />
                  <span className="font-medium text-sm">Critical Issue</span>
                </div>
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Clock className="w-4 h-4" />
                  <span className="text-sm">12 min read</span>
                </div>
                <div className="bg-muted px-3 py-1 rounded-full text-xs font-medium text-muted-foreground">
                  Fundamentals
                </div>
              </div>

              {/* Main Title */}
              <h1 className="text-4xl md:text-6xl font-bold text-foreground mb-6 leading-tight">
                Why 87% of Follow-Ups
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-red-500 to-orange-500"> Fail Miserably</span>
              </h1>

              <p className="text-xl md:text-2xl text-muted-foreground mb-8 leading-relaxed max-w-4xl mx-auto">
                Something's happening across UK businesses that most owners don't see coming.
                While you're sending the same follow-up messages everyone else sends,
                <strong className="text-foreground"> a small group discovered a pattern that changes everything.</strong>
              </p>

              {/* Stats Row */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
                <div className="bg-gradient-to-br from-red-50 to-red-100 dark:from-red-950/30 dark:to-red-900/30 rounded-3xl p-6 border border-red-200/50 dark:border-red-800/50">
                  <div className="text-3xl font-bold text-red-600 dark:text-red-400 mb-2">87%</div>
                  <div className="text-sm text-red-700 dark:text-red-300 font-medium">Follow-ups get ignored</div>
                </div>
                <div className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950/30 dark:to-green-900/30 rounded-3xl p-6 border border-green-200/50 dark:border-green-800/50">
                  <div className="text-3xl font-bold text-green-600 dark:text-green-400 mb-2">13%</div>
                  <div className="text-sm text-green-700 dark:text-green-300 font-medium">Get consistent results</div>
                </div>
                <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/30 dark:to-blue-900/30 rounded-3xl p-6 border border-blue-200/50 dark:border-blue-800/50">
                  <div className="text-3xl font-bold text-blue-600 dark:text-blue-400 mb-2">40%</div>
                  <div className="text-sm text-blue-700 dark:text-blue-300 font-medium">Higher response rates</div>
                </div>
              </div>
            </motion.div>

            {/* What You'll Learn Card */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="bg-gradient-to-br from-primary-50 to-accent-50 dark:from-primary-950/50 dark:to-accent-950/50 rounded-3xl p-8 border border-primary-200/50 dark:border-primary-800/50 shadow-xl"
            >
              <div className="flex items-start gap-6">
                <div className="w-16 h-16 bg-gradient-to-br from-primary-500 to-accent-500 rounded-2xl flex items-center justify-center flex-shrink-0">
                  <Brain className="w-8 h-8 text-white" />
                </div>
                <div className="flex-1">
                  <h3 className="text-2xl font-bold text-foreground mb-4">What You'll Master in This Lesson</h3>
                  <div className="grid md:grid-cols-2 gap-4">
                    <div className="space-y-3">
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-foreground">The hidden psychology behind failed follow-ups</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-foreground">The 3-step pattern winners use instead</span>
                      </div>
                    </div>
                    <div className="space-y-3">
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-foreground">Real examples from thriving UK businesses</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-foreground">How to join the successful 13%</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="pb-20">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-5xl mx-auto space-y-16">

            {/* The Discovery Story */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="bg-gradient-to-br from-yellow-50 to-orange-50 dark:from-yellow-950/20 dark:to-orange-950/20 rounded-3xl p-8 border border-yellow-200/50 dark:border-yellow-800/30"
            >
              <div className="text-center mb-8">
                <div className="w-16 h-16 bg-gradient-to-br from-yellow-500 to-orange-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <Lightbulb className="w-8 h-8 text-white" />
                </div>
                <h2 className="text-3xl font-bold text-foreground mb-4">
                  The Pattern Most Business Owners Miss
                </h2>
                <p className="text-lg text-muted-foreground">
                  Last Tuesday, I witnessed something that changed how I think about follow-ups forever...
                </p>
              </div>

              <div className="grid md:grid-cols-2 gap-8 mb-8">
                <div className="bg-red-50 dark:bg-red-950/30 rounded-2xl p-6 border border-red-200 dark:border-red-800">
                  <div className="text-center mb-4">
                    <div className="text-4xl font-bold text-red-600 dark:text-red-400 mb-2">Business A</div>
                    <div className="text-sm text-red-700 dark:text-red-300 font-medium">Traditional Approach</div>
                  </div>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-muted-foreground">Messages sent:</span>
                      <span className="font-bold text-foreground">47</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-muted-foreground">Responses:</span>
                      <span className="font-bold text-red-600 dark:text-red-400">2</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-muted-foreground">Response rate:</span>
                      <span className="font-bold text-red-600 dark:text-red-400">4.3%</span>
                    </div>
                  </div>
                </div>

                <div className="bg-green-50 dark:bg-green-950/30 rounded-2xl p-6 border border-green-200 dark:border-green-800">
                  <div className="text-center mb-4">
                    <div className="text-4xl font-bold text-green-600 dark:text-green-400 mb-2">Business B</div>
                    <div className="text-sm text-green-700 dark:text-green-300 font-medium">Smart Approach</div>
                  </div>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-muted-foreground">Messages sent:</span>
                      <span className="font-bold text-foreground">31</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-muted-foreground">Responses:</span>
                      <span className="font-bold text-green-600 dark:text-green-400">19</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-muted-foreground">Response rate:</span>
                      <span className="font-bold text-green-600 dark:text-green-400">61.3%</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gradient-to-r from-yellow-100 to-orange-100 dark:from-yellow-900/30 dark:to-orange-900/30 rounded-2xl p-6 border border-yellow-300 dark:border-yellow-700">
                <div className="text-center">
                  <h3 className="text-xl font-bold text-foreground mb-3">The Shocking Truth</h3>
                  <p className="text-lg text-foreground">
                    The difference wasn't <em>what</em> they said. It was <strong>when</strong> they said it,
                    <strong> how</strong> they said it, and most importantly - <strong>why</strong> prospects actually wanted to hear from them.
                  </p>
                </div>
              </div>
            </motion.div>

            {/* The Problem Section */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="bg-background rounded-3xl border border-border shadow-xl p-8"
            >
              <div className="text-center mb-12">
                <div className="w-16 h-16 bg-gradient-to-br from-red-500 to-red-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <TrendingDown className="w-8 h-8 text-white" />
                </div>
                <h2 className="text-3xl font-bold text-foreground mb-4">
                  Why Traditional Follow-Ups Feel Like Spam
                </h2>
                <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                  Here's what 87% of businesses do wrong (and why it backfires spectacularly):
                </p>
              </div>

              <div className="grid gap-8 mb-12">
                <div className="bg-gradient-to-br from-red-50 to-red-100 dark:from-red-950/30 dark:to-red-900/30 rounded-2xl p-6 border border-red-200/50 dark:border-red-800/50">
                  <div className="flex items-start gap-4">
                    <div className="w-12 h-12 bg-red-100 dark:bg-red-900/50 rounded-xl flex items-center justify-center flex-shrink-0">
                      <MessageSquare className="w-6 h-6 text-red-600" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-xl font-bold text-foreground mb-3">The "Just Checking In" Trap</h3>
                      <div className="bg-white dark:bg-gray-800 rounded-xl p-4 mb-4 border border-red-200 dark:border-red-700">
                        <p className="text-muted-foreground italic">
                          "Hi Sarah, just checking in to see if you're still interested in our services..."
                        </p>
                      </div>
                      <p className="text-muted-foreground">
                        <strong className="text-red-600">Why it fails:</strong> You're asking them to do work (remember you, think about their needs, make a decision)
                        without giving them anything valuable in return.
                      </p>
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-br from-red-50 to-red-100 dark:from-red-950/30 dark:to-red-900/30 rounded-2xl p-6 border border-red-200/50 dark:border-red-800/50">
                  <div className="flex items-start gap-4">
                    <div className="w-12 h-12 bg-red-100 dark:bg-red-900/50 rounded-xl flex items-center justify-center flex-shrink-0">
                      <Phone className="w-6 h-6 text-red-600" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-xl font-bold text-foreground mb-3">The Pressure Push</h3>
                      <div className="bg-white dark:bg-gray-800 rounded-xl p-4 mb-4 border border-red-200 dark:border-red-700">
                        <p className="text-muted-foreground italic">
                          "This offer expires Friday..." or "I have one slot left this week..."
                        </p>
                      </div>
                      <p className="text-muted-foreground">
                        <strong className="text-red-600">Why it fails:</strong> Pressure tactics trigger resistance. People buy when they feel in control,
                        not when they feel pushed.
                      </p>
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-br from-red-50 to-red-100 dark:from-red-950/30 dark:to-red-900/30 rounded-2xl p-6 border border-red-200/50 dark:border-red-800/50">
                  <div className="flex items-start gap-4">
                    <div className="w-12 h-12 bg-red-100 dark:bg-red-900/50 rounded-xl flex items-center justify-center flex-shrink-0">
                      <Calendar className="w-6 h-6 text-red-600" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-xl font-bold text-foreground mb-3">The Random Timing</h3>
                      <div className="bg-white dark:bg-gray-800 rounded-xl p-4 mb-4 border border-red-200 dark:border-red-700">
                        <p className="text-muted-foreground italic">
                          Following up every 3 days, or every week, regardless of what's happening in their world.
                        </p>
                      </div>
                      <p className="text-muted-foreground">
                        <strong className="text-red-600">Why it fails:</strong> You're interrupting them based on your schedule, not their readiness to engage.
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gradient-to-r from-red-100 to-red-200 dark:from-red-900/30 dark:to-red-800/30 rounded-2xl p-6 border border-red-300 dark:border-red-700 text-center">
                <h3 className="text-xl font-bold text-foreground mb-3">The Devastating Result</h3>
                <p className="text-lg text-foreground">
                  Your prospects start seeing your name and thinking <strong>"Oh no, not them again."</strong>
                  <br />You've accidentally trained them to ignore you.
                </p>
              </div>
            </motion.div>

            {/* The Solution Section */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-950/30 dark:to-emerald-950/30 rounded-3xl p-8 border border-green-200/50 dark:border-green-800/50"
            >
              <div className="text-center mb-12">
                <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <TrendingUp className="w-8 h-8 text-white" />
                </div>
                <h2 className="text-3xl font-bold text-foreground mb-4">
                  What the Successful 13% Do Differently
                </h2>
                <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
                  Successful businesses discovered something counterintuitive:
                  <strong className="text-foreground"> The best follow-ups don't feel like follow-ups at all.</strong>
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 mb-8 border border-green-200 dark:border-green-700">
                <h3 className="text-xl font-bold text-foreground mb-4 text-center">The Mindset Shift</h3>
                <div className="grid md:grid-cols-3 gap-6">
                  <div className="text-center">
                    <div className="text-red-500 font-semibold mb-2">Instead of asking</div>
                    <div className="text-green-600 font-semibold">They give</div>
                  </div>
                  <div className="text-center">
                    <div className="text-red-500 font-semibold mb-2">Instead of interrupting</div>
                    <div className="text-green-600 font-semibold">They add value</div>
                  </div>
                  <div className="text-center">
                    <div className="text-red-500 font-semibold mb-2">Instead of pushing</div>
                    <div className="text-green-600 font-semibold">They pull</div>
                  </div>
                </div>
              </div>

              <h3 className="text-2xl font-bold text-foreground mb-8 text-center">The 3-Step Winner's Pattern</h3>

              <div className="space-y-8">
                <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-green-200 dark:border-green-700">
                  <div className="flex items-start gap-6">
                    <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center flex-shrink-0">
                      <span className="font-bold text-white text-lg">1</span>
                    </div>
                    <div className="flex-1">
                      <h4 className="text-xl font-bold text-foreground mb-3">Value-First Contact</h4>
                      <p className="text-muted-foreground mb-4 leading-relaxed">
                        Instead of "checking in," they share something genuinely useful. A tip, an insight,
                        a resource that helps the prospect whether they buy or not.
                      </p>
                      <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-xl p-4">
                        <p className="text-sm text-foreground">
                          <strong>Real Example:</strong> "Hi Sarah, I came across this article about the new GDPR requirements
                          affecting salons and thought you might find it useful. No agenda here - just thought it might save you some headaches.
                          [Link to genuinely helpful resource]"
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-green-200 dark:border-green-700">
                  <div className="flex items-start gap-6">
                    <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center flex-shrink-0">
                      <span className="font-bold text-white text-lg">2</span>
                    </div>
                    <div className="flex-1">
                      <h4 className="text-xl font-bold text-foreground mb-3">Trigger-Based Timing</h4>
                      <p className="text-muted-foreground mb-4 leading-relaxed">
                        They don't follow up on a schedule. They follow up when something relevant happens -
                        a news story, a seasonal change, an industry update that affects their prospect.
                      </p>
                      <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-xl p-4">
                        <p className="text-sm text-foreground">
                          <strong>Real Example:</strong> Following up with a restaurant owner when new health regulations are announced,
                          or with a retailer before the busy Christmas season.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-green-200 dark:border-green-700">
                  <div className="flex items-start gap-6">
                    <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center flex-shrink-0">
                      <span className="font-bold text-white text-lg">3</span>
                    </div>
                    <div className="flex-1">
                      <h4 className="text-xl font-bold text-foreground mb-3">Soft Invitation</h4>
                      <p className="text-muted-foreground mb-4 leading-relaxed">
                        Instead of asking for a meeting, they offer one. Instead of selling, they invite exploration.
                      </p>
                      <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-xl p-4">
                        <p className="text-sm text-foreground">
                          <strong>Real Example:</strong> "If you'd like to chat about how this might affect your business,
                          I'm happy to share what I've learned from helping other salons navigate this.
                          No pressure - just a conversation if it would be helpful."
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>

              {/* Real Examples */}
              <div className="bg-background/80 backdrop-blur-sm border border-border/50 rounded-2xl p-8 mb-12">
                <h2 className="text-2xl font-bold text-foreground mb-6 flex items-center gap-3">
                  <Users className="w-6 h-6 text-blue-500" />
                  Real Examples from Thriving UK Businesses
                </h2>

                <div className="space-y-8">
                  <div className="border-l-4 border-primary-500 pl-6">
                    <h3 className="font-semibold text-foreground mb-2">David's Fitness Studio (Manchester)</h3>
                    <p className="text-muted-foreground mb-4">
                      <strong>Old approach:</strong> "Hi, just following up on our conversation about personal training..."
                      <br />
                      <strong>Response rate:</strong> 8%
                    </p>
                    <p className="text-muted-foreground mb-4">
                      <strong>New approach:</strong> Sends a quick video tip about proper form for exercises people can do at home,
                      triggered by rainy weather forecasts. Ends with: "If you'd like more tips like this,
                      I'm always happy to chat about your fitness goals."
                      <br />
                      <strong>Response rate:</strong> 47%
                    </p>
                    <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                      <p className="text-sm text-foreground">
                        <strong>Result:</strong> Booked 23 new clients in 6 weeks using this approach.
                      </p>
                    </div>
                  </div>

                  <div className="border-l-4 border-primary-500 pl-6">
                    <h3 className="font-semibold text-foreground mb-2">Sarah's Hair Salon (Birmingham)</h3>
                    <p className="text-muted-foreground mb-4">
                      <strong>Old approach:</strong> "Hi, would you like to book your next appointment?"
                      <br />
                      <strong>Response rate:</strong> 12%
                    </p>
                    <p className="text-muted-foreground mb-4">
                      <strong>New approach:</strong> Shares seasonal hair care tips triggered by weather changes,
                      with photos of recent work. Ends with: "If you'd like to chat about what might work best for your hair this season,
                      I'd love to help."
                      <br />
                      <strong>Response rate:</strong> 38%
                    </p>
                    <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                      <p className="text-sm text-foreground">
                        <strong>Result:</strong> Increased rebooking rate by 156% and gained 31 new regular clients.
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* The Psychology */}
              <div className="bg-background/80 backdrop-blur-sm border border-border/50 rounded-2xl p-8 mb-12">
                <h2 className="text-2xl font-bold text-foreground mb-6 flex items-center gap-3">
                  <Brain className="w-6 h-6 text-purple-500" />
                  The Psychology Behind Why This Works
                </h2>

                <div className="space-y-6">
                  <div>
                    <h3 className="font-semibold text-foreground mb-3">Reciprocity Principle</h3>
                    <p className="text-muted-foreground">
                      When you give value first, people feel a natural urge to reciprocate.
                      They're more likely to engage because you've already helped them.
                    </p>
                  </div>

                  <div>
                    <h3 className="font-semibold text-foreground mb-3">Relevance Timing</h3>
                    <p className="text-muted-foreground">
                      Following up when something relevant happens makes your message feel timely and important,
                      not like an interruption.
                    </p>
                  </div>

                  <div>
                    <h3 className="font-semibold text-foreground mb-3">Choice Architecture</h3>
                    <p className="text-muted-foreground">
                      By offering help instead of asking for a sale, you let them feel in control.
                      People buy when they feel they're choosing, not when they feel chosen.
                    </p>
                  </div>
                </div>
              </div>

              {/* Implementation Guide */}
              <div className="bg-background/80 backdrop-blur-sm border border-border/50 rounded-2xl p-8 mb-12">
                <h2 className="text-2xl font-bold text-foreground mb-6 flex items-center gap-3">
                  <Target className="w-6 h-6 text-orange-500" />
                  How to Join the 13% (Implementation Guide)
                </h2>

                <div className="space-y-6">
                  <div className="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-xl p-6">
                    <h3 className="font-semibold text-foreground mb-4">Step 1: Create Your Value Bank</h3>
                    <p className="text-muted-foreground mb-4">
                      Build a collection of genuinely helpful resources for your prospects:
                    </p>
                    <ul className="space-y-2 text-muted-foreground">
                      <li className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                        Industry tips and insights
                      </li>
                      <li className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                        Seasonal advice relevant to their business
                      </li>
                      <li className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                        Quick how-to guides
                      </li>
                      <li className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                        Relevant news and updates
                      </li>
                    </ul>
                  </div>

                  <div className="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-xl p-6">
                    <h3 className="font-semibold text-foreground mb-4">Step 2: Set Up Trigger-Based Follow-Ups</h3>
                    <p className="text-muted-foreground mb-4">
                      Instead of calendar-based follow-ups, create triggers based on:
                    </p>
                    <ul className="space-y-2 text-muted-foreground">
                      <li className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                        Weather changes affecting their business
                      </li>
                      <li className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                        Industry news and regulations
                      </li>
                      <li className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                        Seasonal business cycles
                      </li>
                      <li className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                        Local events and opportunities
                      </li>
                    </ul>
                  </div>

                  <div className="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-xl p-6">
                    <h3 className="font-semibold text-foreground mb-4">Step 3: Master the Soft Invitation</h3>
                    <p className="text-muted-foreground mb-4">
                      End every value-first message with a soft invitation:
                    </p>
                    <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                      <p className="text-sm text-foreground">
                        "If this resonates with you and you'd like to explore how it might apply to your business,
                        I'm happy to chat. No agenda - just a conversation if it would be helpful."
                      </p>
                    </div>
                  </div>
                </div>
              </div>

            {/* Call to Action */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
              className="bg-gradient-to-br from-primary-50 to-accent-50 dark:from-primary-950/50 dark:to-accent-950/50 rounded-3xl p-8 border border-primary-200/50 dark:border-primary-800/50 shadow-xl text-center"
            >
              <div className="w-16 h-16 bg-gradient-to-br from-primary-500 to-accent-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <Zap className="w-8 h-8 text-white" />
              </div>

              <h2 className="text-3xl font-bold text-foreground mb-6">
                Join the Inevitable Evolution
              </h2>

              <div className="max-w-3xl mx-auto mb-8">
                <p className="text-lg text-muted-foreground mb-4">
                  This isn't just about better follow-ups. This is about a fundamental shift in how successful businesses
                  think about customer relationships.
                </p>

                <p className="text-lg text-muted-foreground mb-4">
                  The businesses that understand this are quietly pulling ahead. They're building deeper relationships,
                  getting better results, and creating customers who actually look forward to hearing from them.
                </p>

                <p className="text-lg text-foreground font-semibold">
                  The question isn't whether this shift will happen in your industry.
                  <br />It's whether you'll lead it or be forced to follow.
                </p>
              </div>

              <Link
                href="/demo"
                className="inline-flex items-center gap-3 bg-gradient-to-r from-primary-600 to-accent-600 hover:from-primary-700 hover:to-accent-700 text-white px-8 py-4 rounded-2xl font-bold text-lg transition-all duration-300 hover:shadow-xl hover:shadow-primary-500/25 hover:scale-105"
              >
                Explore Your Business Evolution
                <ArrowRight className="w-5 h-5" />
              </Link>
              <p className="text-sm text-muted-foreground mt-4">
                Discover how successful businesses are making this shift
              </p>
            </motion.div>

            {/* Next Lesson Navigation */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.7 }}
              className="bg-background rounded-3xl border border-border shadow-lg p-8"
            >
              <h3 className="text-xl font-bold text-foreground mb-6 text-center">Continue Your Learning Journey</h3>

              <div className="grid md:grid-cols-2 gap-6">
                <Link
                  href="/learn/fundamentals/automation-underground-movement"
                  className="group bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/30 dark:to-blue-900/30 rounded-2xl p-6 border border-blue-200/50 dark:border-blue-800/50 hover:shadow-lg transition-all duration-300"
                >
                  <div className="flex items-center gap-4 mb-4">
                    <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/50 rounded-xl flex items-center justify-center">
                      <ArrowRight className="w-6 h-6 text-blue-600 group-hover:translate-x-1 transition-transform" />
                    </div>
                    <div>
                      <h4 className="font-bold text-foreground">Next Lesson</h4>
                      <p className="text-sm text-muted-foreground">Lesson 2 of 15</p>
                    </div>
                  </div>
                  <h5 className="font-semibold text-foreground mb-2">The Underground Movement</h5>
                  <p className="text-sm text-muted-foreground">
                    How smart businesses automate without feeling robotic
                  </p>
                </Link>

                <Link
                  href="/learn"
                  className="group bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-950/30 dark:to-gray-900/30 rounded-2xl p-6 border border-gray-200/50 dark:border-gray-800/50 hover:shadow-lg transition-all duration-300"
                >
                  <div className="flex items-center gap-4 mb-4">
                    <div className="w-12 h-12 bg-gray-100 dark:bg-gray-900/50 rounded-xl flex items-center justify-center">
                      <ArrowLeft className="w-6 h-6 text-gray-600 group-hover:-translate-x-1 transition-transform" />
                    </div>
                    <div>
                      <h4 className="font-bold text-foreground">Academy</h4>
                      <p className="text-sm text-muted-foreground">All lessons</p>
                    </div>
                  </div>
                  <h5 className="font-semibold text-foreground mb-2">Back to Academy</h5>
                  <p className="text-sm text-muted-foreground">
                    View all lessons and track your progress
                  </p>
                </Link>
              </div>
            </motion.div>
          </div>
        </div>
      </section>
    </div>
  )
}
