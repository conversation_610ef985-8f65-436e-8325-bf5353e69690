import { BlogPost, Category } from '../types'
import { stopWorkingLatePost } from './stop-working-late'
import { noShowNightmarePost } from './no-show-nightmare-solution'
import { weekendWarriorPost } from './weekend-warrior-to-family-time'
import { paymentChasingPost } from './payment-chasing-nightmare'
import { socialMediaPost } from './social-media-time-trap'
import { customerServicePost } from './customer-service-automation'
import { reviewGenerationPost } from './review-generation-system'
import { nextjsWebsitePost } from './nextjs-website-development'
import { hiddenStrugglesPost } from './hidden-struggles-local-businesses'
import { signsYourBusinessNeedsAutomationPost } from './signs-your-business-needs-automation'
import { automationForCustomerSatisfactionPost } from './automation-for-customer-satisfaction'
import { convert89PercentProspectsPost } from './convert-89-percent-prospects-customers'
import { losingCustomersCompetitorsPost } from './losing-customers-competitors'
import { successfulBusinessPatternPost } from './successful-business-pattern'
import { silentRevolutionPost } from './silent-revolution-uk-businesses'

// All blog posts
export const allBlogPosts: BlogPost[] = [
  silentRevolutionPost, // Featured new post at top
  successfulBusinessPatternPost,
  losingCustomersCompetitorsPost,
  convert89PercentProspectsPost,
  automationForCustomerSatisfactionPost,
  signsYourBusinessNeedsAutomationPost,
  hiddenStrugglesPost,
  stopWorkingLatePost,
  paymentChasingPost,
  socialMediaPost,
  customerServicePost,
  reviewGenerationPost,
  noShowNightmarePost,
  weekendWarriorPost,
  nextjsWebsitePost,
]

// Get all posts sorted by date (newest first)
export function getAllBlogPosts(): BlogPost[] {
  return allBlogPosts.sort((a, b) => 
    new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime()
  )
}

// Get post by slug
export function getBlogPostBySlug(slug: string): BlogPost | null {
  return allBlogPosts.find(post => post.slug === slug) || null
}

// Get featured posts
export function getFeaturedPosts(): BlogPost[] {
  return allBlogPosts.filter(post => post.featured)
}

// Get posts by category
export function getPostsByCategory(category: string): BlogPost[] {
  if (category === 'all') return getAllBlogPosts()
  return allBlogPosts.filter(post => 
    post.category.toLowerCase().replace(' ', '-') === category.toLowerCase()
  )
}

// Get related posts
export function getRelatedPosts(currentPost: BlogPost, limit: number = 3): BlogPost[] {
  // Get posts from same category, excluding current post
  const relatedPosts = allBlogPosts.filter(post => 
    post.category === currentPost.category && post.id !== currentPost.id
  )
  
  // If not enough related posts, fill with other posts
  if (relatedPosts.length < limit) {
    const additionalPosts = allBlogPosts.filter(post => 
      post.id !== currentPost.id && 
      !relatedPosts.some(rp => rp.id === post.id)
    )
    relatedPosts.push(...additionalPosts.slice(0, limit - relatedPosts.length))
  }
  
  return relatedPosts.slice(0, limit)
}

// Get categories
export function getCategories(): Category[] {
  const categoryMap = new Map<string, number>()
  
  // Count posts per category
  allBlogPosts.forEach(post => {
    const count = categoryMap.get(post.category) || 0
    categoryMap.set(post.category, count + 1)
  })
  
  // Convert to category objects
  const categories: Category[] = [
    { name: 'All Posts', slug: 'all', count: allBlogPosts.length }
  ]
  
  categoryMap.forEach((count, categoryName) => {
    categories.push({
      name: categoryName,
      slug: categoryName.toLowerCase().replace(' ', '-'),
      count
    })
  })
  
  return categories
}

// Get all slugs for static generation
export function getAllSlugs(): string[] {
  return allBlogPosts.map(post => post.slug)
}
