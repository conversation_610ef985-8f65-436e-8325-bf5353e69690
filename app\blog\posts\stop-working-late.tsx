import React from 'react'
import { BlogPost } from '../types'

const BlogContent = () => {
  return (
    <div className="max-w-4xl mx-auto space-y-12">
      
      {/* Opening Hook */}
      <div className="bg-gradient-to-br from-red-50 to-orange-50 dark:from-red-950/20 dark:to-orange-950/20 rounded-3xl p-8 border border-red-200/50 dark:border-red-800/30">
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6 text-center">
          It's 9:47pm on a Tuesday...
        </h2>
        <p className="text-lg text-gray-700 dark:text-gray-300 mb-4 text-center">
          You're still at your laptop, confirming tomorrow's appointments while your family watches Netflix without you. <strong>Again.</strong>
        </p>
        <p className="text-lg text-gray-700 dark:text-gray-300 text-center font-medium">
          This wasn't the plan, was it?
        </p>
      </div>

      {/* Problem Section */}
      <div>
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
          The Business Owner's Reality Check
        </h2>
        <p className="text-lg text-gray-700 dark:text-gray-300 mb-6">
          You started your business for freedom. More time with family. Financial independence. Being your own boss.
        </p>
        <p className="text-lg text-gray-700 dark:text-gray-300 mb-8">
          But here's what actually happened:
        </p>
        
        <div className="grid md:grid-cols-2 gap-6">
          <div className="bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800/30 rounded-2xl p-6">
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-4">What You Expected</h3>
            <ul className="space-y-2">
              <li className="flex items-center text-gray-700 dark:text-gray-300">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                Work-life balance
              </li>
              <li className="flex items-center text-gray-700 dark:text-gray-300">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                Financial freedom
              </li>
              <li className="flex items-center text-gray-700 dark:text-gray-300">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                Quality family time
              </li>
              <li className="flex items-center text-gray-700 dark:text-gray-300">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                Being in control
              </li>
            </ul>
          </div>
          
          <div className="bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800/30 rounded-2xl p-6">
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-4">What You Got Instead</h3>
            <ul className="space-y-2">
              <li className="flex items-center text-gray-700 dark:text-gray-300">
                <span className="w-2 h-2 bg-red-500 rounded-full mr-3"></span>
                Working until 10pm
              </li>
              <li className="flex items-center text-gray-700 dark:text-gray-300">
                <span className="w-2 h-2 bg-red-500 rounded-full mr-3"></span>
                Stressed about money
              </li>
              <li className="flex items-center text-gray-700 dark:text-gray-300">
                <span className="w-2 h-2 bg-red-500 rounded-full mr-3"></span>
                Missing family moments
              </li>
              <li className="flex items-center text-gray-700 dark:text-gray-300">
                <span className="w-2 h-2 bg-red-500 rounded-full mr-3"></span>
                Feeling overwhelmed
              </li>
            </ul>
          </div>
        </div>
      </div>

      {/* Success Stats */}
      <div className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 rounded-3xl p-8 border border-blue-200/50 dark:border-blue-800/30 text-center">
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
          But Here's What 500+ UK Business Owners Discovered
        </h2>
        
        <div className="mb-8">
          <div className="text-6xl font-black text-blue-600 dark:text-blue-400 mb-2">500+</div>
          <div className="text-xl font-semibold text-gray-700 dark:text-gray-300">UK business owners have cracked the code</div>
        </div>
        
        <div className="grid md:grid-cols-3 gap-4">
          <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-700">
            <div className="text-green-600 dark:text-green-400 font-semibold">✅ Home by 6pm every night</div>
          </div>
          <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-700">
            <div className="text-green-600 dark:text-green-400 font-semibold">✅ Businesses growing faster than ever</div>
          </div>
          <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-700">
            <div className="text-green-600 dark:text-green-400 font-semibold">✅ Actually enjoying weekends</div>
          </div>
        </div>
        
        <p className="text-xl font-bold text-gray-900 dark:text-gray-100 mt-6">
          Want to know their secret?
        </p>
      </div>

      {/* Method Steps */}
      <div>
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
          The Evening Freedom Formula
        </h2>
        <p className="text-lg text-gray-700 dark:text-gray-300 mb-8">
          Over 3 years, we've reverse-engineered exactly how successful business owners reclaim their time. It's not about working harder or hiring more staff.
        </p>

        <div className="grid md:grid-cols-3 gap-6">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 text-center hover:shadow-lg transition-shadow">
            <div className="w-12 h-12 bg-blue-600 text-white rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4">1</div>
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3">Find Your Time Thieves</h3>
            <p className="text-gray-600 dark:text-gray-400">5-minute exercise that reveals everything</p>
          </div>
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 text-center hover:shadow-lg transition-shadow">
            <div className="w-12 h-12 bg-blue-600 text-white rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4">2</div>
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3">Apply The 5-Minute Rule</h3>
            <p className="text-gray-600 dark:text-gray-400">The game-changing principle</p>
          </div>
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 text-center hover:shadow-lg transition-shadow">
            <div className="w-12 h-12 bg-blue-600 text-white rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4">3</div>
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3">Follow The Priority List</h3>
            <p className="text-gray-600 dark:text-gray-400">Maximum impact, minimum effort</p>
          </div>
        </div>
      </div>

      {/* Time Thieves */}
      <div>
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
          Step 1: Find Your Time Thieves
        </h2>
        <p className="text-lg text-gray-700 dark:text-gray-300 mb-8">
          Here's a shocking truth: <strong>80% of your evening work falls into just 3 categories.</strong>
        </p>

        <div className="grid md:grid-cols-3 gap-6 mb-12">
          <div className="bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800/30 rounded-2xl p-6 text-center">
            <div className="text-4xl mb-4">📅</div>
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3">The Booking Bandit</h3>
            <p className="text-gray-700 dark:text-gray-300 mb-4">Confirming, cancelling, rescheduling, reminding... The never-ending cycle.</p>
            <div className="bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300 px-3 py-1 rounded-full text-sm font-semibold">
              Steals: 2-3 hours daily
            </div>
          </div>
          <div className="bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800/30 rounded-2xl p-6 text-center">
            <div className="text-4xl mb-4">💬</div>
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3">The Question Repeater</h3>
            <p className="text-gray-700 dark:text-gray-300 mb-4">"What time are you open?" "Do you have availability?" Same questions, different day.</p>
            <div className="bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300 px-3 py-1 rounded-full text-sm font-semibold">
              Steals: 1-2 hours daily
            </div>
          </div>
          <div className="bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800/30 rounded-2xl p-6 text-center">
            <div className="text-4xl mb-4">📊</div>
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3">The Admin Monster</h3>
            <p className="text-gray-700 dark:text-gray-300 mb-4">Invoicing, payment chasing, scheduling, reporting... Death by a thousand cuts.</p>
            <div className="bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300 px-3 py-1 rounded-full text-sm font-semibold">
              Steals: 1-2 hours daily
            </div>
          </div>
        </div>

        {/* Detailed Time Audit Exercise */}
        <div className="bg-yellow-50 dark:bg-yellow-950/20 border border-yellow-200 dark:border-yellow-800/30 rounded-2xl p-8 mb-8">
          <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4">
            🕵️ The 5-Minute Time Audit (Do This Right Now)
          </h3>
          <p className="text-lg text-gray-700 dark:text-gray-300 mb-6">
            Grab your phone and set a timer for 5 minutes. Write down every task you did yesterday evening after 6pm that was business-related.
          </p>

          <div className="grid md:grid-cols-2 gap-6">
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
              <h4 className="text-lg font-bold text-gray-900 dark:text-gray-100 mb-3">✅ What to Track:</h4>
              <ul className="space-y-2 text-gray-700 dark:text-gray-300">
                <li>• Phone calls about bookings</li>
                <li>• Text messages to customers</li>
                <li>• Updating your calendar</li>
                <li>• Checking emails</li>
                <li>• Social media responses</li>
                <li>• Payment processing</li>
                <li>• Preparing for tomorrow</li>
              </ul>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
              <h4 className="text-lg font-bold text-gray-900 dark:text-gray-100 mb-3">📝 For Each Task, Note:</h4>
              <ul className="space-y-2 text-gray-700 dark:text-gray-300">
                <li>• What time it happened</li>
                <li>• How long it took</li>
                <li>• Could a computer do this?</li>
                <li>• How often does this happen?</li>
                <li>• What triggered it?</li>
              </ul>
            </div>
          </div>

          <div className="mt-6 p-4 bg-blue-100 dark:bg-blue-900/30 rounded-xl">
            <p className="text-blue-800 dark:text-blue-300 font-semibold">
              💡 Pro Tip: Most business owners are shocked to discover they spend 15-20 hours weekly on tasks that could be automated in under 30 minutes.
            </p>
          </div>
        </div>
      </div>

      {/* The 5-Minute Rule */}
      <div>
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
          Step 2: The 5-Minute Rule That Changes Everything
        </h2>
        <p className="text-lg text-gray-700 dark:text-gray-300 mb-8">
          This one rule has saved our clients over 10,000 hours collectively. It's deceptively simple but incredibly powerful.
        </p>

        <div className="bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-950/20 dark:to-red-950/20 rounded-3xl p-8 border border-orange-200/50 dark:border-orange-800/30 text-center mb-12">
          <h3 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4">The Golden Rule</h3>
          <div className="text-xl text-gray-700 dark:text-gray-300 mb-4">
            If a task takes less than 5 minutes but happens more than once per day...
          </div>
          <div className="text-4xl font-black text-orange-600 dark:text-orange-400 mb-6">
            AUTOMATE IT!
          </div>
          <p className="text-lg text-gray-600 dark:text-gray-400">
            Because 5 minutes × 365 days = 30+ hours per year on ONE task
          </p>
        </div>

        {/* Practical Implementation */}
        <div className="mb-12">
          <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6">
            How to Apply This Rule (Step-by-Step)
          </h3>

          <div className="space-y-6">
            <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700">
              <div className="flex items-start space-x-4">
                <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0 mt-1">1</div>
                <div>
                  <h4 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3">Identify Quick Repeaters</h4>
                  <p className="text-gray-700 dark:text-gray-300 mb-4">
                    From your time audit, circle every task that took under 5 minutes but you do multiple times daily.
                  </p>
                  <div className="bg-blue-50 dark:bg-blue-950/20 rounded-xl p-4">
                    <p className="text-blue-800 dark:text-blue-300 font-semibold">Common Examples:</p>
                    <ul className="text-blue-700 dark:text-blue-400 mt-2 space-y-1">
                      <li>• Sending appointment confirmations</li>
                      <li>• Answering "What time are you open?"</li>
                      <li>• Checking if you have availability</li>
                      <li>• Sending directions to your location</li>
                      <li>• Asking for reviews after service</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700">
              <div className="flex items-start space-x-4">
                <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0 mt-1">2</div>
                <div>
                  <h4 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3">Calculate the Hidden Cost</h4>
                  <p className="text-gray-700 dark:text-gray-300 mb-4">
                    Use this formula to see what each "quick task" actually costs you:
                  </p>
                  <div className="bg-gray-50 dark:bg-gray-900 rounded-xl p-4 font-mono text-center">
                    <div className="text-lg font-bold text-gray-900 dark:text-gray-100">
                      (Minutes per task × Daily frequency × 365 days) ÷ 60 = Hours per year
                    </div>
                  </div>
                  <div className="mt-4 p-4 bg-red-50 dark:bg-red-950/20 rounded-xl">
                    <p className="text-red-800 dark:text-red-300 font-semibold">
                      Example: Sending 3 confirmation texts daily (2 min each) = 36.5 hours yearly!
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700">
              <div className="flex items-start space-x-4">
                <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0 mt-1">3</div>
                <div>
                  <h4 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3">Choose Your Automation Method</h4>
                  <p className="text-gray-700 dark:text-gray-300 mb-4">
                    Different tasks need different solutions. Here's your decision tree:
                  </p>

                  <div className="grid md:grid-cols-2 gap-4">
                    <div className="bg-green-50 dark:bg-green-950/20 rounded-xl p-4 border border-green-200 dark:border-green-800/30">
                      <h5 className="font-bold text-green-800 dark:text-green-300 mb-2">📱 SMS/Email Automation</h5>
                      <p className="text-green-700 dark:text-green-400 text-sm mb-2">Best for:</p>
                      <ul className="text-green-600 dark:text-green-500 text-sm space-y-1">
                        <li>• Appointment confirmations</li>
                        <li>• Reminders</li>
                        <li>• Follow-ups</li>
                        <li>• Thank you messages</li>
                      </ul>
                    </div>

                    <div className="bg-purple-50 dark:bg-purple-950/20 rounded-xl p-4 border border-purple-200 dark:border-purple-800/30">
                      <h5 className="font-bold text-purple-800 dark:text-purple-300 mb-2">🤖 Chatbot/FAQ</h5>
                      <p className="text-purple-700 dark:text-purple-400 text-sm mb-2">Best for:</p>
                      <ul className="text-purple-600 dark:text-purple-500 text-sm space-y-1">
                        <li>• Opening hours questions</li>
                        <li>• Pricing inquiries</li>
                        <li>• Service information</li>
                        <li>• Booking availability</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Priority Implementation List */}
      <div>
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
          Step 3: The Priority Implementation List
        </h2>
        <p className="text-lg text-gray-700 dark:text-gray-300 mb-8">
          Don't try to automate everything at once. Follow this proven sequence for maximum impact with minimum effort.
        </p>

        <div className="space-y-6">
          <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950/20 dark:to-emerald-950/20 rounded-2xl p-6 border border-green-200 dark:border-green-800/30">
            <div className="flex items-center space-x-4 mb-4">
              <div className="w-12 h-12 bg-green-600 text-white rounded-full flex items-center justify-center text-xl font-bold">1</div>
              <div>
                <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100">Appointment Confirmations</h3>
                <div className="text-green-600 dark:text-green-400 font-semibold">Impact: 40% fewer no-shows instantly</div>
              </div>
            </div>
            <p className="text-gray-700 dark:text-gray-300 mb-4">
              <strong>Why first:</strong> Immediate revenue protection with zero effort required from you.
            </p>
            <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
              <h4 className="font-bold text-gray-900 dark:text-gray-100 mb-2">Implementation Steps:</h4>
              <ol className="text-gray-700 dark:text-gray-300 space-y-1">
                <li>1. Set up automated SMS 24 hours before appointment</li>
                <li>2. Include: Customer name, service, time, location</li>
                <li>3. Add simple reply options: "Reply Y to confirm, N to cancel"</li>
                <li>4. Auto-update your calendar based on responses</li>
              </ol>
            </div>
          </div>

          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 rounded-2xl p-6 border border-blue-200 dark:border-blue-800/30">
            <div className="flex items-center space-x-4 mb-4">
              <div className="w-12 h-12 bg-blue-600 text-white rounded-full flex items-center justify-center text-xl font-bold">2</div>
              <div>
                <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100">Online Booking System</h3>
                <div className="text-blue-600 dark:text-blue-400 font-semibold">Impact: 90% fewer booking phone calls</div>
              </div>
            </div>
            <p className="text-gray-700 dark:text-gray-300 mb-4">
              <strong>Why second:</strong> Customers love booking themselves 24/7, and you love not being interrupted.
            </p>
            <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
              <h4 className="font-bold text-gray-900 dark:text-gray-100 mb-2">Implementation Steps:</h4>
              <ol className="text-gray-700 dark:text-gray-300 space-y-1">
                <li>1. Choose a booking platform that syncs with your calendar</li>
                <li>2. Set your availability and service durations</li>
                <li>3. Add booking link to your website, social media, and email signature</li>
                <li>4. Train customers: "Book online for fastest service"</li>
              </ol>
            </div>
          </div>

          <div className="bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-950/20 dark:to-pink-950/20 rounded-2xl p-6 border border-purple-200 dark:border-purple-800/30">
            <div className="flex items-center space-x-4 mb-4">
              <div className="w-12 h-12 bg-purple-600 text-white rounded-full flex items-center justify-center text-xl font-bold">3</div>
              <div>
                <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100">Automated Follow-ups</h3>
                <div className="text-purple-600 dark:text-purple-400 font-semibold">Impact: 35% more repeat bookings</div>
              </div>
            </div>
            <p className="text-gray-700 dark:text-gray-300 mb-4">
              <strong>Why third:</strong> Turns one-time customers into loyal clients without you lifting a finger.
            </p>
            <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
              <h4 className="font-bold text-gray-900 dark:text-gray-100 mb-2">Implementation Steps:</h4>
              <ol className="text-gray-700 dark:text-gray-300 space-y-1">
                <li>1. Send thank you message 2 hours after service</li>
                <li>2. Request review 24 hours later (with direct links)</li>
                <li>3. Send rebooking reminder after 4-6 weeks</li>
                <li>4. Include special offers for loyal customers</li>
              </ol>
            </div>
          </div>
        </div>
      </div>

      {/* Real Implementation Timeline */}
      <div>
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
          Your 4-Week Implementation Timeline
        </h2>
        <p className="text-lg text-gray-700 dark:text-gray-300 mb-8">
          Follow this exact schedule to go from working until 9pm to being home by 6pm every night.
        </p>

        <div className="grid md:grid-cols-2 gap-6">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-10 h-10 bg-red-600 text-white rounded-full flex items-center justify-center font-bold">W1</div>
              <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100">Week 1: Detective Mode</h3>
            </div>
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center text-xs font-bold mt-1">1</div>
                <p className="text-gray-700 dark:text-gray-300">Complete the 5-minute time audit daily</p>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center text-xs font-bold mt-1">2</div>
                <p className="text-gray-700 dark:text-gray-300">Track every business task after 6pm</p>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center text-xs font-bold mt-1">3</div>
                <p className="text-gray-700 dark:text-gray-300">Calculate time costs using the formula</p>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center text-xs font-bold mt-1">4</div>
                <p className="text-gray-700 dark:text-gray-300">Identify your top 3 time thieves</p>
              </div>
            </div>
            <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-950/20 rounded-xl">
              <p className="text-blue-800 dark:text-blue-300 text-sm font-semibold">
                💡 Goal: Clear picture of where your time actually goes
              </p>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-10 h-10 bg-orange-600 text-white rounded-full flex items-center justify-center font-bold">W2</div>
              <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100">Week 2: First Victory</h3>
            </div>
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center text-xs font-bold mt-1">1</div>
                <p className="text-gray-700 dark:text-gray-300">Set up appointment confirmation system</p>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center text-xs font-bold mt-1">2</div>
                <p className="text-gray-700 dark:text-gray-300">Test with 5 upcoming appointments</p>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center text-xs font-bold mt-1">3</div>
                <p className="text-gray-700 dark:text-gray-300">Measure no-show rate improvement</p>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center text-xs font-bold mt-1">4</div>
                <p className="text-gray-700 dark:text-gray-300">Refine message timing and content</p>
              </div>
            </div>
            <div className="mt-4 p-3 bg-green-50 dark:bg-green-950/20 rounded-xl">
              <p className="text-green-800 dark:text-green-300 text-sm font-semibold">
                🎯 Goal: See immediate results within days
              </p>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-10 h-10 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold">W3</div>
              <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100">Week 3: Game Changer</h3>
            </div>
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center text-xs font-bold mt-1">1</div>
                <p className="text-gray-700 dark:text-gray-300">Launch online booking system</p>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center text-xs font-bold mt-1">2</div>
                <p className="text-gray-700 dark:text-gray-300">Add booking links everywhere</p>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center text-xs font-bold mt-1">3</div>
                <p className="text-gray-700 dark:text-gray-300">Train customers to book online</p>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center text-xs font-bold mt-1">4</div>
                <p className="text-gray-700 dark:text-gray-300">Track reduction in booking calls</p>
              </div>
            </div>
            <div className="mt-4 p-3 bg-purple-50 dark:bg-purple-950/20 rounded-xl">
              <p className="text-purple-800 dark:text-purple-300 text-sm font-semibold">
                🚀 Goal: 24/7 booking without your involvement
              </p>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-10 h-10 bg-green-600 text-white rounded-full flex items-center justify-center font-bold">W4</div>
              <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100">Week 4: Revenue Boost</h3>
            </div>
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center text-xs font-bold mt-1">1</div>
                <p className="text-gray-700 dark:text-gray-300">Implement follow-up sequences</p>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center text-xs font-bold mt-1">2</div>
                <p className="text-gray-700 dark:text-gray-300">Set up review request automation</p>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center text-xs font-bold mt-1">3</div>
                <p className="text-gray-700 dark:text-gray-300">Create rebooking reminder system</p>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center text-xs font-bold mt-1">4</div>
                <p className="text-gray-700 dark:text-gray-300">Measure repeat booking increase</p>
              </div>
            </div>
            <div className="mt-4 p-3 bg-yellow-50 dark:bg-yellow-950/20 rounded-xl">
              <p className="text-yellow-800 dark:text-yellow-300 text-sm font-semibold">
                💰 Goal: More revenue with less effort
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Final Results */}
      <div>
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
          What to Expect: Your New Reality
        </h2>

        <div className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-950/20 dark:to-emerald-950/20 rounded-3xl p-8 border border-green-200/50 dark:border-green-800/30 text-center mb-8">
          <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4">
            After 4 Weeks, You'll Have:
          </h3>

          <div className="grid md:grid-cols-3 gap-6 mb-6">
            <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-700">
              <div className="text-3xl font-bold text-green-600 dark:text-green-400 mb-2">15-20</div>
              <div className="text-gray-700 dark:text-gray-300">Hours saved weekly</div>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-700">
              <div className="text-3xl font-bold text-green-600 dark:text-green-400 mb-2">40%</div>
              <div className="text-gray-700 dark:text-gray-300">Fewer no-shows</div>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-700">
              <div className="text-3xl font-bold text-green-600 dark:text-green-400 mb-2">25%</div>
              <div className="text-gray-700 dark:text-gray-300">More repeat customers</div>
            </div>
          </div>

          <p className="text-xl font-bold text-gray-900 dark:text-gray-100">
            Most importantly: You'll be home by 6pm every night, present for your family.
          </p>
        </div>

        <div className="text-center">
          <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4">
            The Real Question Isn't "Will This Work?"
          </h3>
          <h3 className="text-2xl font-bold text-blue-600 dark:text-blue-400 mb-6">
            It's "How Much Longer Will You Keep Working Until 9pm?"
          </h3>
          <p className="text-lg text-gray-700 dark:text-gray-300">
            Every day you delay is another evening stolen from your family. The technology exists. The system works.
            Other business owners are already living this reality.
          </p>
        </div>
      </div>

    </div>
  )
}

export const stopWorkingLatePost: BlogPost = {
  id: '1',
  slug: 'stop-working-late-automation-guide',
  title: 'Stop Working Until 9pm: The UK Business Owner\'s Guide to Evening Freedom',
  excerpt: 'You started your business to have freedom, not to work longer hours than your employees. Here\'s how 500+ UK business owners reclaimed their evenings.',
  content: <BlogContent />,
  author: {
    name: 'Chedi Rach',
    role: 'Business Automation Specialist',
    avatar: '/logo/genlogic-icon-only.svg',
    bio: 'Chedi has helped over 300 UK businesses automate their operations and reclaim work-life balance.'
  },
  publishedAt: '2025-07-10',
  readingTime: '8 min read',
  category: 'Productivity',
  tags: ['automation', 'work-life-balance', 'productivity'],
  featured: true,
  image: '/blog-images/stop-working-late-automation-guide.webp',
  views: 2847,
  likes: 156
}
