'use client'

import { useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  ArrowLeft, 
  Clock, 
  CheckCircle,
  ArrowRight,
  Users,
  Target,
  Settings,
  TrendingUp,
  AlertTriangle,
  Lightbulb,
  Brain,
  Zap,
  Award,
  BarChart3,
  CheckSquare,
  UserCheck
} from 'lucide-react'
import Link from 'next/link'

const delegationSteps = [
  {
    step: "1",
    title: "Decision Tree Method",
    description: "Map every decision you make into teachable steps",
    color: "blue",
    details: [
      "Document your thought process",
      "Create if-then decision flows", 
      "Build repeatable frameworks",
      "Test with team members"
    ]
  },
  {
    step: "2", 
    title: "Quality Control Automation",
    description: "Systems that maintain standards without micromanaging",
    color: "green",
    details: [
      "Automated quality checkpoints",
      "Self-correcting processes",
      "Performance dashboards",
      "Feedback loops"
    ]
  },
  {
    step: "3",
    title: "Scaling Success Stories", 
    description: "Real examples of businesses that successfully scaled",
    color: "purple",
    details: [
      "Case study analysis",
      "Implementation roadmaps",
      "Common pitfall avoidance",
      "Measurable outcomes"
    ]
  }
]

export default function DelegationDilemmaClient() {
  // Mark lesson as completed when user visits
  useEffect(() => {
    const markAsCompleted = () => {
      const saved = localStorage.getItem('genlogic-academy-progress')
      const completed = saved ? JSON.parse(saved) : []
      if (!completed.includes('delegation-dilemma')) {
        completed.push('delegation-dilemma')
        localStorage.setItem('genlogic-academy-progress', JSON.stringify(completed))
        
        // Dispatch custom event to notify other components
        window.dispatchEvent(new CustomEvent('academyProgressUpdate'))
      }
    }
    
    // Mark as completed after user has been on page for 30 seconds
    const timer = setTimeout(markAsCompleted, 30000)
    return () => clearTimeout(timer)
  }, [])

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-green-50/30 to-background dark:from-background dark:via-green-950/30 dark:to-background">
      {/* Navigation */}
      <div className="pt-40 pb-8">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-5xl mx-auto">
            <Link 
              href="/learn" 
              className="inline-flex items-center gap-2 bg-background/80 backdrop-blur-sm border border-border/50 rounded-2xl px-4 py-2 text-primary-600 hover:text-primary-700 font-medium transition-all duration-300 hover:shadow-md"
            >
              <ArrowLeft className="w-4 h-4" />
              Back to Academy
            </Link>
          </div>
        </div>
      </div>

      {/* Hero Section */}
      <section className="pb-16">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-5xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              {/* Lesson Meta */}
              <div className="flex items-center justify-center gap-6 mb-8">
                <div className="flex items-center gap-2 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 px-4 py-2 rounded-2xl">
                  <Users className="w-4 h-4" />
                  <span className="font-medium text-sm">Team Building</span>
                </div>
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Clock className="w-4 h-4" />
                  <span className="text-sm">17 min read</span>
                </div>
                <div className="bg-muted px-3 py-1 rounded-full text-xs font-medium text-muted-foreground">
                  Business Systems
                </div>
              </div>

              {/* Main Title */}
              <h1 className="text-4xl md:text-6xl font-bold text-foreground mb-6 leading-tight">
                The Delegation
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-green-500 to-blue-500"> Dilemma:</span>
                <br />How to Clone
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-500 to-purple-500"> Yourself</span>
              </h1>

              <p className="text-xl md:text-2xl text-muted-foreground mb-8 leading-relaxed max-w-4xl mx-auto">
                There's a pattern emerging among UK business owners who successfully scaled beyond £500k. 
                <strong className="text-foreground"> While most delegation fails spectacularly, these businesses discovered the 3-step system that lets you clone your decision-making.</strong>
                Here's how to scale without losing quality or working yourself to death.
              </p>

              {/* Problem Stats */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
                <div className="bg-gradient-to-br from-red-50 to-red-100 dark:from-red-950/30 dark:to-red-900/30 rounded-3xl p-6 border border-red-200/50 dark:border-red-800/50">
                  <div className="flex items-center gap-3 mb-3">
                    <AlertTriangle className="w-6 h-6 text-red-500" />
                    <div className="text-2xl font-bold text-red-600 dark:text-red-400">78%</div>
                  </div>
                  <div className="text-sm text-red-700 dark:text-red-300 font-medium">Of delegation attempts fail within 90 days</div>
                </div>
                <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 dark:from-yellow-950/30 dark:to-yellow-900/30 rounded-3xl p-6 border border-yellow-200/50 dark:border-yellow-800/50">
                  <div className="flex items-center gap-3 mb-3">
                    <Settings className="w-6 h-6 text-yellow-500" />
                    <div className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">65%</div>
                  </div>
                  <div className="text-sm text-yellow-700 dark:text-yellow-300 font-medium">Of business owners still do everything themselves</div>
                </div>
                <div className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950/30 dark:to-green-900/30 rounded-3xl p-6 border border-green-200/50 dark:border-green-800/50">
                  <div className="flex items-center gap-3 mb-3">
                    <TrendingUp className="w-6 h-6 text-green-500" />
                    <div className="text-2xl font-bold text-green-600 dark:text-green-400">340%</div>
                  </div>
                  <div className="text-sm text-green-700 dark:text-green-300 font-medium">Growth rate with successful delegation</div>
                </div>
              </div>
            </motion.div>

            {/* What You'll Learn Card */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="bg-gradient-to-br from-green-50 to-blue-50 dark:from-green-950/50 dark:to-blue-950/50 rounded-3xl p-8 border border-green-200/50 dark:border-green-800/50 shadow-xl"
            >
              <div className="flex items-start gap-6">
                <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-blue-500 rounded-2xl flex items-center justify-center flex-shrink-0">
                  <Brain className="w-8 h-8 text-white" />
                </div>
                <div className="flex-1">
                  <h3 className="text-2xl font-bold text-foreground mb-4">What You'll Master in This Lesson</h3>
                  <div className="grid md:grid-cols-2 gap-4">
                    <div className="space-y-3">
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-foreground">Why most delegation fails (and the 3-step fix)</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-foreground">The decision tree method for training team members</span>
                      </div>
                    </div>
                    <div className="space-y-3">
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-foreground">How to automate quality control without micromanaging</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-foreground">Real examples of businesses that successfully scaled</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Main Content - The 3-Step System */}
      <section className="pb-16">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-4xl mx-auto space-y-12">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="bg-background/80 backdrop-blur-sm rounded-3xl p-8 border border-border/50 shadow-xl"
            >
              <div className="flex items-start gap-6 mb-8">
                <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-blue-500 rounded-xl flex items-center justify-center flex-shrink-0">
                  <Target className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h2 className="text-3xl font-bold text-foreground mb-4">The 3-Step Clone System</h2>
                  <p className="text-lg text-muted-foreground leading-relaxed">
                    Successful business owners who scaled beyond £500k all discovered this same pattern. Here's how to systematically clone your decision-making process.
                  </p>
                </div>
              </div>

              <div className="space-y-8">
                {delegationSteps.map((step, index) => (
                  <div key={index} className={`bg-gradient-to-r ${
                    step.color === 'blue' ? 'from-blue-50 to-blue-100 dark:from-blue-950/30 dark:to-blue-900/30' :
                    step.color === 'green' ? 'from-green-50 to-green-100 dark:from-green-950/30 dark:to-green-900/30' :
                    'from-purple-50 to-purple-100 dark:from-purple-950/30 dark:to-purple-900/30'
                  } rounded-2xl p-6 border ${
                    step.color === 'blue' ? 'border-blue-200/50 dark:border-blue-800/50' :
                    step.color === 'green' ? 'border-green-200/50 dark:border-green-800/50' :
                    'border-purple-200/50 dark:border-purple-800/50'
                  }`}>
                    <div className="flex items-start gap-4">
                      <div className={`w-12 h-12 rounded-xl flex items-center justify-center flex-shrink-0 ${
                        step.color === 'blue' ? 'bg-blue-500' :
                        step.color === 'green' ? 'bg-green-500' :
                        'bg-purple-500'
                      }`}>
                        <span className="text-white font-bold text-lg">{step.step}</span>
                      </div>
                      <div className="flex-1">
                        <h3 className={`text-xl font-bold mb-2 ${
                          step.color === 'blue' ? 'text-blue-700 dark:text-blue-300' :
                          step.color === 'green' ? 'text-green-700 dark:text-green-300' :
                          'text-purple-700 dark:text-purple-300'
                        }`}>
                          {step.title}
                        </h3>
                        <p className={`mb-4 ${
                          step.color === 'blue' ? 'text-blue-600 dark:text-blue-400' :
                          step.color === 'green' ? 'text-green-600 dark:text-green-400' :
                          'text-purple-600 dark:text-purple-400'
                        }`}>
                          {step.description}
                        </p>
                        <div className="grid md:grid-cols-2 gap-2">
                          {step.details.map((detail, detailIndex) => (
                            <div key={detailIndex} className="flex items-center gap-2">
                              <CheckSquare className={`w-4 h-4 ${
                                step.color === 'blue' ? 'text-blue-500' :
                                step.color === 'green' ? 'text-green-500' :
                                'text-purple-500'
                              }`} />
                              <span className="text-sm text-muted-foreground">{detail}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>

            

            {/* Real Case Study: The £500k Breakthrough */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="bg-background/80 backdrop-blur-sm rounded-3xl p-8 border border-border/50 shadow-xl"
            >
              <div className="flex items-start gap-6 mb-8">
                <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl flex items-center justify-center flex-shrink-0">
                  <Award className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h2 className="text-3xl font-bold text-foreground mb-4">Case Study: How Marcus Scaled from £200k to £1.2M</h2>
                  <p className="text-lg text-muted-foreground leading-relaxed">
                    Marcus owned a thriving plumbing business in Leeds, but he was trapped. Working 75 hours a week, doing everything himself, and burning out fast. Here's how he broke through the £500k barrier.
                  </p>
                </div>
              </div>

              <div className="bg-gradient-to-r from-red-50 to-red-100 dark:from-red-950/30 dark:to-red-900/30 rounded-2xl p-6 border border-red-200/50 dark:border-red-800/50 mb-8">
                <h3 className="text-xl font-bold text-red-700 dark:text-red-300 mb-4">The Delegation Disaster</h3>
                <div className="grid md:grid-cols-3 gap-4 mb-4">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-red-600 dark:text-red-400 mb-2">£200k</div>
                    <div className="text-sm text-red-700 dark:text-red-300">Revenue plateau</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-red-600 dark:text-red-400 mb-2">75hrs</div>
                    <div className="text-sm text-red-700 dark:text-red-300">Weekly work hours</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-red-600 dark:text-red-400 mb-2">4</div>
                    <div className="text-sm text-red-700 dark:text-red-300">Failed delegation attempts</div>
                  </div>
                </div>
                <p className="text-red-600 dark:text-red-400 text-sm">
                  "I tried delegating before, but every time I gave someone responsibility, they'd mess it up. I'd end up redoing their work,
                  which took longer than doing it myself. I was convinced I was the only one who could do things right."
                </p>
              </div>

              <div className="space-y-6">
                <div className="bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-950/30 dark:to-blue-900/30 rounded-2xl p-6 border border-blue-200/50 dark:border-blue-800/50">
                  <h4 className="text-lg font-bold text-blue-700 dark:text-blue-300 mb-4">Step 1: The Decision Tree Breakthrough</h4>
                  <p className="text-blue-600 dark:text-blue-400 mb-4">
                    <strong>The Problem:</strong> Marcus was making 200+ decisions per day, but never documented his thought process. When team members faced the same decisions, they guessed wrong.
                  </p>

                  <div className="bg-white dark:bg-gray-800 rounded-xl p-4 mb-4">
                    <h5 className="font-bold text-blue-700 dark:text-blue-300 mb-3">The Decision Tree Method</h5>
                    <div className="space-y-3 text-sm">
                      <div className="bg-blue-50 dark:bg-blue-900/30 rounded-lg p-3">
                        <strong className="text-blue-600">Example: Emergency Call Decision Tree</strong>
                        <div className="mt-2 space-y-1 text-xs">
                          <div>• <strong>If:</strong> Customer says "emergency" or "flooding" → Immediate dispatch (within 2 hours)</div>
                          <div>• <strong>If:</strong> Customer says "urgent" but no water damage → Same day appointment</div>
                          <div>• <strong>If:</strong> Routine repair → Next available slot (24-48 hours)</div>
                          <div>• <strong>If:</strong> Quote request → Schedule within 1 week</div>
                        </div>
                      </div>
                      <div className="bg-blue-50 dark:bg-blue-900/30 rounded-lg p-3">
                        <strong className="text-blue-600">Example: Pricing Decision Tree</strong>
                        <div className="mt-2 space-y-1 text-xs">
                          <div>• <strong>If:</strong> Standard repair (under 2 hours) → Use fixed price list</div>
                          <div>• <strong>If:</strong> Complex job (over 2 hours) → Hourly rate + materials</div>
                          <div>• <strong>If:</strong> Emergency call-out → Add 50% surcharge</div>
                          <div>• <strong>If:</strong> Repeat customer (5+ jobs) → Apply 10% loyalty discount</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-green-100 dark:bg-green-900/30 rounded-xl p-4">
                    <h6 className="font-bold text-green-700 dark:text-green-300 mb-2">Result After 30 Days:</h6>
                    <div className="grid md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <div className="text-green-600 dark:text-green-400">✓ 89% reduction in "Marcus, what should I do?" calls</div>
                        <div className="text-green-600 dark:text-green-400">✓ Team confidence increased dramatically</div>
                      </div>
                      <div>
                        <div className="text-green-600 dark:text-green-400">✓ Consistent customer experience</div>
                        <div className="text-green-600 dark:text-green-400">✓ Marcus freed up 15 hours per week</div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-green-50 to-green-100 dark:from-green-950/30 dark:to-green-900/30 rounded-2xl p-6 border border-green-200/50 dark:border-green-800/50">
                  <h4 className="text-lg font-bold text-green-700 dark:text-green-300 mb-4">Step 2: Quality Control Without Micromanaging</h4>
                  <p className="text-green-600 dark:text-green-400 mb-4">
                    <strong>The Problem:</strong> Marcus was either micromanaging (killing team morale) or completely hands-off (leading to quality issues). He needed a middle ground.
                  </p>

                  <div className="bg-white dark:bg-gray-800 rounded-xl p-4 mb-4">
                    <h5 className="font-bold text-green-700 dark:text-green-300 mb-3">The Automated Quality System</h5>
                    <div className="grid md:grid-cols-2 gap-4">
                      <div>
                        <h6 className="font-bold text-green-600 dark:text-green-400 mb-2">Self-Checking Processes</h6>
                        <ul className="space-y-1 text-sm">
                          <li>• Photo documentation required for every job</li>
                          <li>• Customer sign-off checklist</li>
                          <li>• Before/after photos automatically uploaded</li>
                          <li>• GPS tracking for arrival/departure times</li>
                        </ul>
                      </div>
                      <div>
                        <h6 className="font-bold text-green-600 dark:text-green-400 mb-2">Automated Alerts</h6>
                        <ul className="space-y-1 text-sm">
                          <li>• Job taking longer than estimated → Alert Marcus</li>
                          <li>• Customer complaint → Immediate escalation</li>
                          <li>• Materials cost over budget → Approval required</li>
                          <li>• Team member running late → Auto-notify customer</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <div className="bg-green-50 dark:bg-green-900/30 rounded-xl p-4 mb-4">
                    <h6 className="font-bold text-green-700 dark:text-green-300 mb-2">The Weekly Quality Review</h6>
                    <p className="text-sm text-green-600 dark:text-green-400 mb-3">
                      Instead of daily micromanaging, Marcus implemented a weekly 30-minute review with each team member:
                    </p>
                    <div className="space-y-2 text-sm">
                      <div>• <strong>Review:</strong> Customer feedback scores, photo quality, time management</div>
                      <div>• <strong>Celebrate:</strong> Wins, improvements, customer compliments</div>
                      <div>• <strong>Coach:</strong> Areas for improvement, new techniques, efficiency tips</div>
                      <div>• <strong>Plan:</strong> Next week's goals, training needs, career development</div>
                    </div>
                  </div>

                  <div className="bg-green-100 dark:bg-green-900/30 rounded-xl p-4">
                    <h6 className="font-bold text-green-700 dark:text-green-300 mb-2">Result After 60 Days:</h6>
                    <div className="grid md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <div className="text-green-600 dark:text-green-400">✓ Customer satisfaction: 4.2 → 4.8 stars</div>
                        <div className="text-green-600 dark:text-green-400">✓ Team autonomy increased 300%</div>
                      </div>
                      <div>
                        <div className="text-green-600 dark:text-green-400">✓ Quality issues dropped 78%</div>
                        <div className="text-green-600 dark:text-green-400">✓ Marcus stress level: Dramatically reduced</div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-purple-50 to-purple-100 dark:from-purple-950/30 dark:to-purple-900/30 rounded-2xl p-6 border border-purple-200/50 dark:border-purple-800/50">
                  <h4 className="text-lg font-bold text-purple-700 dark:text-purple-300 mb-4">Step 3: The Scaling Success Formula</h4>
                  <p className="text-purple-600 dark:text-purple-400 mb-4">
                    <strong>The Breakthrough:</strong> With systems in place, Marcus could finally scale. But he needed a formula for sustainable growth.
                  </p>

                  <div className="bg-white dark:bg-gray-800 rounded-xl p-4 mb-4">
                    <h5 className="font-bold text-purple-700 dark:text-purple-300 mb-3">The 3-Phase Scaling System</h5>
                    <div className="space-y-4">
                      <div className="bg-purple-50 dark:bg-purple-900/30 rounded-lg p-3">
                        <h6 className="font-bold text-purple-600 dark:text-purple-400 mb-2">Phase 1: Clone Yourself (Months 1-3)</h6>
                        <ul className="space-y-1 text-sm text-purple-600 dark:text-purple-400">
                          <li>• Document every process you do personally</li>
                          <li>• Create decision trees for all common scenarios</li>
                          <li>• Train one "mini-Marcus" who can handle 80% of decisions</li>
                          <li>• Test systems with low-risk situations first</li>
                        </ul>
                      </div>
                      <div className="bg-purple-50 dark:bg-purple-900/30 rounded-lg p-3">
                        <h6 className="font-bold text-purple-600 dark:text-purple-400 mb-2">Phase 2: Build the Machine (Months 4-6)</h6>
                        <ul className="space-y-1 text-sm text-purple-600 dark:text-purple-400">
                          <li>• Implement quality control automation</li>
                          <li>• Create team leader roles with clear responsibilities</li>
                          <li>• Develop training programs for new hires</li>
                          <li>• Build customer feedback loops</li>
                        </ul>
                      </div>
                      <div className="bg-purple-50 dark:bg-purple-900/30 rounded-lg p-3">
                        <h6 className="font-bold text-purple-600 dark:text-purple-400 mb-2">Phase 3: Scale the Empire (Months 7+)</h6>
                        <ul className="space-y-1 text-sm text-purple-600 dark:text-purple-400">
                          <li>• Hire and train multiple teams using proven systems</li>
                          <li>• Focus on business development and strategy</li>
                          <li>• Expand service offerings or geographic reach</li>
                          <li>• Consider franchising or acquisition opportunities</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <div className="bg-purple-100 dark:bg-purple-900/30 rounded-xl p-4">
                    <h6 className="font-bold text-purple-700 dark:text-purple-300 mb-2">Marcus's Results After 12 Months:</h6>
                    <div className="grid md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <div className="text-purple-600 dark:text-purple-400">✓ Revenue: £200k → £1.2M</div>
                        <div className="text-purple-600 dark:text-purple-400">✓ Team size: 3 → 12 people</div>
                        <div className="text-purple-600 dark:text-purple-400">✓ Working hours: 75 → 35 per week</div>
                      </div>
                      <div>
                        <div className="text-purple-600 dark:text-purple-400">✓ Profit margin: 15% → 28%</div>
                        <div className="text-purple-600 dark:text-purple-400">✓ Customer satisfaction: 4.8/5 stars</div>
                        <div className="text-purple-600 dark:text-purple-400">✓ Stress level: Minimal</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Your Delegation Assessment */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              className="bg-background/80 backdrop-blur-sm rounded-3xl p-8 border border-border/50 shadow-xl"
            >
              <div className="flex items-start gap-6 mb-8">
                <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl flex items-center justify-center flex-shrink-0">
                  <BarChart3 className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h2 className="text-3xl font-bold text-foreground mb-4">Delegation Readiness Assessment</h2>
                  <p className="text-lg text-muted-foreground leading-relaxed">
                    Use this framework to identify exactly where you are in the delegation journey and what to focus on next.
                  </p>
                </div>
              </div>

              <div className="bg-gradient-to-r from-orange-50 to-red-50 dark:from-orange-950/30 dark:to-red-950/30 rounded-2xl p-6 border border-orange-200/50 dark:border-orange-800/50">
                <h3 className="text-xl font-bold text-orange-700 dark:text-orange-300 mb-6">Rate Yourself (1-5 scale)</h3>

                <div className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
                      <h4 className="font-bold text-orange-600 dark:text-orange-400 mb-3">Process Documentation</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span>I have written procedures for key tasks</span>
                          <span className="font-bold">___/5</span>
                        </div>
                        <div className="flex justify-between">
                          <span>My team knows exactly what to do in common situations</span>
                          <span className="font-bold">___/5</span>
                        </div>
                        <div className="flex justify-between">
                          <span>I've documented my decision-making process</span>
                          <span className="font-bold">___/5</span>
                        </div>
                        <div className="flex justify-between border-t pt-2 mt-2">
                          <span className="font-bold">Process Score:</span>
                          <span className="font-bold">___/15</span>
                        </div>
                      </div>
                    </div>

                    <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
                      <h4 className="font-bold text-orange-600 dark:text-orange-400 mb-3">Quality Control</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span>I have systems to monitor quality without micromanaging</span>
                          <span className="font-bold">___/5</span>
                        </div>
                        <div className="flex justify-between">
                          <span>My team self-corrects mistakes quickly</span>
                          <span className="font-bold">___/5</span>
                        </div>
                        <div className="flex justify-between">
                          <span>I get alerts only when I need to intervene</span>
                          <span className="font-bold">___/5</span>
                        </div>
                        <div className="flex justify-between border-t pt-2 mt-2">
                          <span className="font-bold">Quality Score:</span>
                          <span className="font-bold">___/15</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
                      <h4 className="font-bold text-orange-600 dark:text-orange-400 mb-3">Team Development</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span>My team members can work independently</span>
                          <span className="font-bold">___/5</span>
                        </div>
                        <div className="flex justify-between">
                          <span>I have clear career progression paths</span>
                          <span className="font-bold">___/5</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Team members rarely need my approval for decisions</span>
                          <span className="font-bold">___/5</span>
                        </div>
                        <div className="flex justify-between border-t pt-2 mt-2">
                          <span className="font-bold">Team Score:</span>
                          <span className="font-bold">___/15</span>
                        </div>
                      </div>
                    </div>

                    <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
                      <h4 className="font-bold text-orange-600 dark:text-orange-400 mb-3">Personal Freedom</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span>I can take a week off without business suffering</span>
                          <span className="font-bold">___/5</span>
                        </div>
                        <div className="flex justify-between">
                          <span>I work ON the business, not IN it</span>
                          <span className="font-bold">___/5</span>
                        </div>
                        <div className="flex justify-between">
                          <span>I have time for strategic thinking and planning</span>
                          <span className="font-bold">___/5</span>
                        </div>
                        <div className="flex justify-between border-t pt-2 mt-2">
                          <span className="font-bold">Freedom Score:</span>
                          <span className="font-bold">___/15</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-red-100 to-green-100 dark:from-red-900/30 dark:to-green-900/30 rounded-xl p-4 text-center">
                    <h4 className="text-xl font-bold text-gray-700 dark:text-gray-300 mb-2">Total Delegation Score</h4>
                    <div className="text-3xl font-bold text-gray-600 dark:text-gray-400 mb-4">___/60</div>
                    <div className="grid md:grid-cols-3 gap-4 text-sm">
                      <div className="bg-red-100 dark:bg-red-900/30 rounded-lg p-3">
                        <div className="font-bold text-red-600">0-20: Crisis Mode</div>
                        <div className="text-red-600">You're doing everything yourself</div>
                      </div>
                      <div className="bg-yellow-100 dark:bg-yellow-900/30 rounded-lg p-3">
                        <div className="font-bold text-yellow-600">21-40: Building Phase</div>
                        <div className="text-yellow-600">Some delegation, needs improvement</div>
                      </div>
                      <div className="bg-green-100 dark:bg-green-900/30 rounded-lg p-3">
                        <div className="font-bold text-green-600">41-60: Scale Ready</div>
                        <div className="text-green-600">Ready for explosive growth</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Call to Action */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
              className="bg-gradient-to-r from-green-500 to-blue-500 rounded-3xl p-8 text-white text-center"
            >
              <h2 className="text-3xl font-bold mb-4">Something's Changing</h2>
              <p className="text-xl mb-6 opacity-90">
                Smart business owners are quietly discovering what successful companies already know about delegation. The shift is happening whether you participate or not.
              </p>
              <Link
                href="/demo"
                className="inline-flex items-center gap-2 bg-white text-green-600 px-8 py-4 rounded-2xl font-bold text-lg hover:bg-gray-100 transition-all duration-300 shadow-lg hover:shadow-xl"
              >
                See What They Know
                <ArrowRight className="w-5 h-5" />
              </Link>
              <p className="text-sm mt-4 opacity-75">
                Join the business owners who've already discovered the delegation revolution
              </p>
            </motion.div>

            {/* Next Lesson Navigation */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.7 }}
              className="bg-background rounded-3xl border border-border shadow-lg p-8"
            >
              <h3 className="text-xl font-bold text-foreground mb-6 text-center">Continue Your Learning Journey</h3>

              <div className="grid md:grid-cols-2 gap-6">
                <Link
                  href="/learn/fundamentals/revenue-leak-detector"
                  className="group bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/30 dark:to-blue-900/30 rounded-2xl p-6 border border-blue-200/50 dark:border-blue-800/50 hover:shadow-lg transition-all duration-300"
                >
                  <div className="flex items-center gap-4 mb-4">
                    <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/50 rounded-xl flex items-center justify-center">
                      <ArrowRight className="w-6 h-6 text-blue-600 group-hover:translate-x-1 transition-transform" />
                    </div>
                    <div>
                      <h4 className="font-bold text-foreground">Next Lesson</h4>
                      <p className="text-sm text-muted-foreground">Lesson 10 of 15</p>
                    </div>
                  </div>
                  <h5 className="font-semibold text-foreground mb-2">The Revenue Leak Detector</h5>
                  <p className="text-sm text-muted-foreground">
                    Finding hidden money and automating revenue recovery
                  </p>
                </Link>

                <Link
                  href="/learn"
                  className="group bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-950/30 dark:to-gray-900/30 rounded-2xl p-6 border border-gray-200/50 dark:border-gray-800/50 hover:shadow-lg transition-all duration-300"
                >
                  <div className="flex items-center gap-4 mb-4">
                    <div className="w-12 h-12 bg-gray-100 dark:bg-gray-900/50 rounded-xl flex items-center justify-center">
                      <ArrowLeft className="w-6 h-6 text-gray-600 group-hover:-translate-x-1 transition-transform" />
                    </div>
                    <div>
                      <h4 className="font-bold text-foreground">Academy</h4>
                      <p className="text-sm text-muted-foreground">All lessons</p>
                    </div>
                  </div>
                  <h5 className="font-semibold text-foreground mb-2">Back to Academy</h5>
                  <p className="text-sm text-muted-foreground">
                    View all lessons and track your progress
                  </p>
                </Link>
              </div>
            </motion.div>
          </div>
        </div>
      </section>
    </div>
  )
}
