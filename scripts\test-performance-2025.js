#!/usr/bin/env node

/**
 * Performance Testing Script for 2025 Core Web Vitals
 * Tests INP, LCP, CLS, and FCP against 2025 standards
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

// 2025 Performance Thresholds
const THRESHOLDS_2025 = {
  LCP: { good: 2500, needsImprovement: 4000 },
  INP: { good: 200, needsImprovement: 500 },
  CLS: { good: 0.1, needsImprovement: 0.25 },
  FCP: { good: 1800, needsImprovement: 3000 }
};

// Test URLs
const TEST_URLS = [
  'http://localhost:3000',
  'http://localhost:3000/services/business-automation',
  'http://localhost:3000/demo',
  'http://localhost:3000/contact',
  'http://localhost:3000/blog'
];

async function testPagePerformance(url) {
  const browser = await puppeteer.launch({
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });

  try {
    const page = await browser.newPage();
    
    // Set viewport for mobile-first testing
    await page.setViewport({ width: 375, height: 667 });
    
    // Enable performance monitoring
    await page.evaluateOnNewDocument(() => {
      window.performanceMetrics = {
        lcp: null,
        inp: null,
        cls: 0,
        fcp: null
      };

      // Monitor LCP
      if ('PerformanceObserver' in window) {
        try {
          const lcpObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            const lastEntry = entries[entries.length - 1];
            window.performanceMetrics.lcp = lastEntry.startTime;
          });
          lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
        } catch (e) {
          console.warn('LCP observer failed');
        }

        // Monitor INP (2025 standard)
        try {
          const inpObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            entries.forEach(entry => {
              if (entry.duration) {
                window.performanceMetrics.inp = Math.max(
                  window.performanceMetrics.inp || 0,
                  entry.duration
                );
              }
            });
          });
          inpObserver.observe({ entryTypes: ['event'] });
        } catch (e) {
          console.warn('INP observer failed');
        }

        // Monitor CLS
        try {
          let clsValue = 0;
          const clsObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            entries.forEach(entry => {
              if (!entry.hadRecentInput) {
                clsValue += entry.value;
              }
            });
            window.performanceMetrics.cls = clsValue;
          });
          clsObserver.observe({ entryTypes: ['layout-shift'] });
        } catch (e) {
          console.warn('CLS observer failed');
        }

        // Monitor FCP
        try {
          const fcpObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint');
            if (fcpEntry) {
              window.performanceMetrics.fcp = fcpEntry.startTime;
            }
          });
          fcpObserver.observe({ entryTypes: ['paint'] });
        } catch (e) {
          console.warn('FCP observer failed');
        }
      }
    });

    console.log(`\n🔍 Testing: ${url}`);
    
    // Navigate and wait for load
    await page.goto(url, { waitUntil: 'networkidle0', timeout: 30000 });
    
    // Simulate user interaction for INP
    await page.click('body');
    await page.keyboard.press('Tab');
    
    // Wait for metrics to stabilize
    await page.waitForTimeout(3000);
    
    // Get performance metrics
    const metrics = await page.evaluate(() => {
      return {
        ...window.performanceMetrics,
        // Get additional metrics from Performance API
        navigationTiming: performance.getEntriesByType('navigation')[0],
        paintTiming: performance.getEntriesByType('paint')
      };
    });

    return {
      url,
      metrics: {
        lcp: metrics.lcp,
        inp: metrics.inp,
        cls: metrics.cls,
        fcp: metrics.fcp,
        ttfb: metrics.navigationTiming?.responseStart - metrics.navigationTiming?.requestStart,
        domContentLoaded: metrics.navigationTiming?.domContentLoadedEventEnd - metrics.navigationTiming?.navigationStart
      }
    };

  } catch (error) {
    console.error(`❌ Error testing ${url}:`, error.message);
    return { url, error: error.message };
  } finally {
    await browser.close();
  }
}

function evaluateMetric(value, thresholds, metricName) {
  if (value === null || value === undefined) {
    return { score: 'unknown', status: '⚪' };
  }

  if (value <= thresholds.good) {
    return { score: 'good', status: '✅' };
  } else if (value <= thresholds.needsImprovement) {
    return { score: 'needs improvement', status: '⚠️' };
  } else {
    return { score: 'poor', status: '❌' };
  }
}

function generateReport(results) {
  console.log('\n📊 2025 Core Web Vitals Performance Report');
  console.log('=' .repeat(60));

  let totalScore = 0;
  let totalTests = 0;

  results.forEach(result => {
    if (result.error) {
      console.log(`\n❌ ${result.url}: ${result.error}`);
      return;
    }

    console.log(`\n🔗 ${result.url}`);
    console.log('-'.repeat(40));

    const metrics = result.metrics;
    
    // LCP
    const lcpEval = evaluateMetric(metrics.lcp, THRESHOLDS_2025.LCP);
    console.log(`${lcpEval.status} LCP: ${metrics.lcp ? Math.round(metrics.lcp) + 'ms' : 'N/A'} (${lcpEval.score})`);
    
    // INP (2025 focus)
    const inpEval = evaluateMetric(metrics.inp, THRESHOLDS_2025.INP);
    console.log(`${inpEval.status} INP: ${metrics.inp ? Math.round(metrics.inp) + 'ms' : 'N/A'} (${inpEval.score}) [2025 STANDARD]`);
    
    // CLS
    const clsEval = evaluateMetric(metrics.cls, THRESHOLDS_2025.CLS);
    console.log(`${clsEval.status} CLS: ${metrics.cls ? metrics.cls.toFixed(3) : 'N/A'} (${clsEval.score})`);
    
    // FCP
    const fcpEval = evaluateMetric(metrics.fcp, THRESHOLDS_2025.FCP);
    console.log(`${fcpEval.status} FCP: ${metrics.fcp ? Math.round(metrics.fcp) + 'ms' : 'N/A'} (${fcpEval.score})`);

    // Additional metrics
    if (metrics.ttfb) {
      console.log(`ℹ️  TTFB: ${Math.round(metrics.ttfb)}ms`);
    }
    if (metrics.domContentLoaded) {
      console.log(`ℹ️  DOM Ready: ${Math.round(metrics.domContentLoaded)}ms`);
    }

    // Calculate page score
    const scores = [lcpEval, inpEval, clsEval, fcpEval].filter(s => s.score !== 'unknown');
    const goodScores = scores.filter(s => s.score === 'good').length;
    const pageScore = scores.length > 0 ? (goodScores / scores.length) * 100 : 0;
    
    console.log(`📈 Page Score: ${Math.round(pageScore)}%`);
    
    totalScore += pageScore;
    totalTests++;
  });

  // Overall summary
  const overallScore = totalTests > 0 ? totalScore / totalTests : 0;
  console.log('\n🎯 Overall Performance Summary');
  console.log('=' .repeat(60));
  console.log(`📊 Average Score: ${Math.round(overallScore)}%`);
  
  if (overallScore >= 90) {
    console.log('🎉 Excellent! Your site meets 2025 performance standards.');
  } else if (overallScore >= 75) {
    console.log('👍 Good performance, but room for improvement.');
  } else if (overallScore >= 50) {
    console.log('⚠️  Performance needs improvement for 2025 standards.');
  } else {
    console.log('❌ Poor performance. Immediate optimization required.');
  }

  console.log('\n💡 2025 Performance Tips:');
  console.log('   • INP <200ms is critical for 2025 rankings');
  console.log('   • Focus on interaction responsiveness');
  console.log('   • Optimize for mobile-first indexing');
  console.log('   • Monitor real user metrics in production');
}

async function runPerformanceTests() {
  console.log('🚀 Starting 2025 Core Web Vitals Performance Tests');
  console.log('📱 Testing with mobile viewport (375x667)');
  console.log('⏱️  This may take a few minutes...\n');

  const results = [];
  
  for (const url of TEST_URLS) {
    try {
      const result = await testPagePerformance(url);
      results.push(result);
      
      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      console.error(`Failed to test ${url}:`, error);
      results.push({ url, error: error.message });
    }
  }

  generateReport(results);
  
  // Save results to file
  const reportPath = path.join(process.cwd(), 'performance-report-2025.json');
  fs.writeFileSync(reportPath, JSON.stringify(results, null, 2));
  console.log(`\n📄 Detailed report saved to: ${reportPath}`);
}

// Check if development server is running
async function checkServer() {
  try {
    const response = await fetch('http://localhost:3000');
    return response.ok;
  } catch (error) {
    return false;
  }
}

// Main execution
async function main() {
  const serverRunning = await checkServer();
  
  if (!serverRunning) {
    console.error('❌ Development server not running!');
    console.log('💡 Please start the server first: npm run dev');
    process.exit(1);
  }

  await runPerformanceTests();
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { runPerformanceTests, THRESHOLDS_2025 };
