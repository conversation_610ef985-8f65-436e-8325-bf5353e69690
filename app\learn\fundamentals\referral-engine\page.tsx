import { Metadata } from 'next'
import ReferralEngineClient from './ReferralEngineClient'

export const metadata: Metadata = {
  title: 'The Referral Engine: Turning Customers into Salespeople | GenLogic Academy',
  description: 'Why asking for referrals doesn\'t work and the automated system generating 40%+ of new business. Learn the psychology of referral timing and automated referral sequences.',
  keywords: 'referral marketing, customer referrals, referral automation, word of mouth marketing, UK business referrals, referral systems',
  openGraph: {
    title: 'The Referral Engine: Turning Customers into Salespeople',
    description: 'Why asking for referrals doesn\'t work and the automated system generating 40%+ of new business. Learn the psychology of referral timing and automated referral sequences.',
    type: 'article',
    url: 'https://genlogic.io/learn/fundamentals/referral-engine',
    images: [
      {
        url: '/academy-images/og-referral-engine.webp',
        width: 1200,
        height: 630,
        alt: 'The Referral Engine: Turning Customers into Salespeople',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'The Referral Engine: Turning Customers into Salespeople',
    description: 'Why asking for referrals doesn\'t work and the automated system generating 40%+ of new business. Learn the psychology of referral timing and automated referral sequences.',
    images: ['/academy-images/og-referral-engine.webp'],
  },
}

export default function ReferralEnginePage() {
  return <ReferralEngineClient />
}
