import { Metadata } from 'next'
import RetentionRevolutionClient from './RetentionRevolutionClient'

export const metadata: Metadata = {
  title: 'The Retention Revolution: Why Chasing New Customers is Killing Your Business | GenLogic Academy',
  description: 'Discover why acquiring new customers costs 5x more than retaining existing ones. Learn the 4-phase retention system that increased customer lifetime value by 340%.',
  keywords: 'customer retention, customer lifetime value, retention strategies, UK business growth, customer loyalty, retention automation',
  openGraph: {
    title: 'The Retention Revolution: Why Chasing New Customers is Killing Your Business',
    description: 'Discover why acquiring new customers costs 5x more than retaining existing ones. Learn the 4-phase retention system that increased customer lifetime value by 340%.',
    type: 'article',
    url: 'https://genlogic.io/learn/fundamentals/retention-revolution',
    images: [
      {
        url: '/academy-images/og-retention-revolution.webp',
        width: 1200,
        height: 630,
        alt: 'The Retention Revolution: Why Chasing New Customers is Killing Your Business',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'The Retention Revolution: Why Chasing New Customers is Killing Your Business',
    description: 'Discover why acquiring new customers costs 5x more than retaining existing ones. Learn the 4-phase retention system that increased customer lifetime value by 340%.',
    images: ['/academy-images/og-retention-revolution.webp'],
  },
}

export default function RetentionRevolutionPage() {
  return <RetentionRevolutionClient />
}
