import React from 'react'
import { BlogPost } from '../types'

const BlogContent = () => {
  return (
    <div className="max-w-4xl mx-auto space-y-12">
      
      {/* Opening Hook */}
      <div className="bg-gradient-to-br from-red-50 to-orange-50 dark:from-red-950/20 dark:to-orange-950/20 rounded-3xl p-8 border border-red-200/50 dark:border-red-800/30">
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6 text-center">
          "Can you pay your outstanding invoice please?"
        </h2>
        <p className="text-lg text-gray-700 dark:text-gray-300 mb-4 text-center">
          You've sent this message 47 times this month. To the same customers. Who keep promising to pay "next week."
        </p>
        <p className="text-lg text-gray-700 dark:text-gray-300 text-center font-medium">
          Meanwhile, your cash flow is strangling your business growth.
        </p>
      </div>

      {/* The Problem */}
      <div>
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
          The Payment Chasing Nightmare
        </h2>
        <p className="text-lg text-gray-700 dark:text-gray-300 mb-8">
          UK small businesses are owed £23.4 billion in late payments. That's not a statistic - that's your rent, your staff wages, your family's security sitting in someone else's bank account.
        </p>

        <div className="grid md:grid-cols-3 gap-6 mb-8">
          <div className="bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800/30 rounded-2xl p-6 text-center">
            <div className="text-4xl mb-4">💸</div>
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3">Average Late Payment</h3>
            <div className="text-3xl font-bold text-red-600 dark:text-red-400 mb-2">37 days</div>
            <p className="text-gray-700 dark:text-gray-300">Beyond agreed terms</p>
          </div>
          <div className="bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800/30 rounded-2xl p-6 text-center">
            <div className="text-4xl mb-4">⏰</div>
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3">Time Spent Chasing</h3>
            <div className="text-3xl font-bold text-red-600 dark:text-red-400 mb-2">8 hours</div>
            <p className="text-gray-700 dark:text-gray-300">Per week on average</p>
          </div>
          <div className="bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800/30 rounded-2xl p-6 text-center">
            <div className="text-4xl mb-4">📉</div>
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3">Business Impact</h3>
            <div className="text-3xl font-bold text-red-600 dark:text-red-400 mb-2">62%</div>
            <p className="text-gray-700 dark:text-gray-300">Report cash flow issues</p>
          </div>
        </div>
      </div>

      {/* The Hidden Costs */}
      <div>
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
          The Hidden Costs You're Not Calculating
        </h2>
        
        <div className="bg-yellow-50 dark:bg-yellow-950/20 border border-yellow-200 dark:border-yellow-800/30 rounded-2xl p-8 mb-8">
          <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4">
            💰 The Real Cost Calculator
          </h3>
          <p className="text-lg text-gray-700 dark:text-gray-300 mb-6">
            Use this formula to see what late payments actually cost your business:
          </p>
          
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700 mb-6">
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-bold text-gray-900 dark:text-gray-100 mb-3">Direct Costs:</h4>
                <ul className="space-y-2 text-gray-700 dark:text-gray-300">
                  <li>• Time spent chasing: £25/hour × 8 hours = <strong>£200/week</strong></li>
                  <li>• Bank overdraft fees: <strong>£25-50/month</strong></li>
                  <li>• Late payment charges you can't pass on</li>
                  <li>• Credit control software/services</li>
                </ul>
              </div>
              <div>
                <h4 className="font-bold text-gray-900 dark:text-gray-100 mb-3">Hidden Costs:</h4>
                <ul className="space-y-2 text-gray-700 dark:text-gray-300">
                  <li>• Stress and mental health impact</li>
                  <li>• Delayed business investments</li>
                  <li>• Missed growth opportunities</li>
                  <li>• Damaged supplier relationships</li>
                </ul>
              </div>
            </div>
          </div>
          
          <div className="bg-red-100 dark:bg-red-900/30 rounded-xl p-4 text-center">
            <p className="text-red-800 dark:text-red-300 font-bold text-lg">
              Annual Cost: £10,400+ in time alone, plus opportunity costs
            </p>
          </div>
        </div>
      </div>

      {/* The Solution */}
      <div>
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
          The Automated Payment System That Ends This Nightmare
        </h2>
        <p className="text-lg text-gray-700 dark:text-gray-300 mb-8">
          Here's the exact system that helped 200+ UK businesses eliminate payment chasing and improve cash flow by an average of 45%.
        </p>

        <div className="grid md:grid-cols-2 gap-6 mb-8">
          <div className="bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800/30 rounded-2xl p-6">
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-4">Before Automation</h3>
            <ul className="space-y-2">
              <li className="flex items-center text-gray-700 dark:text-gray-300">
                <span className="w-2 h-2 bg-red-500 rounded-full mr-3"></span>
                Manual invoice creation and sending
              </li>
              <li className="flex items-center text-gray-700 dark:text-gray-300">
                <span className="w-2 h-2 bg-red-500 rounded-full mr-3"></span>
                Chasing payments via phone/email
              </li>
              <li className="flex items-center text-gray-700 dark:text-gray-300">
                <span className="w-2 h-2 bg-red-500 rounded-full mr-3"></span>
                Average payment time: 45+ days
              </li>
              <li className="flex items-center text-gray-700 dark:text-gray-300">
                <span className="w-2 h-2 bg-red-500 rounded-full mr-3"></span>
                8+ hours weekly on payment admin
              </li>
            </ul>
          </div>
          
          <div className="bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800/30 rounded-2xl p-6">
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-4">After Automation</h3>
            <ul className="space-y-2">
              <li className="flex items-center text-gray-700 dark:text-gray-300">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                Automatic invoice generation and sending
              </li>
              <li className="flex items-center text-gray-700 dark:text-gray-300">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                Automated payment reminders
              </li>
              <li className="flex items-center text-gray-700 dark:text-gray-300">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                Average payment time: 12 days
              </li>
              <li className="flex items-center text-gray-700 dark:text-gray-300">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                30 minutes weekly oversight
              </li>
            </ul>
          </div>
        </div>
      </div>

      {/* Implementation Steps */}
      <div>
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
          Step-by-Step Implementation Guide
        </h2>

        <div className="space-y-6">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700">
            <div className="flex items-start space-x-4">
              <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0 mt-1">1</div>
              <div>
                <h4 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3">Set Up Automatic Invoicing</h4>
                <p className="text-gray-700 dark:text-gray-300 mb-4">
                  Connect your booking system to automatic invoice generation.
                </p>
                <div className="bg-blue-50 dark:bg-blue-950/20 rounded-xl p-4">
                  <h5 className="font-bold text-blue-800 dark:text-blue-300 mb-2">Implementation:</h5>
                  <ul className="text-blue-700 dark:text-blue-400 space-y-1">
                    <li>• Invoice sent immediately after service completion</li>
                    <li>• Include payment link for instant online payment</li>
                    <li>• Set payment terms: "Payment due within 7 days"</li>
                    <li>• Add late payment charges: "2% per month after due date"</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700">
            <div className="flex items-start space-x-4">
              <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0 mt-1">2</div>
              <div>
                <h4 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3">Create Payment Reminder Sequence</h4>
                <p className="text-gray-700 dark:text-gray-300 mb-4">
                  Automated reminders that get progressively firmer.
                </p>
                <div className="grid md:grid-cols-3 gap-4">
                  <div className="bg-green-50 dark:bg-green-950/20 rounded-xl p-4 border border-green-200 dark:border-green-800/30">
                    <h6 className="font-bold text-green-800 dark:text-green-300 mb-2">Day 3: Friendly</h6>
                    <p className="text-green-700 dark:text-green-400 text-sm">
                      "Just a friendly reminder that your invoice is due in 4 days. Pay online here: [link]"
                    </p>
                  </div>
                  <div className="bg-yellow-50 dark:bg-yellow-950/20 rounded-xl p-4 border border-yellow-200 dark:border-yellow-800/30">
                    <h6 className="font-bold text-yellow-800 dark:text-yellow-300 mb-2">Day 8: Firm</h6>
                    <p className="text-yellow-700 dark:text-yellow-400 text-sm">
                      "Your payment is now 1 day overdue. Please settle immediately to avoid late charges."
                    </p>
                  </div>
                  <div className="bg-red-50 dark:bg-red-950/20 rounded-xl p-4 border border-red-200 dark:border-red-800/30">
                    <h6 className="font-bold text-red-800 dark:text-red-300 mb-2">Day 15: Final</h6>
                    <p className="text-red-700 dark:text-red-400 text-sm">
                      "Final notice: Payment is 8 days overdue. Late charges now apply. Pay now or account will be suspended."
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700">
            <div className="flex items-start space-x-4">
              <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0 mt-1">3</div>
              <div>
                <h4 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3">Implement Payment Incentives</h4>
                <p className="text-gray-700 dark:text-gray-300 mb-4">
                  Reward early payment and penalize late payment.
                </p>
                <div className="bg-purple-50 dark:bg-purple-950/20 rounded-xl p-4">
                  <h5 className="font-bold text-purple-800 dark:text-purple-300 mb-2">Proven Incentive Structure:</h5>
                  <ul className="text-purple-700 dark:text-purple-400 space-y-1">
                    <li>• 2% discount for payment within 24 hours</li>
                    <li>• Standard terms: Payment due within 7 days</li>
                    <li>• 2% monthly charge for late payments</li>
                    <li>• Account suspension after 30 days overdue</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Results */}
      <div>
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
          Expected Results: What You'll Achieve
        </h2>
        
        <div className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-950/20 dark:to-emerald-950/20 rounded-3xl p-8 border border-green-200/50 dark:border-green-800/30 text-center">
          <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6">
            Within 30 Days, You'll See:
          </h3>
          
          <div className="grid md:grid-cols-3 gap-6">
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
              <div className="text-4xl font-bold text-green-600 dark:text-green-400 mb-2">45%</div>
              <div className="text-gray-700 dark:text-gray-300">Faster payments</div>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
              <div className="text-4xl font-bold text-green-600 dark:text-green-400 mb-2">8 hrs</div>
              <div className="text-gray-700 dark:text-gray-300">Weekly time saved</div>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
              <div className="text-4xl font-bold text-green-600 dark:text-green-400 mb-2">£10k+</div>
              <div className="text-gray-700 dark:text-gray-300">Annual cost savings</div>
            </div>
          </div>
        </div>
      </div>

    </div>
  )
}

export const paymentChasingPost: BlogPost = {
  id: '4',
  slug: 'payment-chasing-nightmare',
  title: 'End the Payment Chasing Nightmare: How UK Businesses Automated Their Way to 45% Faster Payments',
  excerpt: 'UK businesses are owed £23.4 billion in late payments. Here\'s the exact automated system that helped 200+ businesses eliminate payment chasing and improve cash flow by 45%.',
  content: <BlogContent />,
  author: {
    name: 'Chedi Rach',
    role: 'Business Automation Specialist',
    avatar: '/logo/genlogic-icon-only.svg',
    bio: 'Chedi has helped over 200 UK businesses automate their payment processes and eliminate cash flow issues.'
  },
  publishedAt: '2025-07-08',
  readingTime: '7 min read',
  category: 'Cash Flow',
  tags: ['payments', 'cash-flow', 'automation', 'invoicing'],
  featured: true,
  image: '/blog-images/payment-chasing-nightmare.webp',
  views: 1923,
  likes: 89
}
