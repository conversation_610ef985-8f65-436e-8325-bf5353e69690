/* ===== GENLOGIC LOGIN BRANDING FOR GOHIGHLEVEL ===== */
/* Using the EXACT structure from GHL Experts - WORKS PERFECTLY */

/* GenLogic Color Variables */
:root {
    --loginBtnColor: #a855f7;
    --loginBtnHover: #7c3aed;
    --bodyBgColor: #fff;
    --login-button-background-color: #a855f7;
    --login-button-background-hover-color: #7c3aed;
    --login-button-text-color: #ffffff;
}

/* GHL Login Page Styling - EXACT COPY from GHL Experts */
.hl_login {
    background-position: center center;
    background-size: cover;
    background-repeat: no-repeat;
    display: flex !important;
    flex-direction: column !important;
    justify-content: center !important;
    align-items: center !important;
    margin: 0 auto !important;
    padding: 0 !important;
    margin-top: -82px !important;
    height: 100vh;
}

.hl_login--header {
    background: #fff0 !important;
    border-bottom: 0 !important;
    padding: 0 !important;
    margin-bottom: 20px !important;
}

div.hl_login--body {
    max-width: 450px !important;
    width: 100% !important;
    background: var(--bodyBgColor) !important;
    border-radius: 10px !important;
    box-shadow: 0px 0px 80px rgba(0, 0, 0, 0.08);
}

.hl_login--body .card {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 auto !important;
    box-shadow: none !important;
    padding: 0 !important;
    margin: 0 !important;
}

.hl_login--body .card .card-body {
    max-width: 100% !important;
    width: 100% !important;
    margin: 0 auto;
    padding-top: 50px;
    padding-bottom: 50px;
}

/* GHL Form Controls - EXACT COPY */
.hl_login .form-control {
    background: #fff !important;
    font-size: .875rem;
    color: #2a3135;
    border: 1px solid #bdbdbd !important;
    border-radius: 5px !important;
    padding: 10px 10px !important;
}

.hl_login--body .login-card-heading {
    margin-bottom: 20px;
    text-align: center;
}

.hl_login--body .heading2 {
    margin-bottom: 10px;
    font-size: 25px;
}

.hl_login .button-holder.form-group {
    margin: 10px auto !important;
}

/* GHL Buttons - EXACT COPY */
.hl_login .btn.btn-blue,
.hl_login .btn.btn-blue:active,
.hl_login .btn.btn-blue:focus {
    background-color: var(--loginBtnColor);
    border-color: var(--loginBtnColor);
    color: #fff;
    transition: 0.3s ease-in;
}

.hl_login .btn.btn-blue:hover {
    background-color: var(--loginBtnHover);
    border-color: var(--loginBtnHover);
    color: #fff;
    transition: 0.3s ease-in;
}

.hl_login button {
  background-color: var(--login-button-background-color);
  color: var(--login-button-text-color);
  min-height: 55px;
}

.hl_login .hover\:bg-curious-blue-600:hover,
.hl_login button:hover {
  background-color: var(--login-button-background-hover-color) !important;
}

/* ===== END GENLOGIC LOGIN BRANDING ===== */
