'use client'

import { useEffect, useState } from 'react'
import { motion } from 'framer-motion'
import { 
  ArrowLeft, 
  Clock, 
  CheckCircle,
  ArrowRight,
  AlertTriangle,
  TrendingDown,
  TrendingUp,
  DollarSign,
  Target,
  Zap,
  BarChart3,
  Users,
  Calendar,
  CreditCard,
  Phone,
  Mail
} from 'lucide-react'
import Link from 'next/link'

const revenueLeaks = [
  {
    leak: "No-Show Appointments",
    lostRevenue: "£12,000/year",
    percentage: "23%",
    color: "red",
    icon: Calendar,
    solution: "Automated reminder sequences"
  },
  {
    leak: "Abandoned Bookings", 
    lostRevenue: "£8,500/year",
    percentage: "18%",
    color: "orange",
    icon: CreditCard,
    solution: "Recovery automation"
  },
  {
    leak: "Missed Follow-Ups",
    lostRevenue: "£15,200/year", 
    percentage: "31%",
    color: "yellow",
    icon: Phone,
    solution: "Systematic follow-up sequences"
  },
  {
    leak: "Pricing Inconsistencies",
    lostRevenue: "£6,800/year",
    percentage: "14%",
    color: "blue",
    icon: DollarSign,
    solution: "Automated pricing systems"
  },
  {
    leak: "Referral Opportunities",
    lostRevenue: "£9,300/year",
    percentage: "19%", 
    color: "purple",
    icon: Users,
    solution: "Automated referral requests"
  }
]

export default function RevenueLeakDetectorClient() {
  // Calculator state
  const [calculatorData, setCalculatorData] = useState({
    monthlyAppointments: '',
    noShowRate: '',
    avgAppointmentValue: '',
    monthlyVisitors: '',
    abandonmentRate: '',
    avgBookingValue: '',
    monthlyCustomers: '',
    nonReturnRate: '',
    avgLifetimeValue: '',
    avgNewCustomerValue: ''
  })

  // Calculate losses
  const noShowLoss = {
    monthly: (parseFloat(calculatorData.monthlyAppointments) || 0) *
             (parseFloat(calculatorData.noShowRate) || 0) / 100 *
             (parseFloat(calculatorData.avgAppointmentValue) || 0),
    get annual() { return this.monthly * 12 }
  }

  const abandonedBookingLoss = {
    monthly: (parseFloat(calculatorData.monthlyVisitors) || 0) *
             (parseFloat(calculatorData.abandonmentRate) || 0) / 100 *
             (parseFloat(calculatorData.avgBookingValue) || 0),
    get annual() { return this.monthly * 12 }
  }

  const followUpLoss = {
    monthly: (parseFloat(calculatorData.monthlyCustomers) || 0) *
             (parseFloat(calculatorData.nonReturnRate) || 0) / 100 *
             (parseFloat(calculatorData.avgLifetimeValue) || 0),
    get annual() { return this.monthly * 12 }
  }

  const referralLoss = {
    monthly: (parseFloat(calculatorData.monthlyCustomers) || 0) * 0.25 *
             (parseFloat(calculatorData.avgNewCustomerValue) || 0),
    get annual() { return this.monthly * 12 }
  }

  const totalLoss = {
    monthly: noShowLoss.monthly + abandonedBookingLoss.monthly + followUpLoss.monthly + referralLoss.monthly,
    annual: noShowLoss.annual + abandonedBookingLoss.annual + followUpLoss.annual + referralLoss.annual
  }

  const handleInputChange = (field: string, value: string) => {
    setCalculatorData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  // Mark lesson as completed when user visits
  useEffect(() => {
    const markAsCompleted = () => {
      const saved = localStorage.getItem('genlogic-academy-progress')
      const completed = saved ? JSON.parse(saved) : []
      if (!completed.includes('revenue-leak-detector')) {
        completed.push('revenue-leak-detector')
        localStorage.setItem('genlogic-academy-progress', JSON.stringify(completed))
        
        // Dispatch custom event to notify other components
        window.dispatchEvent(new CustomEvent('academyProgressUpdate'))
      }
    }
    
    // Mark as completed after user has been on page for 30 seconds
    const timer = setTimeout(markAsCompleted, 30000)
    return () => clearTimeout(timer)
  }, [])

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-red-50/30 to-background dark:from-background dark:via-red-950/30 dark:to-background">
      {/* Navigation */}
      <div className="pt-40 pb-8">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-5xl mx-auto">
            <Link 
              href="/learn" 
              className="inline-flex items-center gap-2 bg-background/80 backdrop-blur-sm border border-border/50 rounded-2xl px-4 py-2 text-primary-600 hover:text-primary-700 font-medium transition-all duration-300 hover:shadow-md"
            >
              <ArrowLeft className="w-4 h-4" />
              Back to Academy
            </Link>
          </div>
        </div>
      </div>

      {/* Hero Section */}
      <section className="pb-16">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-5xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              {/* Lesson Meta */}
              <div className="flex items-center justify-center gap-6 mb-8">
                <div className="flex items-center gap-2 bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 px-4 py-2 rounded-2xl">
                  <AlertTriangle className="w-4 h-4" />
                  <span className="font-medium text-sm">Revenue Recovery</span>
                </div>
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Clock className="w-4 h-4" />
                  <span className="text-sm">15 min read</span>
                </div>
                <div className="bg-muted px-3 py-1 rounded-full text-xs font-medium text-muted-foreground">
                  Business Systems
                </div>
              </div>

              {/* Main Title */}
              <h1 className="text-4xl md:text-6xl font-bold text-foreground mb-6 leading-tight">
                The Revenue Leak
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-red-500 to-orange-500"> Detector:</span>
                <br />Finding Hidden
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-green-500 to-blue-500"> Money</span>
              </h1>

              <p className="text-xl md:text-2xl text-muted-foreground mb-8 leading-relaxed max-w-4xl mx-auto">
                Every UK business is hemorrhaging money in ways they can't see. 
                <strong className="text-foreground"> While most owners focus on getting new customers, smart businesses discovered the 5 hidden leaks that cost them £47k per year.</strong>
                Here's how to plug every leak with automated revenue recovery systems.
              </p>

              {/* Shocking Stats */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
                <div className="bg-gradient-to-br from-red-50 to-red-100 dark:from-red-950/30 dark:to-red-900/30 rounded-3xl p-6 border border-red-200/50 dark:border-red-800/50">
                  <div className="flex items-center gap-3 mb-3">
                    <TrendingDown className="w-6 h-6 text-red-500" />
                    <div className="text-3xl font-bold text-red-600 dark:text-red-400">£47k</div>
                  </div>
                  <div className="text-sm text-red-700 dark:text-red-300 font-medium">Average annual revenue leak per business</div>
                </div>
                <div className="bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-950/30 dark:to-orange-900/30 rounded-3xl p-6 border border-orange-200/50 dark:border-orange-800/50">
                  <div className="flex items-center gap-3 mb-3">
                    <AlertTriangle className="w-6 h-6 text-orange-500" />
                    <div className="text-3xl font-bold text-orange-600 dark:text-orange-400">5</div>
                  </div>
                  <div className="text-sm text-orange-700 dark:text-orange-300 font-medium">Major leak points in every business</div>
                </div>
                <div className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950/30 dark:to-green-900/30 rounded-3xl p-6 border border-green-200/50 dark:border-green-800/50">
                  <div className="flex items-center gap-3 mb-3">
                    <TrendingUp className="w-6 h-6 text-green-500" />
                    <div className="text-3xl font-bold text-green-600 dark:text-green-400">89%</div>
                  </div>
                  <div className="text-sm text-green-700 dark:text-green-300 font-medium">Recovery rate with automated systems</div>
                </div>
              </div>
            </motion.div>

            {/* What You'll Learn Card */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="bg-gradient-to-br from-red-50 to-orange-50 dark:from-red-950/50 dark:to-orange-950/50 rounded-3xl p-8 border border-red-200/50 dark:border-red-800/50 shadow-xl"
            >
              <div className="flex items-start gap-6">
                <div className="w-16 h-16 bg-gradient-to-br from-red-500 to-orange-500 rounded-2xl flex items-center justify-center flex-shrink-0">
                  <Target className="w-8 h-8 text-white" />
                </div>
                <div className="flex-1">
                  <h3 className="text-2xl font-bold text-foreground mb-4">What You'll Master in This Lesson</h3>
                  <div className="grid md:grid-cols-2 gap-4">
                    <div className="space-y-3">
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-foreground">The 5 places money leaks from every business</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-foreground">How to automate revenue recovery systems</span>
                      </div>
                    </div>
                    <div className="space-y-3">
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-foreground">The "abandoned cart" psychology for service businesses</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-foreground">Case study: How one salon recovered £47k in lost revenue</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Main Content - The 5 Revenue Leaks */}
      <section className="pb-16">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-4xl mx-auto space-y-12">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="bg-background/80 backdrop-blur-sm rounded-3xl p-8 border border-border/50 shadow-xl"
            >
              <div className="flex items-start gap-6 mb-8">
                <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-orange-500 rounded-xl flex items-center justify-center flex-shrink-0">
                  <AlertTriangle className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h2 className="text-3xl font-bold text-foreground mb-4">The 5 Hidden Revenue Leaks</h2>
                  <p className="text-lg text-muted-foreground leading-relaxed">
                    Every business has these same 5 leak points. Here's how much they're costing you and how to plug them automatically.
                  </p>
                </div>
              </div>

              <div className="space-y-6">
                {revenueLeaks.map((leak, index) => (
                  <div key={index} className={`bg-gradient-to-r ${
                    leak.color === 'red' ? 'from-red-50 to-red-100 dark:from-red-950/30 dark:to-red-900/30' :
                    leak.color === 'orange' ? 'from-orange-50 to-orange-100 dark:from-orange-950/30 dark:to-orange-900/30' :
                    leak.color === 'yellow' ? 'from-yellow-50 to-yellow-100 dark:from-yellow-950/30 dark:to-yellow-900/30' :
                    leak.color === 'blue' ? 'from-blue-50 to-blue-100 dark:from-blue-950/30 dark:to-blue-900/30' :
                    'from-purple-50 to-purple-100 dark:from-purple-950/30 dark:to-purple-900/30'
                  } rounded-2xl p-6 border ${
                    leak.color === 'red' ? 'border-red-200/50 dark:border-red-800/50' :
                    leak.color === 'orange' ? 'border-orange-200/50 dark:border-orange-800/50' :
                    leak.color === 'yellow' ? 'border-yellow-200/50 dark:border-yellow-800/50' :
                    leak.color === 'blue' ? 'border-blue-200/50 dark:border-blue-800/50' :
                    'border-purple-200/50 dark:border-purple-800/50'
                  }`}>
                    <div className="flex items-start gap-4">
                      <div className={`w-12 h-12 rounded-xl flex items-center justify-center flex-shrink-0 ${
                        leak.color === 'red' ? 'bg-red-500' :
                        leak.color === 'orange' ? 'bg-orange-500' :
                        leak.color === 'yellow' ? 'bg-yellow-500' :
                        leak.color === 'blue' ? 'bg-blue-500' :
                        'bg-purple-500'
                      }`}>
                        <leak.icon className="w-6 h-6 text-white" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-2">
                          <h3 className={`text-xl font-bold ${
                            leak.color === 'red' ? 'text-red-700 dark:text-red-300' :
                            leak.color === 'orange' ? 'text-orange-700 dark:text-orange-300' :
                            leak.color === 'yellow' ? 'text-yellow-700 dark:text-yellow-300' :
                            leak.color === 'blue' ? 'text-blue-700 dark:text-blue-300' :
                            'text-purple-700 dark:text-purple-300'
                          }`}>
                            {leak.leak}
                          </h3>
                          <div className={`text-2xl font-bold ${
                            leak.color === 'red' ? 'text-red-600 dark:text-red-400' :
                            leak.color === 'orange' ? 'text-orange-600 dark:text-orange-400' :
                            leak.color === 'yellow' ? 'text-yellow-600 dark:text-yellow-400' :
                            leak.color === 'blue' ? 'text-blue-600 dark:text-blue-400' :
                            'text-purple-600 dark:text-purple-400'
                          }`}>
                            {leak.lostRevenue}
                          </div>
                        </div>
                        <div className="flex items-center justify-between">
                          <p className={`text-sm ${
                            leak.color === 'red' ? 'text-red-600 dark:text-red-400' :
                            leak.color === 'orange' ? 'text-orange-600 dark:text-orange-400' :
                            leak.color === 'yellow' ? 'text-yellow-600 dark:text-yellow-400' :
                            leak.color === 'blue' ? 'text-blue-600 dark:text-blue-400' :
                            'text-purple-600 dark:text-purple-400'
                          }`}>
                            Solution: {leak.solution}
                          </p>
                          <span className={`text-sm font-medium px-3 py-1 rounded-full ${
                            leak.color === 'red' ? 'bg-red-200 dark:bg-red-800 text-red-700 dark:text-red-300' :
                            leak.color === 'orange' ? 'bg-orange-200 dark:bg-orange-800 text-orange-700 dark:text-orange-300' :
                            leak.color === 'yellow' ? 'bg-yellow-200 dark:bg-yellow-800 text-yellow-700 dark:text-yellow-300' :
                            leak.color === 'blue' ? 'bg-blue-200 dark:bg-blue-800 text-blue-700 dark:text-blue-300' :
                            'bg-purple-200 dark:bg-purple-800 text-purple-700 dark:text-purple-300'
                          }`}>
                            {leak.percentage} of total loss
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>

            {/* Real Case Study */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="bg-background/80 backdrop-blur-sm rounded-3xl p-8 border border-border/50 shadow-xl"
            >
              <div className="flex items-start gap-6 mb-8">
                <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-blue-500 rounded-xl flex items-center justify-center flex-shrink-0">
                  <BarChart3 className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h2 className="text-3xl font-bold text-foreground mb-4">Case Study: How Bella's Beauty Salon Recovered £47,000</h2>
                  <p className="text-lg text-muted-foreground leading-relaxed">
                    Sarah, owner of Bella's Beauty Salon in Manchester, was working 70-hour weeks but barely breaking even. Here's how she discovered and plugged her revenue leaks.
                  </p>
                </div>
              </div>

              <div className="bg-gradient-to-r from-blue-50 to-green-50 dark:from-blue-950/30 dark:to-green-950/30 rounded-2xl p-6 border border-blue-200/50 dark:border-blue-800/50 mb-8">
                <h3 className="text-xl font-bold text-blue-700 dark:text-blue-300 mb-4">The Shocking Discovery</h3>
                <div className="grid md:grid-cols-3 gap-4">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-red-600 dark:text-red-400 mb-2">28%</div>
                    <div className="text-sm text-blue-600 dark:text-blue-400">No-show rate</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-orange-600 dark:text-orange-400 mb-2">£47k</div>
                    <div className="text-sm text-blue-600 dark:text-blue-400">Annual revenue loss</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-600 dark:text-green-400 mb-2">89%</div>
                    <div className="text-sm text-blue-600 dark:text-blue-400">Recovery rate achieved</div>
                  </div>
                </div>
              </div>

              <div className="space-y-6">
                <div className="bg-gradient-to-r from-red-50 to-red-100 dark:from-red-950/30 dark:to-red-900/30 rounded-2xl p-6 border border-red-200/50 dark:border-red-800/50">
                  <h4 className="text-lg font-bold text-red-700 dark:text-red-300 mb-3">Leak #1: No-Show Nightmare</h4>
                  <p className="text-red-600 dark:text-red-400 mb-4">
                    <strong>The Problem:</strong> 28% of appointments were no-shows. Sarah was losing £12,000 annually just from empty appointment slots.
                  </p>
                  <div className="bg-white dark:bg-gray-800 rounded-xl p-4 mb-4">
                    <h5 className="font-bold text-green-700 dark:text-green-300 mb-2">The Solution: 3-Touch Reminder System</h5>
                    <div className="space-y-2 text-sm">
                      <div><strong>Touch 1:</strong> SMS 24 hours before: "Hi [Name], looking forward to seeing you tomorrow at [Time] for your [Service]. Reply CONFIRM to secure your slot."</div>
                      <div><strong>Touch 2:</strong> Email 2 hours before with directions and what to expect</div>
                      <div><strong>Touch 3:</strong> SMS 30 minutes before: "See you in 30 minutes! We're excited to pamper you today."</div>
                    </div>
                  </div>
                  <div className="text-green-700 dark:text-green-300 font-bold">Result: No-show rate dropped to 4% - saving £10,200 annually</div>
                </div>

                <div className="bg-gradient-to-r from-orange-50 to-orange-100 dark:from-orange-950/30 dark:to-orange-900/30 rounded-2xl p-6 border border-orange-200/50 dark:border-orange-800/50">
                  <h4 className="text-lg font-bold text-orange-700 dark:text-orange-300 mb-3">Leak #2: Abandoned Booking Recovery</h4>
                  <p className="text-orange-600 dark:text-orange-400 mb-4">
                    <strong>The Problem:</strong> 35% of people who started booking online never completed it. £8,500 in lost bookings annually.
                  </p>
                  <div className="bg-white dark:bg-gray-800 rounded-xl p-4 mb-4">
                    <h5 className="font-bold text-green-700 dark:text-green-300 mb-2">The Solution: Abandoned Cart Recovery Sequence</h5>
                    <div className="space-y-2 text-sm">
                      <div><strong>1 hour later:</strong> "Did something come up? Your appointment slot is still available - complete your booking in 30 seconds."</div>
                      <div><strong>24 hours later:</strong> "We saved your preferred time slot! Here's a 10% discount to complete your booking today."</div>
                      <div><strong>3 days later:</strong> "Last chance! Your stylist Sarah has one slot left this week - book now or we'll release it."</div>
                    </div>
                  </div>
                  <div className="text-green-700 dark:text-green-300 font-bold">Result: 67% of abandoned bookings recovered - adding £5,700 annually</div>
                </div>

                <div className="bg-gradient-to-r from-yellow-50 to-yellow-100 dark:from-yellow-950/30 dark:to-yellow-900/30 rounded-2xl p-6 border border-yellow-200/50 dark:border-yellow-800/50">
                  <h4 className="text-lg font-bold text-yellow-700 dark:text-yellow-300 mb-3">Leak #3: The Follow-Up Black Hole</h4>
                  <p className="text-yellow-600 dark:text-yellow-400 mb-4">
                    <strong>The Problem:</strong> Zero systematic follow-up after appointments. Customers disappeared and never rebooked.
                  </p>
                  <div className="bg-white dark:bg-gray-800 rounded-xl p-4 mb-4">
                    <h5 className="font-bold text-green-700 dark:text-green-300 mb-2">The Solution: Post-Appointment Nurture Sequence</h5>
                    <div className="space-y-2 text-sm">
                      <div><strong>2 hours later:</strong> "How are you feeling? Here's how to maintain your new look + aftercare tips"</div>
                      <div><strong>1 week later:</strong> "Your hair should be settling beautifully now. Here's a quick styling video just for you"</div>
                      <div><strong>4 weeks later:</strong> "Time for a touch-up? Book your next appointment and get 15% off"</div>
                      <div><strong>8 weeks later:</strong> "We miss you! Your usual slot is available this week - shall we book you in?"</div>
                    </div>
                  </div>
                  <div className="text-green-700 dark:text-green-300 font-bold">Result: 78% rebooking rate vs 23% before - adding £15,200 annually</div>
                </div>

                <div className="bg-gradient-to-r from-purple-50 to-purple-100 dark:from-purple-950/30 dark:to-purple-900/30 rounded-2xl p-6 border border-purple-200/50 dark:border-purple-800/50">
                  <h4 className="text-lg font-bold text-purple-700 dark:text-purple-300 mb-3">Leak #4: The Referral Goldmine</h4>
                  <p className="text-purple-600 dark:text-purple-400 mb-4">
                    <strong>The Problem:</strong> Happy customers never referred anyone because Sarah never asked systematically.
                  </p>
                  <div className="bg-white dark:bg-gray-800 rounded-xl p-4 mb-4">
                    <h5 className="font-bold text-green-700 dark:text-green-300 mb-2">The Solution: Automated Referral Request System</h5>
                    <div className="space-y-2 text-sm">
                      <div><strong>After 3rd visit:</strong> "You're officially part of our VIP family! Know someone who'd love our service? Send them this link for 20% off their first visit"</div>
                      <div><strong>Birthday month:</strong> "Happy Birthday! Celebrate with a friend - bring someone new and you both get 25% off"</div>
                      <div><strong>After compliments:</strong> When customers praise their hair, automatic follow-up: "So glad you love it! Your friends would too - here's a special link to share"</div>
                    </div>
                  </div>
                  <div className="text-green-700 dark:text-green-300 font-bold">Result: 34% of customers now refer others - adding £9,300 annually</div>
                </div>
              </div>

              <div className="bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-950/30 dark:to-blue-950/30 rounded-2xl p-6 border border-green-200/50 dark:border-green-800/50 mt-8">
                <h4 className="text-xl font-bold text-green-700 dark:text-green-300 mb-4">Total Recovery: £40,400 in Year One</h4>
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h5 className="font-bold text-green-600 dark:text-green-400 mb-2">Revenue Recovered:</h5>
                    <ul className="space-y-1 text-sm text-green-600 dark:text-green-400">
                      <li>• No-show reduction: £10,200</li>
                      <li>• Abandoned bookings: £5,700</li>
                      <li>• Follow-up system: £15,200</li>
                      <li>• Referral program: £9,300</li>
                    </ul>
                  </div>
                  <div>
                    <h5 className="font-bold text-blue-600 dark:text-blue-400 mb-2">Business Transformation:</h5>
                    <ul className="space-y-1 text-sm text-blue-600 dark:text-blue-400">
                      <li>• Working hours: 70 → 45 per week</li>
                      <li>• Customer lifetime value: +156%</li>
                      <li>• Stress level: Dramatically reduced</li>
                      <li>• Team morale: Significantly improved</li>
                    </ul>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Your Revenue Leak Assessment */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              className="bg-background/80 backdrop-blur-sm rounded-3xl p-8 border border-border/50 shadow-xl"
            >
              <div className="flex items-start gap-6 mb-8">
                <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl flex items-center justify-center flex-shrink-0">
                  <Target className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h2 className="text-3xl font-bold text-foreground mb-4">Calculate Your Revenue Leaks</h2>
                  <p className="text-lg text-muted-foreground leading-relaxed">
                    Use this framework to identify exactly how much money is leaking from your business right now.
                  </p>
                </div>
              </div>

              <div className="bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-950/30 dark:to-pink-950/30 rounded-2xl p-6 border border-purple-200/50 dark:border-purple-800/50">
                <h3 className="text-xl font-bold text-purple-700 dark:text-purple-300 mb-6">Interactive Revenue Leak Calculator</h3>

                <div className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
                      <h4 className="font-bold text-red-600 dark:text-red-400 mb-3">No-Show Loss</h4>
                      <div className="space-y-3 text-sm">
                        <div className="flex items-center gap-2">
                          <label className="min-w-0 flex-1">Monthly appointments:</label>
                          <input
                            type="number"
                            value={calculatorData.monthlyAppointments}
                            onChange={(e) => handleInputChange('monthlyAppointments', e.target.value)}
                            className="w-20 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-foreground"
                            placeholder="100"
                          />
                        </div>
                        <div className="flex items-center gap-2">
                          <label className="min-w-0 flex-1">No-show rate (%):</label>
                          <input
                            type="number"
                            value={calculatorData.noShowRate}
                            onChange={(e) => handleInputChange('noShowRate', e.target.value)}
                            className="w-20 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-foreground"
                            placeholder="25"
                          />
                        </div>
                        <div className="flex items-center gap-2">
                          <label className="min-w-0 flex-1">Avg appointment value (£):</label>
                          <input
                            type="number"
                            value={calculatorData.avgAppointmentValue}
                            onChange={(e) => handleInputChange('avgAppointmentValue', e.target.value)}
                            className="w-20 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-foreground"
                            placeholder="80"
                          />
                        </div>
                        <div className="border-t pt-2 mt-2">
                          <div className="font-bold text-red-600">Monthly loss: {formatCurrency(noShowLoss.monthly)}</div>
                          <div className="font-bold text-red-600">Annual loss: {formatCurrency(noShowLoss.annual)}</div>
                        </div>
                      </div>
                    </div>

                    <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
                      <h4 className="font-bold text-orange-600 dark:text-orange-400 mb-3">Abandoned Booking Loss</h4>
                      <div className="space-y-3 text-sm">
                        <div className="flex items-center gap-2">
                          <label className="min-w-0 flex-1">Monthly website visitors:</label>
                          <input
                            type="number"
                            value={calculatorData.monthlyVisitors}
                            onChange={(e) => handleInputChange('monthlyVisitors', e.target.value)}
                            className="w-20 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-foreground"
                            placeholder="500"
                          />
                        </div>
                        <div className="flex items-center gap-2">
                          <label className="min-w-0 flex-1">Abandonment rate (%):</label>
                          <input
                            type="number"
                            value={calculatorData.abandonmentRate}
                            onChange={(e) => handleInputChange('abandonmentRate', e.target.value)}
                            className="w-20 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-foreground"
                            placeholder="35"
                          />
                        </div>
                        <div className="flex items-center gap-2">
                          <label className="min-w-0 flex-1">Avg booking value (£):</label>
                          <input
                            type="number"
                            value={calculatorData.avgBookingValue}
                            onChange={(e) => handleInputChange('avgBookingValue', e.target.value)}
                            className="w-20 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-foreground"
                            placeholder="120"
                          />
                        </div>
                        <div className="border-t pt-2 mt-2">
                          <div className="font-bold text-orange-600">Monthly loss: {formatCurrency(abandonedBookingLoss.monthly)}</div>
                          <div className="font-bold text-orange-600">Annual loss: {formatCurrency(abandonedBookingLoss.annual)}</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
                      <h4 className="font-bold text-yellow-600 dark:text-yellow-400 mb-3">Follow-Up Loss</h4>
                      <div className="space-y-3 text-sm">
                        <div className="flex items-center gap-2">
                          <label className="min-w-0 flex-1">Monthly customers:</label>
                          <input
                            type="number"
                            value={calculatorData.monthlyCustomers}
                            onChange={(e) => handleInputChange('monthlyCustomers', e.target.value)}
                            className="w-20 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-foreground"
                            placeholder="80"
                          />
                        </div>
                        <div className="flex items-center gap-2">
                          <label className="min-w-0 flex-1">Non-return rate (%):</label>
                          <input
                            type="number"
                            value={calculatorData.nonReturnRate}
                            onChange={(e) => handleInputChange('nonReturnRate', e.target.value)}
                            className="w-20 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-foreground"
                            placeholder="70"
                          />
                        </div>
                        <div className="flex items-center gap-2">
                          <label className="min-w-0 flex-1">Avg lifetime value (£):</label>
                          <input
                            type="number"
                            value={calculatorData.avgLifetimeValue}
                            onChange={(e) => handleInputChange('avgLifetimeValue', e.target.value)}
                            className="w-20 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-foreground"
                            placeholder="400"
                          />
                        </div>
                        <div className="border-t pt-2 mt-2">
                          <div className="font-bold text-yellow-600">Monthly loss: {formatCurrency(followUpLoss.monthly)}</div>
                          <div className="font-bold text-yellow-600">Annual loss: {formatCurrency(followUpLoss.annual)}</div>
                        </div>
                      </div>
                    </div>

                    <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
                      <h4 className="font-bold text-purple-600 dark:text-purple-400 mb-3">Referral Loss</h4>
                      <div className="space-y-3 text-sm">
                        <div className="flex items-center gap-2">
                          <label className="min-w-0 flex-1">Monthly customers:</label>
                          <input
                            type="number"
                            value={calculatorData.monthlyCustomers}
                            onChange={(e) => handleInputChange('monthlyCustomers', e.target.value)}
                            className="w-20 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-foreground"
                            placeholder="80"
                            disabled
                          />
                        </div>
                        <div className="flex items-center gap-2">
                          <label className="min-w-0 flex-1">Potential referral rate:</label>
                          <span className="w-20 px-2 py-1 text-center font-bold text-purple-600">25%</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <label className="min-w-0 flex-1">Avg new customer value (£):</label>
                          <input
                            type="number"
                            value={calculatorData.avgNewCustomerValue}
                            onChange={(e) => handleInputChange('avgNewCustomerValue', e.target.value)}
                            className="w-20 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-foreground"
                            placeholder="300"
                          />
                        </div>
                        <div className="border-t pt-2 mt-2">
                          <div className="font-bold text-purple-600">Monthly loss: {formatCurrency(referralLoss.monthly)}</div>
                          <div className="font-bold text-purple-600">Annual loss: {formatCurrency(referralLoss.annual)}</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-red-100 to-red-200 dark:from-red-900/30 dark:to-red-800/30 rounded-xl p-6 text-center border-2 border-red-300 dark:border-red-700">
                    <h4 className="text-2xl font-bold text-red-700 dark:text-red-300 mb-3">Total Annual Revenue Leak</h4>
                    <div className="text-5xl font-bold text-red-600 dark:text-red-400 mb-2">{formatCurrency(totalLoss.annual)}</div>
                    <div className="text-lg font-bold text-red-600 dark:text-red-400 mb-2">Monthly: {formatCurrency(totalLoss.monthly)}</div>
                    <p className="text-sm text-red-600 dark:text-red-400">This is money walking out your door every year</p>
                    {totalLoss.annual > 0 && (
                      <div className="mt-4 p-3 bg-red-200 dark:bg-red-800/50 rounded-lg">
                        <p className="text-sm font-bold text-red-700 dark:text-red-300">
                          💡 Recovery Potential: With our systems, you could recover 70-90% of this lost revenue!
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Implementation Roadmap */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
              className="bg-background/80 backdrop-blur-sm rounded-3xl p-8 border border-border/50 shadow-xl"
            >
              <div className="flex items-start gap-6 mb-8">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-green-500 rounded-xl flex items-center justify-center flex-shrink-0">
                  <CheckCircle className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h2 className="text-3xl font-bold text-foreground mb-4">Your 30-Day Revenue Recovery Plan</h2>
                  <p className="text-lg text-muted-foreground leading-relaxed">
                    Follow this step-by-step roadmap to plug your revenue leaks and start recovering lost money within 30 days.
                  </p>
                </div>
              </div>

              <div className="space-y-6">
                <div className="bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-950/30 dark:to-blue-900/30 rounded-2xl p-6 border border-blue-200/50 dark:border-blue-800/50">
                  <h3 className="text-xl font-bold text-blue-700 dark:text-blue-300 mb-4">Week 1: Stop the Bleeding (No-Shows)</h3>
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-bold text-blue-600 dark:text-blue-400 mb-2">Day 1-2: Set Up SMS Reminders</h4>
                      <ul className="space-y-1 text-sm text-blue-600 dark:text-blue-400">
                        <li>• Choose SMS platform (Twilio, TextMagic, etc.)</li>
                        <li>• Write reminder message templates</li>
                        <li>• Test with your own phone number</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-bold text-blue-600 dark:text-blue-400 mb-2">Day 3-7: Implement & Monitor</h4>
                      <ul className="space-y-1 text-sm text-blue-600 dark:text-blue-400">
                        <li>• Launch 24-hour reminder system</li>
                        <li>• Track no-show rates daily</li>
                        <li>• Adjust timing if needed</li>
                      </ul>
                    </div>
                  </div>
                  <div className="mt-4 p-3 bg-green-100 dark:bg-green-900/30 rounded-lg">
                    <p className="text-sm text-green-700 dark:text-green-300"><strong>Expected Result:</strong> 50-70% reduction in no-shows within first week</p>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-orange-50 to-orange-100 dark:from-orange-950/30 dark:to-orange-900/30 rounded-2xl p-6 border border-orange-200/50 dark:border-orange-800/50">
                  <h3 className="text-xl font-bold text-orange-700 dark:text-orange-300 mb-4">Week 2: Recover Abandoned Bookings</h3>
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-bold text-orange-600 dark:text-orange-400 mb-2">Day 8-10: Track Abandonment</h4>
                      <ul className="space-y-1 text-sm text-orange-600 dark:text-orange-400">
                        <li>• Install tracking on booking page</li>
                        <li>• Identify where people drop off</li>
                        <li>• Capture email/phone before payment</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-bold text-orange-600 dark:text-orange-400 mb-2">Day 11-14: Launch Recovery</h4>
                      <ul className="space-y-1 text-sm text-orange-600 dark:text-orange-400">
                        <li>• Set up automated email sequence</li>
                        <li>• Create urgency and incentives</li>
                        <li>• Test and optimize messages</li>
                      </ul>
                    </div>
                  </div>
                  <div className="mt-4 p-3 bg-green-100 dark:bg-green-900/30 rounded-lg">
                    <p className="text-sm text-green-700 dark:text-green-300"><strong>Expected Result:</strong> 40-60% of abandoned bookings recovered</p>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-green-50 to-green-100 dark:from-green-950/30 dark:to-green-900/30 rounded-2xl p-6 border border-green-200/50 dark:border-green-800/50">
                  <h3 className="text-xl font-bold text-green-700 dark:text-green-300 mb-4">Week 3-4: Build Follow-Up & Referral Systems</h3>
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-bold text-green-600 dark:text-green-400 mb-2">Follow-Up Sequence</h4>
                      <ul className="space-y-1 text-sm text-green-600 dark:text-green-400">
                        <li>• 2-hour post-service check-in</li>
                        <li>• 1-week care tips email</li>
                        <li>• 4-week rebooking reminder</li>
                        <li>• 8-week win-back campaign</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-bold text-green-600 dark:text-green-400 mb-2">Referral System</h4>
                      <ul className="space-y-1 text-sm text-green-600 dark:text-green-400">
                        <li>• Create referral tracking links</li>
                        <li>• Design reward structure</li>
                        <li>• Automate referral requests</li>
                        <li>• Track and reward referrers</li>
                      </ul>
                    </div>
                  </div>
                  <div className="mt-4 p-3 bg-green-100 dark:bg-green-900/30 rounded-lg">
                    <p className="text-sm text-green-700 dark:text-green-300"><strong>Expected Result:</strong> 200-300% increase in customer lifetime value</p>
                  </div>
                </div>
              </div>

              <div className="bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-950/30 dark:to-pink-950/30 rounded-2xl p-6 border border-purple-200/50 dark:border-purple-800/50 mt-8">
                <h3 className="text-xl font-bold text-purple-700 dark:text-purple-300 mb-4">30-Day Revenue Recovery Guarantee</h3>
                <div className="grid md:grid-cols-3 gap-4 text-center">
                  <div>
                    <div className="text-3xl font-bold text-purple-600 dark:text-purple-400 mb-2">Week 1</div>
                    <div className="text-sm text-purple-600 dark:text-purple-400">£2,000+ recovered from reduced no-shows</div>
                  </div>
                  <div>
                    <div className="text-3xl font-bold text-purple-600 dark:text-purple-400 mb-2">Week 2</div>
                    <div className="text-sm text-purple-600 dark:text-purple-400">£1,500+ from abandoned booking recovery</div>
                  </div>
                  <div>
                    <div className="text-3xl font-bold text-purple-600 dark:text-purple-400 mb-2">Week 4</div>
                    <div className="text-sm text-purple-600 dark:text-purple-400">£5,000+ monthly recurring from systems</div>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Call to Action */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.7 }}
              className="bg-gradient-to-r from-blue-500 to-purple-500 rounded-3xl p-8 text-white text-center"
            >
              <h2 className="text-3xl font-bold mb-4">Something's Changing</h2>
              <p className="text-xl mb-6 opacity-90">
                Smart business owners are quietly discovering what successful companies already know about revenue recovery. The shift is happening whether you participate or not.
              </p>
              <Link
                href="/demo"
                className="inline-flex items-center gap-2 bg-white text-blue-600 px-8 py-4 rounded-2xl font-bold text-lg hover:bg-gray-100 transition-all duration-300 shadow-lg hover:shadow-xl"
              >
                See What They Know
                <ArrowRight className="w-5 h-5" />
              </Link>
              <p className="text-sm mt-4 opacity-75">
                Join the business owners who've already discovered the revenue revolution
              </p>
            </motion.div>

            {/* Next Lesson Navigation */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.8 }}
              className="bg-background rounded-3xl border border-border shadow-lg p-8"
            >
              <h3 className="text-xl font-bold text-foreground mb-6 text-center">Continue Your Learning Journey</h3>

              <div className="grid md:grid-cols-2 gap-6">
                <Link
                  href="/learn/fundamentals/retention-revolution"
                  className="group bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950/30 dark:to-purple-900/30 rounded-2xl p-6 border border-purple-200/50 dark:border-purple-800/50 hover:shadow-lg transition-all duration-300"
                >
                  <div className="flex items-center gap-4 mb-4">
                    <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/50 rounded-xl flex items-center justify-center">
                      <ArrowRight className="w-6 h-6 text-purple-600 group-hover:translate-x-1 transition-transform" />
                    </div>
                    <div>
                      <h4 className="font-bold text-foreground">Next Lesson</h4>
                      <p className="text-sm text-muted-foreground">Lesson 11 of 15</p>
                    </div>
                  </div>
                  <h5 className="font-semibold text-foreground mb-2">The Retention Revolution</h5>
                  <p className="text-sm text-muted-foreground">
                    Why chasing new customers is killing your business and the retention system that works
                  </p>
                </Link>

                <Link
                  href="/learn"
                  className="group bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-950/30 dark:to-gray-900/30 rounded-2xl p-6 border border-gray-200/50 dark:border-gray-800/50 hover:shadow-lg transition-all duration-300"
                >
                  <div className="flex items-center gap-4 mb-4">
                    <div className="w-12 h-12 bg-gray-100 dark:bg-gray-900/50 rounded-xl flex items-center justify-center">
                      <ArrowLeft className="w-6 h-6 text-gray-600 group-hover:-translate-x-1 transition-transform" />
                    </div>
                    <div>
                      <h4 className="font-bold text-foreground">Academy</h4>
                      <p className="text-sm text-muted-foreground">All lessons</p>
                    </div>
                  </div>
                  <h5 className="font-semibold text-foreground mb-2">Back to Academy</h5>
                  <p className="text-sm text-muted-foreground">
                    View all lessons and track your progress
                  </p>
                </Link>
              </div>
            </motion.div>
          </div>
        </div>
      </section>
    </div>
  )
}
