'use client'

import { useEffect, useState } from 'react'
import { motion } from 'framer-motion'
import {
  ArrowLeft,
  Clock,
  CheckCircle,
  ArrowRight,
  Smartphone,
  Mail,
  MessageSquare,
  Phone,
  TrendingUp,
  TrendingDown,
  Zap,
  Target,
  Users,
  BarChart3,
  AlertTriangle,
  Lightbulb
} from 'lucide-react'
import Link from 'next/link'

const channelStats = [
  {
    channel: "Email",
    openRate: "22%",
    trend: "declining",
    color: "red",
    icon: Mail,
    issues: ["Spam filters", "Inbox overload", "Low engagement"]
  },
  {
    channel: "SMS",
    openRate: "98%",
    trend: "rising",
    color: "green", 
    icon: Smartphone,
    benefits: ["Instant delivery", "Personal device", "High urgency"]
  },
  {
    channel: "Social Media",
    openRate: "67%",
    trend: "growing",
    color: "blue",
    icon: MessageSquare,
    benefits: ["Visual content", "Viral potential", "Community building"]
  },
  {
    channel: "Voice/Video",
    openRate: "89%",
    trend: "emerging",
    color: "purple",
    icon: Phone,
    benefits: ["Personal connection", "Immediate response", "Trust building"]
  }
]

export default function MultiChannelRevolutionClient() {
  // Mark lesson as completed when user visits
  useEffect(() => {
    const markAsCompleted = () => {
      const saved = localStorage.getItem('genlogic-academy-progress')
      const completed = saved ? JSON.parse(saved) : []
      if (!completed.includes('multi-channel-revolution')) {
        completed.push('multi-channel-revolution')
        localStorage.setItem('genlogic-academy-progress', JSON.stringify(completed))
        
        // Dispatch custom event to notify other components
        window.dispatchEvent(new CustomEvent('academyProgressUpdate'))
      }
    }
    
    // Mark as completed after user has been on page for 30 seconds
    const timer = setTimeout(markAsCompleted, 30000)
    return () => clearTimeout(timer)
  }, [])

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-blue-50/30 to-background dark:from-background dark:via-blue-950/30 dark:to-background">
      {/* Navigation */}
      <div className="pt-40 pb-8">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-5xl mx-auto">
            <Link 
              href="/learn" 
              className="inline-flex items-center gap-2 bg-background/80 backdrop-blur-sm border border-border/50 rounded-2xl px-4 py-2 text-primary-600 hover:text-primary-700 font-medium transition-all duration-300 hover:shadow-md"
            >
              <ArrowLeft className="w-4 h-4" />
              Back to Academy
            </Link>
          </div>
        </div>
      </div>

      {/* Hero Section */}
      <section className="pb-16">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-5xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              {/* Lesson Meta */}
              <div className="flex items-center justify-center gap-6 mb-8">
                <div className="flex items-center gap-2 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 px-4 py-2 rounded-2xl">
                  <Smartphone className="w-4 h-4" />
                  <span className="font-medium text-sm">Multi-Channel</span>
                </div>
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Clock className="w-4 h-4" />
                  <span className="text-sm">13 min read</span>
                </div>
                <div className="bg-muted px-3 py-1 rounded-full text-xs font-medium text-muted-foreground">
                  Advanced Communication
                </div>
              </div>

              {/* Main Title */}
              <h1 className="text-4xl md:text-6xl font-bold text-foreground mb-6 leading-tight">
                Beyond Email:
                <br />The Multi-Channel
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-500 to-purple-500"> Revolution</span>
              </h1>

              <p className="text-xl md:text-2xl text-muted-foreground mb-8 leading-relaxed max-w-4xl mx-auto">
                Something's happening across UK businesses that's making email-only follow-up obsolete. 
                <strong className="text-foreground"> While most companies stick to email, smart businesses discovered the SMS strategy that gets 98% open rates.</strong>
                Here's how to orchestrate seamless multi-channel sequences that feel personal at scale.
              </p>

              {/* Channel Performance Stats */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-12">
                {channelStats.map((channel, index) => (
                  <div key={index} className={`bg-gradient-to-br ${
                    channel.color === 'red' ? 'from-red-50 to-red-100 dark:from-red-950/30 dark:to-red-900/30' :
                    channel.color === 'green' ? 'from-green-50 to-green-100 dark:from-green-950/30 dark:to-green-900/30' :
                    channel.color === 'blue' ? 'from-blue-50 to-blue-100 dark:from-blue-950/30 dark:to-blue-900/30' :
                    'from-purple-50 to-purple-100 dark:from-purple-950/30 dark:to-purple-900/30'
                  } rounded-3xl p-4 border ${
                    channel.color === 'red' ? 'border-red-200/50 dark:border-red-800/50' :
                    channel.color === 'green' ? 'border-green-200/50 dark:border-green-800/50' :
                    channel.color === 'blue' ? 'border-blue-200/50 dark:border-blue-800/50' :
                    'border-purple-200/50 dark:border-purple-800/50'
                  }`}>
                    <div className="flex items-center gap-2 mb-2">
                      <channel.icon className={`w-4 h-4 ${
                        channel.color === 'red' ? 'text-red-500' :
                        channel.color === 'green' ? 'text-green-500' :
                        channel.color === 'blue' ? 'text-blue-500' :
                        'text-purple-500'
                      }`} />
                      <span className={`text-xs font-medium ${
                        channel.color === 'red' ? 'text-red-700 dark:text-red-300' :
                        channel.color === 'green' ? 'text-green-700 dark:text-green-300' :
                        channel.color === 'blue' ? 'text-blue-700 dark:text-blue-300' :
                        'text-purple-700 dark:text-purple-300'
                      }`}>
                        {channel.channel}
                      </span>
                    </div>
                    <div className={`text-2xl font-bold mb-1 ${
                      channel.color === 'red' ? 'text-red-600 dark:text-red-400' :
                      channel.color === 'green' ? 'text-green-600 dark:text-green-400' :
                      channel.color === 'blue' ? 'text-blue-600 dark:text-blue-400' :
                      'text-purple-600 dark:text-purple-400'
                    }`}>
                      {channel.openRate}
                    </div>
                    <div className="flex items-center gap-1">
                      {channel.trend === 'declining' ? (
                        <TrendingDown className="w-3 h-3 text-red-500" />
                      ) : (
                        <TrendingUp className={`w-3 h-3 ${
                          channel.color === 'green' ? 'text-green-500' :
                          channel.color === 'blue' ? 'text-blue-500' :
                          'text-purple-500'
                        }`} />
                      )}
                      <span className={`text-xs ${
                        channel.color === 'red' ? 'text-red-600 dark:text-red-400' :
                        channel.color === 'green' ? 'text-green-600 dark:text-green-400' :
                        channel.color === 'blue' ? 'text-blue-600 dark:text-blue-400' :
                        'text-purple-600 dark:text-purple-400'
                      }`}>
                        {channel.trend}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>

            {/* What You'll Learn Card */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-950/50 dark:to-purple-950/50 rounded-3xl p-8 border border-blue-200/50 dark:border-blue-800/50 shadow-xl"
            >
              <div className="flex items-start gap-6">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-500 rounded-2xl flex items-center justify-center flex-shrink-0">
                  <Target className="w-8 h-8 text-white" />
                </div>
                <div className="flex-1">
                  <h3 className="text-2xl font-bold text-foreground mb-4">What You'll Master in This Lesson</h3>
                  <div className="grid md:grid-cols-2 gap-4">
                    <div className="space-y-3">
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-foreground">Why email-only follow-up is dying</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-foreground">The SMS strategy that gets 98% open rates</span>
                      </div>
                    </div>
                    <div className="space-y-3">
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-foreground">How to orchestrate seamless multi-channel sequences</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-foreground">Platform-specific psychology that converts</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Main Content - The Death of Email-Only */}
      <section className="pb-16">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-4xl mx-auto space-y-12">

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="bg-background/80 backdrop-blur-sm rounded-3xl p-8 border border-border/50 shadow-xl"
            >
              <div className="flex items-start gap-6 mb-8">
                <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-orange-500 rounded-xl flex items-center justify-center flex-shrink-0">
                  <AlertTriangle className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h2 className="text-3xl font-bold text-foreground mb-4">The Death of Email-Only Follow-Up</h2>
                  <p className="text-lg text-muted-foreground leading-relaxed">
                    Something shocking is happening across UK businesses. While most companies still rely on email-only follow-up, smart businesses discovered that the game has completely changed.
                  </p>
                </div>
              </div>

              <div className="bg-gradient-to-r from-red-50 to-red-100 dark:from-red-950/30 dark:to-red-900/30 rounded-2xl p-6 border border-red-200/50 dark:border-red-800/50 mb-8">
                <h3 className="text-xl font-bold text-red-700 dark:text-red-300 mb-4">The Brutal Reality of Email in 2024</h3>
                <div className="grid md:grid-cols-3 gap-4">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-red-600 dark:text-red-400 mb-2">22%</div>
                    <div className="text-sm text-red-700 dark:text-red-300">Average email open rate</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-red-600 dark:text-red-400 mb-2">3.2%</div>
                    <div className="text-sm text-red-700 dark:text-red-300">Average click-through rate</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-red-600 dark:text-red-400 mb-2">67%</div>
                    <div className="text-sm text-red-700 dark:text-red-300">End up in spam/promotions</div>
                  </div>
                </div>
              </div>

              <div className="space-y-6">
                <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
                  <h4 className="text-lg font-bold text-red-600 dark:text-red-400 mb-3">Why Email-Only Follow-Up is Failing</h4>
                  <div className="space-y-3">
                    <div className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                      <div>
                        <strong className="text-red-600 dark:text-red-400">Inbox Overload:</strong>
                        <span className="text-muted-foreground"> Average person receives 121 emails per day. Your message gets lost in the noise.</span>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                      <div>
                        <strong className="text-red-600 dark:text-red-400">Spam Filter Evolution:</strong>
                        <span className="text-muted-foreground"> AI-powered filters are getting smarter. Even legitimate business emails get blocked.</span>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                      <div>
                        <strong className="text-red-600 dark:text-red-400">Generational Shift:</strong>
                        <span className="text-muted-foreground"> Younger customers check email once a day (if at all). They live on their phones.</span>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                      <div>
                        <strong className="text-red-600 dark:text-red-400">Delayed Response:</strong>
                        <span className="text-muted-foreground"> Email feels formal and slow. By the time they see it, they've moved on.</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* The Multi-Channel Revolution */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="bg-background/80 backdrop-blur-sm rounded-3xl p-8 border border-border/50 shadow-xl"
            >
              <div className="flex items-start gap-6 mb-8">
                <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-blue-500 rounded-xl flex items-center justify-center flex-shrink-0">
                  <TrendingUp className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h2 className="text-3xl font-bold text-foreground mb-4">The Multi-Channel Revolution</h2>
                  <p className="text-lg text-muted-foreground leading-relaxed">
                    Smart businesses discovered that the secret isn't better emails—it's using the right channel for the right message at the right time.
                  </p>
                </div>
              </div>

              <div className="space-y-8">
                {/* SMS Channel */}
                <div className="bg-gradient-to-r from-green-50 to-green-100 dark:from-green-950/30 dark:to-green-900/30 rounded-2xl p-6 border border-green-200/50 dark:border-green-800/50">
                  <div className="flex items-start gap-4 mb-6">
                    <div className="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center flex-shrink-0">
                      <Smartphone className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-green-700 dark:text-green-300 mb-2">SMS: The 98% Open Rate Champion</h3>
                      <p className="text-green-600 dark:text-green-400">The most powerful channel for urgent, time-sensitive communication</p>
                    </div>
                  </div>

                  <div className="grid md:grid-cols-2 gap-6 mb-6">
                    <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
                      <h4 className="font-bold text-green-600 dark:text-green-400 mb-3">Why SMS Dominates</h4>
                      <ul className="space-y-2 text-sm">
                        <li className="flex items-center gap-2">
                          <CheckCircle className="w-4 h-4 text-green-500" />
                          <span>98% open rate vs 22% for email</span>
                        </li>
                        <li className="flex items-center gap-2">
                          <CheckCircle className="w-4 h-4 text-green-500" />
                          <span>90% read within 3 minutes</span>
                        </li>
                        <li className="flex items-center gap-2">
                          <CheckCircle className="w-4 h-4 text-green-500" />
                          <span>Personal device = immediate attention</span>
                        </li>
                        <li className="flex items-center gap-2">
                          <CheckCircle className="w-4 h-4 text-green-500" />
                          <span>No spam filters to bypass</span>
                        </li>
                      </ul>
                    </div>
                    <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
                      <h4 className="font-bold text-green-600 dark:text-green-400 mb-3">Best Use Cases</h4>
                      <ul className="space-y-2 text-sm">
                        <li className="flex items-center gap-2">
                          <Target className="w-4 h-4 text-green-500" />
                          <span>Appointment reminders</span>
                        </li>
                        <li className="flex items-center gap-2">
                          <Target className="w-4 h-4 text-green-500" />
                          <span>Urgent notifications</span>
                        </li>
                        <li className="flex items-center gap-2">
                          <Target className="w-4 h-4 text-green-500" />
                          <span>Time-sensitive offers</span>
                        </li>
                        <li className="flex items-center gap-2">
                          <Target className="w-4 h-4 text-green-500" />
                          <span>Confirmation messages</span>
                        </li>
                      </ul>
                    </div>
                  </div>

                  <div className="bg-green-100 dark:bg-green-900/30 rounded-xl p-4">
                    <h5 className="font-bold text-green-700 dark:text-green-300 mb-2">SMS Psychology: Why It Works</h5>
                    <p className="text-sm text-green-600 dark:text-green-400">
                      SMS triggers the same psychological response as a personal message from a friend. It feels immediate, important, and personal.
                      The brain processes SMS as "urgent communication" while email is processed as "work to deal with later."
                    </p>
                  </div>
                </div>

                {/* Social Media Channel */}
                <div className="bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-950/30 dark:to-blue-900/30 rounded-2xl p-6 border border-blue-200/50 dark:border-blue-800/50">
                  <div className="flex items-start gap-4 mb-6">
                    <div className="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center flex-shrink-0">
                      <MessageSquare className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-blue-700 dark:text-blue-300 mb-2">Social Media: The Relationship Builder</h3>
                      <p className="text-blue-600 dark:text-blue-400">Where customers spend 2.5 hours daily - perfect for building relationships</p>
                    </div>
                  </div>

                  <div className="grid md:grid-cols-2 gap-6 mb-6">
                    <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
                      <h4 className="font-bold text-blue-600 dark:text-blue-400 mb-3">Platform-Specific Strategies</h4>
                      <div className="space-y-3 text-sm">
                        <div>
                          <strong className="text-blue-600">Facebook Messenger:</strong>
                          <span className="text-muted-foreground"> 67% open rate, perfect for detailed conversations</span>
                        </div>
                        <div>
                          <strong className="text-blue-600">Instagram DM:</strong>
                          <span className="text-muted-foreground"> Visual-first, great for younger demographics</span>
                        </div>
                        <div>
                          <strong className="text-blue-600">WhatsApp Business:</strong>
                          <span className="text-muted-foreground"> 89% open rate, feels most personal</span>
                        </div>
                        <div>
                          <strong className="text-blue-600">LinkedIn:</strong>
                          <span className="text-muted-foreground"> B2B gold mine, professional context</span>
                        </div>
                      </div>
                    </div>
                    <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
                      <h4 className="font-bold text-blue-600 dark:text-blue-400 mb-3">Content That Converts</h4>
                      <ul className="space-y-2 text-sm">
                        <li className="flex items-center gap-2">
                          <Users className="w-4 h-4 text-blue-500" />
                          <span>Behind-the-scenes content</span>
                        </li>
                        <li className="flex items-center gap-2">
                          <Users className="w-4 h-4 text-blue-500" />
                          <span>Customer success stories</span>
                        </li>
                        <li className="flex items-center gap-2">
                          <Users className="w-4 h-4 text-blue-500" />
                          <span>Educational tips and tricks</span>
                        </li>
                        <li className="flex items-center gap-2">
                          <Users className="w-4 h-4 text-blue-500" />
                          <span>Interactive polls and questions</span>
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>

                {/* Voice/Video Channel */}
                <div className="bg-gradient-to-r from-purple-50 to-purple-100 dark:from-purple-950/30 dark:to-purple-900/30 rounded-2xl p-6 border border-purple-200/50 dark:border-purple-800/50">
                  <div className="flex items-start gap-4 mb-6">
                    <div className="w-12 h-12 bg-purple-500 rounded-xl flex items-center justify-center flex-shrink-0">
                      <Phone className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-purple-700 dark:text-purple-300 mb-2">Voice/Video: The Trust Accelerator</h3>
                      <p className="text-purple-600 dark:text-purple-400">89% response rate when used strategically for high-value interactions</p>
                    </div>
                  </div>

                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
                      <h4 className="font-bold text-purple-600 dark:text-purple-400 mb-3">When to Use Voice/Video</h4>
                      <ul className="space-y-2 text-sm">
                        <li className="flex items-center gap-2">
                          <Phone className="w-4 h-4 text-purple-500" />
                          <span>High-value prospect follow-up</span>
                        </li>
                        <li className="flex items-center gap-2">
                          <Phone className="w-4 h-4 text-purple-500" />
                          <span>Complaint resolution</span>
                        </li>
                        <li className="flex items-center gap-2">
                          <Phone className="w-4 h-4 text-purple-500" />
                          <span>Complex service explanations</span>
                        </li>
                        <li className="flex items-center gap-2">
                          <Phone className="w-4 h-4 text-purple-500" />
                          <span>Relationship building calls</span>
                        </li>
                      </ul>
                    </div>
                    <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
                      <h4 className="font-bold text-purple-600 dark:text-purple-400 mb-3">Voice Message Strategy</h4>
                      <div className="space-y-2 text-sm">
                        <div>
                          <strong className="text-purple-600">Personal Touch:</strong>
                          <span className="text-muted-foreground"> Use their name, reference specific details</span>
                        </div>
                        <div>
                          <strong className="text-purple-600">Keep It Short:</strong>
                          <span className="text-muted-foreground"> 30-60 seconds maximum</span>
                        </div>
                        <div>
                          <strong className="text-purple-600">Clear CTA:</strong>
                          <span className="text-muted-foreground"> Specific next step, not vague "call me back"</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Real Case Study: The £89k Multi-Channel Transformation */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              className="bg-background/80 backdrop-blur-sm rounded-3xl p-8 border border-border/50 shadow-xl"
            >
              <div className="flex items-start gap-6 mb-8">
                <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl flex items-center justify-center flex-shrink-0">
                  <BarChart3 className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h2 className="text-3xl font-bold text-foreground mb-4">Case Study: How Elite Fitness Increased Revenue by £89k</h2>
                  <p className="text-lg text-muted-foreground leading-relaxed">
                    Sarah owned a boutique fitness studio in Birmingham. Stuck at £180k revenue with email-only follow-up, she was losing prospects daily. Here's how multi-channel automation transformed everything.
                  </p>
                </div>
              </div>

              <div className="bg-gradient-to-r from-red-50 to-red-100 dark:from-red-950/30 dark:to-red-900/30 rounded-2xl p-6 border border-red-200/50 dark:border-red-800/50 mb-8">
                <h3 className="text-xl font-bold text-red-700 dark:text-red-300 mb-4">The Email-Only Disaster</h3>
                <div className="grid md:grid-cols-3 gap-4 mb-4">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-red-600 dark:text-red-400 mb-2">18%</div>
                    <div className="text-sm text-red-700 dark:text-red-300">Email open rate</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-red-600 dark:text-red-400 mb-2">67%</div>
                    <div className="text-sm text-red-700 dark:text-red-300">Prospects never responded</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-red-600 dark:text-red-400 mb-2">£89k</div>
                    <div className="text-sm text-red-700 dark:text-red-300">Lost revenue annually</div>
                  </div>
                </div>
                <p className="text-red-600 dark:text-red-400 text-sm">
                  "I was sending beautiful emails with workout tips, nutrition advice, success stories. But people just weren't responding.
                  I thought they weren't interested, but it turns out they weren't even seeing my messages."
                </p>
              </div>

              <div className="space-y-8">
                {/* SMS Implementation */}
                <div className="bg-gradient-to-r from-green-50 to-green-100 dark:from-green-950/30 dark:to-green-900/30 rounded-2xl p-6 border border-green-200/50 dark:border-green-800/50">
                  <h4 className="text-lg font-bold text-green-700 dark:text-green-300 mb-4">Channel 1: SMS - The Immediate Impact</h4>
                  <p className="text-green-600 dark:text-green-400 mb-4">
                    <strong>The Strategy:</strong> Use SMS for time-sensitive, urgent communications that require immediate action.
                  </p>

                  <div className="bg-white dark:bg-gray-800 rounded-xl p-4 mb-4">
                    <h5 className="font-bold text-green-700 dark:text-green-300 mb-3">SMS Sequence Examples</h5>
                    <div className="space-y-4">
                      <div className="bg-green-50 dark:bg-green-900/30 rounded-lg p-3">
                        <strong className="text-green-600">Trial Class Reminder (2 hours before):</strong>
                        <div className="mt-2 text-sm text-green-600">
                          "Hi [Name]! Your trial class starts in 2 hours at Elite Fitness. Bring water & a towel - we'll provide everything else!
                          Can't wait to meet you 💪 Reply READY if you're coming!"
                        </div>
                        <div className="mt-2 text-xs text-green-500">Result: 94% attendance rate vs 67% with email</div>
                      </div>
                      <div className="bg-green-50 dark:bg-green-900/30 rounded-lg p-3">
                        <strong className="text-green-600">Abandoned Booking Recovery (1 hour after):</strong>
                        <div className="mt-2 text-sm text-green-600">
                          "Did something come up? Your spot in tomorrow's HIIT class is still available.
                          Secure it now: [link] - only 3 spots left!"
                        </div>
                        <div className="mt-2 text-xs text-green-500">Result: 73% of abandoned bookings recovered</div>
                      </div>
                      <div className="bg-green-50 dark:bg-green-900/30 rounded-lg p-3">
                        <strong className="text-green-600">Membership Expiry (3 days before):</strong>
                        <div className="mt-2 text-sm text-green-600">
                          "Your membership expires in 3 days! Don't lose your progress - renew now and get 1 month FREE: [link]"
                        </div>
                        <div className="mt-2 text-xs text-green-500">Result: 89% renewal rate vs 34% with email</div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-green-100 dark:bg-green-900/30 rounded-xl p-4">
                    <h6 className="font-bold text-green-700 dark:text-green-300 mb-2">SMS Results After 30 Days:</h6>
                    <div className="grid md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <div className="text-green-600 dark:text-green-400">✓ Open rate: 18% → 98%</div>
                        <div className="text-green-600 dark:text-green-400">✓ Response rate: 3% → 67%</div>
                      </div>
                      <div>
                        <div className="text-green-600 dark:text-green-400">✓ Class attendance: +45%</div>
                        <div className="text-green-600 dark:text-green-400">✓ Monthly revenue: +£12,400</div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* WhatsApp Business Implementation */}
                <div className="bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-950/30 dark:to-blue-900/30 rounded-2xl p-6 border border-blue-200/50 dark:border-blue-800/50">
                  <h4 className="text-lg font-bold text-blue-700 dark:text-blue-300 mb-4">Channel 2: WhatsApp Business - The Personal Touch</h4>
                  <p className="text-blue-600 dark:text-blue-400 mb-4">
                    <strong>The Strategy:</strong> Use WhatsApp for personal, conversational communication that builds relationships.
                  </p>

                  <div className="bg-white dark:bg-gray-800 rounded-xl p-4 mb-4">
                    <h5 className="font-bold text-blue-700 dark:text-blue-300 mb-3">WhatsApp Automation Sequences</h5>
                    <div className="space-y-4">
                      <div className="bg-blue-50 dark:bg-blue-900/30 rounded-lg p-3">
                        <strong className="text-blue-600">New Member Welcome Series:</strong>
                        <div className="mt-2 space-y-2 text-sm text-blue-600">
                          <div><strong>Day 1:</strong> "Welcome to the Elite family! 🎉 Here's your personal trainer introduction video..."</div>
                          <div><strong>Day 3:</strong> "How was your first workout? Here are 3 recovery tips to help you bounce back faster..."</div>
                          <div><strong>Day 7:</strong> "You've completed your first week! 💪 Here's a quick form check video for your favorite exercises..."</div>
                        </div>
                        <div className="mt-2 text-xs text-blue-500">Result: 89% engagement rate, 67% complete first month</div>
                      </div>
                      <div className="bg-blue-50 dark:bg-blue-900/30 rounded-lg p-3">
                        <strong className="text-blue-600">Progress Check-ins:</strong>
                        <div className="mt-2 text-sm text-blue-600">
                          "Hey [Name]! Noticed you've been crushing it with consistency this month 🔥
                          How are you feeling? Any goals you want to focus on next?"
                        </div>
                        <div className="mt-2 text-xs text-blue-500">Result: 78% response rate, builds personal connection</div>
                      </div>
                      <div className="bg-blue-50 dark:bg-blue-900/30 rounded-lg p-3">
                        <strong className="text-blue-600">Win-Back Campaign:</strong>
                        <div className="mt-2 text-sm text-blue-600">
                          "Miss seeing you at the studio! Life gets busy, we get it.
                          Want to ease back in? Here's a gentle 20-min workout you can do at home: [video]"
                        </div>
                        <div className="mt-2 text-xs text-blue-500">Result: 45% of inactive members return within 2 weeks</div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-blue-100 dark:bg-blue-900/30 rounded-xl p-4">
                    <h6 className="font-bold text-blue-700 dark:text-blue-300 mb-2">WhatsApp Results After 60 Days:</h6>
                    <div className="grid md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <div className="text-blue-600 dark:text-blue-400">✓ Member retention: +34%</div>
                        <div className="text-blue-600 dark:text-blue-400">✓ Personal training sales: +67%</div>
                      </div>
                      <div>
                        <div className="text-blue-600 dark:text-blue-400">✓ Referrals: +156%</div>
                        <div className="text-blue-600 dark:text-blue-400">✓ Monthly revenue: +£18,900</div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Instagram DM Implementation */}
                <div className="bg-gradient-to-r from-purple-50 to-purple-100 dark:from-purple-950/30 dark:to-purple-900/30 rounded-2xl p-6 border border-purple-200/50 dark:border-purple-800/50">
                  <h4 className="text-lg font-bold text-purple-700 dark:text-purple-300 mb-4">Channel 3: Instagram DM - The Visual Engagement</h4>
                  <p className="text-purple-600 dark:text-purple-400 mb-4">
                    <strong>The Strategy:</strong> Use Instagram for visual storytelling and community building with younger demographics.
                  </p>

                  <div className="bg-white dark:bg-gray-800 rounded-xl p-4 mb-4">
                    <h5 className="font-bold text-purple-700 dark:text-purple-300 mb-3">Instagram Automation Strategy</h5>
                    <div className="space-y-4">
                      <div className="bg-purple-50 dark:bg-purple-900/30 rounded-lg p-3">
                        <strong className="text-purple-600">Story Engagement Automation:</strong>
                        <div className="mt-2 text-sm text-purple-600">
                          When someone reacts to workout stories → Auto DM: "Loved that you're motivated!
                          Want the full workout routine? I'll send it over 💪"
                        </div>
                        <div className="mt-2 text-xs text-purple-500">Result: 67% of story reactors become leads</div>
                      </div>
                      <div className="bg-purple-50 dark:bg-purple-900/30 rounded-lg p-3">
                        <strong className="text-purple-600">Transformation Feature Follow-up:</strong>
                        <div className="mt-2 text-sm text-purple-600">
                          After featuring member transformation → DM to featured member: "You're inspiring so many people!
                          Here's a special discount code for your friends: TRANSFORM20"
                        </div>
                        <div className="mt-2 text-xs text-purple-500">Result: 89% of featured members refer new clients</div>
                      </div>
                      <div className="bg-purple-50 dark:bg-purple-900/30 rounded-lg p-3">
                        <strong className="text-purple-600">Challenge Participation:</strong>
                        <div className="mt-2 text-sm text-purple-600">
                          "Saw you completed Day 5 of our challenge! 🔥 You're on fire!
                          Here's a bonus tip to maximize your results: [video]"
                        </div>
                        <div className="mt-2 text-xs text-purple-500">Result: 78% challenge completion rate vs 23% without DMs</div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-purple-100 dark:bg-purple-900/30 rounded-xl p-4">
                    <h6 className="font-bold text-purple-700 dark:text-purple-300 mb-2">Instagram Results After 90 Days:</h6>
                    <div className="grid md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <div className="text-purple-600 dark:text-purple-400">✓ Follower engagement: +234%</div>
                        <div className="text-purple-600 dark:text-purple-400">✓ Story completion rate: +89%</div>
                      </div>
                      <div>
                        <div className="text-purple-600 dark:text-purple-400">✓ New member signups: +123%</div>
                        <div className="text-purple-600 dark:text-purple-400">✓ Monthly revenue: +£21,700</div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Email Strategic Use */}
                <div className="bg-gradient-to-r from-yellow-50 to-yellow-100 dark:from-yellow-950/30 dark:to-yellow-900/30 rounded-2xl p-6 border border-yellow-200/50 dark:border-yellow-800/50">
                  <h4 className="text-lg font-bold text-yellow-700 dark:text-yellow-300 mb-4">Channel 4: Email - The Content Powerhouse</h4>
                  <p className="text-yellow-600 dark:text-yellow-400 mb-4">
                    <strong>The Strategy:</strong> Use email for detailed content, education, and nurturing when urgency isn't required.
                  </p>

                  <div className="bg-white dark:bg-gray-800 rounded-xl p-4 mb-4">
                    <h5 className="font-bold text-yellow-700 dark:text-yellow-300 mb-3">Email's New Role in Multi-Channel</h5>
                    <div className="space-y-4">
                      <div className="bg-yellow-50 dark:bg-yellow-900/30 rounded-lg p-3">
                        <strong className="text-yellow-600">Weekly Nutrition Deep-Dive:</strong>
                        <div className="mt-2 text-sm text-yellow-600">
                          Detailed meal plans, recipes, shopping lists - content too long for SMS/WhatsApp
                        </div>
                        <div className="mt-2 text-xs text-yellow-500">Result: 45% open rate when positioned correctly</div>
                      </div>
                      <div className="bg-yellow-50 dark:bg-yellow-900/30 rounded-lg p-3">
                        <strong className="text-yellow-600">Success Story Features:</strong>
                        <div className="mt-2 text-sm text-yellow-600">
                          In-depth member transformations with photos, interviews, workout plans
                        </div>
                        <div className="mt-2 text-xs text-yellow-500">Result: 67% forward rate, builds social proof</div>
                      </div>
                      <div className="bg-yellow-50 dark:bg-yellow-900/30 rounded-lg p-3">
                        <strong className="text-yellow-600">Educational Series:</strong>
                        <div className="mt-2 text-sm text-yellow-600">
                          "Fitness Fundamentals" - 12-part series covering form, nutrition, recovery, mindset
                        </div>
                        <div className="mt-2 text-xs text-yellow-500">Result: Positions studio as expert, builds trust</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-950/30 dark:to-blue-950/30 rounded-2xl p-6 border border-green-200/50 dark:border-green-800/50 mt-8">
                <h4 className="text-xl font-bold text-green-700 dark:text-green-300 mb-4">Total Transformation: £89,000 Revenue Increase</h4>
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h5 className="font-bold text-green-600 dark:text-green-400 mb-2">Revenue Breakdown:</h5>
                    <ul className="space-y-1 text-sm text-green-600 dark:text-green-400">
                      <li>• SMS automation: +£12,400/month</li>
                      <li>• WhatsApp engagement: +£18,900/month</li>
                      <li>• Instagram DMs: +£21,700/month</li>
                      <li>• Email optimization: +£4,200/month</li>
                      <li><strong>• Total monthly increase: £57,200</strong></li>
                    </ul>
                  </div>
                  <div>
                    <h5 className="font-bold text-blue-600 dark:text-blue-400 mb-2">Business Transformation:</h5>
                    <ul className="space-y-1 text-sm text-blue-600 dark:text-blue-400">
                      <li>• Member retention: +67%</li>
                      <li>• Class attendance: +89%</li>
                      <li>• Referral rate: +234%</li>
                      <li>• Customer lifetime value: +156%</li>
                      <li><strong>• Annual revenue: £180k → £269k</strong></li>
                    </ul>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Multi-Channel Implementation Framework */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
              className="bg-background/80 backdrop-blur-sm rounded-3xl p-8 border border-border/50 shadow-xl"
            >
              <div className="flex items-start gap-6 mb-8">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-xl flex items-center justify-center flex-shrink-0">
                  <Target className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h2 className="text-3xl font-bold text-foreground mb-4">Your 60-Day Multi-Channel Implementation</h2>
                  <p className="text-lg text-muted-foreground leading-relaxed">
                    Follow this proven roadmap to implement multi-channel automation and see results within 60 days.
                  </p>
                </div>
              </div>

              <div className="space-y-6">
                <div className="bg-gradient-to-r from-green-50 to-green-100 dark:from-green-950/30 dark:to-green-900/30 rounded-2xl p-6 border border-green-200/50 dark:border-green-800/50">
                  <h3 className="text-xl font-bold text-green-700 dark:text-green-300 mb-4">Phase 1: SMS Foundation (Days 1-14)</h3>
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-bold text-green-600 dark:text-green-400 mb-2">Week 1: Setup & Testing</h4>
                      <ul className="space-y-1 text-sm text-green-600 dark:text-green-400">
                        <li>• Choose SMS platform (Twilio, TextMagic, etc.)</li>
                        <li>• Set up phone number and compliance</li>
                        <li>• Create 5 core message templates</li>
                        <li>• Test with small group (10-20 customers)</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-bold text-green-600 dark:text-green-400 mb-2">Week 2: Launch & Optimize</h4>
                      <ul className="space-y-1 text-sm text-green-600 dark:text-green-400">
                        <li>• Launch appointment reminders</li>
                        <li>• Implement booking confirmations</li>
                        <li>• Add abandoned cart recovery</li>
                        <li>• Monitor response rates and adjust</li>
                      </ul>
                    </div>
                  </div>
                  <div className="mt-4 p-3 bg-green-100 dark:bg-green-900/30 rounded-lg">
                    <p className="text-sm text-green-700 dark:text-green-300"><strong>Expected Result:</strong> 60-80% improvement in response rates within 2 weeks</p>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-950/30 dark:to-blue-900/30 rounded-2xl p-6 border border-blue-200/50 dark:border-blue-800/50">
                  <h3 className="text-xl font-bold text-blue-700 dark:text-blue-300 mb-4">Phase 2: WhatsApp Integration (Days 15-30)</h3>
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-bold text-blue-600 dark:text-blue-400 mb-2">Week 3: WhatsApp Business Setup</h4>
                      <ul className="space-y-1 text-sm text-blue-600 dark:text-blue-400">
                        <li>• Set up WhatsApp Business account</li>
                        <li>• Create business profile and catalog</li>
                        <li>• Design welcome message sequence</li>
                        <li>• Set up automated responses</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-bold text-blue-600 dark:text-blue-400 mb-2">Week 4: Personal Touch Campaigns</h4>
                      <ul className="space-y-1 text-sm text-blue-600 dark:text-blue-400">
                        <li>• Launch new customer onboarding</li>
                        <li>• Implement progress check-ins</li>
                        <li>• Create win-back sequences</li>
                        <li>• Add referral request automation</li>
                      </ul>
                    </div>
                  </div>
                  <div className="mt-4 p-3 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                    <p className="text-sm text-blue-700 dark:text-blue-300"><strong>Expected Result:</strong> 40-60% increase in customer engagement and retention</p>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-purple-50 to-purple-100 dark:from-purple-950/30 dark:to-purple-900/30 rounded-2xl p-6 border border-purple-200/50 dark:border-purple-800/50">
                  <h3 className="text-xl font-bold text-purple-700 dark:text-purple-300 mb-4">Phase 3: Social Media Automation (Days 31-45)</h3>
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-bold text-purple-600 dark:text-purple-400 mb-2">Week 5-6: Instagram Integration</h4>
                      <ul className="space-y-1 text-sm text-purple-600 dark:text-purple-400">
                        <li>• Set up Instagram Business account</li>
                        <li>• Create story engagement automation</li>
                        <li>• Design DM response templates</li>
                        <li>• Launch challenge participation tracking</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-bold text-purple-600 dark:text-purple-400 mb-2">Week 6-7: Facebook Integration</h4>
                      <ul className="space-y-1 text-sm text-purple-600 dark:text-purple-400">
                        <li>• Set up Facebook Messenger automation</li>
                        <li>• Create comment response workflows</li>
                        <li>• Implement lead magnet sequences</li>
                        <li>• Add event promotion automation</li>
                      </ul>
                    </div>
                  </div>
                  <div className="mt-4 p-3 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
                    <p className="text-sm text-purple-700 dark:text-purple-300"><strong>Expected Result:</strong> 100-200% increase in social media leads and engagement</p>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-yellow-50 to-yellow-100 dark:from-yellow-950/30 dark:to-yellow-900/30 rounded-2xl p-6 border border-yellow-200/50 dark:border-yellow-800/50">
                  <h3 className="text-xl font-bold text-yellow-700 dark:text-yellow-300 mb-4">Phase 4: Email Optimization (Days 46-60)</h3>
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-bold text-yellow-600 dark:text-yellow-400 mb-2">Week 7-8: Email Repositioning</h4>
                      <ul className="space-y-1 text-sm text-yellow-600 dark:text-yellow-400">
                        <li>• Audit current email performance</li>
                        <li>• Redesign for educational content</li>
                        <li>• Create weekly newsletter format</li>
                        <li>• Implement segmentation strategy</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-bold text-yellow-600 dark:text-yellow-400 mb-2">Week 8-9: Integration & Testing</h4>
                      <ul className="space-y-1 text-sm text-yellow-600 dark:text-yellow-400">
                        <li>• Connect all channels in unified system</li>
                        <li>• Test cross-channel workflows</li>
                        <li>• Optimize message timing and frequency</li>
                        <li>• Measure and analyze results</li>
                      </ul>
                    </div>
                  </div>
                  <div className="mt-4 p-3 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg">
                    <p className="text-sm text-yellow-700 dark:text-yellow-300"><strong>Expected Result:</strong> 300-500% improvement in overall communication effectiveness</p>
                  </div>
                </div>
              </div>

              <div className="bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-950/30 dark:to-blue-950/30 rounded-2xl p-6 border border-green-200/50 dark:border-green-800/50 mt-8">
                <h3 className="text-xl font-bold text-green-700 dark:text-green-300 mb-4">60-Day Revenue Projection</h3>
                <div className="grid md:grid-cols-4 gap-4 text-center">
                  <div>
                    <div className="text-2xl font-bold text-green-600 dark:text-green-400 mb-2">Week 2</div>
                    <div className="text-sm text-green-600 dark:text-green-400">+£3,000 from SMS</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-blue-600 dark:text-blue-400 mb-2">Week 4</div>
                    <div className="text-sm text-blue-600 dark:text-blue-400">+£8,000 from WhatsApp</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-purple-600 dark:text-purple-400 mb-2">Week 6</div>
                    <div className="text-sm text-purple-600 dark:text-purple-400">+£15,000 from Social</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-orange-600 dark:text-orange-400 mb-2">Week 8</div>
                    <div className="text-sm text-orange-600 dark:text-orange-400">+£25,000 total monthly</div>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Call to Action */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.7 }}
              className="bg-gradient-to-r from-blue-500 to-purple-500 rounded-3xl p-8 text-white text-center"
            >
              <h2 className="text-3xl font-bold mb-4">Something's Changing</h2>
              <p className="text-xl mb-6 opacity-90">
                Smart business owners are quietly discovering what successful companies already know about multi-channel communication. The shift is happening whether you participate or not.
              </p>
              <Link
                href="/demo"
                className="inline-flex items-center gap-2 bg-white text-blue-600 px-8 py-4 rounded-2xl font-bold text-lg hover:bg-gray-100 transition-all duration-300 shadow-lg hover:shadow-xl"
              >
                See What They Know
                <ArrowRight className="w-5 h-5" />
              </Link>
              <p className="text-sm mt-4 opacity-75">
                Join the business owners who've already discovered the multi-channel revolution
              </p>
            </motion.div>

            {/* Next Lesson Navigation */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.8 }}
              className="bg-background rounded-3xl border border-border shadow-lg p-8"
            >
              <h3 className="text-xl font-bold text-foreground mb-6 text-center">Continue Your Learning Journey</h3>

              <div className="grid md:grid-cols-2 gap-6">
                <Link
                  href="/learn/fundamentals/delegation-dilemma"
                  className="group bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950/30 dark:to-green-900/30 rounded-2xl p-6 border border-green-200/50 dark:border-green-800/50 hover:shadow-lg transition-all duration-300"
                >
                  <div className="flex items-center gap-4 mb-4">
                    <div className="w-12 h-12 bg-green-100 dark:bg-green-900/50 rounded-xl flex items-center justify-center">
                      <ArrowRight className="w-6 h-6 text-green-600 group-hover:translate-x-1 transition-transform" />
                    </div>
                    <div>
                      <h4 className="font-bold text-foreground">Next Lesson</h4>
                      <p className="text-sm text-muted-foreground">Lesson 9 of 15</p>
                    </div>
                  </div>
                  <h5 className="font-semibold text-foreground mb-2">The Delegation Dilemma</h5>
                  <p className="text-sm text-muted-foreground">
                    Why successful entrepreneurs clone themselves and how to build your delegation system
                  </p>
                </Link>

                <Link
                  href="/learn"
                  className="group bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-950/30 dark:to-gray-900/30 rounded-2xl p-6 border border-gray-200/50 dark:border-gray-800/50 hover:shadow-lg transition-all duration-300"
                >
                  <div className="flex items-center gap-4 mb-4">
                    <div className="w-12 h-12 bg-gray-100 dark:bg-gray-900/50 rounded-xl flex items-center justify-center">
                      <ArrowLeft className="w-6 h-6 text-gray-600 group-hover:-translate-x-1 transition-transform" />
                    </div>
                    <div>
                      <h4 className="font-bold text-foreground">Academy</h4>
                      <p className="text-sm text-muted-foreground">All lessons</p>
                    </div>
                  </div>
                  <h5 className="font-semibold text-foreground mb-2">Back to Academy</h5>
                  <p className="text-sm text-muted-foreground">
                    View all lessons and track your progress
                  </p>
                </Link>
              </div>
            </motion.div>
          </div>
        </div>
      </section>
    </div>
  )
}
