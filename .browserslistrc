# Ultra-modern browsers for maximum JavaScript optimization
# Targets ES2022+ features to minimize polyfills and transpilation

# Only modern browsers (last 1 year)
last 1 version
> 1%
not dead

# ES2022+ support (reduces legacy JavaScript by ~15KB)
Chrome >= 94
Firefox >= 93
Safari >= 15
Edge >= 94

# Modern mobile browsers
iOS >= 15
Android >= 94

# Exclude all legacy browsers aggressively
not ie <= 11
not chrome < 94
not firefox < 93
not safari < 15
not edge < 94
not op_mini all
not android < 94
not ios < 15

# This aggressive targeting reduces:
# - Polyfills for modern JS features
# - Transpilation overhead
# - Bundle size by ~10-15KB
