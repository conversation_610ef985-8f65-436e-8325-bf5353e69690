'use client'

// Dynamic keyword system for faster SEO ranking
export interface KeywordSet {
  primary: string[]
  secondary: string[]
  location: string[]
  industry: string[]
  painPoints: string[]
  solutions: string[]
  timeframes: string[]
  benefits: string[]
}

// Comprehensive keyword database
export const keywordDatabase: KeywordSet = {
  primary: [
    'customer conversion system',
    'sales optimization',
    'lead conversion automation',
    'prospect conversion',
    'revenue growth system',
    'customer acquisition',
    'sales automation',
    'conversion optimization'
  ],

  secondary: [
    'convert prospects customers',
    'increase sales conversion',
    'improve conversion rate',
    'boost revenue',
    'optimize sales process',
    'maximize customer acquisition',
    'enhance sales performance',
    'accelerate business growth'
  ],
  
  location: [
    'UK businesses',
    'London companies',
    'Manchester enterprises',
    'Birmingham firms',
    'Leeds organizations',
    'Glasgow businesses',
    'Sheffield companies',
    'Liverpool firms',
    'Bristol enterprises',
    'Cardiff businesses',
    'Edinburgh companies',
    'Newcastle firms'
  ],
  
  industry: [
    'hair salons',
    'beauty clinics',
    'fitness studios',
    'dental practices',
    'medical clinics',
    'consulting firms',
    'home services',
    'automotive services',
    'wellness centers',
    'professional services',
    'healthcare providers',
    'service businesses'
  ],
  
  painPoints: [
    'losing prospects to competitors',
    'low conversion rates',
    'missed sales opportunities',
    'poor follow-up systems',
    'prospects going cold',
    'inadequate lead nurturing',
    'weak sales processes',
    'revenue leakage',
    'ineffective customer acquisition',
    'poor prospect management'
  ],
  
  solutions: [
    'smart follow-up automation',
    'conversion optimization',
    'lead nurturing sequences',
    'prospect tracking systems',
    'sales process automation',
    'customer conversion funnels',
    'revenue optimization',
    'sales performance enhancement'
  ],
  
  timeframes: [
    'within 24 hours',
    'same day setup',
    'instant results',
    'immediate impact',
    'quick implementation',
    'fast deployment',
    'rapid transformation',
    'swift automation'
  ],
  
  benefits: [
    '89% conversion rate achieved',
    '£50K+ additional revenue annually',
    '89% prospect conversion success',
    '94% customer satisfaction',
    'doubled sales performance',
    'tripled conversion rates',
    'maximized revenue potential',
    'optimized customer acquisition'
  ]
}

// Get user's location from IP or browser
export function getUserLocation(): string {
  if (typeof window === 'undefined') return 'UK'
  
  // Try to get from localStorage first
  const savedLocation = localStorage.getItem('user-location')
  if (savedLocation) return savedLocation
  
  // Detect from timezone
  const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone
  const locationMap: Record<string, string> = {
    'Europe/London': 'London',
    'Europe/Manchester': 'Manchester',
    'Europe/Birmingham': 'Birmingham',
    'Europe/Leeds': 'Leeds',
    'Europe/Glasgow': 'Glasgow',
    'Europe/Sheffield': 'Sheffield',
    'Europe/Liverpool': 'Liverpool',
    'Europe/Bristol': 'Bristol',
    'Europe/Cardiff': 'Cardiff',
    'Europe/Edinburgh': 'Edinburgh'
  }
  
  const detectedLocation = locationMap[timezone] || 'UK'
  localStorage.setItem('user-location', detectedLocation)
  return detectedLocation
}

// Get time-based keywords (trending during different times)
export function getTimeBasedKeywords(): string[] {
  const hour = new Date().getHours()

  if (hour >= 6 && hour < 12) {
    return ['morning sales optimization', 'start converting prospects', 'early revenue growth']
  } else if (hour >= 12 && hour < 17) {
    return ['midday conversion boost', 'business hours sales', 'afternoon revenue optimization']
  } else if (hour >= 17 && hour < 22) {
    return ['evening sales success', 'convert more customers', 'maximize revenue potential']
  } else {
    return ['24/7 conversion system', 'round-the-clock sales', 'always-on customer acquisition']
  }
}

// Get seasonal keywords
export function getSeasonalKeywords(): string[] {
  const month = new Date().getMonth()
  
  if (month >= 0 && month <= 2) { // Q1
    return ['New Year efficiency', 'Q1 goals', 'fresh start automation', 'January productivity']
  } else if (month >= 3 && month <= 5) { // Q2
    return ['spring cleaning processes', 'Easter efficiency', 'Q2 optimization']
  } else if (month >= 6 && month <= 8) { // Q3
    return ['summer automation', 'holiday coverage', 'vacation-proof business']
  } else { // Q4
    return ['year-end efficiency', 'Christmas preparation', 'holiday automation', 'Q4 results']
  }
}

// Dynamic keyword rotation system
export class DynamicKeywordRotator {
  private rotationIndex: number = 0
  private lastRotation: number = 0
  private rotationInterval: number = 30000 // 30 seconds

  constructor() {
    if (typeof window !== 'undefined') {
      this.rotationIndex = parseInt(localStorage.getItem('keyword-rotation-index') || '0')
    }
  }

  getRotatedKeyword(keywordArray: string[]): string {
    const now = Date.now()
    
    // Rotate every 30 seconds
    if (now - this.lastRotation > this.rotationInterval) {
      this.rotationIndex = (this.rotationIndex + 1) % keywordArray.length
      this.lastRotation = now
      
      if (typeof window !== 'undefined') {
        localStorage.setItem('keyword-rotation-index', this.rotationIndex.toString())
      }
    }
    
    return keywordArray[this.rotationIndex] || keywordArray[0]
  }

  getPersonalizedKeywords(userLocation: string, userIndustry?: string): {
    primary: string
    secondary: string
    location: string
    industry: string
    painPoint: string
    solution: string
    benefit: string
    timeframe: string
  } {
    const locationKeywords = keywordDatabase.location.filter(loc => 
      loc.toLowerCase().includes(userLocation.toLowerCase())
    )
    
    const industryKeywords = userIndustry 
      ? keywordDatabase.industry.filter(ind => 
          ind.toLowerCase().includes(userIndustry.toLowerCase())
        )
      : keywordDatabase.industry

    return {
      primary: this.getRotatedKeyword(keywordDatabase.primary),
      secondary: this.getRotatedKeyword(keywordDatabase.secondary),
      location: this.getRotatedKeyword(locationKeywords.length > 0 ? locationKeywords : ['UK businesses']),
      industry: this.getRotatedKeyword(industryKeywords),
      painPoint: this.getRotatedKeyword(keywordDatabase.painPoints),
      solution: this.getRotatedKeyword(keywordDatabase.solutions),
      benefit: this.getRotatedKeyword(keywordDatabase.benefits),
      timeframe: this.getRotatedKeyword(keywordDatabase.timeframes)
    }
  }
}

// Hook for using dynamic keywords in components
export function useDynamicKeywords(userIndustry?: string) {
  const location = getUserLocation()
  const rotator = new DynamicKeywordRotator()
  const timeKeywords = getTimeBasedKeywords()
  const seasonalKeywords = getSeasonalKeywords()
  
  const personalizedKeywords = rotator.getPersonalizedKeywords(location, userIndustry)
  
  return {
    ...personalizedKeywords,
    timeKeywords,
    seasonalKeywords,
    location,
    // Helper functions for common patterns
    getHeroTitle: () => `${personalizedKeywords.primary} for ${personalizedKeywords.location}`,
    getSubtitle: () => `${personalizedKeywords.secondary} with ${personalizedKeywords.solution}`,
    getBenefit: () => `${personalizedKeywords.benefit} ${personalizedKeywords.timeframe}`,
    getPainPoint: () => `Stop ${personalizedKeywords.painPoint}`,
    getLocationSpecific: () => `${personalizedKeywords.industry} in ${location}`,
    getUrgency: () => `${personalizedKeywords.timeframe} - ${personalizedKeywords.benefit}`
  }
}

// Generate dynamic meta tags with high-frequency keywords
export function generateDynamicMeta(keywords: ReturnType<typeof useDynamicKeywords>) {
  // High-frequency keywords from scan
  const highFreqKeywords = [
    'customers', 'business', 'conversion', 'prospects', 'genlogic', 'sales', 'revenue', 'growth',
    'convert prospects', 'customer conversion', 'business owners', 'sales optimization', 'revenue increase',
    'uk businesses', 'conversion rate', 'uk business'
  ]

  return {
    title: `GenLogic - Convert 89% of Prospects | ${keywords.primary} for UK Business Owners`,
    description: `Convert 89% of prospects into paying customers. GenLogic helps UK business owners ${keywords.secondary} through smart follow-up automation. ${keywords.getBenefit()} for ${keywords.industry}.`,
    keywords: [
      'customer conversion UK',
      'UK business owners',
      'convert prospects customers',
      'sales optimization',
      'conversion rate',
      'revenue growth',
      'UK businesses',
      'customer acquisition',
      'business owners',
      keywords.primary,
      keywords.secondary,
      keywords.location,
      keywords.industry,
      keywords.solution,
      ...keywords.timeKeywords,
      ...keywords.seasonalKeywords,
      ...highFreqKeywords
    ].join(', ')
  }
}
