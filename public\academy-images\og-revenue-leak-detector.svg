<svg width="1200" height="630" viewBox="0 0 1200 630" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#dc2626;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#ea580c;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f59e0b;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="cardGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.95" />
      <stop offset="100%" style="stop-color:#fef2f2;stop-opacity:0.9" />
    </linearGradient>
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#dc2626;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ea580c;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="8" stdDeviation="16" flood-color="#000000" flood-opacity="0.1"/>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="1200" height="630" fill="url(#bgGradient)"/>
  
  <!-- Decorative Elements -->
  <circle cx="100" cy="100" r="60" fill="#ffffff" opacity="0.1"/>
  <circle cx="1100" cy="530" r="80" fill="#ffffff" opacity="0.1"/>
  <path d="M950,50 L1150,150 L1050,250 L950,150 Z" fill="#ffffff" opacity="0.05"/>
  
  <!-- Main Content Card -->
  <rect x="80" y="80" width="1040" height="470" rx="24" fill="url(#cardGradient)" filter="url(#shadow)"/>
  
  <!-- GenLogic Academy Badge -->
  <rect x="120" y="120" width="180" height="40" rx="20" fill="#1e40af"/>
  <text x="210" y="140" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="14" font-weight="bold">GENLOGIC ACADEMY</text>
  
  <!-- Revenue Leak Visual -->
  <g transform="translate(380, 125)">
    <!-- Bucket with holes -->
    <rect x="0" y="0" width="60" height="80" rx="8" fill="url(#accentGradient)"/>
    <!-- Holes in bucket -->
    <circle cx="15" cy="25" r="4" fill="#ffffff"/>
    <circle cx="35" cy="40" r="3" fill="#ffffff"/>
    <circle cx="45" cy="20" r="3" fill="#ffffff"/>
    <circle cx="25" cy="60" r="4" fill="#ffffff"/>
    
    <!-- Money drops falling -->
    <circle cx="15" cy="95" r="2" fill="#f59e0b"/>
    <circle cx="35" cy="105" r="2" fill="#f59e0b"/>
    <circle cx="45" cy="90" r="2" fill="#f59e0b"/>
    <circle cx="25" cy="110" r="2" fill="#f59e0b"/>
    
    <!-- £ symbols -->
    <text x="20" y="45" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="16" font-weight="bold">£</text>
    <text x="40" y="30" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">£</text>
  </g>
  
  <!-- Main Headline -->
  <text x="120" y="250" fill="#1e293b" font-family="Arial, sans-serif" font-size="40" font-weight="bold">
    <tspan x="120" dy="0">Revenue Leak</tspan>
    <tspan x="120" dy="50" fill="#dc2626">Detector:</tspan>
    <tspan x="120" dy="50">Stop the Bleeding</tspan>
  </text>
  
  <!-- Subtitle -->
  <text x="120" y="380" fill="#64748b" font-family="Arial, sans-serif" font-size="18" font-weight="normal">
    <tspan x="120" dy="0">Why £47,000 walks out your door every year</tspan>
    <tspan x="120" dy="24">and the 5-step system to plug every leak</tspan>
  </text>
  
  <!-- Revenue Leak Stats Box -->
  <g transform="translate(650, 180)">
    <rect x="0" y="0" width="400" height="280" rx="20" fill="#f8fafc" stroke="#e2e8f0" stroke-width="2"/>
    <text x="200" y="35" text-anchor="middle" fill="#1e293b" font-family="Arial, sans-serif" font-size="18" font-weight="bold">Revenue Leak Analysis</text>
    
    <!-- No-Show Leak -->
    <rect x="30" y="60" width="340" height="45" rx="10" fill="#fef2f2" stroke="#dc2626" stroke-width="1"/>
    <text x="50" y="80" fill="#dc2626" font-family="Arial, sans-serif" font-size="16" font-weight="bold">No-Shows: £18,400/year</text>
    <text x="50" y="95" fill="#64748b" font-family="Arial, sans-serif" font-size="12">28% no-show rate costs you dearly</text>
    
    <!-- Follow-up Leak -->
    <rect x="30" y="115" width="340" height="45" rx="10" fill="#fef2f2" stroke="#ea580c" stroke-width="1"/>
    <text x="50" y="135" fill="#ea580c" font-family="Arial, sans-serif" font-size="16" font-weight="bold">Poor Follow-up: £15,200/year</text>
    <text x="50" y="150" fill="#64748b" font-family="Arial, sans-serif" font-size="12">67% never return without automation</text>
    
    <!-- Referral Leak -->
    <rect x="30" y="170" width="340" height="45" rx="10" fill="#fef2f2" stroke="#f59e0b" stroke-width="1"/>
    <text x="50" y="190" fill="#f59e0b" font-family="Arial, sans-serif" font-size="16" font-weight="bold">Lost Referrals: £13,400/year</text>
    <text x="50" y="205" fill="#64748b" font-family="Arial, sans-serif" font-size="12">Missing 25% potential referrals</text>
    
    <!-- What You'll Get -->
    <text x="50" y="235" fill="#1e293b" font-family="Arial, sans-serif" font-size="14" font-weight="bold">Interactive Tools Included:</text>
    <text x="50" y="250" fill="#64748b" font-family="Arial, sans-serif" font-size="12">✓ Revenue leak calculator</text>
    <text x="200" y="250" fill="#64748b" font-family="Arial, sans-serif" font-size="12">✓ 30-day recovery plan</text>
  </g>
  
  <!-- Bottom Stats -->
  <rect x="120" y="460" width="100" height="50" rx="10" fill="#fef2f2"/>
  <text x="170" y="480" text-anchor="middle" fill="#dc2626" font-family="Arial, sans-serif" font-size="16" font-weight="bold">17 min</text>
  <text x="170" y="495" text-anchor="middle" fill="#64748b" font-family="Arial, sans-serif" font-size="12">read time</text>
  
  <rect x="240" y="460" width="120" height="50" rx="10" fill="#f3e8ff"/>
  <text x="300" y="480" text-anchor="middle" fill="#8b5cf6" font-family="Arial, sans-serif" font-size="16" font-weight="bold">Lesson 10</text>
  <text x="300" y="495" text-anchor="middle" fill="#64748b" font-family="Arial, sans-serif" font-size="12">of 15</text>
  
  <!-- URL -->
  <text x="1080" y="600" text-anchor="end" fill="#64748b" font-family="Arial, sans-serif" font-size="14">genlogic.io/learn</text>
</svg>
