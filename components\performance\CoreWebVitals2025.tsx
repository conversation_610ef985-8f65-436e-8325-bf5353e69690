'use client'

import { useEffect } from 'react'

/**
 * Core Web Vitals 2025 Monitoring Component
 * Updated for 2025 standards with INP (Interaction to Next Paint)
 * Serverless-compatible and optimized for Vercel
 */
export function CoreWebVitals2025() {
  useEffect(() => {
    // Only run in browser environment
    if (typeof window === 'undefined') return

    // Check if Performance Observer is supported
    if (!('PerformanceObserver' in window)) {
      console.warn('PerformanceObserver not supported')
      return
    }

    // Track Largest Contentful Paint (LCP) - Target: <2.5s
    const observeLCP = () => {
      try {
        const lcpObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries()
          const lastEntry = entries[entries.length - 1] as any
          
          if (lastEntry && window.gtag) {
            const lcpValue = Math.round(lastEntry.startTime)
            
            window.gtag('event', 'web_vitals_2025', {
              event_category: 'performance',
              event_label: 'LCP',
              value: lcpValue,
              custom_parameter_1: 'core_web_vitals_2025',
              custom_parameter_2: lcpValue < 2500 ? 'good' : lcpValue < 4000 ? 'needs_improvement' : 'poor'
            })

            // Log for debugging in development
            if (process.env.NODE_ENV === 'development') {
              console.log(`[CWV 2025] LCP: ${lcpValue}ms (${lcpValue < 2500 ? 'Good' : lcpValue < 4000 ? 'Needs Improvement' : 'Poor'})`)
            }
          }
        })
        
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })
        return lcpObserver
      } catch (e) {
        console.warn('LCP observer failed:', e)
        return null
      }
    }

    // Track Interaction to Next Paint (INP) - Target: <200ms (NEW for 2025)
    const observeINP = () => {
      try {
        const inpObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries()
          
          entries.forEach((entry: any) => {
            if (entry.duration && window.gtag) {
              const inpValue = Math.round(entry.duration)
              
              window.gtag('event', 'web_vitals_2025', {
                event_category: 'performance',
                event_label: 'INP',
                value: inpValue,
                custom_parameter_1: 'core_web_vitals_2025',
                custom_parameter_2: inpValue < 200 ? 'good' : inpValue < 500 ? 'needs_improvement' : 'poor'
              })

              // Log for debugging in development
              if (process.env.NODE_ENV === 'development') {
                console.log(`[CWV 2025] INP: ${inpValue}ms (${inpValue < 200 ? 'Good' : inpValue < 500 ? 'Needs Improvement' : 'Poor'})`)
              }
            }
          })
        })
        
        inpObserver.observe({ entryTypes: ['event'] })
        return inpObserver
      } catch (e) {
        console.warn('INP observer failed:', e)
        return null
      }
    }

    // Track Cumulative Layout Shift (CLS) - Target: <0.1
    const observeCLS = () => {
      try {
        let clsValue = 0
        const clsObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries()
          
          entries.forEach((entry: any) => {
            if (!entry.hadRecentInput && entry.value) {
              clsValue += entry.value
            }
          })

          if (clsValue > 0 && window.gtag) {
            const clsScore = Math.round(clsValue * 1000) / 1000
            
            window.gtag('event', 'web_vitals_2025', {
              event_category: 'performance',
              event_label: 'CLS',
              value: Math.round(clsValue * 1000),
              custom_parameter_1: 'core_web_vitals_2025',
              custom_parameter_2: clsScore < 0.1 ? 'good' : clsScore < 0.25 ? 'needs_improvement' : 'poor'
            })

            // Log for debugging in development
            if (process.env.NODE_ENV === 'development') {
              console.log(`[CWV 2025] CLS: ${clsScore} (${clsScore < 0.1 ? 'Good' : clsScore < 0.25 ? 'Needs Improvement' : 'Poor'})`)
            }
          }
        })
        
        clsObserver.observe({ entryTypes: ['layout-shift'] })
        return clsObserver
      } catch (e) {
        console.warn('CLS observer failed:', e)
        return null
      }
    }

    // Track First Contentful Paint (FCP) - Target: <1.8s
    const observeFCP = () => {
      try {
        const fcpObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries()
          const fcpEntry = entries[0] as any
          
          if (fcpEntry && window.gtag) {
            const fcpValue = Math.round(fcpEntry.startTime)
            
            window.gtag('event', 'web_vitals_2025', {
              event_category: 'performance',
              event_label: 'FCP',
              value: fcpValue,
              custom_parameter_1: 'core_web_vitals_2025',
              custom_parameter_2: fcpValue < 1800 ? 'good' : fcpValue < 3000 ? 'needs_improvement' : 'poor'
            })

            // Log for debugging in development
            if (process.env.NODE_ENV === 'development') {
              console.log(`[CWV 2025] FCP: ${fcpValue}ms (${fcpValue < 1800 ? 'Good' : fcpValue < 3000 ? 'Needs Improvement' : 'Poor'})`)
            }
          }
        })
        
        fcpObserver.observe({ entryTypes: ['paint'] })
        return fcpObserver
      } catch (e) {
        console.warn('FCP observer failed:', e)
        return null
      }
    }

    // Initialize all observers
    const lcpObserver = observeLCP()
    const inpObserver = observeINP()
    const clsObserver = observeCLS()
    const fcpObserver = observeFCP()

    // Track overall page performance score
    const trackOverallScore = () => {
      setTimeout(() => {
        if (window.gtag) {
          window.gtag('event', 'page_performance_2025', {
            event_category: 'performance',
            event_label: 'overall_score',
            custom_parameter_1: 'performance_tracking',
            custom_parameter_2: new Date().toISOString()
          })
        }
      }, 5000) // Track after 5 seconds
    }

    trackOverallScore()

    // Cleanup function for serverless compatibility
    return () => {
      try {
        lcpObserver?.disconnect()
        inpObserver?.disconnect()
        clsObserver?.disconnect()
        fcpObserver?.disconnect()
      } catch (e) {
        console.warn('Observer cleanup failed:', e)
      }
    }
  }, [])

  // This component doesn't render anything visible
  return null
}

// Export helper for manual performance tracking
export function trackCustomPerformanceMetric(name: string, value: number, category = 'custom') {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'custom_performance_2025', {
      event_category: category,
      event_label: name,
      value: Math.round(value),
      custom_parameter_1: 'custom_metrics_2025'
    })
  }
}

// Export performance thresholds for 2025
export const PERFORMANCE_THRESHOLDS_2025 = {
  LCP: { good: 2500, needsImprovement: 4000 },
  INP: { good: 200, needsImprovement: 500 },
  CLS: { good: 0.1, needsImprovement: 0.25 },
  FCP: { good: 1800, needsImprovement: 3000 }
} as const
