'use client'

import Link from 'next/link'
import { motion } from 'framer-motion'
import { CustomButton } from '@/components/ui/custom-button'
import {
  Calendar,
  Clock,
  MessageSquare,
  Phone,
  AlertCircle,
  CheckCircle,
  TrendingUp,
  Users,
  ArrowRight,
  Star,
} from 'lucide-react'
import TestimonialsSection from '@/components/TestimonialsSection'
import { SchemaMarkup } from '@/components/seo/SchemaMarkup'
import { DynamicContent, DynamicHeadline, DynamicMetaTags } from '@/components/seo/DynamicContent'
import { StructuredData } from '@/components/seo/StructuredData'
import { AIOptimizedContent } from '@/components/seo/AIOptimizedContent'
import { useIndustryDetection } from '@/hooks/useIndustryDetection'
// import { SEOKeywordBooster } from '@/components/seo/KeywordRichContent'


export default function HomePage() {
  const { detectedIndustry } = useIndustryDetection()

  const dailyFrustrations = [
    {
      title: 'Chasing No-Show Customers',
      description: 'You spend your evenings calling customers who didn\'t show up, trying to understand what happened while your time slot sits empty.',
      highlight: 'Average business loses £2,400/month to no-shows',
      icon: <Phone className="w-8 h-8" />,
    },
    {
      title: 'Manual Reminder Marathon',
      description: 'Every day you\'re sending texts, making calls, and writing emails to remind customers about their appointments. It never ends.',
      highlight: '3+ hours daily spent on reminders',
      icon: <Clock className="w-8 h-8" />,
    },
    {
      title: 'Lost Follow-Up Opportunities',
      description: 'You know you should follow up with customers for reviews and rebookings, but there\'s never enough time. Opportunities slip away.',
      highlight: '60% of customers never return without follow-up',
      icon: <AlertCircle className="w-8 h-8" />,
    },
  ]

  const solutions = [
    {
      title: 'Customers Book Themselves',
      description: 'Your customers can book appointments 24/7 through your personalized booking page. No more phone tag or missed calls during busy periods.',
      highlight: 'Available even when you\'re with other customers',
      icon: <Calendar className="w-8 h-8" />,
    },
    {
      title: 'Never Lose Another Customer',
      description: 'Smart follow-up sequences ensure every prospect gets the attention they deserve. Turn missed calls into booked appointments automatically.',
      highlight: '89% of prospects convert into customers',
      icon: <MessageSquare className="w-8 h-8" />,
    },
    {
      title: 'Turn One-Time Customers Into Repeat Buyers',
      description: 'Automated thank-you messages, review requests, and rebooking prompts keep customers coming back. Build a loyal customer base that generates consistent revenue.',
      highlight: '40% increase in repeat bookings',
      icon: <TrendingUp className="w-8 h-8" />,
    },
  ]

  return (
    <div className="bg-background transition-colors duration-300">
      {/* Dynamic Meta Tags for SEO */}
      <DynamicMetaTags userIndustry={detectedIndustry} />

      {/* SEO Keyword Booster - TEMPORARILY REMOVED FOR TESTING */}
      {/* <SEOKeywordBooster /> */}

      {/* Static H1 for SEO (hidden but crawlable) */}
      <h1 className="sr-only">GenLogic Customer Conversion System - Convert 89% of Prospects Into Customers</h1>

      {/* SEO Schema Markup */}
      <SchemaMarkup type="organization" />
      <SchemaMarkup type="localBusiness" />
      <SchemaMarkup type="softwareApplication" />
      <SchemaMarkup type="website" />

      {/* 2025 SEO Optimizations */}
      <StructuredData type="faq" />
      <AIOptimizedContent page="home" />

      {/* WebPage Schema for Homepage */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebPage",
            "@id": "https://genlogic.io#webpage",
            "url": "https://genlogic.io",
            "name": "GenLogic - Stop Working Late Evenings | Business Automation UK",
            "description": "Stop working late evenings every day. GenLogic automates customer bookings, reduces no-shows by 85%, and helps UK business owners save 20+ hours weekly.",
            "inLanguage": "en-GB",
            "isPartOf": {
              "@type": "WebSite",
              "@id": "https://genlogic.io#website",
              "url": "https://genlogic.io",
              "name": "GenLogic"
            },
            "about": {
              "@type": "Organization",
              "@id": "https://genlogic.io#organization"
            },
            "mainEntity": {
              "@type": "SoftwareApplication",
              "name": "GenLogic Business Automation Platform"
            }
          })
        }}
      />

      {/* Hero Section */}
      <section className="relative header-spacing pb-20 overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-primary-50/30 via-background to-accent-50/20 dark:from-primary-950/20 dark:via-background dark:to-accent-950/10"></div>
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-primary-200/20 rounded-full blur-3xl dark:bg-primary-800/10"></div>
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-accent-200/20 rounded-full blur-3xl dark:bg-accent-800/10"></div>

        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center min-h-[80vh]">
            {/* Left Column - Content */}
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, ease: 'easeOut' }}
              className="text-left"
            >
              <div className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 text-sm font-semibold tracking-wide mb-8 dark:from-green-900/30 dark:to-emerald-900/30 dark:text-green-400 border border-green-200/50 dark:border-green-800/50">
                <TrendingUp className="w-4 h-4 mr-2" />
                Increase Your Sales
              </div>

              <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-foreground mb-8 leading-[1.1]">
                <DynamicContent userIndustry={detectedIndustry}>
                  {(keywords) => (
                    <>
                      Turn more{' '}
                      <span className="relative">
                        <span className="text-green-700 dark:text-green-400">prospects into customers</span>
                        <div className="absolute -bottom-2 left-0 right-0 h-3 bg-gradient-to-r from-green-200 to-emerald-200 dark:from-green-800 dark:to-emerald-800 rounded-full opacity-30"></div>
                      </span>
                      {' '}with smart follow-up
                    </>
                  )}
                </DynamicContent>
              </h1>

              <p className="text-xl text-muted-foreground mb-8 leading-relaxed max-w-xl">
                Most UK local businesses convert only 15-20% of prospects into customers.
                <strong className="text-foreground"> GenLogic helps you convert 89% through smart follow-up automation that never misses a lead.</strong>
              </p>

              <div className="flex flex-col sm:flex-row gap-4 mb-12">
                <CustomButton
                  variant="primary"
                  size="lg"
                  href="/demo"
                  icon={<ArrowRight className="w-5 h-5" />}
                  iconPosition="right"
                  className="shadow-xl hover:shadow-2xl hover:shadow-green-500/25 transform hover:scale-105 transition-all duration-300"
                >
                  See How It Works
                </CustomButton>

                <CustomButton
                  variant="outline"
                  size="lg"
                  href="/contact"
                  className="border-2 hover:bg-green-50 dark:hover:bg-green-900/20"
                >
                  Free Consultation
                </CustomButton>
              </div>

              {/* Trust indicators */}
              <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4 text-sm text-muted-foreground">
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                  <span><strong className="text-foreground">89%</strong> conversion rate achieved</span>
                </div>
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                  <span><strong className="text-foreground">£50K+</strong> additional revenue yearly</span>
                </div>
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-purple-500 rounded-full mr-3"></div>
                  <span><strong className="text-foreground">500+</strong> UK businesses growing</span>
                </div>
              </div>
            </motion.div>

            {/* Right Column - Visual */}
            <motion.div
              initial={{ opacity: 0, x: 30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2, ease: 'easeOut' }}
              className="relative"
            >
              <div className="relative bg-gradient-to-br from-primary-50 to-accent-50 dark:from-primary-950/50 dark:to-accent-950/50 rounded-3xl p-8 border border-primary-200/50 dark:border-primary-800/50 shadow-2xl">
                {/* Before/After Comparison */}
                <div className="space-y-6">
                  <div className="text-center mb-8">
                    <h3 className="text-2xl font-bold text-foreground mb-2">UK Business Owners: Your Evenings Before vs After</h3>
                    <p className="text-muted-foreground">See the transformation</p>
                  </div>

                  {/* Before */}
                  <div className="bg-red-50 dark:bg-red-950/30 rounded-2xl p-6 border border-red-200 dark:border-red-800">
                    <div className="flex items-center mb-4">
                      <div className="w-3 h-3 bg-red-500 rounded-full mr-3"></div>
                      <span className="font-semibold text-red-800 dark:text-red-300">Before GenLogic</span>
                    </div>
                    <div className="space-y-2 text-sm text-red-700 dark:text-red-400">
                      <div>📞 Calling customers about appointments</div>
                      <div>📱 Sending reminder texts manually</div>
                      <div>😤 Dealing with 6+ no-shows daily</div>
                      <div>⏰ Working until 9pm on admin</div>
                    </div>
                  </div>

                  {/* After */}
                  <div className="bg-green-50 dark:bg-green-950/30 rounded-2xl p-6 border border-green-200 dark:border-green-800">
                    <div className="flex items-center mb-4">
                      <div className="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
                      <span className="font-semibold text-green-800 dark:text-green-300">After GenLogic</span>
                    </div>
                    <div className="space-y-2 text-sm text-green-700 dark:text-green-400">
                      <div>✅ Customers book themselves online</div>
                      <div>🤖 Automatic reminders sent</div>
                      <div>😊 95% of customers show up</div>
                      <div>🏠 Home by 6pm every day</div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Problems Section */}
      <section className="py-24 bg-gradient-to-b from-background to-muted/30">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: 'easeOut' }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-red-100 text-red-800 text-sm font-semibold tracking-wide mb-8 dark:bg-red-900/30 dark:text-red-400 border border-red-200/50 dark:border-red-800/50">
              The Daily Struggle
            </div>
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-foreground mb-6 leading-tight">
              UK Business Owners: Sound familiar? You're not alone.
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Every evening, thousands of UK business owners are stuck managing customers and dealing with no-shows instead of living their lives.
            </p>
          </motion.div>

          {/* Pain Points Grid */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
            {dailyFrustrations.map((frustration, index) => (
              <motion.div
                key={frustration.title}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 + 0.1 }}
                viewport={{ once: true }}
                className="bg-background rounded-3xl p-8 border border-border shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <div className="w-16 h-16 bg-red-100 dark:bg-red-900/30 rounded-2xl flex items-center justify-center mb-6">
                  {frustration.icon}
                </div>
                <h3 className="text-xl font-bold text-foreground mb-4">{frustration.title}</h3>
                <p className="text-muted-foreground mb-4 leading-relaxed">
                  {frustration.description}
                </p>
                <div className="bg-red-50 dark:bg-red-950/30 rounded-xl p-4 border border-red-200 dark:border-red-800">
                  <div className="text-red-800 dark:text-red-300 font-semibold text-sm">Impact:</div>
                  <div className="text-red-600 dark:text-red-400 font-bold">{frustration.highlight}</div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Emotional Quote */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
            className="text-center"
          >
            <div className="bg-gradient-to-br from-primary-50 to-accent-50 dark:from-primary-950/50 dark:to-accent-950/50 rounded-3xl p-12 max-w-4xl mx-auto border border-primary-200/50 dark:border-primary-800/50 shadow-xl">
              <div className="text-3xl mb-6">😔</div>
              <blockquote className="text-2xl font-medium text-foreground mb-6 leading-relaxed italic">
                "I started this business to follow my passion. Now I feel like I'm drowning in admin work.
                When did I become a full-time secretary?"
              </blockquote>
              <cite className="text-muted-foreground font-medium">— Emma, Beauty Salon Owner, Manchester</cite>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Solutions Section */}
      <section className="py-24 bg-gradient-to-b from-muted/30 to-background">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: 'easeOut' }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-green-100 text-green-800 text-sm font-semibold tracking-wide mb-8 dark:bg-green-900/30 dark:text-green-400 border border-green-200/50 dark:border-green-800/50">
              The GenLogic Solution
            </div>
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-foreground mb-6 leading-tight">
              Business Automation: Finish work at 6pm every day
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              While your competitors are stuck managing customers, you're home with your family. Here's how GenLogic's business automation transforms UK businesses.
            </p>
          </motion.div>

          {/* Solutions Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16">
            {solutions.map((solution, index) => (
              <motion.div
                key={solution.title}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 + 0.1 }}
                viewport={{ once: true }}
                className="bg-background rounded-3xl p-8 border border-border shadow-lg hover:shadow-xl transition-all duration-300 group"
              >
                <div className="w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                  {solution.icon}
                </div>
                <h3 className="text-xl font-bold text-foreground mb-4">{solution.title}</h3>
                <p className="text-muted-foreground mb-6 leading-relaxed">
                  {solution.description}
                </p>
                <div className="bg-green-50 dark:bg-green-950/30 rounded-xl p-4 border border-green-200 dark:border-green-800">
                  <div className="text-green-800 dark:text-green-300 font-semibold text-sm">Result:</div>
                  <div className="text-green-600 dark:text-green-400 font-bold">{solution.highlight}</div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Transformation Story */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
            className="bg-gradient-to-r from-primary-600 to-primary-700 rounded-3xl p-12 text-white text-center shadow-2xl"
          >
            <div className="max-w-4xl mx-auto">
              <div className="text-4xl mb-6">🎉</div>
              <h3 className="text-3xl font-bold mb-6">The Result? 20+ Hours Saved Weekly, Fewer No-Shows</h3>
              <p className="text-xl mb-8 text-primary-100 leading-relaxed">
                "I used to work until 9pm every night managing customers. Now I'm home by 6pm, my no-shows dropped by 85%,
                and my revenue increased by 30%. GenLogic didn't just automate my business—it gave me my evenings back."
              </p>
              <div className="flex items-center justify-center gap-8 text-primary-100">
                <div className="text-center">
                  <div className="text-3xl font-bold text-white">85%</div>
                  <div className="text-sm">Fewer No-Shows</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-white">30+</div>
                  <div className="text-sm">Hours Saved Weekly</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-white">£120</div>
                  <div className="text-sm">Starting From</div>
                </div>
              </div>
              <cite className="block mt-6 text-primary-200 font-medium">— James, Fitness Studio Owner, Birmingham</cite>
            </div>
          </motion.div>
        </div>
      </section>

      {/* SEO CONTENT - BULLETPROOF HIDDEN with multiple hiding methods */}
      <div
        style={{
          position: 'absolute',
          left: '-10000px',
          top: '-10000px',
          width: '1px',
          height: '1px',
          overflow: 'hidden',
          opacity: '0',
          visibility: 'hidden',
          display: 'none'
        }}
        aria-hidden="true"
        className="sr-only"
      >
        <h2>Complete Business Automation Guide for UK Business Owners</h2>

        <h3>Why UK Business Owners Work Late Every Evening</h3>
        <p>
          Every evening across the UK, thousands of business owners are stuck in their offices dealing with the same repetitive tasks.
          Customer inquiries flood in after hours, no-shows disrupt carefully planned schedules, and manual booking management
          consumes precious family time. This cycle leaves business owners exhausted, frustrated, and questioning why they started
          their business in the first place.
        </p>
        <p>
          The average UK business owner spends 23 hours weekly on administrative tasks that could be automated. From managing
          customer bookings to chasing payments, these manual processes steal time from what matters most - growing the business
          and spending time with family. GenLogic's business automation platform addresses these exact pain points, helping
          business owners reclaim their evenings and weekends.
        </p>

        <h3>How Business Automation Transforms UK Businesses</h3>
        <p>
          Business automation isn't just about technology - it's about freedom. When UK business owners implement GenLogic's
          automation solutions, they typically see immediate improvements in their work-life balance. Automated booking systems
          handle customer inquiries 24/7, reducing no-shows by up to 85% through intelligent reminder sequences.
        </p>
        <p>
          Customer management becomes effortless with automated follow-ups, review requests, and payment reminders. Business
          owners report saving 20+ hours weekly, allowing them to focus on strategic growth rather than daily administrative
          tasks. The result? More customers, higher revenue, and most importantly, evenings spent with family instead of
          buried in paperwork.
        </p>

        <h3>GenLogic Business Automation: Complete Solution for UK Businesses</h3>

        <h4>Customer Booking Automation</h4>
        <p>
          Automated booking systems that work 24/7, reducing no-shows and managing customer appointments without manual intervention.
          Perfect for UK service businesses, home improvement companies, and professional services.
        </p>

        <h4>Customer Communication</h4>
        <p>
          Intelligent SMS and email sequences that nurture customers, send reminders, and generate reviews automatically.
          Keeps customers engaged while saving business owners hours of manual communication.
        </p>

        <h4>Business Analytics</h4>
        <p>
          Comprehensive reporting and analytics that show exactly how automation is improving business performance.
          Track no-show reduction, customer satisfaction, and time savings in real-time.
        </p>

        <h3>Join 500+ UK Business Owners Who Stopped Working Late Evenings</h3>
        <p>
          From Manchester to London, UK business owners are discovering how GenLogic's business automation platform transforms
          their daily operations. Stop working until 9pm every evening. Start spending time with family. Get your life back
          with proven automation solutions designed specifically for UK businesses.
        </p>
      </div>

      {/* Testimonials */}
      <TestimonialsSection />

      {/* Final CTA Section */}
      <section className="py-24 bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800 text-white relative overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(255,255,255,0.1),transparent_50%)]"></div>
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(255,255,255,0.05),transparent_50%)]"></div>

        <div className="relative z-10 max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: 'easeOut' }}
            viewport={{ once: true }}
          >
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-white/20 text-white/90 text-sm font-semibold tracking-wide mb-8 border border-white/30">
              <Clock className="w-4 h-4 mr-2" />
              Your Evening Starts Now
            </div>

            <h2 className="text-3xl sm:text-4xl lg:text-6xl font-bold mb-8 leading-tight">
              Ready to get your{' '}
              <span className="relative">
                <span className="text-accent-300">evenings back?</span>
                <div className="absolute -bottom-2 left-0 right-0 h-3 bg-white/30 rounded-full"></div>
              </span>
            </h2>

            <p className="text-xl text-primary-100 mb-12 max-w-3xl mx-auto leading-relaxed">
              Join over 500 UK business owners who've already stopped working evenings and started living again.
              <strong className="text-white"> Your transformation starts today.</strong>
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center mb-12">
              <CustomButton
                variant="primary"
                size="lg"
                href="/demo"
                icon={<ArrowRight className="w-5 h-5" />}
                iconPosition="right"
                className="bg-white text-primary-700 hover:bg-primary-50 shadow-2xl hover:shadow-white/25 transform hover:scale-105 transition-all duration-300 text-lg px-8 py-4"
              >
                Book Your Free Demo
              </CustomButton>

              <CustomButton
                variant="outline"
                size="lg"
                href="/pricing"
                className="border-2 border-white/30 text-white hover:bg-white/10 hover:border-white/50 text-lg px-8 py-4"
              >
                See Simple Pricing
              </CustomButton>
            </div>

            {/* Trust indicators */}
            <div className="flex flex-col sm:flex-row items-center justify-center gap-8 text-primary-100">
              <div className="flex items-center">
                <CheckCircle className="w-5 h-5 text-green-300 mr-3" />
                <span><strong className="text-white">14-day</strong> free trial</span>
              </div>
              <div className="flex items-center">
                <Users className="w-5 h-5 text-blue-300 mr-3" />
                <span><strong className="text-white">No</strong> credit card required</span>
              </div>
              <div className="flex items-center">
                <Star className="w-5 h-5 text-yellow-300 mr-3" />
                <span><strong className="text-white">UK-based</strong> support team</span>
              </div>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  )
}
