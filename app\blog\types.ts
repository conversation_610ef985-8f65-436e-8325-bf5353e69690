export interface Author {
  name: string
  role: string
  avatar: string
  bio: string
}

export interface BlogPost {
  id: string
  slug: string
  title: string
  excerpt: string
  content: string | React.ReactElement
  author: Author
  publishedAt: string
  readingTime: string
  category: string
  tags: string[]
  featured: boolean
  image: string
  views: number
  likes: number
}

export interface Category {
  name: string
  slug: string
  count: number
}
