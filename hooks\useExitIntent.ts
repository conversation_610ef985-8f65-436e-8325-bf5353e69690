'use client'

import { useEffect, useCallback, useState } from 'react'

interface UseExitIntentOptions {
  enabled?: boolean
  threshold?: number
  delay?: number
  onExitIntent?: () => void
}

export function useExitIntent({
  enabled = true,
  threshold = 0,
  delay = 0,
  onExitIntent
}: UseExitIntentOptions = {}) {
  const [hasTriggered, setHasTriggered] = useState(false)

  const handleMouseLeave = useCallback((e: MouseEvent) => {
    if (!enabled || hasTriggered) return

    // Check if mouse is moving toward the top of the screen (exit intent)
    if (e.clientY <= threshold) {
      setHasTriggered(true)
      
      if (delay > 0) {
        setTimeout(() => {
          onExitIntent?.()
        }, delay)
      } else {
        onExitIntent?.()
      }
    }
  }, [enabled, hasTriggered, threshold, delay, onExitIntent])

  const handleBeforeUnload = useCallback((e: BeforeUnloadEvent) => {
    if (!enabled || hasTriggered) return

    setHasTriggered(true)
    onExitIntent?.()
  }, [enabled, hasTriggered, onExitIntent])

  const handleVisibilityChange = useCallback(() => {
    if (!enabled || hasTriggered) return

    if (document.visibilityState === 'hidden') {
      setHasTriggered(true)
      onExitIntent?.()
    }
  }, [enabled, hasTriggered, onExitIntent])

  useEffect(() => {
    if (!enabled) return

    // Mouse leave detection (desktop)
    document.addEventListener('mouseleave', handleMouseLeave)
    
    // Before unload detection (tab close/navigation)
    window.addEventListener('beforeunload', handleBeforeUnload)
    
    // Visibility change detection (mobile/tab switching)
    document.addEventListener('visibilitychange', handleVisibilityChange)

    return () => {
      document.removeEventListener('mouseleave', handleMouseLeave)
      window.removeEventListener('beforeunload', handleBeforeUnload)
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [handleMouseLeave, handleBeforeUnload, handleVisibilityChange, enabled])

  const reset = useCallback(() => {
    setHasTriggered(false)
  }, [])

  return {
    hasTriggered,
    reset
  }
}
