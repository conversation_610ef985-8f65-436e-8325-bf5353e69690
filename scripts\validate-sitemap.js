#!/usr/bin/env node

/**
 * Sitemap Validation and Cleaning Script
 * Fixes the <script> tag issue identified in the sitemap analysis
 */

const https = require('https');
const http = require('http');
const fs = require('fs');
const path = require('path');

async function fetchSitemap(url) {
  return new Promise((resolve, reject) => {
    const client = url.startsWith('https:') ? https : http;
    
    client.get(url, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve(data);
      });
    }).on('error', (err) => {
      reject(err);
    });
  });
}

function validateSitemapXML(xmlContent) {
  const issues = [];

  // Check for script tags (critical issue)
  if (xmlContent.includes('<script')) {
    const scriptMatches = xmlContent.match(/<script[^>]*>/g) || [];
    issues.push({
      type: 'critical',
      issue: 'Invalid <script> tags found in sitemap',
      count: scriptMatches.length,
      details: scriptMatches
    });
  }

  // Check for invalid date formats (2025 critical)
  const lastmodMatches = xmlContent.match(/<lastmod>(.*?)<\/lastmod>/g) || [];
  const invalidDates = [];
  lastmodMatches.forEach(match => {
    const dateStr = match.replace(/<\/?lastmod>/g, '');
    const date = new Date(dateStr);
    if (isNaN(date.getTime()) || !dateStr.includes('T') || !dateStr.includes('Z')) {
      invalidDates.push(dateStr);
    }
  });

  if (invalidDates.length > 0) {
    issues.push({
      type: 'error',
      issue: 'Invalid date format in <lastmod> tags (must be ISO 8601)',
      count: invalidDates.length,
      details: invalidDates.slice(0, 5)
    });
  }
  
  // Check for proper XML declaration
  if (!xmlContent.startsWith('<?xml version="1.0"')) {
    issues.push({
      type: 'error',
      issue: 'Missing or invalid XML declaration'
    });
  }
  
  // Check for proper urlset namespace
  if (!xmlContent.includes('xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"')) {
    issues.push({
      type: 'error',
      issue: 'Missing or invalid sitemap namespace'
    });
  }
  
  // Check for valid URLs (enhanced validation)
  const urlMatches = xmlContent.match(/<loc>(.*?)<\/loc>/g) || [];
  const invalidUrls = [];
  const longUrls = [];

  urlMatches.forEach(match => {
    const url = match.replace(/<\/?loc>/g, '');

    // Check URL format
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      invalidUrls.push(url);
    }

    // Check URL length (Google limit: 2048 characters)
    if (url.length > 2048) {
      longUrls.push(`${url.substring(0, 50)}... (${url.length} chars)`);
    }

    // Check for dangerous content
    if (url.includes('<script') || url.includes('javascript:') || url.includes(' ')) {
      invalidUrls.push(url);
    }
  });

  if (invalidUrls.length > 0) {
    issues.push({
      type: 'error',
      issue: 'Invalid or dangerous URLs found',
      details: invalidUrls.slice(0, 5)
    });
  }

  if (longUrls.length > 0) {
    issues.push({
      type: 'warning',
      issue: 'URLs exceeding 2048 character limit',
      details: longUrls
    });
  }
  
  // Check for duplicate URLs
  const urls = urlMatches.map(match => match.replace(/<\/?loc>/g, ''));
  const duplicates = urls.filter((url, index) => urls.indexOf(url) !== index);
  
  if (duplicates.length > 0) {
    issues.push({
      type: 'warning',
      issue: 'Duplicate URLs found',
      details: [...new Set(duplicates)]
    });
  }
  
  return {
    isValid: issues.filter(i => i.type === 'critical' || i.type === 'error').length === 0,
    issues,
    urlCount: urlMatches.length
  };
}

function cleanSitemapXML(xmlContent) {
  let cleaned = xmlContent;
  
  // Remove all script tags and their content
  cleaned = cleaned.replace(/<script[^>]*>.*?<\/script>/gs, '');
  cleaned = cleaned.replace(/<script[^>]*\/>/g, '');
  cleaned = cleaned.replace(/<script[^>]*>/g, '');
  
  // Remove any remaining script closing tags
  cleaned = cleaned.replace(/<\/script>/g, '');
  
  // Clean up extra whitespace and newlines
  cleaned = cleaned.replace(/\n\s*\n/g, '\n');
  cleaned = cleaned.trim();
  
  // Ensure proper XML structure
  if (!cleaned.startsWith('<?xml')) {
    cleaned = '<?xml version="1.0" encoding="UTF-8"?>\n' + cleaned;
  }
  
  return cleaned;
}

async function validateSitemap(sitemapUrl) {
  console.log('🔍 Validating GenLogic Sitemap');
  console.log('=' .repeat(50));
  console.log(`📍 URL: ${sitemapUrl}\n`);
  
  try {
    // Fetch sitemap
    console.log('📥 Fetching sitemap...');
    const xmlContent = await fetchSitemap(sitemapUrl);
    
    // Validate
    console.log('🔎 Analyzing sitemap structure...');
    const validation = validateSitemapXML(xmlContent);
    
    console.log(`📊 Found ${validation.urlCount} URLs in sitemap\n`);
    
    // Report issues
    if (validation.issues.length === 0) {
      console.log('✅ Sitemap is valid! No issues found.');
    } else {
      console.log('⚠️  Issues found:');
      
      validation.issues.forEach((issue, index) => {
        const icon = issue.type === 'critical' ? '🚨' : issue.type === 'error' ? '❌' : '⚠️';
        console.log(`\n${icon} ${issue.type.toUpperCase()}: ${issue.issue}`);
        
        if (issue.count) {
          console.log(`   Count: ${issue.count}`);
        }
        
        if (issue.details && issue.details.length > 0) {
          console.log('   Details:');
          issue.details.slice(0, 5).forEach(detail => {
            console.log(`     • ${detail}`);
          });
          if (issue.details.length > 5) {
            console.log(`     ... and ${issue.details.length - 5} more`);
          }
        }
      });
    }
    
    // Generate cleaned version if needed
    const criticalIssues = validation.issues.filter(i => i.type === 'critical');
    if (criticalIssues.length > 0) {
      console.log('\n🔧 Generating cleaned sitemap...');
      const cleanedXML = cleanSitemapXML(xmlContent);
      
      // Validate cleaned version
      const cleanedValidation = validateSitemapXML(cleanedXML);
      
      if (cleanedValidation.isValid) {
        console.log('✅ Cleaned sitemap is valid!');
        
        // Save cleaned version
        const outputPath = path.join(process.cwd(), 'sitemap-cleaned.xml');
        fs.writeFileSync(outputPath, cleanedXML);
        console.log(`💾 Cleaned sitemap saved to: ${outputPath}`);
        
        console.log('\n📋 Next Steps:');
        console.log('1. Replace your current sitemap with the cleaned version');
        console.log('2. Resubmit sitemap in Google Search Console');
        console.log('3. Check robots.txt includes: Sitemap: https://genlogic.io/sitemap.xml');
      } else {
        console.log('❌ Cleaned sitemap still has issues. Manual review required.');
      }
    }
    
    // Summary
    console.log('\n📈 Sitemap Health Summary:');
    console.log(`   URLs: ${validation.urlCount}`);
    console.log(`   Valid: ${validation.isValid ? 'Yes' : 'No'}`);
    console.log(`   Critical Issues: ${validation.issues.filter(i => i.type === 'critical').length}`);
    console.log(`   Errors: ${validation.issues.filter(i => i.type === 'error').length}`);
    console.log(`   Warnings: ${validation.issues.filter(i => i.type === 'warning').length}`);
    
    return validation;
    
  } catch (error) {
    console.error('❌ Failed to validate sitemap:', error.message);
    return null;
  }
}

async function checkRobotsTxt(baseUrl) {
  console.log('\n🤖 Checking robots.txt...');
  
  try {
    const robotsUrl = `${baseUrl}/robots.txt`;
    const robotsContent = await fetchSitemap(robotsUrl);
    
    const hasSitemap = robotsContent.includes('Sitemap:');
    const sitemapUrl = robotsContent.match(/Sitemap:\s*(.*)/)?.[1]?.trim();
    
    console.log(`📍 Robots.txt URL: ${robotsUrl}`);
    console.log(`✅ Contains sitemap directive: ${hasSitemap ? 'Yes' : 'No'}`);
    
    if (hasSitemap && sitemapUrl) {
      console.log(`🗺️  Sitemap URL: ${sitemapUrl}`);
    } else {
      console.log('⚠️  Add this line to robots.txt:');
      console.log(`   Sitemap: ${baseUrl}/sitemap.xml`);
    }
    
  } catch (error) {
    console.log(`❌ Could not fetch robots.txt: ${error.message}`);
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2);
  const sitemapUrl = args[0] || 'https://genlogic.io/sitemap.xml';
  const baseUrl = sitemapUrl.replace('/sitemap.xml', '');
  
  console.log('🗺️  GenLogic Sitemap Validator & Cleaner\n');
  
  // Validate sitemap
  const validation = await validateSitemap(sitemapUrl);
  
  // Check robots.txt
  await checkRobotsTxt(baseUrl);
  
  // Final recommendations
  console.log('\n💡 Recommendations:');
  console.log('   • Remove <script> tags from sitemap (critical for SEO)');
  console.log('   • Resubmit cleaned sitemap in Google Search Console');
  console.log('   • Use "URL Inspection Tool" for priority pages');
  console.log('   • Monitor indexing progress over 3-14 days');
  console.log('   • Do NOT delete Search Console property');
  
  if (validation && !validation.isValid) {
    process.exit(1);
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { validateSitemap, cleanSitemapXML };
