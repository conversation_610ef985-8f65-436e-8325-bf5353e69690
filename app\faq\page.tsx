'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { ChevronDown, ChevronUp, HelpCircle } from 'lucide-react'
import { SchemaMarkup } from '@/components/seo/SchemaMarkup'

// Metadata will be handled by Next.js App Router metadata API in a separate file

const faqs = [
  {
    category: 'Getting Started',
    questions: [
      {
        question: 'How quickly can I set up GenLogic for my UK business?',
        answer: 'Most UK businesses are up and running within 15 minutes. Our setup wizard guides you through connecting your calendar, customizing your messages, and configuring your automation rules. No technical knowledge required.'
      },
      {
        question: 'Do I need to change my current booking system?',
        answer: 'No! GenLogic integrates with your existing booking system, whether you use online booking, phone bookings, or a combination. We work alongside your current process to add automation without disruption.'
      },
      {
        question: 'What if my customers don\'t like automated messages?',
        answer: 'Our messages are designed to feel personal and helpful, not robotic. UK businesses report 94% customer satisfaction with our automated communications. Customers appreciate the convenience and reliability of automatic confirmations and reminders.'
      }
    ]
  },
  {
    category: 'Features & Functionality',
    questions: [
      {
        question: 'How does GenLogic reduce no-shows by 85%?',
        answer: 'We send strategic reminders at optimal times: 24 hours before (builds anticipation), 2 hours before (final reminder), and immediate confirmation when booked. This proven sequence dramatically reduces no-shows for UK businesses.'
      },
      {
        question: 'Can I customize the messages for my business?',
        answer: 'Absolutely! Every message can be personalized with your business name, branding, and tone. You can customize confirmations, reminders, follow-ups, and even add special offers or instructions specific to your services.'
      },
      {
        question: 'Does GenLogic work with SMS and email?',
        answer: 'Yes, GenLogic sends both SMS and email automatically. SMS has higher open rates (98% vs 20% for email), but email allows for richer content. You can choose which channels to use for different message types.'
      },
      {
        question: 'What analytics and reporting do I get?',
        answer: 'You get detailed insights including no-show rates, message open rates, customer response patterns, revenue impact, and time saved. Perfect for understanding your business performance and ROI.'
      }
    ]
  },
  {
    category: 'Pricing & Plans',
    questions: [
      {
        question: 'How much does GenLogic cost for UK businesses?',
        answer: 'Plans start at £29/month for small businesses, £49/month for growing businesses, and £99/month for larger operations. All plans include unlimited messages, full automation, and UK-based support. No setup fees or hidden costs.'
      },
      {
        question: 'Is there a free trial available?',
        answer: 'Yes! We offer a 14-day free trial with full access to all features. No credit card required to start. Most UK businesses see results within the first week of using GenLogic.'
      },
      {
        question: 'Are there any additional costs for SMS messages?',
        answer: 'No hidden SMS costs! All plans include unlimited SMS and email messages to UK numbers. International messages may incur additional charges, but we\'ll always notify you first.'
      },
      {
        question: 'Can I cancel anytime?',
        answer: 'Yes, you can cancel anytime with no penalties or cancellation fees. We\'re confident you\'ll love the time savings and revenue growth, but there\'s no long-term commitment required.'
      }
    ]
  },
  {
    category: 'Technical & Support',
    questions: [
      {
        question: 'What support do you provide for UK businesses?',
        answer: 'We provide UK-based email and phone support during business hours (9am-6pm GMT), plus comprehensive online resources. Our team understands UK business needs and regulations, including GDPR compliance.'
      },
      {
        question: 'Is GenLogic GDPR compliant?',
        answer: 'Yes, GenLogic is fully GDPR compliant. We help UK businesses manage customer consent, provide data export tools, and ensure all customer communications meet UK data protection requirements.'
      },
      {
        question: 'What happens to my data if I cancel?',
        answer: 'Your data remains yours. We provide easy export tools and will securely delete your data upon request, in compliance with UK GDPR regulations. We never sell or share your customer data.'
      },
      {
        question: 'Do you integrate with popular UK business tools?',
        answer: 'Yes! We integrate with popular UK tools including Calendly, Acuity, Square, Stripe, Mailchimp, and many others. If you need a specific integration, our team can often accommodate custom requests.'
      }
    ]
  },
  {
    category: 'Results & ROI',
    questions: [
      {
        question: 'How much time will GenLogic save my business?',
        answer: 'UK businesses typically save 20+ hours per week on manual admin tasks. This includes time spent on booking confirmations, reminder calls, follow-up messages, and chasing no-show customers.'
      },
      {
        question: 'What ROI can I expect from GenLogic?',
        answer: 'Most UK businesses see 400%+ ROI within 3 months. The combination of reduced no-shows (saving lost revenue), increased repeat bookings, and time savings creates significant value for your business.'
      },
      {
        question: 'How quickly will I see results?',
        answer: 'You\'ll see immediate time savings from day one. No-show reduction typically improves within the first week, and most businesses report significant revenue impact within 30 days of implementation.'
      }
    ]
  }
]

export default function FAQPage() {
  const [openItems, setOpenItems] = useState<string[]>([])

  const toggleItem = (id: string) => {
    setOpenItems(prev => 
      prev.includes(id) 
        ? prev.filter(item => item !== id)
        : [...prev, id]
    )
  }

  // Prepare FAQ data for schema markup
  const allFAQs = faqs.flatMap(category => 
    category.questions.map(q => ({
      question: q.question,
      answer: q.answer
    }))
  )

  return (
    <>
      <SchemaMarkup type="faq" data={{ faqs: allFAQs }} />
      
      <div className="relative w-full overflow-hidden">
        {/* Header */}
        <section className="header-spacing py-20 relative">
          <div className="container relative z-10 mx-auto px-4 md:px-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, ease: 'easeOut' }}
              className="mx-auto max-w-4xl text-center"
            >
              <div className="bg-background border border-primary-200/20 dark:border-primary-800/20 rounded-3xl p-12 shadow-lg backdrop-blur-sm">
                <HelpCircle className="w-16 h-16 text-primary-700 dark:text-primary-400 mx-auto mb-6" />
                
                <h1 className="text-4xl font-bold text-foreground sm:text-5xl mb-6">
                  Frequently Asked <span className="text-primary-700 dark:text-primary-400">Questions</span>
                </h1>
                
                <p className="text-xl text-muted-foreground leading-relaxed">
                  <strong className="text-foreground">Everything you need to know</strong> about GenLogic business automation for UK businesses. Can't find your answer? <a href="/contact" className="text-primary-700 dark:text-primary-400 hover:underline">Contact our UK team</a>.
                </p>
              </div>
            </motion.div>
          </div>
        </section>

        {/* FAQ Content */}
        <section className="py-20">
          <div className="container mx-auto px-4 md:px-6">
            <div className="max-w-4xl mx-auto">
              {faqs.map((category, categoryIndex) => (
                <motion.div
                  key={category.category}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: categoryIndex * 0.1 }}
                  viewport={{ once: true }}
                  className="mb-12"
                >
                  <h2 className="text-2xl font-bold text-foreground mb-6 pb-3 border-b border-primary-200/30 dark:border-primary-800/30">
                    {category.category}
                  </h2>
                  
                  <div className="space-y-4">
                    {category.questions.map((faq, index) => {
                      const itemId = `${categoryIndex}-${index}`
                      const isOpen = openItems.includes(itemId)
                      
                      return (
                        <div
                          key={index}
                          className="bg-background border border-primary-200/20 dark:border-primary-800/20 rounded-2xl shadow-sm overflow-hidden"
                        >
                          <button
                            onClick={() => toggleItem(itemId)}
                            className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-primary-50/50 dark:hover:bg-primary-950/30 transition-colors duration-200"
                          >
                            <h3 className="text-lg font-semibold text-foreground pr-4">
                              {faq.question}
                            </h3>
                            {isOpen ? (
                              <ChevronUp className="w-5 h-5 text-primary-700 dark:text-primary-400 flex-shrink-0" />
                            ) : (
                              <ChevronDown className="w-5 h-5 text-primary-700 dark:text-primary-400 flex-shrink-0" />
                            )}
                          </button>
                          
                          <AnimatePresence>
                            {isOpen && (
                              <motion.div
                                initial={{ height: 0, opacity: 0 }}
                                animate={{ height: 'auto', opacity: 1 }}
                                exit={{ height: 0, opacity: 0 }}
                                transition={{ duration: 0.3, ease: 'easeInOut' }}
                              >
                                <div className="px-6 pb-4 text-muted-foreground leading-relaxed">
                                  {faq.answer}
                                </div>
                              </motion.div>
                            )}
                          </AnimatePresence>
                        </div>
                      )
                    })}
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>
      </div>
    </>
  )
}
