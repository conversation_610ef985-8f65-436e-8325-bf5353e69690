import { NextResponse } from 'next/server'
import { getAllBlogPosts } from '../blog/posts'

export async function GET() {
  const posts = getAllBlogPosts()
  const siteUrl = 'https://genlogic.io'
  
  const rssXml = `<?xml version="1.0" encoding="UTF-8"?>
<rss version="2.0" xmlns:atom="http://www.w3.org/2005/Atom" xmlns:content="http://purl.org/rss/1.0/modules/content/">
  <channel>
    <title>GenLogic Blog - Business Automation for UK Local Businesses</title>
    <description>Stop working late evenings. Learn how UK business owners are saving 20+ hours weekly with business automation. Get your evenings back with GenLogic.</description>
    <link>${siteUrl}/blog</link>
    <atom:link href="${siteUrl}/rss" rel="self" type="application/rss+xml"/>
    <language>en-GB</language>
    <lastBuildDate>${new Date().toUTCString()}</lastBuildDate>
    <pubDate>${new Date().toUTCString()}</pubDate>
    <ttl>60</ttl>
    <image>
      <url>${siteUrl}/logo/genlogic-logo.png</url>
      <title>GenLogic Blog</title>
      <link>${siteUrl}/blog</link>
      <width>144</width>
      <height>144</height>
      <description>GenLogic - Business Automation for UK Local Businesses</description>
    </image>
    <managingEditor><EMAIL> (Chedi Rach)</managingEditor>
    <webMaster><EMAIL> (GenLogic Team)</webMaster>
    <category>Business</category>
    <category>Automation</category>
    <category>Local Business</category>
    <category>UK Business</category>
    <copyright>Copyright ${new Date().getFullYear()} GenLogic. All rights reserved.</copyright>
    <docs>https://www.rssboard.org/rss-specification</docs>
    <generator>GenLogic Next.js RSS Generator</generator>
${posts.map(post => {
  const postUrl = `${siteUrl}/blog/${post.slug}`
  const imageUrl = post.image ? `${siteUrl}${post.image.replace('.svg', '.webp')}` : `${siteUrl}/logo/genlogic-logo.png`
  
  // Convert React content to plain text description
  const plainTextExcerpt = post.excerpt
    .replace(/<[^>]*>/g, '') // Remove HTML tags
    .replace(/&[^;]+;/g, '') // Remove HTML entities
    .trim()

  return `    <item>
      <title><![CDATA[${post.title}]]></title>
      <description><![CDATA[${plainTextExcerpt}]]></description>
      <link>${postUrl}</link>
      <guid isPermaLink="true">${postUrl}</guid>
      <pubDate>${new Date(post.publishedAt).toUTCString()}</pubDate>
      <author><EMAIL> (${post.author.name})</author>
      <category><![CDATA[${post.category}]]></category>
${post.tags.map(tag => `      <category><![CDATA[${tag}]]></category>`).join('\n')}
      <enclosure url="${imageUrl}" type="image/webp" length="0"/>
      <content:encoded><![CDATA[
        <div>
          <img src="${imageUrl}" alt="${post.title}" style="width: 100%; max-width: 600px; height: auto; margin-bottom: 20px;" />
          <p><strong>Reading Time:</strong> ${post.readingTime}</p>
          <p><strong>Category:</strong> ${post.category}</p>
          <p>${plainTextExcerpt}</p>
          <p><a href="${postUrl}" target="_blank">Read the full article on GenLogic.io</a></p>
          <hr />
          <p><strong>About GenLogic:</strong> We help UK business owners save 20+ hours weekly through business automation. Stop working late evenings and get your life back.</p>
          <p><strong>Contact:</strong> +44 7401 137621 | <EMAIL> | <a href="${siteUrl}">genlogic.io</a></p>
        </div>
      ]]></content:encoded>
    </item>`
}).join('\n')}
  </channel>
</rss>`

  return new NextResponse(rssXml, {
    headers: {
      'Content-Type': 'application/rss+xml; charset=utf-8',
      'Cache-Control': 'public, max-age=3600, s-maxage=3600', // Cache for 1 hour
    },
  })
}
