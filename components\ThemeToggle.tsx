'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Sun, Moon } from 'lucide-react'

export default function ThemeToggle() {
  const [theme, setTheme] = useState<'light' | 'dark'>('light')
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
    // Check for saved theme or system preference
    const savedTheme = localStorage.getItem('genlogic-theme') as 'light' | 'dark'
    const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
    const initialTheme = savedTheme || systemTheme
    setTheme(initialTheme)

    // Apply theme to document
    document.documentElement.classList.remove('light', 'dark')
    document.documentElement.classList.add(initialTheme)
  }, [])

  const toggleTheme = () => {
    const newTheme = theme === 'light' ? 'dark' : 'light'
    setTheme(newTheme)
    localStorage.setItem('genlogic-theme', newTheme)
    document.documentElement.classList.remove('light', 'dark')
    document.documentElement.classList.add(newTheme)
  }

  if (!mounted) {
    return null // Prevent hydration mismatch
  }

  return (
    <motion.button
      onClick={toggleTheme}
      data-theme-toggle
      className="relative inline-flex items-center justify-center w-12 h-6 bg-gray-200 dark:bg-gray-700 rounded-full transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800"
      whileTap={{ scale: 0.95 }}
      aria-label={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}
    >
        {/* Toggle background */}
        <div className="absolute inset-0 rounded-full bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600"></div>

        {/* Toggle circle */}
        <motion.div
          className="relative z-10 flex items-center justify-center w-5 h-5 bg-white dark:bg-gray-900 rounded-full shadow-md"
          animate={{
            x: theme === 'light' ? -9 : 9,
          }}
          transition={{
            type: "spring",
            stiffness: 500,
            damping: 30
          }}
        >
          {/* Icons */}
          <motion.div
            animate={{
              opacity: theme === 'light' ? 1 : 0,
              scale: theme === 'light' ? 1 : 0.5,
            }}
            transition={{ duration: 0.2 }}
            className="absolute"
          >
            <Sun className="w-3 h-3 text-yellow-500" />
          </motion.div>

          <motion.div
            animate={{
              opacity: theme === 'dark' ? 1 : 0,
              scale: theme === 'dark' ? 1 : 0.5,
            }}
            transition={{ duration: 0.2 }}
            className="absolute"
          >
            <Moon className="w-3 h-3 text-blue-400" />
          </motion.div>
        </motion.div>
      </motion.button>
    )
}
