import { Metadata } from 'next'
import PricingPsychologyRevolutionClient from './PricingPsychologyRevolutionClient'

export const metadata: Metadata = {
  title: 'The Pricing Psychology Revolution | GenLogic Academy',
  description: 'Why your pricing strategy is leaving money on the table and the value ladder that doubles revenue. Learn the psychology of pricing and automated value optimization.',
  keywords: 'pricing psychology, value ladder, pricing strategy, revenue optimization, UK business pricing, pricing automation',
  openGraph: {
    title: 'The Pricing Psychology Revolution',
    description: 'Why your pricing strategy is leaving money on the table and the value ladder that doubles revenue. Learn the psychology of pricing and automated value optimization.',
    type: 'article',
    url: 'https://genlogic.io/learn/fundamentals/pricing-psychology-revolution',
    images: [
      {
        url: '/academy-images/og-pricing-psychology-revolution.webp',
        width: 1200,
        height: 630,
        alt: 'The Pricing Psychology Revolution',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'The Pricing Psychology Revolution',
    description: 'Why your pricing strategy is leaving money on the table and the value ladder that doubles revenue. Learn the psychology of pricing and automated value optimization.',
    images: ['/academy-images/og-pricing-psychology-revolution.webp'],
  },
}

export default function PricingPsychologyRevolutionPage() {
  return <PricingPsychologyRevolutionClient />
}
