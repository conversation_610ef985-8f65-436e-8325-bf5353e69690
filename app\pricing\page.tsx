'use client'

import Link from 'next/link'
import { motion } from 'framer-motion'
import { CheckCircle, Star, ArrowRight } from 'lucide-react'
import type { Metadata } from 'next'

export default function PricingPage() {

  return (
    <div className="relative w-full overflow-hidden">
      {/* Pricing Schema for SEO */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Product",
            "name": "GenLogic Business Automation Platform",
            "description": "Complete business automation software for UK local businesses. Automate bookings, reduce no-shows, and save time.",
            "brand": {
              "@type": "Brand",
              "name": "GenLogic"
            },
            "offers": [
              {
                "@type": "Offer",
                "name": "Silver Plan",
                "description": "Perfect for small businesses starting with automation",
                "price": "120",
                "priceCurrency": "GBP",
                "priceSpecification": {
                  "@type": "UnitPriceSpecification",
                  "price": "120",
                  "priceCurrency": "GBP",
                  "unitText": "MONTH"
                },
                "availability": "https://schema.org/InStock",
                "url": "https://genlogic.io/pricing",
                "seller": {
                  "@type": "Organization",
                  "name": "GenLogic"
                }
              },
              {
                "@type": "Offer",
                "name": "Gold Plan",
                "description": "Ideal for growing businesses needing advanced features",
                "price": "420",
                "priceCurrency": "GBP",
                "priceSpecification": {
                  "@type": "UnitPriceSpecification",
                  "price": "420",
                  "priceCurrency": "GBP",
                  "unitText": "MONTH"
                },
                "availability": "https://schema.org/InStock",
                "url": "https://genlogic.io/pricing",
                "seller": {
                  "@type": "Organization",
                  "name": "GenLogic"
                }
              },
              {
                "@type": "Offer",
                "name": "Platinum Plan",
                "description": "Enterprise-grade solution for maximum automation",
                "priceSpecification": {
                  "@type": "UnitPriceSpecification",
                  "priceCurrency": "GBP",
                  "unitText": "MONTH"
                },
                "availability": "https://schema.org/InStock",
                "url": "https://genlogic.io/contact",
                "seller": {
                  "@type": "Organization",
                  "name": "GenLogic"
                }
              }
            ]
          })
        }}
      />
      {/* Header */}
      <section className="header-spacing py-20 relative">
        <div className="container relative z-10 mx-auto px-4 md:px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: 'easeOut' }}
            className="mx-auto max-w-4xl text-center"
          >
            <div className="bg-background border border-primary-200/20 dark:border-primary-800/20 rounded-3xl p-12 shadow-lg backdrop-blur-sm">
              <h1 className="text-4xl font-bold text-foreground sm:text-5xl md:text-6xl mb-6">
                Complete <span className="text-primary-700 dark:text-primary-400">Business Automation</span> Platform
              </h1>
              <p className="text-xl text-muted-foreground leading-relaxed mb-4">
                <strong className="text-foreground">Everything you need to automate your business in one platform.</strong> CRM, marketing automation, appointment scheduling, reputation management, and more.
              </p>
              <p className="text-lg text-muted-foreground leading-relaxed mb-6">
                No setup fees. No contracts. Cancel anytime. <strong className="text-foreground">Start automating today.</strong>
              </p>
              <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
                <Star className="w-4 h-4 text-yellow-400 fill-current" />
                <span>200+ UK businesses automated and growing</span>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Pricing Cards */}
      <section className="py-20">
        <div className="container mx-auto px-4 md:px-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">

            {/* Silver Plan */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
              className="bg-background border border-primary-200/20 dark:border-primary-800/20 rounded-3xl p-8 shadow-lg hover:shadow-xl transition-all duration-300"
            >
              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-foreground mb-4">Silver Plan</h3>
                <div className="mb-6">
                  <span className="text-4xl font-bold text-primary-700 dark:text-primary-400">£120</span>
                  <span className="text-muted-foreground">/month</span>
                </div>
                <p className="text-muted-foreground mb-2">Perfect for small businesses starting with automation</p>
                <p className="text-sm text-foreground font-medium">Includes 10 hours of expert setup & support</p>
              </div>

              <ul className="space-y-4 mb-8">
                <li className="flex items-center">
                  <CheckCircle className="w-5 h-5 text-primary-600 dark:text-primary-400 mr-3 flex-shrink-0" />
                  <span className="text-foreground"><strong>45-minute monthly sales consultation</strong></span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="w-5 h-5 text-primary-600 dark:text-primary-400 mr-3 flex-shrink-0" />
                  <span className="text-foreground"><strong>10 hours monthly developer support</strong></span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="w-5 h-5 text-primary-600 dark:text-primary-400 mr-3 flex-shrink-0" />
                  <span className="text-foreground">Automated Lead Management</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="w-5 h-5 text-primary-600 dark:text-primary-400 mr-3 flex-shrink-0" />
                  <span className="text-foreground">Appointment Scheduling</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="w-5 h-5 text-primary-600 dark:text-primary-400 mr-3 flex-shrink-0" />
                  <span className="text-foreground">Email & SMS Campaigns</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="w-5 h-5 text-primary-600 dark:text-primary-400 mr-3 flex-shrink-0" />
                  <span className="text-foreground">Basic Reputation Management</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="w-5 h-5 text-primary-600 dark:text-primary-400 mr-3 flex-shrink-0" />
                  <span className="text-foreground">1 User Account</span>
                </li>
              </ul>

              <Link
                href="/demo"
                className="w-full inline-flex items-center justify-center px-6 py-3 border-2 border-primary-200 dark:border-primary-800 text-primary-700 dark:text-primary-400 rounded-2xl hover:border-primary-300 dark:hover:border-primary-700 hover:bg-primary-50 dark:hover:bg-primary-900/20 transition-all duration-300 font-medium"
              >
                Get Started
              </Link>
            </motion.div>

            {/* Gold Plan - Most Popular */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
              className="bg-primary-50 dark:bg-primary-950/30 border-2 border-primary-300 dark:border-primary-700 rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-300 relative scale-105"
            >
              <div className="text-center -mt-4 mb-6">
                <span className="bg-primary-700 dark:bg-primary-600 text-white px-6 py-2 rounded-full text-sm font-semibold shadow-lg inline-block">
                  Most Popular
                </span>
              </div>

              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-foreground mb-4">Gold Plan</h3>
                <div className="mb-6">
                  <span className="text-4xl font-bold text-primary-700 dark:text-primary-400">£420</span>
                  <span className="text-muted-foreground">/month</span>
                </div>
                <p className="text-muted-foreground mb-2">Ideal for growing businesses needing advanced features</p>
                <p className="text-sm text-foreground font-medium">Includes 20 hours of expert development & optimization</p>
              </div>

              <ul className="space-y-4 mb-8">
                <li className="flex items-center">
                  <CheckCircle className="w-5 h-5 text-primary-600 dark:text-primary-400 mr-3 flex-shrink-0" />
                  <span className="text-foreground"><strong>Weekly sales strategy sessions</strong></span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="w-5 h-5 text-primary-600 dark:text-primary-400 mr-3 flex-shrink-0" />
                  <span className="text-foreground"><strong>20 hours monthly developer support</strong></span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="w-5 h-5 text-primary-600 dark:text-primary-400 mr-3 flex-shrink-0" />
                  <span className="text-foreground">Everything in Silver plus:</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="w-5 h-5 text-primary-600 dark:text-primary-400 mr-3 flex-shrink-0" />
                  <span className="text-foreground">Advanced Marketing Automation</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="w-5 h-5 text-primary-600 dark:text-primary-400 mr-3 flex-shrink-0" />
                  <span className="text-foreground">Custom Reporting & Analytics</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="w-5 h-5 text-primary-600 dark:text-primary-400 mr-3 flex-shrink-0" />
                  <span className="text-foreground">Social Media Planner</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="w-5 h-5 text-primary-600 dark:text-primary-400 mr-3 flex-shrink-0" />
                  <span className="text-foreground">Up to 5 User Accounts</span>
                </li>
              </ul>

              <Link
                href="/demo"
                className="w-full inline-flex items-center justify-center px-6 py-3 bg-primary-700 hover:bg-primary-800 dark:bg-primary-600 dark:hover:bg-primary-700 text-white rounded-2xl transition-all duration-300 font-medium shadow-lg hover:shadow-xl"
              >
                Get Started
                <ArrowRight className="w-4 h-4 ml-2" />
              </Link>
            </motion.div>

            {/* Platinum Plan */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              viewport={{ once: true }}
              className="bg-background border border-primary-200/20 dark:border-primary-800/20 rounded-3xl p-8 shadow-lg hover:shadow-xl transition-all duration-300"
            >
              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-foreground mb-4">Platinum Plan</h3>
                <div className="mb-6">
                  <span className="text-4xl font-bold text-primary-700 dark:text-primary-400">Contact Sales</span>
                </div>
                <p className="text-muted-foreground mb-2">Enterprise-grade solution for maximum automation</p>
                <p className="text-sm text-foreground font-medium">Custom solution tailored to your business needs</p>
              </div>

              <ul className="space-y-4 mb-8">
                <li className="flex items-center">
                  <CheckCircle className="w-5 h-5 text-primary-600 dark:text-primary-400 mr-3 flex-shrink-0" />
                  <span className="text-foreground">Custom enterprise solution</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="w-5 h-5 text-primary-600 dark:text-primary-400 mr-3 flex-shrink-0" />
                  <span className="text-foreground">Unlimited automation features</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="w-5 h-5 text-primary-600 dark:text-primary-400 mr-3 flex-shrink-0" />
                  <span className="text-foreground">Dedicated account manager</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="w-5 h-5 text-primary-600 dark:text-primary-400 mr-3 flex-shrink-0" />
                  <span className="text-foreground">Priority support & training</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="w-5 h-5 text-primary-600 dark:text-primary-400 mr-3 flex-shrink-0" />
                  <span className="text-foreground">Custom integrations & workflows</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="w-5 h-5 text-primary-600 dark:text-primary-400 mr-3 flex-shrink-0" />
                  <span className="text-foreground">Tailored to your business needs</span>
                </li>
              </ul>

              <Link
                href="/contact"
                className="w-full inline-flex items-center justify-center px-6 py-3 border-2 border-primary-200 dark:border-primary-800 text-primary-700 dark:text-primary-400 rounded-2xl hover:border-primary-300 dark:hover:border-primary-700 hover:bg-primary-50 dark:hover:bg-primary-900/20 transition-all duration-300 font-medium"
              >
                Contact Sales
              </Link>
            </motion.div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20">
        <div className="container mx-auto px-4 md:px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: 'easeOut' }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <div className="bg-background border border-primary-200/20 dark:border-primary-800/20 rounded-3xl p-8 shadow-lg max-w-3xl mx-auto">
              <h2 className="text-3xl font-bold text-foreground mb-4">
                We Know You Have <span className="text-primary-700 dark:text-primary-400">Questions</span>
              </h2>
              <p className="text-lg text-muted-foreground">
                <strong className="text-foreground">You've been burned by software promises before.</strong> Here are the honest answers to what UK business owners ask us most.
              </p>
            </div>
          </motion.div>

          <div className="max-w-4xl mx-auto space-y-6">
            {[
              {
                question: "Will this actually save me time, or is it just another system to manage?",
                answer: "We get it - you've tried software before that promised the world and delivered headaches. GenLogic is different. Our customers save 20+ hours weekly within the first month. Sarah from Manchester went from working until 9pm to leaving at 6pm. No complex setup, no learning curve - just results."
              },
              {
                question: "What if I try it and it doesn't work for my business?",
                answer: "You can cancel anytime, no questions asked. But here's the thing - 94% of our customers stay because they can't imagine going back to manual admin work. Try it for 14 days free. If you're not saving hours of time weekly, we don't deserve your money."
              },
              {
                question: "I'm not tech-savvy. Will I be able to use this?",
                answer: "If you can send a text message, you can use GenLogic. We built it for busy business owners, not tech experts. Our UK-based team will set everything up for you during your free trial. Most customers are up and running in under 10 minutes."
              },
              {
                question: "How do I know this will work with my specific business?",
                answer: "We work with 500+ UK businesses - from hair salons in Manchester to plumbers in Edinburgh. Every business is different, but the pain of chasing no-shows and managing admin is universal. Book a free demo and we'll show you exactly how it works for businesses like yours."
              }
            ].map((faq, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-background border border-primary-200/20 dark:border-primary-800/20 rounded-2xl p-6 shadow-lg"
              >
                <h3 className="text-lg font-semibold text-foreground mb-3">
                  {faq.question}
                </h3>
                <p className="text-muted-foreground leading-relaxed">
                  {faq.answer}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20">
        <div className="container mx-auto px-4 md:px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: 'easeOut' }}
            viewport={{ once: true }}
            className="text-center"
          >
            <div className="bg-primary-50 dark:bg-primary-950/30 border border-primary-200/30 dark:border-primary-800/30 rounded-3xl p-12 shadow-lg max-w-4xl mx-auto">
              <h2 className="text-3xl font-bold text-foreground sm:text-4xl mb-6">
                Stop Working <span className="text-primary-700 dark:text-primary-400">Late Tonight</span>
              </h2>
              <p className="text-xl text-muted-foreground mb-4">
                <strong className="text-foreground">You could be home by 6pm tomorrow.</strong> Start your free trial now and see why 500+ UK business owners trust GenLogic with their time.
              </p>
              <p className="text-lg text-muted-foreground mb-8">
                No credit card required. No setup fees. No risk - just results.
              </p>
              <Link
                href="/demo"
                className="inline-flex items-center justify-center px-8 py-4 bg-primary-700 hover:bg-primary-800 dark:bg-primary-600 dark:hover:bg-primary-700 text-white rounded-2xl transition-all duration-300 font-medium shadow-lg hover:shadow-xl text-lg"
              >
                Start Your Free Trial
                <ArrowRight className="w-5 h-5 ml-2" />
              </Link>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  )
}
