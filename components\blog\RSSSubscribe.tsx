'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { Rss, Copy, Check, Mail, TrendingUp, Users, Zap } from 'lucide-react'

export default function RSSSubscribe() {
  const [copied, setCopied] = useState(false)
  const rssUrl = 'https://genlogic.io/rss'

  const copyRSSUrl = async () => {
    try {
      await navigator.clipboard.writeText(rssUrl)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('Failed to copy RSS URL:', err)
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true }}
      className="bg-gradient-to-br from-purple-50 via-blue-50 to-green-50 dark:from-purple-950/20 dark:via-blue-950/20 dark:to-green-950/20 rounded-3xl p-8 border border-purple-200/50 dark:border-purple-800/30 shadow-xl"
    >
      <div className="text-center">
        {/* Icon with gradient background */}
        <div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-purple-500 to-blue-600 rounded-3xl flex items-center justify-center shadow-lg">
          <TrendingUp className="w-10 h-10 text-white" />
        </div>

        <h3 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4">
          Get <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-blue-600">Conversion Insights</span> First
        </h3>

        <p className="text-xl text-gray-700 dark:text-gray-300 mb-8 max-w-2xl mx-auto">
          Join 2,500+ UK business owners getting weekly tips to convert more prospects into customers.
          <strong className="text-purple-600 dark:text-purple-400"> No spam, just results.</strong>
        </p>

        {/* Stats */}
        <div className="grid grid-cols-3 gap-4 mb-8">
          <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-4 border border-gray-200/50 dark:border-gray-700/50">
            <div className="flex items-center justify-center mb-2">
              <Users className="w-5 h-5 text-purple-600 dark:text-purple-400" />
            </div>
            <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">2.5K+</div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Subscribers</div>
          </div>
          <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-4 border border-gray-200/50 dark:border-gray-700/50">
            <div className="flex items-center justify-center mb-2">
              <Zap className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            </div>
            <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">89%</div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Conversion Rate</div>
          </div>
          <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-4 border border-gray-200/50 dark:border-gray-700/50">
            <div className="flex items-center justify-center mb-2">
              <TrendingUp className="w-5 h-5 text-green-600 dark:text-green-400" />
            </div>
            <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">£50K+</div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Avg Revenue</div>
          </div>
        </div>

        <div className="space-y-6">
          {/* Main Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/rss"
              target="_blank"
              rel="noopener noreferrer"
              className="group inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-bold rounded-2xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
            >
              <Rss className="w-5 h-5 mr-3 group-hover:animate-pulse" />
              Get RSS Feed
            </a>

            <a
              href="mailto:<EMAIL>?subject=Subscribe to GenLogic Conversion Tips"
              className="group inline-flex items-center justify-center px-8 py-4 bg-white/90 hover:bg-white dark:bg-gray-800/90 dark:hover:bg-gray-800 text-gray-900 dark:text-gray-100 font-bold rounded-2xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-700"
            >
              <Mail className="w-5 h-5 mr-3 group-hover:animate-bounce" />
              Email Updates
            </a>
          </div>

          {/* RSS URL Display - Collapsible */}
          <div className="bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm rounded-2xl p-4 border border-gray-200/50 dark:border-gray-700/50">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">RSS Feed URL:</p>
                <code className="text-sm text-purple-600 dark:text-purple-400 font-mono bg-gray-100 dark:bg-gray-700 px-3 py-1 rounded-lg">
                  {rssUrl}
                </code>
              </div>
              <button
                onClick={copyRSSUrl}
                className="ml-4 p-3 text-purple-600 dark:text-purple-400 hover:bg-purple-50 dark:hover:bg-purple-950/30 rounded-xl transition-all duration-200 transform hover:scale-110"
                title="Copy RSS URL"
              >
                {copied ? (
                  <Check className="w-5 h-5 text-green-600" />
                ) : (
                  <Copy className="w-5 h-5" />
                )}
              </button>
            </div>
          </div>

          {/* Popular RSS Readers - Modern Design */}
          <div className="bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-800/50 dark:to-blue-950/20 rounded-2xl p-6 border border-gray-200/50 dark:border-gray-700/50">
            <p className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 text-center">
              🚀 Popular RSS Readers
            </p>
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-3">
              <a
                href="https://feedly.com"
                target="_blank"
                rel="noopener noreferrer"
                className="group bg-white dark:bg-gray-800 hover:bg-green-50 dark:hover:bg-green-950/20 px-4 py-3 rounded-xl border border-gray-200 dark:border-gray-700 text-center transition-all duration-200 transform hover:scale-105"
              >
                <div className="text-sm font-medium text-gray-900 dark:text-gray-100 group-hover:text-green-600 dark:group-hover:text-green-400">
                  Feedly
                </div>
              </a>
              <a
                href="https://inoreader.com"
                target="_blank"
                rel="noopener noreferrer"
                className="group bg-white dark:bg-gray-800 hover:bg-blue-50 dark:hover:bg-blue-950/20 px-4 py-3 rounded-xl border border-gray-200 dark:border-gray-700 text-center transition-all duration-200 transform hover:scale-105"
              >
                <div className="text-sm font-medium text-gray-900 dark:text-gray-100 group-hover:text-blue-600 dark:group-hover:text-blue-400">
                  Inoreader
                </div>
              </a>
              <a
                href="https://newsblur.com"
                target="_blank"
                rel="noopener noreferrer"
                className="group bg-white dark:bg-gray-800 hover:bg-orange-50 dark:hover:bg-orange-950/20 px-4 py-3 rounded-xl border border-gray-200 dark:border-gray-700 text-center transition-all duration-200 transform hover:scale-105"
              >
                <div className="text-sm font-medium text-gray-900 dark:text-gray-100 group-hover:text-orange-600 dark:group-hover:text-orange-400">
                  NewsBlur
                </div>
              </a>
              <a
                href="https://theoldreader.com"
                target="_blank"
                rel="noopener noreferrer"
                className="group bg-white dark:bg-gray-800 hover:bg-purple-50 dark:hover:bg-purple-950/20 px-4 py-3 rounded-xl border border-gray-200 dark:border-gray-700 text-center transition-all duration-200 transform hover:scale-105"
              >
                <div className="text-sm font-medium text-gray-900 dark:text-gray-100 group-hover:text-purple-600 dark:group-hover:text-purple-400">
                  Old Reader
                </div>
              </a>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  )
}
