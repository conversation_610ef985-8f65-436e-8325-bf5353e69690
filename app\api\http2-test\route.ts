import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  // Get protocol information from headers
  const protocol = request.headers.get('x-forwarded-proto') || 'unknown'
  const host = request.headers.get('host') || 'unknown'
  const userAgent = request.headers.get('user-agent') || 'unknown'
  
  // Check for HTTP/2 indicators
  const http2Indicators = {
    protocol: protocol,
    host: host,
    isHTTPS: protocol === 'https',
    hasHTTP2Headers: request.headers.has(':method') || request.headers.has(':path'),
    vercelRegion: request.headers.get('x-vercel-id') || 'unknown',
    cfRay: request.headers.get('cf-ray') || 'none',
    connection: request.headers.get('connection') || 'unknown'
  }
  
  // Response with HTTP/2 information
  const response = {
    timestamp: new Date().toISOString(),
    http2Status: {
      isHTTPS: http2Indicators.isHTTPS,
      protocol: http2Indicators.protocol,
      likelyHTTP2: http2Indicators.isHTTPS && (
        http2Indicators.hasHTTP2Headers ||
        http2Indicators.vercelRegion !== 'unknown'
      ),
      vercelHosted: http2Indicators.vercelRegion !== 'unknown',
      region: http2Indicators.vercelRegion
    },
    headers: {
      protocol: http2Indicators.protocol,
      host: http2Indicators.host,
      connection: http2Indicators.connection,
      vercelId: http2Indicators.vercelRegion,
      cfRay: http2Indicators.cfRay
    },
    recommendations: [] as string[]
  }
  
  // Add recommendations based on findings
  if (!http2Indicators.isHTTPS) {
    response.recommendations.push('Enable HTTPS to support HTTP/2')
  }
  
  if (http2Indicators.vercelRegion === 'unknown') {
    response.recommendations.push('Deploy to Vercel for automatic HTTP/2 support')
  }
  
  if (response.http2Status.likelyHTTP2) {
    response.recommendations.push('HTTP/2 appears to be enabled! ✅')
  } else {
    response.recommendations.push('HTTP/2 may not be enabled. Check hosting configuration.')
  }
  
  return NextResponse.json(response, {
    status: 200,
    headers: {
      'Content-Type': 'application/json',
      'Cache-Control': 'no-cache',
      'X-HTTP2-Test': 'true'
    }
  })
}

export async function POST(request: NextRequest) {
  return NextResponse.json(
    { message: 'HTTP/2 test endpoint - use GET method' },
    { status: 405 }
  )
}
