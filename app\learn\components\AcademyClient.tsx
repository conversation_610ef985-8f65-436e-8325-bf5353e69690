'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  BookOpen,
  Users,
  TrendingUp,
  Zap,
  Target,
  Clock,
  ChevronRight,
  Play,
  CheckCircle,
  ArrowRight,
  ArrowLeft,
  Sparkles,
  Brain,
  Lightbulb,
  MessageSquare,
  Settings,
  Star
} from 'lucide-react'
import Link from 'next/link'

interface Lesson {
  id: string
  title: string
  description: string
  duration: string
  category: string
  slug: string
  isCompleted?: boolean
  isFeatured?: boolean
}

const lessons: Lesson[] = [
  // Fundamentals (1-5)
  {
    id: '1',
    title: 'Why 87% of Follow-Ups Fail (And What Winners Do Instead)',
    description: 'Discover the hidden pattern successful businesses use to turn prospects into loyal customers.',
    duration: '14 min read',
    category: 'Fundamentals',
    slug: 'why-follow-ups-fail',
    isFeatured: true
  },
  {
    id: '2',
    title: 'The Underground Movement: How Smart Businesses Automate',
    description: 'What forward-thinking UK business owners discovered about automation that changed everything.',
    duration: '16 min read',
    category: 'Fundamentals',
    slug: 'automation-underground-movement'
  },
  {
    id: '3',
    title: 'The 3-Touch Rule That Converts 40% More Prospects',
    description: 'The psychological sequence that makes prospects want to buy from you.',
    duration: '12 min read',
    category: 'Fundamentals',
    slug: 'three-touch-rule'
  },
  {
    id: '4',
    title: 'From Chaos to Control: The Business Owner\'s Evolution',
    description: 'How successful entrepreneurs transformed from working IN their business to working ON it.',
    duration: '19 min read',
    category: 'Fundamentals',
    slug: 'chaos-to-control'
  },
  {
    id: '5',
    title: 'The Silent Revolution in Customer Communication',
    description: 'Why traditional customer service is quietly being abandoned by thriving businesses.',
    duration: '16 min read',
    category: 'Fundamentals',
    slug: 'silent-revolution'
  },
  // Advanced Communication (6-8)
  {
    id: '6',
    title: 'The Psychology of Timing: When Prospects Actually Buy',
    description: 'Why 73% of sales happen on the 5th-12th touchpoint and the buying window psychology most miss.',
    duration: '16 min read',
    category: 'Advanced Communication',
    slug: 'psychology-of-timing'
  },
  {
    id: '7',
    title: 'The Invisible Influence: How Words Shape Customer Decisions',
    description: 'The 7 psychological triggers that make prospects say yes and why "checking in" kills sales.',
    duration: '18 min read',
    category: 'Advanced Communication',
    slug: 'invisible-influence'
  },
  {
    id: '8',
    title: 'Beyond Email: The Multi-Channel Revolution',
    description: 'Why email-only follow-up is dying and the SMS strategy that gets 98% open rates.',
    duration: '21 min read',
    category: 'Advanced Communication',
    slug: 'multi-channel-revolution'
  },
  // Business Systems (9-12)
  {
    id: '9',
    title: 'The Delegation Dilemma: How to Clone Yourself',
    description: 'Why most delegation fails and the 3-step fix that lets you scale without losing quality.',
    duration: '17 min read',
    category: 'Business Systems',
    slug: 'delegation-dilemma'
  },
  {
    id: '10',
    title: 'The Revenue Leak Detector: Finding Hidden Money',
    description: 'The 5 places money leaks from every business and how to automate revenue recovery.',
    duration: '17 min read',
    category: 'Business Systems',
    slug: 'revenue-leak-detector'
  },
  {
    id: '11',
    title: 'The Retention Revolution: Why Chasing New Customers is Killing Your Business',
    description: 'Why acquiring new customers costs 5x more than retaining existing ones and the 4-phase retention system that increased customer lifetime value by 340%.',
    duration: '18 min read',
    category: 'Business Systems',
    slug: 'retention-revolution'
  },
  {
    id: '12',
    title: 'The Referral Engine: Turning Customers into Salespeople',
    description: 'Why asking for referrals doesn\'t work and the automated system generating 40%+ of new business.',
    duration: '16 min read',
    category: 'Business Systems',
    slug: 'referral-engine'
  },
  // Growth Strategy (13-15)
  {
    id: '13',
    title: 'The Pricing Psychology Revolution',
    description: 'Why your pricing strategy is leaving money on the table and the value ladder that doubles revenue.',
    duration: '20 min read',
    category: 'Growth Strategy',
    slug: 'pricing-psychology-revolution'
  },
  {
    id: '14',
    title: 'The Retention Secret: Why Customers Really Leave',
    description: 'The early warning signs most miss and automated retention sequences that save 60% of leaving customers.',
    duration: '18 min read',
    category: 'Growth Strategy',
    slug: 'retention-secret'
  },
  {
    id: '15',
    title: 'The Scale Breakthrough: From £100k to £1M',
    description: 'The 3 bottlenecks that stop businesses at each revenue level and how automation changes as you scale.',
    duration: '22 min read',
    category: 'Growth Strategy',
    slug: 'scale-breakthrough',
    isFeatured: true
  }
]

const categories = [
  { name: 'All Lessons', count: lessons.length, icon: BookOpen },
  { name: 'Fundamentals', count: lessons.filter(l => l.category === 'Fundamentals').length, icon: Brain },
  { name: 'Advanced Communication', count: lessons.filter(l => l.category === 'Advanced Communication').length, icon: MessageSquare },
  { name: 'Business Systems', count: lessons.filter(l => l.category === 'Business Systems').length, icon: Settings },
  { name: 'Growth Strategy', count: lessons.filter(l => l.category === 'Growth Strategy').length, icon: TrendingUp }
]

export default function AcademyClient() {
  const [selectedCategory, setSelectedCategory] = useState('All Lessons')
  const [searchTerm, setSearchTerm] = useState('')
  const [completedLessons, setCompletedLessons] = useState<string[]>([])
  const [visibleLessons, setVisibleLessons] = useState(6) // Show 6 lessons initially
  const [isLoadingMore, setIsLoadingMore] = useState(false)
  const [showMobileSidebar, setShowMobileSidebar] = useState(false)

  // Load completed lessons from localStorage
  useEffect(() => {
    const loadProgress = () => {
      const saved = localStorage.getItem('genlogic-academy-progress')
      if (saved) {
        setCompletedLessons(JSON.parse(saved))
      }
    }

    // Load initial progress
    loadProgress()

    // Listen for storage changes (when lessons are completed)
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'genlogic-academy-progress') {
        loadProgress()
      }
    }

    // Listen for custom events (for same-tab updates)
    const handleProgressUpdate = () => {
      loadProgress()
    }

    window.addEventListener('storage', handleStorageChange)
    window.addEventListener('academyProgressUpdate', handleProgressUpdate)

    // Also check for updates periodically
    const interval = setInterval(loadProgress, 2000)

    return () => {
      window.removeEventListener('storage', handleStorageChange)
      window.removeEventListener('academyProgressUpdate', handleProgressUpdate)
      clearInterval(interval)
    }
  }, [])

  // Reset visible lessons when category or search changes
  useEffect(() => {
    setVisibleLessons(6)
  }, [selectedCategory, searchTerm])

  // Load more lessons function
  const handleLoadMore = () => {
    setIsLoadingMore(true)
    // Simulate loading delay for better UX
    setTimeout(() => {
      setVisibleLessons(prev => prev + 6)
      setIsLoadingMore(false)
      // Smooth scroll to the first new lesson
      setTimeout(() => {
        const lessonElements = document.querySelectorAll('[data-lesson-card]')
        const targetIndex = Math.max(0, visibleLessons - 2) // Scroll to show some context
        if (lessonElements[targetIndex]) {
          lessonElements[targetIndex].scrollIntoView({
            behavior: 'smooth',
            block: 'start',
            inline: 'nearest'
          })
        }
      }, 100)
    }, 500)
  }

  // Calculate progress
  const completionRate = Math.round((completedLessons.length / lessons.length) * 100)

  const filteredLessons = lessons.filter(lesson => {
    const matchesCategory = selectedCategory === 'All Lessons' || lesson.category === selectedCategory
    const matchesSearch = lesson.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         lesson.description.toLowerCase().includes(searchTerm.toLowerCase())
    return matchesCategory && matchesSearch
  })

  // Get visible lessons based on current limit
  const visibleLessonsData = filteredLessons.slice(0, visibleLessons)
  const hasMoreLessons = filteredLessons.length > visibleLessons

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-primary-50/30 to-background dark:from-background dark:via-primary-950/30 dark:to-background">
      {/* Hero Section */}
      <section className="pt-40 pb-16">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-4xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="mb-6"
            >
              <div className="inline-flex items-center gap-2 bg-gradient-to-r from-green-100 to-blue-100 dark:from-green-900/30 dark:to-blue-900/30 text-green-700 dark:text-green-300 px-4 py-2 rounded-full text-sm font-medium mb-6">
                <Sparkles className="w-4 h-4" />
                15 Comprehensive Lessons • 250+ Minutes of Expert Content
              </div>

              <h1 className="text-4xl md:text-6xl font-bold text-foreground mb-6 leading-tight">
                Master the <span className="text-transparent bg-clip-text bg-gradient-to-r from-green-500 to-blue-500">Automation Revolution</span>
                <br />That's Transforming UK Businesses
              </h1>

              <p className="text-xl text-muted-foreground mb-8 leading-relaxed max-w-4xl">
                While most business owners work 60+ hours a week, smart entrepreneurs discovered the automation systems that run their businesses for them.
                <strong className="text-foreground">Learn the exact strategies generating £100k-£1M+ revenue with less stress, more freedom, and predictable growth.</strong>
              </p>

              {/* Key Benefits */}
              <div className="grid md:grid-cols-3 gap-4 mb-8">
                <div className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950/30 dark:to-green-900/30 rounded-2xl p-4 border border-green-200/50 dark:border-green-800/50">
                  <div className="flex items-center gap-3 mb-2">
                    <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
                      <CheckCircle className="w-4 h-4 text-white" />
                    </div>
                    <span className="font-bold text-green-700 dark:text-green-300">Real Case Studies</span>
                  </div>
                  <p className="text-sm text-green-600 dark:text-green-400">£156k-£2.3M transformations with exact strategies</p>
                </div>

                <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/30 dark:to-blue-900/30 rounded-2xl p-4 border border-blue-200/50 dark:border-blue-800/50">
                  <div className="flex items-center gap-3 mb-2">
                    <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                      <Brain className="w-4 h-4 text-white" />
                    </div>
                    <span className="font-bold text-blue-700 dark:text-blue-300">Interactive Tools</span>
                  </div>
                  <p className="text-sm text-blue-600 dark:text-blue-400">ROI calculators and implementation roadmaps</p>
                </div>

                <div className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950/30 dark:to-purple-900/30 rounded-2xl p-4 border border-purple-200/50 dark:border-purple-800/50">
                  <div className="flex items-center gap-3 mb-2">
                    <div className="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center">
                      <Target className="w-4 h-4 text-white" />
                    </div>
                    <span className="font-bold text-purple-700 dark:text-purple-300">Proven Systems</span>
                  </div>
                  <p className="text-sm text-purple-600 dark:text-purple-400">Step-by-step automation blueprints</p>
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="flex flex-col sm:flex-row gap-6 justify-center items-center"
            >
              <div className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                  <CheckCircle className="w-3 h-3 text-white" />
                </div>
                <span>100% Free • No Email Required</span>
              </div>
              <div className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                <div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                  <Clock className="w-3 h-3 text-white" />
                </div>
                <span>250+ Minutes of Expert Content</span>
              </div>
              <div className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                <div className="w-5 h-5 bg-purple-500 rounded-full flex items-center justify-center">
                  <Star className="w-3 h-3 text-white" />
                </div>
                <span>Real £100k-£1M+ Case Studies</span>
              </div>
            </motion.div>

            {/* Academy Stats */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="mt-12 grid grid-cols-2 md:grid-cols-4 gap-6 max-w-3xl mx-auto"
            >
              <div className="text-center">
                <div className="text-3xl font-bold text-foreground mb-1">15</div>
                <div className="text-sm text-muted-foreground">Comprehensive Lessons</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-foreground mb-1">250+</div>
                <div className="text-sm text-muted-foreground">Minutes of Content</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-foreground mb-1">8</div>
                <div className="text-sm text-muted-foreground">Interactive Calculators</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-foreground mb-1">15+</div>
                <div className="text-sm text-muted-foreground">Real Case Studies</div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="pb-20">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-7xl mx-auto">
            <div className="grid lg:grid-cols-4 gap-8">
              {/* Enhanced Sidebar - Hidden on mobile, toggleable */}
              <motion.div
                initial={{ opacity: 0, x: -30 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                className="hidden lg:block lg:col-span-1"
              >
                <div className="sticky top-40">
                  {/* Learning Progress Card */}
                  <div className="bg-gradient-to-br from-primary-50 to-accent-50 dark:from-primary-950/50 dark:to-accent-950/50 rounded-3xl p-6 border border-primary-200/50 dark:border-primary-800/50 shadow-xl mb-6">
                    <div className="flex items-center gap-3 mb-4">
                      <div className="w-12 h-12 bg-primary-100 dark:bg-primary-900/30 rounded-2xl flex items-center justify-center">
                        <Brain className="w-6 h-6 text-primary-600" />
                      </div>
                      <div>
                        <h3 className="font-bold text-foreground">Your Transformation</h3>
                        <p className="text-sm text-muted-foreground">From chaos to control</p>
                      </div>
                    </div>

                    <div className="space-y-3">
                      <div className="flex justify-between text-sm">
                        <span className="text-muted-foreground">Lessons Completed</span>
                        <span className="font-semibold text-foreground">{completedLessons.length}/15</span>
                      </div>
                      <div className="w-full bg-primary-200/50 dark:bg-primary-800/30 rounded-full h-2">
                        <div
                          className="bg-gradient-to-r from-primary-500 to-primary-600 h-2 rounded-full transition-all duration-500"
                          style={{ width: `${completionRate}%` }}
                        ></div>
                      </div>
                      <div className="text-center">
                        <span className="text-xs text-primary-600 font-medium">{completionRate}% Complete</span>
                      </div>
                    </div>
                  </div>

                  {/* Learning Paths */}
                  <div className="bg-background rounded-3xl border border-border shadow-lg p-6 mb-6">
                    <h3 className="text-lg font-bold text-foreground mb-6 flex items-center gap-2">
                      <Lightbulb className="w-5 h-5 text-primary-600" />
                      Learning Paths
                    </h3>

                    <div className="space-y-3">
                      {categories.map((category) => {
                        const Icon = category.icon
                        const isActive = selectedCategory === category.name

                        return (
                          <motion.button
                            key={category.name}
                            onClick={() => setSelectedCategory(category.name)}
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                            className={`w-full text-left p-4 rounded-2xl transition-all duration-300 group ${
                              isActive
                                ? 'bg-gradient-to-r from-primary-500 to-primary-600 text-white shadow-lg shadow-primary-500/25'
                                : 'bg-muted/50 hover:bg-primary-50 dark:hover:bg-primary-900/20 text-muted-foreground hover:text-primary-600 border border-transparent hover:border-primary-200 dark:hover:border-primary-800'
                            }`}
                          >
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-3">
                                <div className={`w-8 h-8 rounded-xl flex items-center justify-center ${
                                  isActive
                                    ? 'bg-white/20'
                                    : 'bg-primary-100 dark:bg-primary-900/30 group-hover:bg-primary-200 dark:group-hover:bg-primary-800/50'
                                }`}>
                                  <Icon className={`w-4 h-4 ${
                                    isActive
                                      ? 'text-white'
                                      : 'text-primary-600 group-hover:text-primary-700'
                                  }`} />
                                </div>
                                <span className="font-semibold text-sm">{category.name}</span>
                              </div>
                              <span className={`text-xs px-2 py-1 rounded-full font-medium ${
                                isActive
                                  ? 'bg-white/20 text-white'
                                  : 'bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 group-hover:bg-primary-200 dark:group-hover:bg-primary-800/50'
                              }`}>
                                {category.count}
                              </span>
                            </div>
                          </motion.button>
                        )
                      })}
                    </div>
                  </div>

                  {/* CTA Card */}
                  <motion.div
                    whileHover={{ scale: 1.02 }}
                    className="relative bg-background rounded-3xl border border-border shadow-xl overflow-hidden"
                  >
                    {/* Subtle gradient overlay */}
                    <div className="absolute inset-0 bg-gradient-to-br from-primary-50/30 to-accent-50/30 dark:from-primary-950/20 dark:to-accent-950/20"></div>

                    <div className="relative p-6">
                      {/* Icon with sophisticated styling */}
                      <div className="flex items-center gap-3 mb-4">
                        <div className="w-10 h-10 bg-gradient-to-br from-primary-500 to-accent-500 rounded-xl flex items-center justify-center shadow-lg shadow-primary-500/25">
                          <Zap className="w-5 h-5 text-white" />
                        </div>
                        <div className="w-2 h-2 bg-primary-500 rounded-full animate-pulse"></div>
                        <div className="w-1 h-1 bg-primary-400 rounded-full animate-pulse" style={{ animationDelay: '0.5s' }}></div>
                      </div>

                      <h4 className="text-lg font-bold text-foreground mb-3 leading-tight">
                        Something's Changing
                      </h4>

                      <p className="text-sm text-muted-foreground mb-6 leading-relaxed">
                        Smart business owners are quietly discovering what successful companies already know.
                        <span className="block mt-2 text-primary-600 dark:text-primary-400 font-medium">
                          The shift is happening whether you participate or not.
                        </span>
                      </p>

                      {/* Sophisticated button */}
                      <Link
                        href="/demo"
                        className="group relative inline-flex items-center gap-2 bg-gradient-to-r from-primary-500 to-accent-500 hover:from-primary-600 hover:to-accent-600 text-white px-6 py-3 rounded-2xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl hover:shadow-primary-500/25 text-sm w-full justify-center"
                      >
                        <span className="relative z-10">See What They Know</span>
                        <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform duration-300 relative z-10" />

                        {/* Button shine effect */}
                        <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700 rounded-2xl"></div>
                      </Link>

                      {/* Subtle indicator */}
                      <div className="flex items-center justify-center gap-2 mt-4 text-xs text-muted-foreground">
                        <div className="w-1 h-1 bg-green-500 rounded-full"></div>
                        <span>500+ UK businesses already know</span>
                      </div>
                    </div>
                  </motion.div>
                </div>
              </motion.div>

              {/* Lessons Grid - Full width on mobile */}
              <div className="lg:col-span-3 w-full">
                <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.4 }}
                  className="mb-8"
                >
                  {/* Mobile Progress Bar - Only visible on mobile */}
                  <div className="lg:hidden mb-8">
                    <div className="bg-gradient-to-br from-primary-50 to-accent-50 dark:from-primary-950/50 dark:to-accent-950/50 rounded-2xl p-4 border border-primary-200/50 dark:border-primary-800/50">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center gap-2">
                          <Brain className="w-5 h-5 text-primary-600" />
                          <span className="font-semibold text-foreground">Progress</span>
                        </div>
                        <span className="text-sm font-medium text-foreground">{completedLessons.length}/15</span>
                      </div>
                      <div className="w-full bg-primary-200/50 dark:bg-primary-800/30 rounded-full h-2">
                        <div
                          className="bg-gradient-to-r from-primary-500 to-primary-600 h-2 rounded-full transition-all duration-500"
                          style={{ width: `${completionRate}%` }}
                        ></div>
                      </div>
                      <div className="text-center mt-2">
                        <span className="text-xs text-primary-600 font-medium">{completionRate}% Complete</span>
                      </div>
                    </div>
                  </div>

                  <div className="mb-8">
                    <div className="flex flex-col lg:flex-row gap-6 justify-between items-start lg:items-center mb-6">
                      <div>
                        <h2 className="text-3xl font-bold text-foreground mb-2">
                          {selectedCategory === 'All Lessons' ? 'All Lessons' : selectedCategory}
                        </h2>
                        <p className="text-muted-foreground flex items-center gap-2">
                          <Target className="w-4 h-4" />
                          {filteredLessons.length} lesson{filteredLessons.length !== 1 ? 's' : ''} available
                        </p>
                      </div>

                      <div className="w-full lg:w-auto flex gap-3">
                        {/* Mobile Sidebar Toggle */}
                        <button
                          onClick={() => setShowMobileSidebar(!showMobileSidebar)}
                          className="lg:hidden flex items-center gap-2 bg-primary-100 dark:bg-primary-900/30 text-primary-600 px-4 py-3 rounded-2xl font-medium transition-all duration-300 hover:bg-primary-200 dark:hover:bg-primary-800/50"
                        >
                          <Settings className="w-5 h-5" />
                          <span className="hidden sm:inline">Filters</span>
                        </button>

                        <div className="relative flex-1 lg:flex-none">
                          <input
                            type="text"
                            placeholder="Search lessons..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="w-full lg:w-80 pl-12 pr-4 py-3 border border-border rounded-2xl bg-background/50 backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-300 transition-all duration-300 shadow-sm hover:shadow-md"
                          />
                          <div className="absolute left-4 top-1/2 -translate-y-1/2">
                            <Brain className="w-5 h-5 text-muted-foreground" />
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Quick Stats */}
                    <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
                      <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/30 dark:to-blue-900/30 rounded-2xl p-4 border border-blue-200/50 dark:border-blue-800/50">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-xl flex items-center justify-center">
                            <BookOpen className="w-4 h-4 text-blue-600" />
                          </div>
                          <div>
                            <div className="text-lg font-bold text-foreground">15</div>
                            <div className="text-xs text-muted-foreground">Expert Lessons</div>
                          </div>
                        </div>
                      </div>

                      <div className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950/30 dark:to-green-900/30 rounded-2xl p-4 border border-green-200/50 dark:border-green-800/50">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-xl flex items-center justify-center">
                            <Clock className="w-4 h-4 text-green-600" />
                          </div>
                          <div>
                            <div className="text-lg font-bold text-foreground">250+</div>
                            <div className="text-xs text-muted-foreground">Minutes Content</div>
                          </div>
                        </div>
                      </div>

                      <div className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950/30 dark:to-purple-900/30 rounded-2xl p-4 border border-purple-200/50 dark:border-purple-800/50">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-purple-100 dark:bg-purple-900/30 rounded-xl flex items-center justify-center">
                            <Target className="w-4 h-4 text-purple-600" />
                          </div>
                          <div>
                            <div className="text-lg font-bold text-foreground">8</div>
                            <div className="text-xs text-muted-foreground">ROI Calculators</div>
                          </div>
                        </div>
                      </div>

                      <div className="bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-950/30 dark:to-orange-900/30 rounded-2xl p-4 border border-orange-200/50 dark:border-orange-800/50">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-orange-100 dark:bg-orange-900/30 rounded-xl flex items-center justify-center">
                            <TrendingUp className="w-4 h-4 text-orange-600" />
                          </div>
                          <div>
                            <div className="text-lg font-bold text-foreground">£2.3M+</div>
                            <div className="text-xs text-muted-foreground">Case Studies</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-6">
                    <AnimatePresence mode="wait">
                      {visibleLessonsData.map((lesson, index) => (
                        <motion.div
                          key={lesson.id}
                          data-lesson-card
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: -20 }}
                          transition={{ duration: 0.4, delay: (index % 6) * 0.1 }}
                          whileHover={{ y: -5 }}
                          className="group relative"
                        >
                          <Link href={`/learn/fundamentals/${lesson.slug}`}>
                            <div className={`relative bg-background rounded-3xl border shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden ${
                              lesson.isFeatured
                                ? 'border-primary-200 dark:border-primary-800 bg-gradient-to-br from-primary-50/50 to-background dark:from-primary-950/30 dark:to-background'
                                : 'border-border hover:border-primary-200 dark:hover:border-primary-800'
                            }`}>
                              {lesson.isFeatured && (
                                <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-primary-500 to-accent-500"></div>
                              )}

                              <div className="p-4 sm:p-6 lg:p-8">
                                <div className="flex items-start gap-4 sm:gap-6">
                                  {/* Icon Section */}
                                  <div className={`flex-shrink-0 w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 ${
                                    lesson.isFeatured
                                      ? 'bg-gradient-to-br from-primary-500 to-accent-500 shadow-lg shadow-primary-500/25'
                                      : 'bg-primary-100 dark:bg-primary-900/30 group-hover:bg-primary-200 dark:group-hover:bg-primary-800/50'
                                  }`}>
                                    <Play className={`w-6 h-6 ${
                                      lesson.isFeatured ? 'text-white' : 'text-primary-600'
                                    }`} />
                                  </div>

                                  {/* Content Section */}
                                  <div className="flex-1 min-w-0">
                                    {/* Meta Info */}
                                    <div className="flex flex-wrap items-center gap-2 sm:gap-4 mb-4">
                                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                        <Clock className="w-4 h-4" />
                                        {lesson.duration}
                                      </div>
                                      <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                                        lesson.isFeatured
                                          ? 'bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300'
                                          : 'bg-muted text-muted-foreground'
                                      }`}>
                                        {lesson.category}
                                      </div>
                                      {lesson.isFeatured && (
                                        <div className="flex items-center gap-1 bg-gradient-to-r from-primary-500 to-accent-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                                          <Sparkles className="w-3 h-3" />
                                          Featured
                                        </div>
                                      )}
                                    </div>

                                    {/* Title */}
                                    <h3 className="text-lg sm:text-xl font-bold text-foreground mb-3 group-hover:text-primary-600 transition-colors leading-tight">
                                      {lesson.title}
                                    </h3>

                                    {/* Description */}
                                    <p className="text-muted-foreground mb-6 leading-relaxed">
                                      {lesson.description}
                                    </p>

                                    {/* CTA */}
                                    <div className="flex items-center justify-between">
                                      <div className="inline-flex items-center gap-2 text-primary-600 hover:text-primary-700 font-semibold transition-colors group/link">
                                        Start Learning
                                        <ChevronRight className="w-4 h-4 group-hover/link:translate-x-1 transition-transform" />
                                      </div>

                                      {/* Progress Indicator */}
                                      <div className="flex items-center gap-2">
                                        {completedLessons.includes(lesson.slug) ? (
                                          <div className="flex items-center gap-2 bg-green-100 dark:bg-green-900/30 px-3 py-1 rounded-full">
                                            <CheckCircle className="w-3 h-3 text-green-500" />
                                            <span className="text-xs font-medium text-green-700 dark:text-green-300">Completed</span>
                                          </div>
                                        ) : (
                                          <div className="flex items-center gap-1">
                                            <div className="w-2 h-2 bg-muted rounded-full"></div>
                                            <div className="w-2 h-2 bg-muted rounded-full"></div>
                                            <div className="w-2 h-2 bg-muted rounded-full"></div>
                                          </div>
                                        )}</div>
                                    </div>
                                  </div>
                                </div>
                              </div>

                              {/* Hover Effect Overlay */}
                              <div className="absolute inset-0 bg-gradient-to-r from-primary-500/5 to-accent-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                            </div>
                          </Link>
                        </motion.div>
                      ))}
                    </AnimatePresence>

                    {/* Load More / Show All / Collapse Buttons */}
                    {hasMoreLessons ? (
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6, delay: 0.2 }}
                        className="flex flex-wrap justify-center gap-3 sm:gap-4 pt-8"
                      >
                        <button
                          onClick={handleLoadMore}
                          disabled={isLoadingMore}
                          className="inline-flex items-center gap-2 sm:gap-3 bg-gradient-to-r from-primary-500 to-accent-500 hover:from-primary-600 hover:to-accent-600 disabled:from-gray-400 disabled:to-gray-500 text-white px-4 sm:px-6 lg:px-8 py-3 sm:py-4 rounded-2xl font-semibold text-sm sm:text-base transition-all duration-300 shadow-lg hover:shadow-xl disabled:cursor-not-allowed disabled:shadow-none"
                        >
                          {isLoadingMore ? (
                            <>
                              <div className="w-4 h-4 sm:w-5 sm:h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                              <span className="hidden sm:inline">Loading More Lessons...</span>
                              <span className="sm:hidden">Loading...</span>
                            </>
                          ) : (
                            <>
                              <span className="hidden sm:inline">Load More Lessons ({Math.min(6, filteredLessons.length - visibleLessons)})</span>
                              <span className="sm:hidden">Load More ({Math.min(6, filteredLessons.length - visibleLessons)})</span>
                              <ChevronRight className="w-4 h-4 sm:w-5 sm:h-5" />
                            </>
                          )}
                        </button>

                        {filteredLessons.length > 12 && (
                          <button
                            onClick={() => setVisibleLessons(filteredLessons.length)}
                            disabled={isLoadingMore}
                            className="inline-flex items-center gap-2 bg-muted hover:bg-muted/80 text-muted-foreground hover:text-foreground px-4 sm:px-6 py-3 sm:py-4 rounded-2xl font-medium text-sm sm:text-base transition-all duration-300 disabled:cursor-not-allowed disabled:opacity-50"
                          >
                            <span className="hidden sm:inline">Show All ({filteredLessons.length})</span>
                            <span className="sm:hidden">All ({filteredLessons.length})</span>
                          </button>
                        )}
                      </motion.div>
                    ) : filteredLessons.length > 6 && visibleLessons >= filteredLessons.length ? (
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6, delay: 0.2 }}
                        className="flex justify-center pt-8"
                      >
                        <button
                          onClick={() => setVisibleLessons(6)}
                          className="inline-flex items-center gap-2 bg-muted hover:bg-muted/80 text-muted-foreground hover:text-foreground px-4 sm:px-6 py-3 sm:py-4 rounded-2xl font-medium text-sm sm:text-base transition-all duration-300"
                        >
                          Show Less
                          <ChevronRight className="w-4 h-4 sm:w-5 sm:h-5 rotate-90" />
                        </button>
                      </motion.div>
                    ) : null}

                    {/* Show total count */}
                    <div className="text-center pt-4">
                      <p className="text-sm text-muted-foreground">
                        Showing {visibleLessonsData.length} of {filteredLessons.length} lessons
                        {filteredLessons.length !== lessons.length && (
                          <span> (filtered from {lessons.length} total)</span>
                        )}
                      </p>
                    </div>
                  </div>
                </motion.div>
              </div>
            </div>

            {/* Mobile Sidebar Overlay */}
            <AnimatePresence>
              {showMobileSidebar && (
                <>
                  {/* Backdrop */}
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    onClick={() => setShowMobileSidebar(false)}
                    className="lg:hidden fixed inset-0 bg-black/50 backdrop-blur-sm z-40"
                  />

                  {/* Mobile Sidebar */}
                  <motion.div
                    initial={{ x: -300, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    exit={{ x: -300, opacity: 0 }}
                    transition={{ type: "spring", damping: 25, stiffness: 200 }}
                    className="lg:hidden fixed left-0 top-0 bottom-0 w-80 bg-background border-r border-border shadow-2xl z-50 overflow-y-auto"
                  >
                    <div className="p-6">
                      {/* Close Button */}
                      <div className="flex items-center justify-between mb-6">
                        <h3 className="text-lg font-bold text-foreground">Learning Dashboard</h3>
                        <button
                          onClick={() => setShowMobileSidebar(false)}
                          className="p-2 hover:bg-muted rounded-xl transition-colors"
                        >
                          <ArrowLeft className="w-5 h-5" />
                        </button>
                      </div>

                      {/* Learning Progress Card */}
                      <div className="bg-gradient-to-br from-primary-50 to-accent-50 dark:from-primary-950/50 dark:to-accent-950/50 rounded-3xl p-6 border border-primary-200/50 dark:border-primary-800/50 shadow-xl mb-6">
                        <div className="flex items-center gap-3 mb-4">
                          <div className="w-12 h-12 bg-primary-100 dark:bg-primary-900/30 rounded-2xl flex items-center justify-center">
                            <Brain className="w-6 h-6 text-primary-600" />
                          </div>
                          <div>
                            <h3 className="font-bold text-foreground">Your Transformation</h3>
                            <p className="text-sm text-muted-foreground">From chaos to control</p>
                          </div>
                        </div>

                        <div className="space-y-3">
                          <div className="flex justify-between text-sm">
                            <span className="text-muted-foreground">Lessons Completed</span>
                            <span className="font-semibold text-foreground">{completedLessons.length}/15</span>
                          </div>
                          <div className="w-full bg-primary-200/50 dark:bg-primary-800/30 rounded-full h-2">
                            <div
                              className="bg-gradient-to-r from-primary-500 to-primary-600 h-2 rounded-full transition-all duration-500"
                              style={{ width: `${completionRate}%` }}
                            ></div>
                          </div>
                          <div className="text-center">
                            <span className="text-xs text-primary-600 font-medium">{completionRate}% Complete</span>
                          </div>
                        </div>
                      </div>

                      {/* Category Filters */}
                      <div className="bg-background rounded-3xl border border-border shadow-lg p-6">
                        <h3 className="text-lg font-bold text-foreground mb-6 flex items-center gap-2">
                          <Lightbulb className="w-5 h-5 text-primary-600" />
                          Categories
                        </h3>
                        <div className="space-y-2">
                          {categories.map((category) => (
                            <button
                              key={category.name}
                              onClick={() => {
                                setSelectedCategory(category.name)
                                setShowMobileSidebar(false)
                              }}
                              className={`w-full flex items-center justify-between p-3 rounded-2xl transition-all duration-300 ${
                                selectedCategory === category.name
                                  ? 'bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 shadow-sm'
                                  : 'hover:bg-muted text-muted-foreground hover:text-foreground'
                              }`}
                            >
                              <div className="flex items-center gap-3">
                                <category.icon className="w-4 h-4" />
                                <span className="font-medium">{category.name}</span>
                              </div>
                              <span className="text-xs bg-muted px-2 py-1 rounded-full">
                                {category.count}
                              </span>
                            </button>
                          ))}
                        </div>
                      </div>
                    </div>
                  </motion.div>
                </>
              )}
            </AnimatePresence>
          </div>
        </div>
      </section>
    </div>
  )
}
