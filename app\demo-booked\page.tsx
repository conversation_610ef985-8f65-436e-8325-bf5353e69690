'use client'

import Link from 'next/link'
import { motion } from 'framer-motion'
import { CheckCircle, ArrowRight, Calendar, MessageSquare, BarChart3 } from 'lucide-react'
import { CustomButton } from '@/components/ui/custom-button'

export default function DemoBookedPage() {
  return (
    <div className="relative w-full overflow-hidden">
      {/* Header Section */}
      <section className="header-spacing py-20 relative">
        <div className="container relative z-10 mx-auto px-4 md:px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: 'easeOut' }}
            className="mx-auto max-w-4xl text-center"
          >
            <div className="bg-background border border-primary-200/20 dark:border-primary-800/20 rounded-3xl p-12 shadow-lg backdrop-blur-sm">
              <div className="w-20 h-20 bg-gradient-to-br from-success-green to-success-green/80 rounded-full flex items-center justify-center mx-auto mb-6">
                <CheckCircle className="w-10 h-10 text-white" />
              </div>
              <h1 className="text-4xl font-bold text-foreground sm:text-5xl md:text-6xl mb-6">
                Your <span className="text-primary-700 dark:text-primary-400">Evenings Back</span> Start Here! 🎉
              </h1>
              <p className="text-xl text-muted-foreground leading-relaxed mb-6">
                <strong className="text-foreground">You've just taken the first step to stop working until 9pm every night.</strong> We're excited to show you exactly how GenLogic will give you back 20+ hours weekly and reduce your no-shows by 85%.
              </p>
            </div>
          </motion.div>

          {/* What to Expect Section */}
          <div className="mx-auto max-w-4xl">
            <div className="bg-background border border-primary-200/20 dark:border-primary-800/20 rounded-3xl shadow-xl p-8 mb-12">
              <h2 className="text-3xl font-bold text-foreground mb-8 text-center">
                Here's Exactly How We'll <span className="text-primary-700 dark:text-primary-400">Transform</span> Your Business
              </h2>

            <div className="grid md:grid-cols-2 gap-8">
              <div className="space-y-6">
                <div className="flex items-start space-x-4">
                  <div className="w-8 h-8 bg-primary-700/10 dark:bg-primary-400/10 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                    <span className="text-primary-700 dark:text-primary-400 font-bold text-sm">1</span>
                  </div>
                  <div>
                    <h3 className="font-semibold text-foreground mb-2">Understanding Your Pain Points (5 mins)</h3>
                    <p className="text-muted-foreground text-sm">We'll talk about those late evenings, the manual booking chaos, and exactly how much time you're losing to no-shows and admin tasks each week.</p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="w-8 h-8 bg-primary-700/10 dark:bg-primary-400/10 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                    <span className="text-primary-700 dark:text-primary-400 font-bold text-sm">2</span>
                  </div>
                  <div>
                    <h3 className="font-semibold text-foreground mb-2">Your Freedom Blueprint (15 mins)</h3>
                    <p className="text-muted-foreground text-sm">Watch us build your automated booking system live - see how customers book themselves, get reminded automatically, and how you'll never chase no-shows again.</p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="w-8 h-8 bg-primary-700/10 dark:bg-primary-400/10 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                    <span className="text-primary-700 dark:text-primary-400 font-bold text-sm">3</span>
                  </div>
                  <div>
                    <h3 className="font-semibold text-foreground mb-2">Your Personal Action Plan (10 mins)</h3>
                    <p className="text-muted-foreground text-sm">We'll create your specific roadmap to freedom - exactly how GenLogic will work for your business type and when you'll start seeing those precious evening hours back.</p>
                  </div>
                </div>
              </div>

              <div className="space-y-6">
                <div className="bg-muted/50 rounded-2xl p-6">
                  <h3 className="font-semibold text-foreground mb-4">Your Life After GenLogic:</h3>
                  <ul className="space-y-3 text-sm">
                    <li className="flex items-center space-x-3">
                      <CheckCircle className="w-4 h-4 text-success-green flex-shrink-0" />
                      <span className="text-foreground">Finish work by 6pm and actually mean it</span>
                    </li>
                    <li className="flex items-center space-x-3">
                      <CheckCircle className="w-4 h-4 text-success-green flex-shrink-0" />
                      <span className="text-foreground">Watch no-shows drop from 30% to under 5%</span>
                    </li>
                    <li className="flex items-center space-x-3">
                      <CheckCircle className="w-4 h-4 text-success-green flex-shrink-0" />
                      <span className="text-foreground">Never chase customers for confirmations again</span>
                    </li>
                    <li className="flex items-center space-x-3">
                      <CheckCircle className="w-4 h-4 text-success-green flex-shrink-0" />
                      <span className="text-foreground">Grow revenue without working longer hours</span>
                    </li>
                    <li className="flex items-center space-x-3">
                      <CheckCircle className="w-4 h-4 text-success-green flex-shrink-0" />
                      <span className="text-foreground">Spend evenings with family, not spreadsheets</span>
                    </li>
                  </ul>
                </div>
              </div>
              </div>
            </div>
            </div>
          </div>

          {/* About GenLogic Platform */}
          <div className="mx-auto max-w-4xl">
            <div className="bg-background border border-primary-200/20 dark:border-primary-800/20 rounded-3xl shadow-xl p-8 mb-12">
              <h2 className="text-3xl font-bold text-foreground mb-8 text-center">
                The Technology That <span className="text-primary-700 dark:text-primary-400">Sets You Free</span>
              </h2>

            <div className="grid md:grid-cols-3 gap-6 mb-8">
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-primary-700 to-primary-800 dark:from-primary-600 dark:to-primary-700 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <Calendar className="w-8 h-8 text-white" />
                </div>
                <h3 className="font-semibold text-foreground mb-2">24/7 Booking Freedom</h3>
                <p className="text-sm text-muted-foreground">Customers book themselves while you sleep - no more phone tag or missed opportunities</p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-primary-700 to-primary-800 dark:from-primary-600 dark:to-primary-700 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <MessageSquare className="w-8 h-8 text-white" />
                </div>
                <h3 className="font-semibold text-foreground mb-2">No-Show Elimination</h3>
                <p className="text-sm text-muted-foreground">Smart reminders that actually get customers to show up - 85% fewer no-shows guaranteed</p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-primary-700 to-primary-800 dark:from-primary-600 dark:to-primary-700 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <BarChart3 className="w-8 h-8 text-white" />
                </div>
                <h3 className="font-semibold text-foreground mb-2">Growth Insights</h3>
                <p className="text-sm text-muted-foreground">See exactly how much time and money you're saving - plus where to grow next</p>
              </div>
            </div>

            <div className="bg-muted/50 rounded-2xl p-6 text-center">
              <p className="text-foreground mb-4">
                <strong>Built specifically for UK business owners</strong> who are tired of working until 9pm every night.
                We've helped thousands of local businesses reclaim their evenings and weekends while growing their revenue.
              </p>
              <p className="text-sm text-muted-foreground">
                No tech headaches • Working in 24 hours • UK support that actually cares
              </p>
            </div>
            </div>
          </div>

          {/* Next Steps */}
          <div className="mx-auto max-w-4xl">
            <div className="bg-gradient-to-br from-primary-600 to-primary-700 rounded-3xl shadow-xl p-8 text-white text-center mb-12">
              <h2 className="text-3xl font-bold mb-8">Your Journey to <span className="text-white/90">Freedom</span> Starts Now</h2>
            <div className="grid md:grid-cols-3 gap-6 mb-8">
              <div>
                <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center mx-auto mb-3">
                  <span className="font-bold text-lg">1</span>
                </div>
                <h3 className="font-semibold mb-2">Your Demo Details</h3>
                <p className="text-sm text-primary-100">Check your inbox - we've sent everything you need including a calendar invite and what to prepare</p>
              </div>
              <div>
                <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center mx-auto mb-3">
                  <span className="font-bold text-lg">2</span>
                </div>
                <h3 className="font-semibold mb-2">We'll Be Ready</h3>
                <p className="text-sm text-primary-100">Our team will call 15 minutes early to make sure everything's perfect for your demo</p>
              </div>
              <div>
                <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center mx-auto mb-3">
                  <span className="font-bold text-lg">3</span>
                </div>
                <h3 className="font-semibold mb-2">Your Transformation</h3>
                <p className="text-sm text-primary-100">Watch us show you exactly how to get your evenings back and grow your business</p>
              </div>
            </div>

            <div className="border-t border-white/20 pt-6">
              <p className="text-primary-100 mb-6">
                Worried about anything? We're here to help make this perfect for you.
              </p>
              <CustomButton
                variant="secondary"
                size="lg"
                href="/contact"
                icon={<ArrowRight className="w-4 h-4" />}
                iconPosition="right"
                className="bg-white text-primary-600 hover:bg-primary-50"
              >
                Chat With Our Team
              </CustomButton>
            </div>
            </div>
          

          {/* Back to Home */}
          <div className="mx-auto max-w-4xl">
            <div className="text-center">
              <Link
                href="/"
                className="inline-flex items-center space-x-2 text-muted-foreground hover:text-primary-700 dark:hover:text-primary-400 transition-colors"
              >
                <ArrowRight className="w-4 h-4 rotate-180" />
                <span>Back to GenLogic Home</span>
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
