import { Metadata } from 'next'
import InvisibleInfluenceClient from './InvisibleInfluenceClient'

export const metadata: Metadata = {
  title: 'The Invisible Influence: How Words Shape Customer Decisions | GenLogic Academy',
  description: 'Discover the 7 psychological triggers that make prospects say yes and why "checking in" kills sales. Learn language patterns that build trust at scale.',
  keywords: 'sales psychology, persuasive language, customer psychology, sales scripts, influence techniques, UK sales training',
  openGraph: {
    title: 'The Invisible Influence: How Words Shape Customer Decisions',
    description: 'Discover the 7 psychological triggers that make prospects say yes and why "checking in" kills sales. Learn language patterns that build trust at scale.',
    type: 'article',
    url: 'https://genlogic.io/learn/fundamentals/invisible-influence',
    images: [
      {
        url: '/academy-images/og-invisible-influence.webp',
        width: 1200,
        height: 630,
        alt: 'The Invisible Influence: How Words Shape Customer Decisions',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'The Invisible Influence: How Words Shape Customer Decisions',
    description: 'Discover the 7 psychological triggers that make prospects say yes and why "checking in" kills sales. Learn language patterns that build trust at scale.',
    images: ['/academy-images/og-invisible-influence.webp'],
  },
}

export default function InvisibleInfluencePage() {
  return <InvisibleInfluenceClient />
}
