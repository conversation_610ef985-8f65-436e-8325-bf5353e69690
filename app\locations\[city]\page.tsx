import { SchemaMarkup, BreadcrumbSchema } from '@/components/seo/SchemaMarkup'
import { notFound } from 'next/navigation'
import { LocationPageClient } from './LocationClient'

// Valid UK cities for location pages
const validCities = [
  'london', 'manchester', 'birmingham', 'leeds', 'glasgow', 'sheffield',
  'bradford', 'liverpool', 'edinburgh', 'bristol', 'cardiff', 'leicester',
  'coventry', 'nottingham', 'newcastle', 'belfast'
]

interface LocationPageProps {
  params: {
    city: string
  }
}

export default function LocationPage({ params }: LocationPageProps) {
  const citySlug = params.city.toLowerCase()

  // Check if city is valid
  if (!validCities.includes(citySlug)) {
    notFound()
  }

  // Format city name for display
  const cityName = citySlug.charAt(0).toUpperCase() + citySlug.slice(1)

  // City-specific data
  const cityData = {
    london: {
      population: '9 million',
      businesses: '400,000+',
      specialties: ['Financial Services', 'Professional Services', 'Healthcare', 'Retail'],
      painPoint: 'competing in the fast-paced London market'
    },
    manchester: {
      population: '2.7 million',
      businesses: '85,000+',
      specialties: ['Manufacturing', 'Healthcare', 'Professional Services', 'Retail'],
      painPoint: 'managing growing customer demand'
    },
    birmingham: {
      population: '2.9 million',
      businesses: '75,000+',
      specialties: ['Manufacturing', 'Automotive', 'Healthcare', 'Professional Services'],
      painPoint: 'streamlining operations for efficiency'
    }
  }

  const currentCityData = cityData[citySlug as keyof typeof cityData] || {
    population: '1+ million',
    businesses: '50,000+',
    specialties: ['Local Services', 'Healthcare', 'Professional Services', 'Retail'],
    painPoint: 'growing their local business'
  }

  // Breadcrumb data
  const breadcrumbItems = [
    { name: 'Home', url: 'https://genlogic.io' },
    { name: 'Locations', url: 'https://genlogic.io/locations' },
    { name: cityName, url: `https://genlogic.io/locations/${citySlug}` }
  ]

  return (
    <>
      <SchemaMarkup type="localBusiness" />
      <SchemaMarkup
        type="service"
        data={{
          name: `Business Automation Services ${cityName}`,
          description: `Professional business automation software for ${cityName} local businesses. Reduce no-shows, save time, and grow revenue.`
        }}
      />
      <BreadcrumbSchema items={breadcrumbItems} />

      <LocationPageClient
        cityName={cityName}
        cityData={currentCityData}
        citySlug={citySlug}
      />
    </>
  )
}

// Generate static params for all valid cities
export async function generateStaticParams() {
  return validCities.map((city) => ({
    city: city,
  }))
}

// Generate metadata for each city page
export async function generateMetadata({ params }: LocationPageProps) {
  const citySlug = params.city.toLowerCase()
  const cityName = citySlug.charAt(0).toUpperCase() + citySlug.slice(1)

  if (!validCities.includes(citySlug)) {
    return {
      title: 'Page Not Found - GenLogic',
      description: 'The requested location page was not found.'
    }
  }

  return {
    title: `Business Automation ${cityName} - GenLogic | Save 20+ Hours Weekly`,
    description: `Stop working late in ${cityName}. GenLogic helps local businesses reduce no-shows by 85% and save 20+ hours weekly. Free demo for ${cityName} businesses.`,
    keywords: `business automation ${cityName}, booking system ${cityName}, SMS reminders ${cityName}, customer automation ${cityName}, reduce no shows ${cityName}`,
    openGraph: {
      title: `Business Automation ${cityName} - GenLogic`,
      description: `Stop working late in ${cityName}. GenLogic helps local businesses reduce no-shows by 85% and save 20+ hours weekly.`,
      url: `https://genlogic.io/locations/${citySlug}`,
      siteName: 'GenLogic',
      locale: 'en_GB',
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: `Business Automation ${cityName} - GenLogic`,
      description: `Stop working late in ${cityName}. GenLogic helps local businesses reduce no-shows by 85% and save 20+ hours weekly.`,
    },
    alternates: {
      canonical: `https://genlogic.io/locations/${citySlug}`,
    },
  }
}
