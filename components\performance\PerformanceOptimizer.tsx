'use client'

import { useEffect, useState } from 'react'

export function PerformanceOptimizer() {
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])
  useEffect(() => {
    // Only run on client side
    if (typeof window === 'undefined') return
    // Preload critical resources only if they exist
    const preloadCriticalResources = () => {
      // Only preload resources that are actually used on the page
      const currentPath = window.location.pathname

      // Preload fonts only for pages that need them immediately
      if (currentPath === '/' || currentPath === '/demo' || currentPath === '/contact') {
        const fontLink = document.createElement('link')
        fontLink.rel = 'preload'
        fontLink.href = 'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap'
        fontLink.as = 'style'
        fontLink.crossOrigin = 'anonymous'
        document.head.appendChild(fontLink)
      }

      // Only preload images that are visible above the fold
      const criticalImages = ['/favicon.svg']

      criticalImages.forEach(src => {
        // Check if image actually exists before preloading
        const img = new Image()
        img.onload = () => {
          const link = document.createElement('link')
          link.rel = 'preload'
          link.href = src
          link.as = 'image'
          document.head.appendChild(link)
        }
        img.src = src
      })
    }

    // Optimize images with intersection observer
    const optimizeImages = () => {
      const images = document.querySelectorAll('img[data-src]')
      
      if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              const img = entry.target as HTMLImageElement
              img.src = img.dataset.src || ''
              img.classList.remove('lazy')
              imageObserver.unobserve(img)
            }
          })
        })

        images.forEach(img => imageObserver.observe(img))
      }
    }

    // Reduce layout shifts
    const preventLayoutShifts = () => {
      // Add aspect ratio containers for dynamic content
      const dynamicContainers = document.querySelectorAll('[data-dynamic-content]')
      dynamicContainers.forEach(container => {
        const element = container as HTMLElement
        if (!element.style.minHeight) {
          element.style.minHeight = '200px' // Prevent layout shift
        }
      })
    }

    // Optimize third-party scripts
    const optimizeThirdPartyScripts = () => {
      // Delay non-critical third-party scripts
      const scripts = document.querySelectorAll('script[data-delay]')
      
      const loadDelayedScripts = () => {
        scripts.forEach(script => {
          const newScript = document.createElement('script')
          Array.from(script.attributes).forEach(attr => {
            if (attr.name !== 'data-delay') {
              newScript.setAttribute(attr.name, attr.value)
            }
          })
          newScript.innerHTML = script.innerHTML
          script.parentNode?.replaceChild(newScript, script)
        })
      }

      // Load after user interaction or 3 seconds
      let loaded = false
      const load = () => {
        if (!loaded) {
          loaded = true
          loadDelayedScripts()
        }
      }

      ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'].forEach(event => {
        document.addEventListener(event, load, { once: true, passive: true })
      })

      setTimeout(load, 3000)
    }

    // Run optimizations
    preloadCriticalResources()
    optimizeImages()
    preventLayoutShifts()
    optimizeThirdPartyScripts()

    // Cleanup function
    return () => {
      // Remove event listeners if needed
    }
  }, [isClient])

  // Only run optimizations on client side
  if (!isClient) {
    return null
  }

  return null // This component doesn't render anything
}

// Hook for performance monitoring
export function usePerformanceMonitoring() {
  useEffect(() => {
    // Only run on client side
    if (typeof window === 'undefined') return

    // Monitor Core Web Vitals
    if ('PerformanceObserver' in window) {
      // Largest Contentful Paint
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        const lastEntry = entries[entries.length - 1]
        
        if (window.gtag) {
          window.gtag('event', 'web_vitals', {
            event_category: 'performance',
            event_label: 'LCP',
            value: Math.round(lastEntry.startTime)
          })
        }
      })

      // Interaction to Next Paint (INP) - Replaced FID in March 2024
      const inpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach(entry => {
          if (window.gtag && 'duration' in entry) {
            const inpEntry = entry as any // Type assertion for INP entry
            window.gtag('event', 'web_vitals', {
              event_category: 'performance',
              event_label: 'INP',
              value: Math.round(inpEntry.duration),
              custom_parameter_1: 'core_web_vitals_2025'
            })
          }
        })
      })

      // Keep FID for backwards compatibility but prioritize INP
      const fidObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach(entry => {
          if (window.gtag && 'processingStart' in entry) {
            const fidEntry = entry as any // Type assertion for FID entry
            window.gtag('event', 'web_vitals', {
              event_category: 'performance',
              event_label: 'FID_LEGACY',
              value: Math.round(fidEntry.processingStart - fidEntry.startTime)
            })
          }
        })
      })

      // Cumulative Layout Shift
      const clsObserver = new PerformanceObserver((list) => {
        let clsValue = 0
        const entries = list.getEntries()

        entries.forEach(entry => {
          const clsEntry = entry as any // Type assertion for CLS entry
          if (!clsEntry.hadRecentInput && 'value' in clsEntry) {
            clsValue += clsEntry.value
          }
        })

        if (window.gtag && clsValue > 0) {
          window.gtag('event', 'web_vitals', {
            event_category: 'performance',
            event_label: 'CLS',
            value: Math.round(clsValue * 1000)
          })
        }
      })

      try {
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })
        inpObserver.observe({ entryTypes: ['event'] }) // INP uses 'event' entry type
        fidObserver.observe({ entryTypes: ['first-input'] }) // Keep for compatibility
        clsObserver.observe({ entryTypes: ['layout-shift'] })
      } catch (e) {
        // Fallback for browsers that don't support all entry types
        console.warn('Performance monitoring not fully supported')
      }

      return () => {
        lcpObserver.disconnect()
        inpObserver.disconnect()
        fidObserver.disconnect()
        clsObserver.disconnect()
      }
    }
  }, [])
}
