import { Metadata } from 'next'
import { SchemaMarkup, BreadcrumbSchema } from '@/components/seo/SchemaMarkup'
import WebsiteDevelopmentClient from './WebsiteDevelopmentClient'

export const metadata: Metadata = {
  title: 'Next.js Website Development | Fast, Modern Websites for UK Businesses | GenLogic',
  description: 'Tired of websites that don\'t convert visitors into customers? Get a high-converting Next.js website built by GenLogic. 3x faster loading, conversion-optimized design, and built to turn prospects into customers. Contact our UK team.',
  keywords: 'high converting website development UK, conversion optimized websites, customer acquisition websites, sales focused web design UK, lead generation websites, revenue optimized web development',
  openGraph: {
    title: 'High-Converting Website Development | Turn Visitors Into Customers | UK',
    description: 'Tired of websites that don\'t convert? Get a high-converting Next.js website built by GenLogic. 3x faster loading, conversion-optimized design, and built to turn prospects into customers.',
    url: 'https://genlogic.io/website-development',
    type: 'website',
    images: [
      {
        url: 'https://genlogic.io/og-website-development.webp',
        width: 1200,
        height: 630,
        alt: 'GenLogic Next.js Website Development Services',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Next.js Website Development | Fast, Modern Websites for UK Businesses',
    description: 'Tired of slow WordPress websites? Get a lightning-fast Next.js website built by GenLogic. 3x faster loading, mobile-first design, and built for conversions.',
    images: ['https://genlogic.io/og-website-development.webp'],
  },
  alternates: {
    canonical: 'https://genlogic.io/website-development',
  },
}

const websiteServiceData = {
  name: "Next.js Website Development",
  description: "Professional Next.js website development services for UK businesses. Fast, modern, and conversion-optimized websites.",
  serviceType: "Website Development",
  offers: {
    priceCurrency: "GBP",
    description: "Custom Next.js website development with modern design and performance optimization"
  }
}

const breadcrumbItems = [
  { name: 'Home', url: '/' },
  { name: 'Website Development', url: '/website-development' }
]

export default function WebsiteDevelopmentPage() {
  return (
    <>
      <SchemaMarkup type="service" data={websiteServiceData} />
      <BreadcrumbSchema items={breadcrumbItems} />
      <WebsiteDevelopmentClient />
    </>
  )
}
