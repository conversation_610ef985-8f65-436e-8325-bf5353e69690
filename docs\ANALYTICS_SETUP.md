# Analytics Setup Guide - GenLogic v2

This guide will help you set up Google Analytics 4 and Google Tag Manager for comprehensive tracking of your GenLogic website.

## 🎯 What You'll Track

### Conversions
- Demo bookings (with business type and value)
- Contact form submissions (with subject categorization)
- Website development quote requests
- Newsletter signups

### Engagement
- Button clicks and CTA interactions
- Form interactions (start, errors, completion)
- Page views and navigation
- Blog article engagement
- Video plays and completions
- Scroll depth tracking

### Business Intelligence
- Lead source attribution
- Customer journey mapping
- Conversion funnel analysis
- ROI tracking by traffic source

## 📊 Step 1: Google Analytics 4 Setup

### 1.1 Create GA4 Property
1. Go to [Google Analytics](https://analytics.google.com)
2. Click "Create Account" or use existing account
3. Create a new property for GenLogic
4. Choose "Web" as platform
5. Enter your website URL: `https://genlogic.io`
6. Copy your **Measurement ID** (format: G-XXXXXXXXXX)

### 1.2 Configure Enhanced Ecommerce
1. In GA4, go to Admin → Data Streams
2. Click your web stream
3. Enable "Enhanced measurement"
4. Turn on all recommended events

### 1.3 Set Up Conversion Goals
Create these conversion events in GA4:
- `demo_booking` (Primary conversion)
- `contact_submission` (Secondary conversion)
- `website_quote_request` (High-value conversion)

## 🏷️ Step 2: Google Tag Manager Setup

### 2.1 Create GTM Container
1. Go to [Google Tag Manager](https://tagmanager.google.com)
2. Create new account for GenLogic
3. Create container for your website
4. Copy your **Container ID** (format: GTM-XXXXXXX)

### 2.2 Install GTM Code
The code is already integrated in your website. Just add your Container ID to environment variables.

### 2.3 Configure GTM Tags
Set up these tags in GTM:

#### GA4 Configuration Tag
- Tag Type: Google Analytics: GA4 Configuration
- Measurement ID: Your GA4 Measurement ID
- Trigger: All Pages

#### Conversion Tags
- Demo Booking Tag (trigger: demo_booking event)
- Contact Submission Tag (trigger: contact_submission event)
- Website Quote Tag (trigger: website_quote_request event)

## 🔧 Step 3: Environment Variables

Add these to your `.env.local` file:

```bash
# Google Analytics 4
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX

# Google Tag Manager
NEXT_PUBLIC_GTM_ID=GTM-XXXXXXX
```

## 📈 Step 4: Verify Installation

### 4.1 Test GA4 Tracking
1. Install [Google Analytics Debugger](https://chrome.google.com/webstore/detail/google-analytics-debugger/jnkmfdileelhofjcijamephohjechhna) Chrome extension
2. Visit your website
3. Check browser console for GA4 events
4. Verify events appear in GA4 Real-time reports

### 4.2 Test GTM Tracking
1. Install [Tag Assistant](https://chrome.google.com/webstore/detail/tag-assistant-legacy-by/kejbdjndbnbjgmefkgdddjlbokphdefk) Chrome extension
2. Visit your website
3. Check that GTM container loads
4. Verify tags fire correctly

### 4.3 Test Conversions
1. Submit demo form → Check for `demo_booking` event
2. Submit contact form → Check for `contact_submission` event
3. Request website quote → Check for `website_quote_request` event

## 📊 Step 5: Custom Dashboards

### 5.1 GA4 Custom Reports
Create these custom reports:
- **Conversion Funnel**: Page views → Form starts → Submissions
- **Lead Quality**: Conversion rate by traffic source
- **Business Type Analysis**: Demo bookings by business type
- **Geographic Performance**: Conversions by UK regions

### 5.2 GTM Variables
Set up these custom variables:
- Business Type (from form submissions)
- Form Source (demo vs contact)
- Conversion Value (dynamic based on service)
- User Journey Stage

## 🎯 Step 6: Advanced Tracking

### 6.1 Enhanced Ecommerce
Track demo bookings as ecommerce events:
```javascript
// Automatically tracked by our system
gtag('event', 'purchase', {
  transaction_id: 'demo_' + timestamp,
  value: 588, // Average demo value
  currency: 'GBP',
  items: [{
    item_id: 'demo_booking',
    item_name: 'Business Automation Demo',
    category: 'Services',
    quantity: 1,
    price: 588
  }]
})
```

### 6.2 Custom Dimensions
Set up these custom dimensions in GA4:
- Business Type
- Form Source
- Lead Score
- Customer Journey Stage
- Geographic Region

## 🔍 Step 7: Monitoring & Optimization

### 7.1 Weekly Reports
Monitor these KPIs weekly:
- Conversion rate by traffic source
- Form abandonment rate
- Average session duration
- Pages per session
- Bounce rate by page

### 7.2 Monthly Analysis
- Lead quality by source
- Customer acquisition cost
- Lifetime value trends
- Geographic performance
- Device/browser performance

## 🚨 Troubleshooting

### Common Issues
1. **Events not firing**: Check browser console for errors
2. **GTM not loading**: Verify Container ID is correct
3. **GA4 not tracking**: Check Measurement ID format
4. **Conversions not recording**: Verify event names match exactly

### Debug Tools
- GA4 DebugView
- GTM Preview Mode
- Browser Developer Tools
- Google Analytics Debugger Extension

## 📞 Support

If you need help with analytics setup:
- Check GA4 documentation
- Use GTM community forums
- Contact Google Analytics support
- Review our tracking implementation in the code

---

**Next Steps**: Once analytics are set up, consider adding:
- Facebook Pixel for retargeting
- Hotjar for user behavior analysis
- Microsoft Clarity for session recordings
- LinkedIn Insight Tag for B2B tracking
