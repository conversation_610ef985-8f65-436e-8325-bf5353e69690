import { Metadata } from 'next'
import PsychologyOfTimingClient from './PsychologyOfTimingClient'

export const metadata: Metadata = {
  title: 'The Psychology of Timing: When Prospects Actually Buy | GenLogic Academy',
  description: 'Discover why 73% of sales happen on the 5th-12th touchpoint and learn the buying window psychology most businesses miss. Master perfect timing automation.',
  keywords: 'sales timing, prospect psychology, buying window, sales touchpoints, timing automation, UK sales strategy',
  openGraph: {
    title: 'The Psychology of Timing: When Prospects Actually Buy',
    description: 'Discover why 73% of sales happen on the 5th-12th touchpoint and learn the buying window psychology most businesses miss. Master perfect timing automation.',
    type: 'article',
    url: 'https://genlogic.io/learn/fundamentals/psychology-of-timing',
    images: [
      {
        url: '/academy-images/og-psychology-of-timing.webp',
        width: 1200,
        height: 630,
        alt: 'The Psychology of Timing: When Prospects Actually Buy',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'The Psychology of Timing: When Prospects Actually Buy',
    description: 'Discover why 73% of sales happen on the 5th-12th touchpoint and learn the buying window psychology most businesses miss. Master perfect timing automation.',
    images: ['/academy-images/og-psychology-of-timing.webp'],
  },
}

export default function PsychologyOfTimingPage() {
  return <PsychologyOfTimingClient />
}
