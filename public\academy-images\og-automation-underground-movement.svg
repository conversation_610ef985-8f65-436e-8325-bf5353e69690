<svg width="1200" height="630" viewBox="0 0 1200 630" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#7c3aed;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#8b5cf6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="cardGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.95" />
      <stop offset="100%" style="stop-color:#f3e8ff;stop-opacity:0.9" />
    </linearGradient>
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f59e0b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f97316;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="8" stdDeviation="16" flood-color="#000000" flood-opacity="0.1"/>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="1200" height="630" fill="url(#bgGradient)"/>
  
  <!-- Decorative Elements -->
  <circle cx="100" cy="100" r="60" fill="#ffffff" opacity="0.1"/>
  <circle cx="1100" cy="530" r="80" fill="#ffffff" opacity="0.1"/>
  <path d="M950,50 Q1000,100 1050,50 Q1100,100 1150,50" stroke="#ffffff" stroke-width="3" fill="none" opacity="0.1"/>
  
  <!-- Main Content Card -->
  <rect x="80" y="80" width="1040" height="470" rx="24" fill="url(#cardGradient)" filter="url(#shadow)"/>
  
  <!-- GenLogic Academy Badge -->
  <rect x="120" y="120" width="180" height="40" rx="20" fill="#1e40af"/>
  <text x="210" y="140" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="14" font-weight="bold">GENLOGIC ACADEMY</text>
  
  <!-- Lightning/Automation Icon -->
  <g transform="translate(350, 135)">
    <circle cx="0" cy="0" r="25" fill="#7c3aed"/>
    <path d="M-8,-15 L8,-5 L-2,-5 L8,15 L-8,5 L2,5 Z" fill="white"/>
  </g>
  
  <!-- Underground Movement Visual -->
  <g transform="translate(420, 120)">
    <!-- Hidden Layer -->
    <rect x="0" y="0" width="300" height="8" rx="4" fill="#e5e7eb"/>
    <text x="150" y="-10" text-anchor="middle" fill="#64748b" font-family="Arial, sans-serif" font-size="12">Surface Level (What Everyone Sees)</text>
    
    <!-- Underground Layer -->
    <rect x="0" y="30" width="300" height="20" rx="10" fill="#7c3aed"/>
    <text x="150" y="20" text-anchor="middle" fill="#7c3aed" font-family="Arial, sans-serif" font-size="14" font-weight="bold">Underground Movement</text>
    <text x="150" y="65" text-anchor="middle" fill="#7c3aed" font-family="Arial, sans-serif" font-size="12">Smart Automation Patterns</text>
  </g>
  
  <!-- Main Headline -->
  <text x="120" y="250" fill="#1e293b" font-family="Arial, sans-serif" font-size="40" font-weight="bold">
    <tspan x="120" dy="0">The Underground</tspan>
    <tspan x="120" dy="50" fill="#7c3aed">Movement:</tspan>
    <tspan x="120" dy="50">How Smart Businesses</tspan>
    <tspan x="120" dy="50" fill="#7c3aed">Automate</tspan>
  </text>
  
  <!-- Subtitle -->
  <text x="120" y="420" fill="#64748b" font-family="Arial, sans-serif" font-size="18" font-weight="normal">
    <tspan x="120" dy="0">What 200+ UK businesses discovered about</tspan>
    <tspan x="120" dy="24">automation that changed everything</tspan>
  </text>
  
  <!-- Insights Box -->
  <g transform="translate(650, 180)">
    <rect x="0" y="0" width="400" height="300" rx="20" fill="#f8fafc" stroke="#e2e8f0" stroke-width="2"/>
    <text x="200" y="35" text-anchor="middle" fill="#1e293b" font-family="Arial, sans-serif" font-size="18" font-weight="bold">Underground Secrets</text>
    
    <!-- Secret 1 -->
    <rect x="30" y="60" width="340" height="50" rx="10" fill="#f3e8ff"/>
    <text x="50" y="80" fill="#7c3aed" font-family="Arial, sans-serif" font-size="16" font-weight="bold">4 Underground Principles</text>
    <text x="50" y="95" fill="#64748b" font-family="Arial, sans-serif" font-size="12">Behavior-based triggers that feel human</text>
    
    <!-- Secret 2 -->
    <rect x="30" y="125" width="340" height="50" rx="10" fill="#fef3c7"/>
    <text x="50" y="145" fill="#f59e0b" font-family="Arial, sans-serif" font-size="16" font-weight="bold">85% Time Saved</text>
    <text x="50" y="160" fill="#64748b" font-family="Arial, sans-serif" font-size="12">On admin tasks without losing personal touch</text>
    
    <!-- Secret 3 -->
    <rect x="30" y="190" width="340" height="50" rx="10" fill="#dcfce7"/>
    <text x="50" y="210" fill="#16a34a" font-family="Arial, sans-serif" font-size="16" font-weight="bold">Real Case Studies</text>
    <text x="50" y="225" fill="#64748b" font-family="Arial, sans-serif" font-size="12">From dental practices to fitness studios</text>
    
    <!-- Reading Info -->
    <rect x="30" y="255" width="160" height="30" rx="8" fill="#dbeafe"/>
    <text x="45" y="275" fill="#1e40af" font-family="Arial, sans-serif" font-size="14" font-weight="bold">15 min read</text>
    
    <rect x="210" y="255" width="160" height="30" rx="8" fill="#e0e7ff"/>
    <text x="225" y="275" fill="#6366f1" font-family="Arial, sans-serif" font-size="14" font-weight="bold">Lesson 2 of 5</text>
  </g>
  
  <!-- Bottom Stats -->
  <rect x="120" y="460" width="120" height="50" rx="10" fill="#f3e8ff"/>
  <text x="180" y="480" text-anchor="middle" fill="#7c3aed" font-family="Arial, sans-serif" font-size="16" font-weight="bold">200+</text>
  <text x="180" y="495" text-anchor="middle" fill="#64748b" font-family="Arial, sans-serif" font-size="12">UK Businesses</text>
  
  <rect x="260" y="460" width="120" height="50" rx="10" fill="#fef3c7"/>
  <text x="320" y="480" text-anchor="middle" fill="#f59e0b" font-family="Arial, sans-serif" font-size="16" font-weight="bold">Advanced</text>
  <text x="320" y="495" text-anchor="middle" fill="#64748b" font-family="Arial, sans-serif" font-size="12">Strategy</text>
  
  <!-- URL -->
  <text x="1080" y="600" text-anchor="end" fill="#64748b" font-family="Arial, sans-serif" font-size="14">genlogic.io/learn</text>
</svg>
