import { Metadata } from 'next'
import { SchemaMarkup, BreadcrumbSchema } from '@/components/seo/SchemaMarkup'
import SolutionsClient from './SolutionsClient'

export const metadata: Metadata = {
  title: 'Customer Conversion Solutions for UK Local Businesses - GenLogic',
  description: 'Discover how successful UK businesses are quietly transforming their operations with smart automation solutions. Join 500+ businesses converting 89% of prospects into customers.',
  keywords: 'business automation solutions UK, customer conversion system, local business automation, booking automation, SMS automation, email follow-up',
  openGraph: {
    title: 'Customer Conversion Solutions for UK Local Businesses - GenLogic',
    description: 'Discover how successful UK businesses are quietly transforming their operations with smart automation solutions.',
    url: 'https://genlogic.io/solutions',
    type: 'website',
    images: [
      {
        url: 'https://genlogic.io/og-solutions.jpg',
        width: 1200,
        height: 630,
        alt: 'GenLogic GoHighLevel Automation Solutions',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Customer Conversion Solutions for UK Local Businesses - GenLogic',
    description: 'Discover how successful UK businesses are quietly transforming their operations with smart automation solutions.',
    images: ['https://genlogic.io/og-solutions.jpg'],
  },
  alternates: {
    canonical: 'https://genlogic.io/solutions',
  },
}

const solutionsServiceData = {
  name: "GoHighLevel Automation Solutions",
  description: "White-label GoHighLevel automation solutions for UK local businesses. Booking automation, customer conversion, and revenue optimization.",
  serviceType: "Business Automation",
  offers: {
    priceCurrency: "GBP",
    description: "Complete automation solutions with GoHighLevel technology for UK local businesses"
  }
}

const breadcrumbItems = [
  { name: 'Home', url: '/' },
  { name: 'Solutions', url: '/solutions' }
]

export default function SolutionsPage() {
  return (
    <>
      <SchemaMarkup type="service" data={solutionsServiceData} />
      <BreadcrumbSchema items={breadcrumbItems} />
      <SolutionsClient />
    </>
  )
}