'use client'

import { motion } from 'framer-motion'
import { cn } from '@/lib/utils'
import { ReactNode } from 'react'

interface CardHoverEffectProps {
  icon: ReactNode
  title: string
  description: string
  variant?: 'purple' | 'blue' | 'amber' | 'rose'
  glowEffect?: boolean
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

const variantStyles = {
  purple: {
    gradient: 'from-purple-500/20 to-purple-500/5',
    iconBg: 'bg-gradient-to-br from-purple-500/20 to-purple-500/5',
    iconColor: 'text-purple-500',
    titleColor: 'text-purple-500',
    glow: 'shadow-purple-500/25',
  },
  blue: {
    gradient: 'from-blue-500/20 to-blue-500/5',
    iconBg: 'bg-gradient-to-br from-blue-500/20 to-blue-500/5',
    iconColor: 'text-blue-500',
    titleColor: 'text-blue-500',
    glow: 'shadow-blue-500/25',
  },
  amber: {
    gradient: 'from-amber-500/20 to-amber-500/5',
    iconBg: 'bg-gradient-to-br from-amber-500/20 to-amber-500/5',
    iconColor: 'text-amber-500',
    titleColor: 'text-amber-500',
    glow: 'shadow-amber-500/25',
  },
  rose: {
    gradient: 'from-rose-500/20 to-rose-500/5',
    iconBg: 'bg-gradient-to-br from-rose-500/20 to-rose-500/5',
    iconColor: 'text-rose-500',
    titleColor: 'text-rose-500',
    glow: 'shadow-rose-500/25',
  },
}

const sizeStyles = {
  sm: {
    card: 'p-4',
    icon: 'w-8 h-8 mb-3',
    iconContainer: 'w-10 h-10',
    title: 'text-sm font-semibold',
    description: 'text-xs',
  },
  md: {
    card: 'p-6',
    icon: 'w-6 h-6 mb-4',
    iconContainer: 'w-12 h-12',
    title: 'text-base font-semibold',
    description: 'text-sm',
  },
  lg: {
    card: 'p-8',
    icon: 'w-6 h-6 mb-6',
    iconContainer: 'w-14 h-14',
    title: 'text-lg font-semibold',
    description: 'text-base',
  },
}

export function CardHoverEffect({
  icon,
  title,
  description,
  variant = 'purple',
  glowEffect = false,
  size = 'md',
  className,
}: CardHoverEffectProps) {
  const variantStyle = variantStyles[variant]
  const sizeStyle = sizeStyles[size]

  return (
    <motion.div
      whileHover={{ 
        y: -5, 
        scale: 1.02,
        boxShadow: glowEffect ? `0 20px 40px ${variantStyle.glow}` : '0 20px 40px rgba(0,0,0,0.1)'
      }}
      className={cn(
        "group relative block overflow-hidden rounded-2xl border border-border/40 bg-gradient-to-br backdrop-blur-3xl transition-all duration-300",
        variantStyle.gradient,
        sizeStyle.card,
        className
      )}
    >
      <div className={cn(
        "inline-flex items-center justify-center rounded-2xl backdrop-blur-sm",
        variantStyle.iconBg,
        sizeStyle.iconContainer,
        sizeStyle.icon
      )}>
        <div className={variantStyle.iconColor}>
          {icon}
        </div>
      </div>

      <div className="space-y-2">
        <h3 className={cn(
          "font-semibold transition-colors",
          variantStyle.titleColor,
          sizeStyle.title
        )}>
          {title}
        </h3>

        <p className={cn(
          "text-muted-foreground leading-relaxed",
          sizeStyle.description
        )}>
          {description}
        </p>
      </div>

      {/* Hover effect overlay */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
    </motion.div>
  )
}
