'use client'

import { useEffect, useState } from 'react'
import { motion } from 'framer-motion'
import { 
  ArrowLeft, 
  Clock, 
  Users, 
  CheckCircle,
  ArrowRight,
  Brain,
  MessageSquare,
  Phone,
  Mail,
  Zap,
  TrendingUp,
  TrendingDown,
  Shield,
  Target,
  Lightbulb,
  AlertTriangle,
  Eye,
  Heart,
  Star,
  Headphones,
  Bot,
  UserCheck,
  Award,
  BarChart3,
  Gift,
  Share2,
  DollarSign,
  Calculator,
  Activity,
  Bell,
  Rocket,
  Building
} from 'lucide-react'
import Link from 'next/link'

const scalingBottlenecks = [
  {
    bottleneck: "Owner Dependency",
    revenue_ceiling: "£100k-150k",
    description: "Everything requires owner approval or involvement",
    symptoms: ["Working 60+ hours", "Can't take holidays", "All decisions through you", "Team waits for direction"],
    solution: "Decision delegation systems",
    breakthrough: "Autonomous team operations",
    color: "red"
  },
  {
    bottleneck: "Manual Processes", 
    revenue_ceiling: "£200k-300k",
    description: "Key business processes still done manually",
    symptoms: ["Repetitive daily tasks", "Human error issues", "Inconsistent delivery", "Team burnout"],
    solution: "Process automation",
    breakthrough: "Systematic efficiency",
    color: "orange"
  },
  {
    bottleneck: "Customer Acquisition",
    revenue_ceiling: "£400k-600k",
    description: "Growth limited by marketing and sales capacity",
    symptoms: ["Inconsistent lead flow", "Sales team maxed out", "High acquisition costs", "Market saturation"],
    solution: "Automated marketing funnels",
    breakthrough: "Predictable growth engine",
    color: "yellow"
  },
  {
    bottleneck: "Operational Complexity",
    revenue_ceiling: "£800k-1M",
    description: "Business becomes too complex to manage effectively",
    symptoms: ["Communication breakdowns", "Quality inconsistency", "Team coordination issues", "System conflicts"],
    solution: "Integrated automation platform",
    breakthrough: "Scalable infrastructure",
    color: "blue"
  }
]

const automationEvolution = [
  {
    stage: "Survival",
    revenue: "£0-50k",
    focus: "Manual everything, owner does all",
    automation: "Basic email templates",
    team_size: "1-2 people",
    priority: "Stay alive, find product-market fit",
    color: "red"
  },
  {
    stage: "Stability", 
    revenue: "£50k-150k",
    focus: "Consistent delivery, basic systems",
    automation: "CRM, basic workflows",
    team_size: "3-5 people",
    priority: "Reliable operations, customer satisfaction",
    color: "orange"
  },
  {
    stage: "Growth",
    revenue: "£150k-500k",
    focus: "Scaling operations, team building",
    automation: "Marketing automation, sales funnels",
    team_size: "6-15 people",
    priority: "Predictable growth, team efficiency",
    color: "yellow"
  },
  {
    stage: "Scale",
    revenue: "£500k-1M+",
    focus: "Systems integration, optimization",
    automation: "Full business automation platform",
    team_size: "15+ people",
    priority: "Systematic excellence, market dominance",
    color: "green"
  }
]

export default function ScaleBreakthroughClient() {
  // Scale ROI Calculator state
  const [scaleData, setScaleData] = useState({
    currentRevenue: '',
    targetRevenue: '',
    currentTeamSize: '',
    targetTeamSize: '',
    avgSalary: '',
    automationInvestment: ''
  })

  // Calculate scaling ROI
  const revenueIncrease = (parseFloat(scaleData.targetRevenue) || 0) - (parseFloat(scaleData.currentRevenue) || 0)
  const teamIncrease = (parseFloat(scaleData.targetTeamSize) || 0) - (parseFloat(scaleData.currentTeamSize) || 0)
  const teamCostIncrease = teamIncrease * (parseFloat(scaleData.avgSalary) || 0)
  const automationCost = parseFloat(scaleData.automationInvestment) || 0
  const netGain = revenueIncrease - teamCostIncrease - automationCost
  const roi = automationCost > 0 ? ((netGain / automationCost) * 100) : 0

  const handleScaleInputChange = (field: string, value: string) => {
    setScaleData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  // Mark lesson as completed when user visits
  useEffect(() => {
    const markAsCompleted = () => {
      const saved = localStorage.getItem('genlogic-academy-progress')
      const completed = saved ? JSON.parse(saved) : []
      if (!completed.includes('scale-breakthrough')) {
        completed.push('scale-breakthrough')
        localStorage.setItem('genlogic-academy-progress', JSON.stringify(completed))
        
        // Dispatch custom event to notify other components
        window.dispatchEvent(new CustomEvent('academyProgressUpdate'))
      }
    }
    
    // Mark as completed after user has been on page for 30 seconds
    const timer = setTimeout(markAsCompleted, 30000)
    return () => clearTimeout(timer)
  }, [])

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-green-50/30 to-background dark:from-background dark:via-green-950/30 dark:to-background">
      {/* Navigation */}
      <div className="pt-40 pb-8">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-5xl mx-auto">
            <Link 
              href="/learn" 
              className="inline-flex items-center gap-2 bg-background/80 backdrop-blur-sm border border-border/50 rounded-2xl px-4 py-2 text-primary-600 hover:text-primary-700 font-medium transition-all duration-300 hover:shadow-md"
            >
              <ArrowLeft className="w-4 h-4" />
              Back to Academy
            </Link>
          </div>
        </div>
      </div>

      {/* Hero Section */}
      <section className="pb-16">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-5xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              {/* Lesson Meta */}
              <div className="flex items-center justify-center gap-6 mb-8">
                <div className="flex items-center gap-2 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 px-4 py-2 rounded-2xl">
                  <Rocket className="w-4 h-4" />
                  <span className="font-medium text-sm">Scaling Strategy</span>
                </div>
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Clock className="w-4 h-4" />
                  <span className="text-sm">22 min read</span>
                </div>
                <div className="bg-muted px-3 py-1 rounded-full text-xs font-medium text-muted-foreground">
                  Growth Strategy
                </div>
              </div>

              {/* Main Title */}
              <h1 className="text-4xl md:text-6xl font-bold text-foreground mb-6 leading-tight">
                The Scale Breakthrough:
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-green-500 to-blue-500"> From £100k</span>
                <br />to
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-500 to-pink-500"> £1 Million</span>
              </h1>

              <p className="text-xl md:text-2xl text-muted-foreground mb-8 leading-relaxed max-w-4xl mx-auto">
                Something's happening across UK businesses that's making traditional scaling impossible. 
                <strong className="text-foreground"> While 89% of businesses plateau at £100k, smart business owners discovered the automation evolution that systematically breaks through every scaling bottleneck.</strong>
                Here's the 4-stage breakthrough system that transforms businesses from survival to market dominance.
              </p>

              {/* Shocking Stats */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
                <div className="bg-gradient-to-br from-red-50 to-red-100 dark:from-red-950/30 dark:to-red-900/30 rounded-3xl p-6 border border-red-200/50 dark:border-red-800/50">
                  <div className="flex items-center gap-3 mb-3">
                    <TrendingDown className="w-6 h-6 text-red-500" />
                    <div className="text-3xl font-bold text-red-600 dark:text-red-400">89%</div>
                  </div>
                  <div className="text-sm text-red-700 dark:text-red-300 font-medium">Of businesses plateau at £100k</div>
                </div>
                <div className="bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-950/30 dark:to-orange-900/30 rounded-3xl p-6 border border-orange-200/50 dark:border-orange-800/50">
                  <div className="flex items-center gap-3 mb-3">
                    <AlertTriangle className="w-6 h-6 text-orange-500" />
                    <div className="text-3xl font-bold text-orange-600 dark:text-orange-400">67%</div>
                  </div>
                  <div className="text-sm text-orange-700 dark:text-orange-300 font-medium">Hit owner dependency bottleneck</div>
                </div>
                <div className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950/30 dark:to-green-900/30 rounded-3xl p-6 border border-green-200/50 dark:border-green-800/50">
                  <div className="flex items-center gap-3 mb-3">
                    <TrendingUp className="w-6 h-6 text-green-500" />
                    <div className="text-3xl font-bold text-green-600 dark:text-green-400">340%</div>
                  </div>
                  <div className="text-sm text-green-700 dark:text-green-300 font-medium">Growth with systematic automation</div>
                </div>
              </div>
            </motion.div>

            {/* What You'll Learn Card */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="bg-gradient-to-br from-green-50 to-blue-50 dark:from-green-950/50 dark:to-blue-950/50 rounded-3xl p-8 border border-green-200/50 dark:border-green-800/50 shadow-xl"
            >
              <div className="flex items-start gap-6">
                <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-blue-500 rounded-2xl flex items-center justify-center flex-shrink-0">
                  <Brain className="w-8 h-8 text-white" />
                </div>
                <div className="flex-1">
                  <h3 className="text-2xl font-bold text-foreground mb-4">What You'll Master in This Lesson</h3>
                  <div className="grid md:grid-cols-2 gap-4">
                    <div className="space-y-3">
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-foreground">Why 89% of businesses plateau at £100k and how to break through</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-foreground">The 4 scaling bottlenecks that kill growth at predictable revenue levels</span>
                      </div>
                    </div>
                    <div className="space-y-3">
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-foreground">The automation evolution from survival to market dominance</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-foreground">Case study: £2.3M breakthrough with systematic automation</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Main Content - Scaling Bottlenecks */}
      <section className="pb-16">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-4xl mx-auto space-y-12">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="bg-background/80 backdrop-blur-sm rounded-3xl p-8 border border-border/50 shadow-xl"
            >
              <div className="flex items-start gap-6 mb-8">
                <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-orange-500 rounded-xl flex items-center justify-center flex-shrink-0">
                  <AlertTriangle className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h2 className="text-3xl font-bold text-foreground mb-4">The 4 Scaling Bottlenecks That Kill Growth</h2>
                  <p className="text-lg text-muted-foreground leading-relaxed">
                    Smart business owners discovered that scaling follows predictable patterns. Here are the 4 bottlenecks that stop growth at specific revenue levels and how to break through each one.
                  </p>
                </div>
              </div>

              <div className="space-y-8">
                {scalingBottlenecks.map((bottleneck, index) => (
                  <div key={index} className={`bg-gradient-to-r ${
                    bottleneck.color === 'red' ? 'from-red-50 to-red-100 dark:from-red-950/30 dark:to-red-900/30' :
                    bottleneck.color === 'orange' ? 'from-orange-50 to-orange-100 dark:from-orange-950/30 dark:to-orange-900/30' :
                    bottleneck.color === 'yellow' ? 'from-yellow-50 to-yellow-100 dark:from-yellow-950/30 dark:to-yellow-900/30' :
                    'from-blue-50 to-blue-100 dark:from-blue-950/30 dark:to-blue-900/30'
                  } rounded-2xl p-6 border ${
                    bottleneck.color === 'red' ? 'border-red-200/50 dark:border-red-800/50' :
                    bottleneck.color === 'orange' ? 'border-orange-200/50 dark:border-orange-800/50' :
                    bottleneck.color === 'yellow' ? 'border-yellow-200/50 dark:border-yellow-800/50' :
                    'border-blue-200/50 dark:border-blue-800/50'
                  }`}>
                    <div className="flex items-start gap-4 mb-6">
                      <div className={`w-16 h-16 rounded-xl flex items-center justify-center flex-shrink-0 ${
                        bottleneck.color === 'red' ? 'bg-red-500' :
                        bottleneck.color === 'orange' ? 'bg-orange-500' :
                        bottleneck.color === 'yellow' ? 'bg-yellow-500' :
                        'bg-blue-500'
                      }`}>
                        <span className="text-white font-bold text-lg">{index + 1}</span>
                      </div>
                      <div className="flex-1">
                        <h4 className={`text-xl font-bold mb-2 ${
                          bottleneck.color === 'red' ? 'text-red-700 dark:text-red-300' :
                          bottleneck.color === 'orange' ? 'text-orange-700 dark:text-orange-300' :
                          bottleneck.color === 'yellow' ? 'text-yellow-700 dark:text-yellow-300' :
                          'text-blue-700 dark:text-blue-300'
                        }`}>
                          {bottleneck.bottleneck}
                        </h4>
                        <div className={`text-lg font-bold mb-2 ${
                          bottleneck.color === 'red' ? 'text-red-600 dark:text-red-400' :
                          bottleneck.color === 'orange' ? 'text-orange-600 dark:text-orange-400' :
                          bottleneck.color === 'yellow' ? 'text-yellow-600 dark:text-yellow-400' :
                          'text-blue-600 dark:text-blue-400'
                        }`}>Revenue Ceiling: {bottleneck.revenue_ceiling}</div>
                        <p className={`mb-4 ${
                          bottleneck.color === 'red' ? 'text-red-600 dark:text-red-400' :
                          bottleneck.color === 'orange' ? 'text-orange-600 dark:text-orange-400' :
                          bottleneck.color === 'yellow' ? 'text-yellow-600 dark:text-yellow-400' :
                          'text-blue-600 dark:text-blue-400'
                        }`}>
                          {bottleneck.description}
                        </p>
                      </div>
                    </div>

                    <div className="grid md:grid-cols-3 gap-4">
                      <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
                        <h5 className={`font-bold mb-2 ${
                          bottleneck.color === 'red' ? 'text-red-600 dark:text-red-400' :
                          bottleneck.color === 'orange' ? 'text-orange-600 dark:text-orange-400' :
                          bottleneck.color === 'yellow' ? 'text-yellow-600 dark:text-yellow-400' :
                          'text-blue-600 dark:text-blue-400'
                        }`}>Warning Signs:</h5>
                        <ul className="space-y-1 text-sm">
                          {bottleneck.symptoms.map((symptom, symptomIndex) => (
                            <li key={symptomIndex} className={`${
                              bottleneck.color === 'red' ? 'text-red-600 dark:text-red-400' :
                              bottleneck.color === 'orange' ? 'text-orange-600 dark:text-orange-400' :
                              bottleneck.color === 'yellow' ? 'text-yellow-600 dark:text-yellow-400' :
                              'text-blue-600 dark:text-blue-400'
                            }`}>
                              • {symptom}
                            </li>
                          ))}
                        </ul>
                      </div>
                      <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
                        <h5 className={`font-bold mb-2 ${
                          bottleneck.color === 'red' ? 'text-red-600 dark:text-red-400' :
                          bottleneck.color === 'orange' ? 'text-orange-600 dark:text-orange-400' :
                          bottleneck.color === 'yellow' ? 'text-yellow-600 dark:text-yellow-400' :
                          'text-blue-600 dark:text-blue-400'
                        }`}>Solution:</h5>
                        <p className={`text-sm ${
                          bottleneck.color === 'red' ? 'text-red-600 dark:text-red-400' :
                          bottleneck.color === 'orange' ? 'text-orange-600 dark:text-orange-400' :
                          bottleneck.color === 'yellow' ? 'text-yellow-600 dark:text-yellow-400' :
                          'text-blue-600 dark:text-blue-400'
                        }`}>
                          {bottleneck.solution}
                        </p>
                      </div>
                      <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
                        <h5 className={`font-bold mb-2 ${
                          bottleneck.color === 'red' ? 'text-red-600 dark:text-red-400' :
                          bottleneck.color === 'orange' ? 'text-orange-600 dark:text-orange-400' :
                          bottleneck.color === 'yellow' ? 'text-yellow-600 dark:text-yellow-400' :
                          'text-blue-600 dark:text-blue-400'
                        }`}>Breakthrough:</h5>
                        <p className={`text-sm ${
                          bottleneck.color === 'red' ? 'text-red-600 dark:text-red-400' :
                          bottleneck.color === 'orange' ? 'text-orange-600 dark:text-orange-400' :
                          bottleneck.color === 'yellow' ? 'text-yellow-600 dark:text-yellow-400' :
                          'text-blue-600 dark:text-blue-400'
                        }`}>
                          {bottleneck.breakthrough}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>

            {/* The Automation Evolution */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="bg-background/80 backdrop-blur-sm rounded-3xl p-8 border border-border/50 shadow-xl"
            >
              <div className="flex items-start gap-6 mb-8">
                <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-blue-500 rounded-xl flex items-center justify-center flex-shrink-0">
                  <Rocket className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h2 className="text-3xl font-bold text-foreground mb-4">The Automation Evolution: From Survival to Dominance</h2>
                  <p className="text-lg text-muted-foreground leading-relaxed">
                    Smart businesses discovered that automation needs evolve with revenue. Here's the 4-stage evolution that systematically breaks through every scaling bottleneck.
                  </p>
                </div>
              </div>

              <div className="space-y-8">
                {automationEvolution.map((stage, index) => (
                  <div key={index} className={`bg-gradient-to-r ${
                    stage.color === 'red' ? 'from-red-50 to-red-100 dark:from-red-950/30 dark:to-red-900/30' :
                    stage.color === 'orange' ? 'from-orange-50 to-orange-100 dark:from-orange-950/30 dark:to-orange-900/30' :
                    stage.color === 'yellow' ? 'from-yellow-50 to-yellow-100 dark:from-yellow-950/30 dark:to-yellow-900/30' :
                    'from-green-50 to-green-100 dark:from-green-950/30 dark:to-green-900/30'
                  } rounded-2xl p-6 border ${
                    stage.color === 'red' ? 'border-red-200/50 dark:border-red-800/50' :
                    stage.color === 'orange' ? 'border-orange-200/50 dark:border-orange-800/50' :
                    stage.color === 'yellow' ? 'border-yellow-200/50 dark:border-yellow-800/50' :
                    'border-green-200/50 dark:border-green-800/50'
                  }`}>
                    <div className="flex items-start gap-4 mb-6">
                      <div className={`w-16 h-16 rounded-xl flex items-center justify-center flex-shrink-0 ${
                        stage.color === 'red' ? 'bg-red-500' :
                        stage.color === 'orange' ? 'bg-orange-500' :
                        stage.color === 'yellow' ? 'bg-yellow-500' :
                        'bg-green-500'
                      }`}>
                        <Building className="w-8 h-8 text-white" />
                      </div>
                      <div className="flex-1">
                        <h4 className={`text-xl font-bold mb-2 ${
                          stage.color === 'red' ? 'text-red-700 dark:text-red-300' :
                          stage.color === 'orange' ? 'text-orange-700 dark:text-orange-300' :
                          stage.color === 'yellow' ? 'text-yellow-700 dark:text-yellow-300' :
                          'text-green-700 dark:text-green-300'
                        }`}>
                          Stage {index + 1}: {stage.stage}
                        </h4>
                        <div className={`text-lg font-bold mb-2 ${
                          stage.color === 'red' ? 'text-red-600 dark:text-red-400' :
                          stage.color === 'orange' ? 'text-orange-600 dark:text-orange-400' :
                          stage.color === 'yellow' ? 'text-yellow-600 dark:text-yellow-400' :
                          'text-green-600 dark:text-green-400'
                        }`}>Revenue: {stage.revenue}</div>
                        <p className={`mb-4 ${
                          stage.color === 'red' ? 'text-red-600 dark:text-red-400' :
                          stage.color === 'orange' ? 'text-orange-600 dark:text-orange-400' :
                          stage.color === 'yellow' ? 'text-yellow-600 dark:text-yellow-400' :
                          'text-green-600 dark:text-green-400'
                        }`}>
                          <strong>Focus:</strong> {stage.focus}
                        </p>
                      </div>
                    </div>

                    <div className="grid md:grid-cols-3 gap-4">
                      <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
                        <h5 className={`font-bold mb-2 ${
                          stage.color === 'red' ? 'text-red-600 dark:text-red-400' :
                          stage.color === 'orange' ? 'text-orange-600 dark:text-orange-400' :
                          stage.color === 'yellow' ? 'text-yellow-600 dark:text-yellow-400' :
                          'text-green-600 dark:text-green-400'
                        }`}>Automation Level:</h5>
                        <p className={`text-sm ${
                          stage.color === 'red' ? 'text-red-600 dark:text-red-400' :
                          stage.color === 'orange' ? 'text-orange-600 dark:text-orange-400' :
                          stage.color === 'yellow' ? 'text-yellow-600 dark:text-yellow-400' :
                          'text-green-600 dark:text-green-400'
                        }`}>
                          {stage.automation}
                        </p>
                      </div>
                      <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
                        <h5 className={`font-bold mb-2 ${
                          stage.color === 'red' ? 'text-red-600 dark:text-red-400' :
                          stage.color === 'orange' ? 'text-orange-600 dark:text-orange-400' :
                          stage.color === 'yellow' ? 'text-yellow-600 dark:text-yellow-400' :
                          'text-green-600 dark:text-green-400'
                        }`}>Team Size:</h5>
                        <p className={`text-sm ${
                          stage.color === 'red' ? 'text-red-600 dark:text-red-400' :
                          stage.color === 'orange' ? 'text-orange-600 dark:text-orange-400' :
                          stage.color === 'yellow' ? 'text-yellow-600 dark:text-yellow-400' :
                          'text-green-600 dark:text-green-400'
                        }`}>
                          {stage.team_size}
                        </p>
                      </div>
                      <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
                        <h5 className={`font-bold mb-2 ${
                          stage.color === 'red' ? 'text-red-600 dark:text-red-400' :
                          stage.color === 'orange' ? 'text-orange-600 dark:text-orange-400' :
                          stage.color === 'yellow' ? 'text-yellow-600 dark:text-yellow-400' :
                          'text-green-600 dark:text-green-400'
                        }`}>Priority:</h5>
                        <p className={`text-sm ${
                          stage.color === 'red' ? 'text-red-600 dark:text-red-400' :
                          stage.color === 'orange' ? 'text-orange-600 dark:text-orange-400' :
                          stage.color === 'yellow' ? 'text-yellow-600 dark:text-yellow-400' :
                          'text-green-600 dark:text-green-400'
                        }`}>
                          {stage.priority}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>

            {/* Call to Action */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              className="bg-gradient-to-r from-green-500 to-blue-500 rounded-3xl p-8 text-white text-center"
            >
              <h2 className="text-3xl font-bold mb-4">🎉 Congratulations! You've Completed the Academy</h2>
              <p className="text-xl mb-6 opacity-90">
                Smart business owners are quietly discovering what successful companies already know about scaling breakthrough. The shift is happening whether you participate or not.
              </p>
              <Link
                href="/demo"
                className="inline-flex items-center gap-2 bg-white text-green-600 px-8 py-4 rounded-2xl font-bold text-lg hover:bg-gray-100 transition-all duration-300 shadow-lg hover:shadow-xl"
              >
                See What They Know
                <ArrowRight className="w-5 h-5" />
              </Link>
              <p className="text-sm mt-4 opacity-75">
                Join the business owners who've already discovered the scaling revolution
              </p>
            </motion.div>

            {/* Final Lesson Navigation */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.7 }}
              className="bg-background rounded-3xl border border-border shadow-lg p-8"
            >
              <h3 className="text-xl font-bold text-foreground mb-6 text-center">🎉 Academy Complete!</h3>

              <div className="grid md:grid-cols-2 gap-6">
                <Link
                  href="/demo"
                  className="group bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950/30 dark:to-green-900/30 rounded-2xl p-6 border border-green-200/50 dark:border-green-800/50 hover:shadow-lg transition-all duration-300"
                >
                  <div className="flex items-center gap-4 mb-4">
                    <div className="w-12 h-12 bg-green-100 dark:bg-green-900/50 rounded-xl flex items-center justify-center">
                      <ArrowRight className="w-6 h-6 text-green-600 group-hover:translate-x-1 transition-transform" />
                    </div>
                    <div>
                      <h4 className="font-bold text-foreground">Ready to Implement?</h4>
                      <p className="text-sm text-muted-foreground">Book your strategy call</p>
                    </div>
                  </div>
                  <h5 className="font-semibold text-foreground mb-2">See What They Know</h5>
                  <p className="text-sm text-muted-foreground">
                    Join the business owners who've already discovered the scaling revolution
                  </p>
                </Link>

                <Link
                  href="/learn"
                  className="group bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-950/30 dark:to-gray-900/30 rounded-2xl p-6 border border-gray-200/50 dark:border-gray-800/50 hover:shadow-lg transition-all duration-300"
                >
                  <div className="flex items-center gap-4 mb-4">
                    <div className="w-12 h-12 bg-gray-100 dark:bg-gray-900/50 rounded-xl flex items-center justify-center">
                      <ArrowLeft className="w-6 h-6 text-gray-600 group-hover:-translate-x-1 transition-transform" />
                    </div>
                    <div>
                      <h4 className="font-bold text-foreground">Academy</h4>
                      <p className="text-sm text-muted-foreground">All lessons</p>
                    </div>
                  </div>
                  <h5 className="font-semibold text-foreground mb-2">Back to Academy</h5>
                  <p className="text-sm text-muted-foreground">
                    Review lessons and track your progress
                  </p>
                </Link>
              </div>
            </motion.div>
          </div>
        </div>
      </section>
    </div>
  )
}
