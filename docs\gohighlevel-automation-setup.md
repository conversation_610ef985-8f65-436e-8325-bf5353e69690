# GoHighLevel Automation Setup Guide for GenLogic

This guide will help you set up sophisticated automation workflows in GoHighLevel that leverage the dynamic tagging system from your GenLogic website forms.

## 🏷️ Tag Structure Overview

### Form Source Tags
- `contact_page` - Contact form submissions
- `demo_page` - Demo form submissions

### Intent Tags
- `high_intent` - Demo requests (score 80+)
- `general_inquiry` - Contact form submissions
- `sales_ready` - Demo qualified leads
- `nurture_required` - General inquiries needing nurturing

### Subject Tags (Contact Form)
- `subject_general` - General inquiries
- `subject_support` - Technical support requests
- `subject_billing` - Billing questions
- `subject_demo` - Demo requests via contact form
- `subject_partnership` - Partnership opportunities
- `subject_other` - Other inquiries

### Business Type Tags (Demo Form)
- `business_salon` - Hair Salon / Beauty
- `business_fitness` - Gym / Fitness Studio
- `business_healthcare` - Healthcare / Clinic
- `business_automotive` - Automotive Services
- `business_home_improvement` - Home Improvement
- `business_professional` - Professional Services
- `business_other` - Other business types

### Lead Score Tags
- `score_hot` - 80+ points (demo requests)
- `score_warm` - 60-79 points
- `score_qualified` - 40-59 points
- `score_cold` - Below 40 points

### Value Tags
- `high_value_prospect` - Healthcare/Professional (£98/month potential)
- `premium_segment` - High-value business types
- `urgent_inquiry` - Contains urgent keywords

## 🔄 Recommended Workflow Structure

### 1. Master Contact Workflow
**Trigger**: Tag added = `genlogic_website`
**Purpose**: Initial contact processing and routing

**Steps**:
1. Wait 2 minutes (allow all tags to be applied)
2. If/Else based on tags:
   - Has `demo_page` → Route to Demo Workflow
   - Has `contact_page` → Route to Contact Workflow
3. Add to appropriate nurture sequence

### 2. Demo Request Workflow (High Priority)
**Trigger**: Tag added = `demo_page`
**Purpose**: Fast response for demo requests

**Steps**:
1. **Immediate Response** (0 minutes)
   - Send SMS: "Thanks for requesting a demo! We'll call you within 2 hours to schedule your 15-minute session."
   - Send Email: Branded demo confirmation with calendar link

2. **Business Type Routing** (5 minutes)
   - If/Else based on business type tags:
     - `business_healthcare` OR `business_professional` → High-value sequence
     - `business_salon` OR `business_fitness` → Standard sequence
     - `business_home_improvement` → Starter sequence

3. **Follow-up Sequence**
   - 2 hours: Internal task to call prospect
   - 4 hours: SMS reminder if no response
   - 1 day: Email with case studies for their business type
   - 3 days: Final follow-up with special offer

### 3. Contact Form Workflow
**Trigger**: Tag added = `contact_page`
**Purpose**: Subject-specific responses

**Steps**:
1. **Subject-Based Routing** (0 minutes)
   - If/Else based on subject tags:
     - `subject_support` → Technical support sequence
     - `subject_billing` → Billing team notification
     - `subject_demo` → Route to demo workflow
     - `subject_partnership` → Partnership team notification
     - `subject_general` → General inquiry sequence

2. **Response Timing**
   - Support: Immediate auto-response + 1-hour team notification
   - Billing: Immediate response + team notification
   - General: 2-hour response target
   - Partnership: 24-hour response (business hours)

### 4. Business Type Specific Sequences

#### Healthcare/Professional (Premium)
- Focus on compliance, security, GDPR
- Case studies from medical practices
- ROI calculations for professional services
- Premium feature highlights

#### Salon/Fitness (Standard)
- No-show reduction statistics
- Customer retention benefits
- Social proof from similar businesses
- Standard feature set

#### Home Improvement (Starter)
- Cost-effective solutions
- Simple setup process
- Basic automation benefits
- Starter plan promotion

## 📊 Automation Triggers by Tag Combinations

### High-Priority Triggers
```
demo_page + high_value_prospect = Immediate sales team notification
demo_page + urgent_inquiry = 30-minute response SLA
business_healthcare + demo_page = Premium demo script
```

### Nurture Triggers
```
contact_page + subject_general = 7-day nurture sequence
score_qualified + business_salon = Salon-specific case studies
score_warm + NOT demo_page = Demo invitation sequence
```

### Re-engagement Triggers
```
30 days no activity + score_hot = Re-engagement campaign
60 days no activity + demo_page = Special offer sequence
```

## 🎯 Segmentation Strategies

### For Email Campaigns
- **Segment 1**: `demo_page` + `business_healthcare` (Premium prospects)
- **Segment 2**: `demo_page` + `business_salon` (Standard prospects)
- **Segment 3**: `contact_page` + `subject_general` (Nurture prospects)
- **Segment 4**: `score_hot` + NOT `demo_page` (Demo invitation)

### For SMS Campaigns
- **Immediate**: `demo_page` (Demo confirmations)
- **Follow-up**: `urgent_inquiry` (Priority responses)
- **Nurture**: `score_warm` + 7 days no activity

## 🔧 Custom Field Mapping

Ensure these custom fields are created in GoHighLevel:

### Contact Fields
- `form_source` (text) - Maps to form source tag
- `lead_score` (number) - Numerical lead score
- `business_type` (dropdown) - Business category
- `subject` (text) - Contact form subject
- `submission_date` (date) - Form submission timestamp
- `opportunity_value` (number) - Potential deal value

### Opportunity Fields
- `demo_requested` (checkbox) - Demo request flag
- `business_segment` (dropdown) - Premium/Standard/Starter
- `urgency_level` (dropdown) - Normal/High/Urgent

## 📈 Performance Tracking

### Key Metrics to Monitor
1. **Response Times by Tag**
   - `demo_page` average response time
   - `urgent_inquiry` response time
   - `subject_support` resolution time

2. **Conversion Rates by Segment**
   - Demo show rate by business type
   - Contact-to-demo conversion by subject
   - Tag-to-customer conversion rates

3. **Automation Effectiveness**
   - Open rates by business type
   - Click rates by lead score
   - Response rates by urgency level

## 🚀 Implementation Checklist

### Phase 1: Basic Setup
- [ ] Create master contact workflow
- [ ] Set up demo request workflow
- [ ] Configure contact form workflow
- [ ] Test all trigger conditions

### Phase 2: Advanced Segmentation
- [ ] Create business type specific sequences
- [ ] Set up lead score based automation
- [ ] Configure urgency-based routing
- [ ] Test tag combinations

### Phase 3: Optimization
- [ ] Monitor performance metrics
- [ ] A/B test message content by segment
- [ ] Optimize timing based on response rates
- [ ] Refine tag-based routing

This setup will give you sophisticated, targeted automation that responds intelligently to each prospect's specific situation and intent level.
