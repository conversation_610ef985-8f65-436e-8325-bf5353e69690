import { NextRequest, NextResponse } from 'next/server'
import { ghlService, formatContactData, validateGDPRConsent, generateFormSourceTag } from '@/lib/gohighlevel'
import { z } from 'zod'

// Validation schemas for different form types with enhanced dropdown validation
const contactFormSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
  phone: z.string().optional(),
  subject: z.enum([
    'website',
    'automation',
    'general',
    'support',
    'billing',
    'partnership',
    'other'
  ], 'Please select a valid subject'),
  message: z.string().min(10, 'Message must be at least 10 characters'),
  gdprConsent: z.boolean().refine(val => val === true, 'GDPR consent is required'),
  formType: z.literal('contact')
})

const demoFormSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
  phone: z.string().optional(),
  business: z.string().min(2, 'Business name is required'),
  businessType: z.enum([
    'salon',
    'fitness',
    'healthcare',
    'automotive',
    'home-improvement',
    'professional',
    'other'
  ], 'Please select a valid business type'),
  message: z.string().optional(),
  gdprConsent: z.boolean().refine(val => val === true, 'GDPR consent is required'),
  formType: z.literal('demo')
})

// Rate limiting (simple in-memory store - use Redis in production)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>()

function checkRateLimit(ip: string): boolean {
  const now = Date.now()
  const windowMs = 15 * 60 * 1000 // 15 minutes
  const maxRequests = 5

  const record = rateLimitStore.get(ip)
  
  if (!record || now > record.resetTime) {
    rateLimitStore.set(ip, { count: 1, resetTime: now + windowMs })
    return true
  }

  if (record.count >= maxRequests) {
    return false
  }

  record.count++
  return true
}

export async function POST(request: NextRequest) {
  try {
    // Get client IP for rate limiting
    const ip = request.ip || request.headers.get('x-forwarded-for') || 'unknown'
    
    // Check rate limit
    if (!checkRateLimit(ip)) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Too many requests. Please try again in 15 minutes.' 
        },
        { status: 429 }
      )
    }

    // Parse request body
    const body = await request.json()
    
    // Validate based on form type
    let validatedData
    try {
      if (body.formType === 'contact') {
        validatedData = contactFormSchema.parse(body)
      } else if (body.formType === 'demo') {
        validatedData = demoFormSchema.parse(body)
      } else {
        throw new Error('Invalid form type')
      }
    } catch (validationError) {
      if (validationError instanceof z.ZodError) {
        return NextResponse.json(
          { 
            success: false, 
            error: 'Validation failed',
            details: validationError.issues.map(err => ({
              field: err.path.join('.'),
              message: err.message
            }))
          },
          { status: 400 }
        )
      }
      throw validationError
    }

    // Validate GDPR consent
    if (!validateGDPRConsent(validatedData)) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'GDPR consent is required for UK businesses' 
        },
        { status: 400 }
      )
    }

    // Format data for GoHighLevel
    const source = validatedData.formType === 'demo' ? 'demo_booking' : 'contact_form'
    const contactData = formatContactData(validatedData, source)

    // Submit to GoHighLevel
    const ghlResult = await ghlService.createContact(contactData)

    if (!ghlResult.success) {
      console.error('GHL submission failed:', ghlResult.error)
      
      // Still return success to user but log the error
      // You might want to implement a fallback like email notification
      return NextResponse.json({
        success: true,
        message: getSuccessMessage(validatedData.formType),
        warning: 'Form submitted but there was an issue with our CRM integration'
      })
    }

    // Create opportunities based on form type and subject
    if (ghlResult.contactId) {
      if (validatedData.formType === 'demo') {
        // Calculate opportunity value based on business type
        const businessTypeValues = {
          'healthcare': 1176, // £98/month * 12 months (premium segment)
          'professional': 1176, // £98/month * 12 months (premium segment)
          'automotive': 588, // £49/month * 12 months (standard)
          'salon': 588, // £49/month * 12 months (standard)
          'fitness': 588, // £49/month * 12 months (standard)
          'home-improvement': 348, // £29/month * 12 months (starter)
          'other': 588 // Default to standard
        }

        const opportunityValue = businessTypeValues[validatedData.businessType as keyof typeof businessTypeValues] || 588

        await ghlService.createOpportunity(ghlResult.contactId, {
          title: `Demo Request - ${validatedData.business} (${validatedData.businessType})`,
          value: opportunityValue,
          source: `website_demo_${validatedData.businessType}`,
          stage: 'demo_requested'
        })

        // Add additional tags based on opportunity value
        if (opportunityValue >= 1000) {
          await ghlService.addTags(ghlResult.contactId, ['high_value_prospect', 'premium_segment'])
        }
      } else if (validatedData.formType === 'contact' && validatedData.subject === 'website') {
        // Create opportunity for website development inquiries
        await ghlService.createOpportunity(ghlResult.contactId, {
          title: `Website Development Quote - ${validatedData.name}`,
          value: 2500, // Average website project value
          source: 'website_development_inquiry',
          stage: 'quote_requested'
        })

        // Add website-specific tags
        await ghlService.addTags(ghlResult.contactId, ['website_development', 'quote_requested', 'high_value_prospect'])
      } else if (validatedData.formType === 'contact' && validatedData.subject === 'automation') {
        // Create opportunity for automation inquiries from contact form
        await ghlService.createOpportunity(ghlResult.contactId, {
          title: `Business Automation Inquiry - ${validatedData.name}`,
          value: 588, // Standard automation value
          source: 'automation_contact_inquiry',
          stage: 'inquiry_received'
        })

        // Add automation-specific tags
        await ghlService.addTags(ghlResult.contactId, ['automation_inquiry', 'contact_form_lead'])
      }
    }

    // Send success response
    return NextResponse.json({
      success: true,
      message: getSuccessMessage(validatedData.formType),
      contactId: ghlResult.contactId
    })

  } catch (error) {
    console.error('Form submission error:', error)
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'An unexpected error occurred. Please try again or contact us directly.' 
      },
      { status: 500 }
    )
  }
}

function getSuccessMessage(formType: string): string {
  if (formType === 'demo') {
    return "Demo request received! We'll contact you within 24 hours to schedule your personalized 15-minute demo."
  } else {
    return "Message received! Our UK-based team will respond within 24 hours."
  }
}

// Handle preflight requests for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  })
}
