import { MetadataRoute } from 'next'
import { getAllBlogPosts } from './blog/posts'

export default function sitemap(): MetadataRoute.Sitemap {
  const posts = getAllBlogPosts()
  const siteUrl = 'https://genlogic.io'

  // Ensure clean sitemap generation (prevent script injection)

  // Only include final destination URLs - no redirected pages
  // These are the actual pages that exist and should be indexed
  const staticPages = [
    '', // Homepage
    '/about',
    '/contact',
    '/demo',
    '/pricing',
    '/blog',
    '/solutions', // GoHighLevel automation solutions page
    '/services/business-automation', // Main service page (destination for redirects)
    '/website-development',
    '/privacy',
    '/terms',
    '/faq',
    '/learn',
    // Academy lesson pages
    '/learn/fundamentals/why-follow-ups-fail',
    '/learn/fundamentals/automation-underground-movement',
    '/learn/fundamentals/three-touch-rule',
    '/learn/fundamentals/chaos-to-control',
    '/learn/fundamentals/silent-revolution',
    '/learn/fundamentals/psychology-of-timing',
    '/learn/fundamentals/invisible-influence',
    '/learn/fundamentals/multi-channel-revolution',
    '/learn/fundamentals/delegation-dilemma',
    '/learn/fundamentals/revenue-leak-detector',
    '/learn/fundamentals/retention-revolution',
    '/learn/fundamentals/referral-engine',
    '/learn/fundamentals/pricing-psychology-revolution',
    '/learn/fundamentals/retention-secret',
    '/learn/fundamentals/scale-breakthrough',
  ]

  // Validate that all pages exist and are not redirected
  const validatedPages = staticPages.filter(page => {
    // Exclude any pages that would be redirected by middleware
    const redirectedPaths = [
      '/services/lead-conversion',
      '/services/revenue-growth',
      '/services/sms-automation',
      '/services/email-automation',
      '/services/booking-system',
      '/services/customer-conversion',
      '/services/sales-optimization',
      '/automation',
      '/booking',
      '/sms',
      '/email',
      '/leads',
      '/conversion',
      '/sales',
      '/consultation',
      '/quote',
      '/get-started',
      '/start',
      '/trial'
    ]

    return !redirectedPaths.includes(page)
  })

  const staticUrls = validatedPages.map(page => {
    // No need to sanitize staticPages since we control them, but keep for paranoia
    const cleanPage = page.replace(/[<>'"&]/g, '')
    return {
      url: `${siteUrl}${cleanPage}`,
      lastModified: new Date().toISOString(),
      changeFrequency: (cleanPage === '' || cleanPage === '/blog' ? 'daily' : 'weekly') as 'daily' | 'weekly',
      priority: cleanPage === '' ? 1.0 :
                cleanPage === '/blog' ? 0.9 :
                cleanPage === '/learn' ? 0.9 :
                cleanPage === '/solutions' ? 0.9 :
                cleanPage.startsWith('/learn/fundamentals/') ? 0.8 : 0.7,
    }
  })

  const blogUrls = posts
    .filter(post => {
      // Only include published posts with valid data
      return (
        post.slug &&
        post.slug.trim() !== '' &&
        post.publishedAt &&
        !post.slug.includes('<script') &&
        !post.slug.includes('javascript:') &&
        // Exclude future-dated posts (if any)
        new Date(post.publishedAt) <= new Date()
      )
    })
    .map(post => {
      // Sanitize blog slug to prevent any injection and ensure URL safety
      const cleanSlug = post.slug
        .replace(/[<>'"&]/g, '')
        .replace(/[^\w\-]/g, '-') // Keep only alphanumeric, underscore, and hyphen
        .replace(/-+/g, '-')
        .replace(/^-|-$/g, '')
        .toLowerCase()

      // Ensure valid publishedAt date, fallback to current date
      const publishDate = new Date(post.publishedAt)
      const isValidDate = !isNaN(publishDate.getTime())

      return {
        url: `${siteUrl}/blog/${cleanSlug}`,
        lastModified: isValidDate ? publishDate.toISOString() : new Date().toISOString(),
        changeFrequency: 'monthly' as 'monthly',
        priority: post.featured ? 0.8 : 0.7, // Higher priority for featured posts
      }
    })

  // Log sitemap generation for debugging (development only)
  if (typeof window === 'undefined' && process.env.NODE_ENV !== 'production') {
    console.log(`[SITEMAP] Generated ${staticUrls.length} static URLs and ${blogUrls.length} blog URLs`)
  }

  // Combine and validate all URLs
  const sitemapData = [...staticUrls, ...blogUrls]

  // Final comprehensive validation for XML sitemap compliance
  const validatedSitemap = sitemapData.filter(item => {
    // Ensure URL is valid and safe
    const isValidUrl = (
      item.url &&
      item.url.startsWith('http') &&
      !item.url.includes('<script') &&
      !item.url.includes('javascript:') &&
      !item.url.includes(' ') &&
      item.url.length < 2048 && // Google's URL length limit
      item.lastModified &&
      typeof item.lastModified === 'string'
    )

    // Log invalid URLs in development
    if (!isValidUrl && process.env.NODE_ENV !== 'production') {
      console.warn(`[SITEMAP] Invalid URL filtered out: ${item.url}`)
    }

    return isValidUrl
  })

  // Sort by priority (highest first) for better crawl order
  return validatedSitemap.sort((a, b) => (b.priority || 0) - (a.priority || 0))
}
