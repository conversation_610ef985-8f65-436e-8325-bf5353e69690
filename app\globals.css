@tailwind base;
@tailwind components;
@tailwind utilities;

/* BULLETPROOF HIDING - Multiple methods to ensure content is NEVER visible */
.sr-only {
  position: absolute !important;
  left: -10000px !important;
  top: -10000px !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  z-index: -9999 !important;
  max-width: 1px !important;
  max-height: 1px !important;
}

/* Performance and Accessibility Optimizations */
@layer base {
  /* Fix deprecated API warning for image overflow */
  img, video, canvas {
    /* Use modern clip value instead of deprecated visible */
    overflow: clip;
    /* Fallback for older browsers */
    overflow: hidden;
    /* Ensure proper containment for shared element transitions */
    contain: layout;
  }

  /* Additional media element optimizations */
  img {
    /* Prevent layout shift during loading */
    height: auto;
    max-width: 100%;
    /* Modern image rendering */
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }

  video {
    /* Ensure proper video containment */
    object-fit: cover;
    contain: layout style;
  }

  canvas {
    /* Prevent canvas overflow issues */
    contain: layout style paint;
  }

  /* Improve touch targets - exclude theme toggle and small buttons */
  button:not([data-theme-toggle]):not(.btn-sm),
  a:not(.btn-sm),
  input,
  select,
  textarea {
    min-height: 44px;
  }

  button:not([data-theme-toggle]):not(.btn-sm):not(.icon-only),
  a:not(.btn-sm):not(.icon-only) {
    min-width: 44px;
  }

  /* Reduce layout shifts */
  img {
    height: auto;
    max-width: 100%;
  }

  /* Optimize font loading - handled by Next.js font optimization */

  /* Improve focus visibility */
  :focus-visible {
    outline: 2px solid hsl(var(--primary));
    outline-offset: 2px;
  }

  /* Optimize animations for reduced motion */
  @media (prefers-reduced-motion: reduce) {
    *, *::before, *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
}

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  --foreground-rgb: 15, 23, 42;
  --background-start-rgb: 248, 250, 252;
  --background-end-rgb: 255, 255, 255;

  /* Light mode professional color palette */
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --muted: 210 40% 98%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --border: 214.3 31.8% 91.4%;
  --primary: 248 68% 70%;
  --primary-foreground: 210 40% 98%;

  /* Custom GenLogic brand colors */
  --navy: 222.2 84% 4.9%;
  --warm-gray: 215.4 16.3% 46.9%;
  --accent-orange: 24 95% 53%;
  --success-green: 142 76% 36%;
}

.dark {
  --foreground-rgb: 248, 250, 252;
  --background-start-rgb: 15, 23, 42;
  --background-end-rgb: 2, 6, 23;

  /* Dark mode professional color palette */
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;
  --border: 217.2 32.6% 17.5%;
  --primary: 248 68% 70%;
  --primary-foreground: 222.2 84% 4.9%;

  /* Dark mode custom colors */
  --navy: 210 40% 98%;
  --warm-gray: 215 20.2% 65.1%;
  --accent-orange: 24 95% 53%;
  --success-green: 142 76% 36%;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

/* CONSOLIDATED html rules - no conflicts */
html {
  height: 100% !important;
  overflow-x: hidden !important;
  overflow-y: scroll !important;
  scroll-behavior: smooth;
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--primary)) hsl(var(--muted) / 0.3);
}

body {
  height: 100%;
  margin: 0;
  padding: 0;
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
  /* Ensure only one scrollbar - use clip to avoid deprecated warning */
  overflow: clip;
}

a {
  color: inherit;
  text-decoration: none;
}

.btn-primary {
  background-color: #1d4ed8;
  color: white;
  font-weight: 600;
  padding: 1rem 2rem;
  border-radius: 0.75rem;
  transition: all 0.3s ease;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.btn-primary:hover {
  background-color: #1e40af;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.btn-secondary {
  background-color: white;
  color: #1d4ed8;
  font-weight: 600;
  padding: 1rem 2rem;
  border-radius: 0.75rem;
  border: 2px solid #dbeafe;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.btn-secondary:hover {
  background-color: #f9fafb;
  border-color: #bfdbfe;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

/* Header spacing utility for consistent clearance across all pages */
.header-spacing {
  padding-top: 10rem;
}

@media (min-width: 640px) {
  .header-spacing {
    padding-top: 11rem;
  }
}

@media (min-width: 1024px) {
  .header-spacing {
    padding-top: 12rem;
  }
}



/* ===== SCROLLBAR FIX & CUSTOM STYLING ===== */

/* Testimonial pagination dots - FORCE proper sizing */
.testimonial-pagination-dot {
  width: 12px !important;
  height: 12px !important;
  min-width: 12px !important;
  max-width: 12px !important;
  min-height: 12px !important;
  max-height: 12px !important;
  border-radius: 50% !important;
  transition: all 0.3s ease !important;
  overflow: visible !important;
  flex-shrink: 0 !important;
  box-sizing: border-box !important;
}

.testimonial-pagination-dot.active {
  transform: scale(1.25) !important;
  width: 12px !important;
  height: 12px !important;
}

/* REMOVED - consolidated into main html rule above */

body {
  overflow: clip !important;
  height: auto !important;
  min-height: 100vh !important;
}

/* Prevent ALL elements from creating their own scroll contexts */
* {
  box-sizing: border-box;
}

/* Target only layout containers, not design elements */
#__next,
[data-nextjs-scroll-focus-boundary],
body > div:first-child {
  overflow: clip !important;
  height: auto !important;
  min-height: auto !important;
}

/* Ensure main layout elements don't create scroll context */
main,
body > div > header,
body > div > main,
body > div > footer {
  overflow-x: clip !important;
}

/* Preserve overflow-hidden for design elements */
.rounded-3xl,
.rounded-2xl,
.rounded-xl,
.group,
[class*="bg-gradient"],
[class*="shadow"],
.testimonial-card {
  overflow: hidden !important;
}

/* Specific testimonial card styling */
.testimonial-card {
  height: auto !important;
  min-height: auto !important;
  max-height: none !important;
}

/* Exclude pagination dots from testimonial card overflow rules */
.testimonial-card *:not(.testimonial-pagination-dot) {
  overflow: clip !important;
}

.testimonial-card .relative:not(.testimonial-pagination-dot) {
  overflow: clip !important;
}

/* Only allow vertical overflow on specific scrollable containers */
.overflow-y-auto, .overflow-auto {
  overflow-x: hidden !important;
  overflow-y: auto !important;
}

/* Webkit browsers (Chrome, Safari, Edge) */
::-webkit-scrollbar {
  width: 14px;
  height: 14px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted) / 0.3);
  border-radius: 12px;
  margin: 2px;
  border: 1px solid hsl(var(--border) / 0.2);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(
    135deg,
    hsl(var(--primary)) 0%,
    hsl(248 68% 60%) 50%,
    hsl(var(--primary)) 100%
  );
  border-radius: 12px;
  border: 2px solid hsl(var(--background));
  box-shadow: 0 2px 4px hsl(var(--primary) / 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(
    135deg,
    hsl(248 68% 65%) 0%,
    hsl(248 68% 55%) 50%,
    hsl(248 68% 65%) 100%
  );
  border-color: hsl(var(--muted));
  box-shadow: 0 4px 8px hsl(var(--primary) / 0.3);
  transform: scale(1.02);
}

::-webkit-scrollbar-thumb:active {
  background: linear-gradient(
    135deg,
    hsl(248 68% 50%) 0%,
    hsl(248 68% 40%) 50%,
    hsl(248 68% 50%) 100%
  );
  transform: scale(0.98);
}

::-webkit-scrollbar-corner {
  background: hsl(var(--muted) / 0.3);
  border-radius: 8px;
}

/* REMOVED - consolidated into main html rule above */

/* Dark mode scrollbar adjustments */
.dark ::-webkit-scrollbar-track {
  background: hsl(var(--muted) / 0.2);
  border-color: hsl(var(--border) / 0.1);
}

.dark ::-webkit-scrollbar-thumb {
  background: linear-gradient(
    135deg,
    hsl(var(--primary)) 0%,
    hsl(248 68% 65%) 50%,
    hsl(var(--primary)) 100%
  );
  border-color: hsl(var(--background));
  box-shadow: 0 2px 4px hsl(var(--primary) / 0.3);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(
    135deg,
    hsl(248 68% 70%) 0%,
    hsl(248 68% 60%) 50%,
    hsl(248 68% 70%) 100%
  );
  border-color: hsl(var(--muted));
  box-shadow: 0 4px 8px hsl(var(--primary) / 0.4);
}

.dark ::-webkit-scrollbar-corner {
  background: hsl(var(--muted) / 0.2);
}

/* Mobile scrollbar optimization */
@media (max-width: 768px) {
  ::-webkit-scrollbar {
    width: 10px;
    height: 10px;
  }

  ::-webkit-scrollbar-track {
    margin: 1px;
    border-radius: 8px;
  }

  ::-webkit-scrollbar-thumb {
    border-radius: 8px;
    border: 1px solid hsl(var(--background));
  }

  /* Hide scrollbar on mobile but keep functionality */
  @media (hover: none) and (pointer: coarse) {
    ::-webkit-scrollbar {
      width: 0px;
      background: transparent;
    }
  }
}

/* REMOVED - consolidated into main html rule above */

@media (prefers-reduced-motion: reduce) {
  html {
    scroll-behavior: auto;
  }
}

/* Custom scrollbar utility class */
.custom-scroll {
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--primary)) hsl(var(--muted) / 0.3);
}

.custom-scroll::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

.custom-scroll::-webkit-scrollbar-track {
  background: hsl(var(--muted) / 0.2);
  border-radius: 8px;
  margin: 2px;
}

.custom-scroll::-webkit-scrollbar-thumb {
  background: linear-gradient(
    135deg,
    hsl(var(--primary)) 0%,
    hsl(248 68% 60%) 100%
  );
  border-radius: 8px;
  border: 1px solid hsl(var(--background));
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.custom-scroll::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(
    135deg,
    hsl(248 68% 65%) 0%,
    hsl(248 68% 55%) 100%
  );
  transform: scale(1.05);
  box-shadow: 0 2px 4px hsl(var(--primary) / 0.2);
}

/* Scrollbar for modal and popup containers */
.modal-scroll::-webkit-scrollbar {
  width: 8px;
}

.modal-scroll::-webkit-scrollbar-track {
  background: hsl(var(--muted) / 0.1);
  border-radius: 6px;
}

.modal-scroll::-webkit-scrollbar-thumb {
  background: hsl(var(--primary) / 0.6);
  border-radius: 6px;
  transition: all 0.2s ease;
}

.modal-scroll::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--primary) / 0.8);
}
