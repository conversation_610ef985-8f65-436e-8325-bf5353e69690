/* ===== GOHIGHLEVEL SPECIFIC GENLOGIC STYLING ===== */
/* Target specific GoHighLevel elements with GenLogic branding */

/* ===== GHL FORM STYLING ===== */
/* GoHighLevel Form Containers - Using actual GHL classes */
.menu-field-wrap,
.form-container,
.form-wrapper,
.survey-container,
.quiz-container,
[class*="form"],
[class*="survey"],
[class*="quiz"] {
  background: white !important;
  border-radius: 16px !important;
  padding: 32px !important;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1) !important;
  border: 1px solid #e2e8f0 !important;
  font-family: 'Inter', sans-serif !important;
}

/* Form Headers - Target actual GHL form headers */
.form-container h1,
.form-container h2,
.form-wrapper h1,
.form-wrapper h2,
.survey-container h1,
.survey-container h2,
[class*="form"] h1,
[class*="form"] h2,
[class*="survey"] h1,
[class*="survey"] h2 {
  color: #0f172a !important;
  font-weight: 700 !important;
  margin-bottom: 8px !important;
  text-align: center !important;
  font-family: 'Inter', sans-serif !important;
}

.form-container p,
.form-wrapper p,
.survey-container p,
[class*="form"] p,
[class*="survey"] p {
  color: #64748b !important;
  text-align: center !important;
  margin-bottom: 24px !important;
  font-family: 'Inter', sans-serif !important;
}

/* ===== GHL CALENDAR STYLING ===== */
/* Calendar Widget */
.calendar-widget,
.hl-calendar,
[class*="calendar"] {
  border-radius: 16px !important;
  overflow: hidden !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
}

.calendar-header {
  background: linear-gradient(135deg, #a855f7 0%, #7c3aed 100%) !important;
  color: white !important;
  padding: 16px !important;
  font-weight: 600 !important;
}

.calendar-day {
  border: 1px solid #e2e8f0 !important;
  transition: all 0.2s ease !important;
}

.calendar-day:hover {
  background: rgba(168, 85, 247, 0.1) !important;
}

.calendar-day.selected {
  background: #a855f7 !important;
  color: white !important;
}

/* ===== GHL SURVEY STYLING ===== */
/* Survey Forms */
.survey-container,
.hl-survey {
  background: white !important;
  border-radius: 16px !important;
  padding: 24px !important;
  margin: 16px auto !important;
  max-width: 600px !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
}

.survey-question {
  margin-bottom: 24px !important;
}

.survey-question h3 {
  color: #0f172a !important;
  font-weight: 600 !important;
  margin-bottom: 12px !important;
}

/* Radio Buttons & Checkboxes */
input[type="radio"],
input[type="checkbox"] {
  accent-color: #a855f7 !important;
  transform: scale(1.2) !important;
  margin-right: 8px !important;
}

/* ===== GHL CHAT WIDGET ===== */
/* Chat Bubble */
.chat-widget,
.hl-chat {
  border-radius: 50px !important;
  background: linear-gradient(135deg, #a855f7 0%, #7c3aed 100%) !important;
  box-shadow: 0 4px 12px rgba(168, 85, 247, 0.3) !important;
}

.chat-widget:hover {
  transform: scale(1.05) !important;
  box-shadow: 0 8px 20px rgba(168, 85, 247, 0.4) !important;
}

/* Chat Window */
.chat-window {
  border-radius: 16px !important;
  overflow: hidden !important;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.2) !important;
}

.chat-header {
  background: linear-gradient(135deg, #a855f7 0%, #7c3aed 100%) !important;
  color: white !important;
  padding: 16px !important;
  font-weight: 600 !important;
}

.chat-message.bot {
  background: rgba(168, 85, 247, 0.1) !important;
  border-radius: 12px 12px 12px 4px !important;
  padding: 12px !important;
  margin: 8px 0 !important;
}

.chat-message.user {
  background: #a855f7 !important;
  color: white !important;
  border-radius: 12px 12px 4px 12px !important;
  padding: 12px !important;
  margin: 8px 0 !important;
  margin-left: auto !important;
  max-width: 80% !important;
}

/* ===== GHL FUNNEL PAGES ===== */
/* Funnel Page Containers */
.funnel-page,
.hl-funnel {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
  min-height: 100vh !important;
}

/* Funnel Sections */
.funnel-section {
  padding: 48px 24px !important;
  text-align: center !important;
}

.funnel-hero {
  background: linear-gradient(135deg, #a855f7 0%, #7c3aed 100%) !important;
  color: white !important;
  padding: 80px 24px !important;
}

.funnel-hero h1 {
  color: white !important;
  font-size: 3rem !important;
  font-weight: 700 !important;
  margin-bottom: 16px !important;
}

.funnel-hero p {
  color: rgba(255, 255, 255, 0.9) !important;
  font-size: 1.25rem !important;
  margin-bottom: 32px !important;
}

/* ===== GHL POPUP STYLING ===== */
/* Popup Containers */
.popup-container,
.hl-popup,
.modal {
  background: white !important;
  border-radius: 20px !important;
  padding: 32px !important;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.2) !important;
  border: none !important;
  max-width: 500px !important;
  margin: auto !important;
}

.popup-overlay {
  background: rgba(15, 23, 42, 0.8) !important;
  backdrop-filter: blur(4px) !important;
}

.popup-close {
  background: #f1f5f9 !important;
  border: none !important;
  border-radius: 50% !important;
  width: 32px !important;
  height: 32px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  color: #64748b !important;
  transition: all 0.2s ease !important;
}

.popup-close:hover {
  background: #e2e8f0 !important;
  color: #334155 !important;
}

/* ===== GHL MEMBERSHIP AREA ===== */
/* Member Dashboard */
.member-dashboard {
  background: #f8fafc !important;
  padding: 24px !important;
}

.member-nav {
  background: white !important;
  border-radius: 12px !important;
  padding: 16px !important;
  margin-bottom: 24px !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

.member-nav a {
  color: #64748b !important;
  padding: 8px 16px !important;
  border-radius: 8px !important;
  transition: all 0.2s ease !important;
}

.member-nav a:hover,
.member-nav a.active {
  background: rgba(168, 85, 247, 0.1) !important;
  color: #a855f7 !important;
}

/* ===== GHL COURSE STYLING ===== */
/* Course Content */
.course-container {
  background: white !important;
  border-radius: 16px !important;
  padding: 32px !important;
  margin: 24px auto !important;
  max-width: 800px !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
}

.course-progress {
  background: #e2e8f0 !important;
  border-radius: 10px !important;
  height: 8px !important;
  overflow: hidden !important;
  margin: 16px 0 !important;
}

.course-progress-fill {
  background: linear-gradient(90deg, #a855f7 0%, #7c3aed 100%) !important;
  height: 100% !important;
  transition: width 0.3s ease !important;
}

/* ===== GHL MOBILE RESPONSIVENESS ===== */
@media (max-width: 768px) {
  .ghl-form,
  .hl-form,
  .popup-container,
  .course-container {
    margin: 16px !important;
    padding: 24px !important;
    border-radius: 12px !important;
  }
  
  .funnel-hero {
    padding: 48px 16px !important;
  }
  
  .funnel-hero h1 {
    font-size: 2rem !important;
  }
  
  .chat-window {
    width: 90vw !important;
    height: 70vh !important;
  }
}

/* ===== CUSTOM GENLOGIC UTILITIES ===== */
.genlogic-gradient-text {
  background: linear-gradient(135deg, #a855f7 0%, #7c3aed 100%) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
}

.genlogic-hover-lift:hover {
  transform: translateY(-4px) !important;
  transition: transform 0.3s ease !important;
}

.genlogic-focus-ring:focus {
  outline: none !important;
  box-shadow: 0 0 0 3px rgba(168, 85, 247, 0.2) !important;
}

/* ===== OVERRIDE GOHIGHLEVEL DEFAULTS ===== */
/* Force GenLogic styling over GHL defaults */
.menu-field-wrap input,
.menu-field-wrap button,
.menu-field-wrap select,
.menu-field-wrap textarea,
.form-container input,
.form-container button,
.form-container select,
.form-container textarea,
[class*="form"] input,
[class*="form"] button,
[class*="form"] select,
[class*="form"] textarea {
  font-family: 'Inter', sans-serif !important;
}

/* Remove GHL default orange branding colors */
[style*="background-color: #ff6b35"],
[style*="background: #ff6b35"],
[style*="background-color: #f46a35"],
[style*="background: #f46a35"],
[class*="orange"],
.btn-orange,
.button-orange {
  background: linear-gradient(135deg, #a855f7 0%, #7c3aed 100%) !important;
  color: white !important;
}

[style*="color: #ff6b35"],
[style*="color: #f46a35"] {
  color: #a855f7 !important;
}

/* Target common GHL button classes */
.btn-default,
.btn-submit,
.submit-button,
.form-submit-btn,
[class*="submit-btn"],
[class*="form-btn"] {
  background: linear-gradient(135deg, #a855f7 0%, #7c3aed 100%) !important;
  border: none !important;
  color: white !important;
  font-weight: 600 !important;
  padding: 12px 24px !important;
  border-radius: 12px !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 4px 6px -1px rgba(168, 85, 247, 0.2) !important;
  cursor: pointer !important;
  font-family: 'Inter', sans-serif !important;
}

.btn-default:hover,
.btn-submit:hover,
.submit-button:hover,
.form-submit-btn:hover,
[class*="submit-btn"]:hover,
[class*="form-btn"]:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 10px 15px -3px rgba(168, 85, 247, 0.3) !important;
  background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%) !important;
}

/* Force styling on inline styles */
[style*="background"] {
  background: linear-gradient(135deg, #a855f7 0%, #7c3aed 100%) !important;
}

/* Target GHL specific form elements */
.field-input,
.form-field-input,
.survey-input,
.quiz-input,
[data-field-type="input"],
[data-field-type="email"],
[data-field-type="phone"],
[data-field-type="text"] {
  border: 2px solid #e2e8f0 !important;
  border-radius: 12px !important;
  padding: 12px 16px !important;
  font-family: 'Inter', sans-serif !important;
  transition: all 0.3s ease !important;
}

.field-input:focus,
.form-field-input:focus,
.survey-input:focus,
.quiz-input:focus,
[data-field-type="input"]:focus,
[data-field-type="email"]:focus,
[data-field-type="phone"]:focus,
[data-field-type="text"]:focus {
  border-color: #a855f7 !important;
  box-shadow: 0 0 0 3px rgba(168, 85, 247, 0.1) !important;
  outline: none !important;
}

/* ===== END GHL SPECIFIC STYLING ===== */
