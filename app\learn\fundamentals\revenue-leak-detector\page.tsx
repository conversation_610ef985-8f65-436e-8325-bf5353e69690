import { Metadata } from 'next'
import RevenueLeakDetectorClient from './RevenueLeakDetectorClient'

export const metadata: Metadata = {
  title: 'The Revenue Leak Detector: Finding Hidden Money | GenLogic Academy',
  description: 'Discover the 5 places money leaks from every business and how to automate revenue recovery systems. Learn how one salon recovered £47k in lost revenue.',
  keywords: 'revenue recovery, business leaks, lost revenue, automation systems, UK business revenue, revenue optimization',
  openGraph: {
    title: 'The Revenue Leak Detector: Finding Hidden Money',
    description: 'Discover the 5 places money leaks from every business and how to automate revenue recovery systems. Learn how one salon recovered £47k in lost revenue.',
    type: 'article',
    url: 'https://genlogic.io/learn/fundamentals/revenue-leak-detector',
    images: [
      {
        url: '/academy-images/og-revenue-leak-detector.webp',
        width: 1200,
        height: 630,
        alt: 'The Revenue Leak Detector: Finding Hidden Money',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'The Revenue Leak Detector: Finding Hidden Money',
    description: 'Discover the 5 places money leaks from every business and how to automate revenue recovery systems. Learn how one salon recovered £47k in lost revenue.',
    images: ['/academy-images/og-revenue-leak-detector.webp'],
  },
}

export default function RevenueLeakDetectorPage() {
  return <RevenueLeakDetectorClient />
}
