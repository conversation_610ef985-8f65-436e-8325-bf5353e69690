# GenLogic v2 MVP - Business Automation Platform

A modern, professional business automation platform built with Next.js 14, designed to help UK local businesses reclaim their time through smart automation.

## 🚀 Features

### 🎯 Core Functionality
- **Demo Booking System** - Integrated with GoHighLevel CRM
- **Contact Forms** - Professional error handling and validation
- **Blog System** - Load more functionality with featured articles
- **SEO Optimized** - Schema markup, meta tags, and local SEO
- **GDPR Compliant** - UK business compliance built-in

### 🎨 Design & UX
- **Modern UI** - Sophisticated color palette with light/dark mode
- **Responsive Design** - Mobile-first approach
- **Framer Motion Animations** - Smooth, professional animations
- **Custom Components** - Reusable UI components
- **Professional Branding** - Complete logo system (SVG)

### 🔧 Technical Features
- **Next.js 14** - App Router, Server Components
- **TypeScript** - Type-safe development
- **Tailwind CSS** - Utility-first styling
- **GoHighLevel Integration** - CRM automation
- **Form Validation** - Real-time error handling
- **Exit Intent Popups** - Conversion optimization

## 🛠️ Tech Stack

- **Framework:** Next.js 14 (App Router)
- **Language:** TypeScript
- **Styling:** Tailwind CSS
- **Animations:** Framer Motion
- **CRM:** GoHighLevel API
- **Icons:** Lucide React
- **Deployment:** Vercel Ready

## 📁 Project Structure

```
genlogicv2/
├── app/                    # Next.js App Router
│   ├── blog/              # Blog system with load more
│   ├── contact/           # Contact form
│   ├── demo/              # Demo booking
│   ├── privacy/           # Privacy policy
│   └── api/               # API routes
├── components/            # Reusable components
│   ├── ui/               # UI components
│   ├── seo/              # SEO components
│   └── forms/            # Form components
├── lib/                  # Utilities and integrations
├── public/logo/          # Complete logo system
└── docs/                 # Documentation
```

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ 
- npm or yarn
- GoHighLevel account (optional)

### Installation

1. **Clone the repository**
```bash
git clone https://github.com/chedirachdi/genv2.git
cd genv2
```

2. **Install dependencies**
```bash
npm install
```

3. **Environment Setup**
```bash
cp .env.example .env.local
```

4. **Configure Environment Variables**
```env
# GoHighLevel Integration (Optional)
GHL_API_KEY=your_ghl_api_key
GHL_LOCATION_ID=your_location_id

# Site Configuration
NEXT_PUBLIC_SITE_URL=http://localhost:3000
```

5. **Run Development Server**
```bash
npm run dev
```

Visit `http://localhost:3000` to see the application.

## 🔧 Configuration

### GoHighLevel Setup
1. Get API key from GoHighLevel Settings → API Keys
2. Find Location ID in Settings → Business Info
3. Add to `.env.local`

### SEO Configuration
- Update `lib/seo-config.ts` with your business details
- Modify schema markup in `components/seo/`

## 📝 Content Management

### Blog Posts
- Located in `app/blog/posts/`
- Each post is a React component
- Supports featured articles and categories

### Forms
- Demo form: `app/demo/page.tsx`
- Contact form: `app/contact/page.tsx`
- Both integrate with GoHighLevel

## 🎨 Branding

Complete logo system available in `public/logo/`:
- `genlogic-logo.svg` - Main logo
- `genlogic-icon-only.svg` - Icon only
- `genlogic-horizontal-white.svg` - White version
- `genlogic-horizontal-black.svg` - Black version
- `genlogic-monochrome.svg` - Print version

## 🚀 Deployment

### Vercel (Recommended)
1. Connect GitHub repository to Vercel
2. Add environment variables
3. Deploy automatically

### Other Platforms
- Build: `npm run build`
- Start: `npm start`

## 📊 Features Implemented

- ✅ Professional homepage with conversion optimization
- ✅ Demo booking with GoHighLevel integration
- ✅ Contact forms with validation
- ✅ Blog system with load more functionality
- ✅ SEO optimization and schema markup
- ✅ GDPR compliance
- ✅ Light/dark mode toggle
- ✅ Exit intent popups
- ✅ Professional error handling
- ✅ Complete logo system
- ✅ Mobile responsive design

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📄 License

This project is proprietary software for GenLogic.

## 📞 Support

For support and questions:
- Email: <EMAIL>
- Website: https://genlogic.io

---

**GenLogic v2** - Helping UK businesses reclaim their time through smart automation.
