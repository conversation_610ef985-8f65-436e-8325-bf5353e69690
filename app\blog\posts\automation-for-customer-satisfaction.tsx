import React from 'react'
import { BlogPost } from '../types'

const BlogContent = () => {
  return (
    <div className="max-w-4xl mx-auto space-y-12">
      
      {/* Opening Hook */}
      <div className="bg-gradient-to-br from-emerald-50 to-green-50 dark:from-emerald-950/20 dark:to-green-950/20 rounded-3xl p-8 border border-emerald-200/50 dark:border-emerald-800/30">
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6 text-center">
          "Your customers will love you even more when you automate."
        </h2>
        <p className="text-lg text-gray-700 dark:text-gray-300 mb-4 text-center">
          That's what happened to <PERSON> from Birmingham. She was worried that automating her customer communications would make her business feel "cold" and "impersonal." <strong>The opposite happened.</strong>
        </p>
        <p className="text-lg text-gray-700 dark:text-gray-300 text-center font-medium">
          Here's how automation actually improves customer satisfaction (and why your customers will thank you for it).
        </p>
      </div>

      {/* The Myth vs Reality */}
      <div>
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
          The Biggest Myth About Business Automation
        </h2>
        <p className="text-lg text-gray-700 dark:text-gray-300 mb-6">
          "Automation makes businesses cold and impersonal." That's what 90% of UK business owners tell me when I first mention automation.
        </p>
        <p className="text-lg text-gray-700 dark:text-gray-300 mb-8">
          <strong>Here's the truth:</strong> Bad automation makes businesses cold. Good automation makes them more reliable, responsive, and professional than ever before.
        </p>
        
        <div className="grid md:grid-cols-2 gap-8 mb-8">
          <div className="bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800/30 rounded-2xl p-6">
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
              <span className="text-2xl mr-3">❌</span>
              Bad Automation (What People Fear)
            </h3>
            <ul className="space-y-3">
              <li className="flex items-start">
                <span className="w-2 h-2 bg-red-500 rounded-full mr-3 mt-2"></span>
                <span className="text-gray-700 dark:text-gray-300">Generic, robotic messages</span>
              </li>
              <li className="flex items-start">
                <span className="w-2 h-2 bg-red-500 rounded-full mr-3 mt-2"></span>
                <span className="text-gray-700 dark:text-gray-300">No human touch or personality</span>
              </li>
              <li className="flex items-start">
                <span className="w-2 h-2 bg-red-500 rounded-full mr-3 mt-2"></span>
                <span className="text-gray-700 dark:text-gray-300">Customers feel like numbers</span>
              </li>
              <li className="flex items-start">
                <span className="w-2 h-2 bg-red-500 rounded-full mr-3 mt-2"></span>
                <span className="text-gray-700 dark:text-gray-300">Inflexible, one-size-fits-all approach</span>
              </li>
            </ul>
          </div>

          <div className="bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800/30 rounded-2xl p-6">
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
              <span className="text-2xl mr-3">✅</span>
              Good Automation (What Actually Happens)
            </h3>
            <ul className="space-y-3">
              <li className="flex items-start">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-3 mt-2"></span>
                <span className="text-gray-700 dark:text-gray-300">Personalized, timely communications</span>
              </li>
              <li className="flex items-start">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-3 mt-2"></span>
                <span className="text-gray-700 dark:text-gray-300">Consistent, professional service</span>
              </li>
              <li className="flex items-start">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-3 mt-2"></span>
                <span className="text-gray-700 dark:text-gray-300">Customers feel valued and informed</span>
              </li>
              <li className="flex items-start">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-3 mt-2"></span>
                <span className="text-gray-700 dark:text-gray-300">Tailored to customer preferences</span>
              </li>
            </ul>
          </div>
        </div>
      </div>

      {/* How Automation Improves Customer Satisfaction */}
      <div>
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6 text-center">
          7 Ways Automation Actually Improves Customer Satisfaction
        </h2>
        <p className="text-lg text-gray-700 dark:text-gray-300 mb-8 text-center">
          Based on feedback from 500+ UK businesses and their customers.
        </p>
        
        <div className="space-y-6">
          {/* Way 1 */}
          <div className="bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800/30 rounded-2xl p-6">
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
              <span className="text-2xl mr-3">⚡</span>
              1. Instant Response Times (24/7 Availability)
            </h3>
            <p className="text-gray-700 dark:text-gray-300 mb-3">
              Your customers can book appointments, get quotes, and receive information at 2am on Sunday. No more "I'll call you back tomorrow" or missed opportunities.
            </p>
            <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
              <p className="text-sm text-gray-600 dark:text-gray-400 italic">
                "I love that I can book with David's company anytime. I work shifts, so being able to schedule at midnight is perfect." - Customer feedback, Curb Appeal
              </p>
            </div>
          </div>

          {/* Way 2 */}
          <div className="bg-purple-50 dark:bg-purple-950/20 border border-purple-200 dark:border-purple-800/30 rounded-2xl p-6">
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
              <span className="text-2xl mr-3">🎯</span>
              2. Consistent, Professional Communication
            </h3>
            <p className="text-gray-700 dark:text-gray-300 mb-3">
              No more forgetting to send confirmations or follow-ups. Every customer gets the same high-quality experience, whether you're having a good day or a stressful one.
            </p>
            <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
              <p className="text-sm text-gray-600 dark:text-gray-400 italic">
                "Anthony's team always sends perfect confirmations and reminders. Very professional." - Customer review, Universal Windows
              </p>
            </div>
          </div>

          {/* Way 3 */}
          <div className="bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800/30 rounded-2xl p-6">
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
              <span className="text-2xl mr-3">📱</span>
              3. Proactive Updates and Reminders
            </h3>
            <p className="text-gray-700 dark:text-gray-300 mb-3">
              Customers love being kept in the loop. Automated reminders, progress updates, and arrival notifications show you care about their time and experience.
            </p>
            <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
              <p className="text-sm text-gray-600 dark:text-gray-400 italic">
                "The text reminders are brilliant. I never forget appointments anymore, and I know exactly when they're arriving." - Customer feedback
              </p>
            </div>
          </div>

          {/* Way 4 */}
          <div className="bg-orange-50 dark:bg-orange-950/20 border border-orange-200 dark:border-orange-800/30 rounded-2xl p-6">
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
              <span className="text-2xl mr-3">🎨</span>
              4. Personalized Experience at Scale
            </h3>
            <p className="text-gray-700 dark:text-gray-300 mb-3">
              Good automation uses customer data to personalize every interaction. Names, preferences, history, and specific needs are remembered and referenced automatically.
            </p>
            <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
              <p className="text-sm text-gray-600 dark:text-gray-400 italic">
                "They remembered my preferred appointment times and even mentioned my previous project. Felt very personal." - Customer testimonial
              </p>
            </div>
          </div>

          {/* Way 5 */}
          <div className="bg-pink-50 dark:bg-pink-950/20 border border-pink-200 dark:border-pink-800/30 rounded-2xl p-6">
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
              <span className="text-2xl mr-3">🚀</span>
              5. Faster Problem Resolution
            </h3>
            <p className="text-gray-700 dark:text-gray-300 mb-3">
              Automated systems can instantly route customer inquiries to the right person, provide immediate answers to common questions, and escalate urgent issues automatically.
            </p>
            <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
              <p className="text-sm text-gray-600 dark:text-gray-400 italic">
                "Got an instant quote and my question was answered immediately. Much better than waiting for callbacks." - Customer review
              </p>
            </div>
          </div>

          {/* Way 6 */}
          <div className="bg-indigo-50 dark:bg-indigo-950/20 border border-indigo-200 dark:border-indigo-800/30 rounded-2xl p-6">
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
              <span className="text-2xl mr-3">⭐</span>
              6. Never Forgotten Follow-ups
            </h3>
            <p className="text-gray-700 dark:text-gray-300 mb-3">
              Automation ensures no customer falls through the cracks. Follow-ups happen on time, every time, making customers feel valued and cared for long after the job is done.
            </p>
            <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
              <p className="text-sm text-gray-600 dark:text-gray-400 italic">
                "They followed up to make sure I was happy with the work. Shows they really care about customer satisfaction." - Customer feedback
              </p>
            </div>
          </div>

          {/* Way 7 */}
          <div className="bg-teal-50 dark:bg-teal-950/20 border border-teal-200 dark:border-teal-800/30 rounded-2xl p-6">
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
              <span className="text-2xl mr-3">🎁</span>
              7. Surprise and Delight Moments
            </h3>
            <p className="text-gray-700 dark:text-gray-300 mb-3">
              Automated birthday messages, anniversary reminders, and special offers show customers you remember and value them beyond just transactions.
            </p>
            <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
              <p className="text-sm text-gray-600 dark:text-gray-400 italic">
                "They sent me a birthday discount! I had no idea they remembered. Made me feel like a valued customer." - Customer testimonial
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Real Customer Satisfaction Results */}
      <div className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 rounded-3xl p-8 border border-blue-200/50 dark:border-blue-800/30">
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6 text-center">
          Real Customer Satisfaction Results from UK Businesses
        </h2>
        
        <div className="grid md:grid-cols-3 gap-6 mb-8">
          <div className="text-center">
            <div className="text-4xl font-black text-blue-600 dark:text-blue-400 mb-2">94%</div>
            <div className="text-lg font-semibold text-gray-700 dark:text-gray-300">Customer Satisfaction</div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Up from 78% pre-automation</div>
          </div>
          <div className="text-center">
            <div className="text-4xl font-black text-blue-600 dark:text-blue-400 mb-2">67%</div>
            <div className="text-lg font-semibold text-gray-700 dark:text-gray-300">More 5-Star Reviews</div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Automated review requests</div>
          </div>
          <div className="text-center">
            <div className="text-4xl font-black text-blue-600 dark:text-blue-400 mb-2">89%</div>
            <div className="text-lg font-semibold text-gray-700 dark:text-gray-300">Customer Retention</div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Improved follow-up systems</div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700">
          <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-4">What Customers Actually Say About Automation</h3>
          <div className="space-y-4">
            <blockquote className="border-l-4 border-blue-500 pl-4">
              <p className="text-gray-700 dark:text-gray-300 italic mb-2">
                "The booking system is so easy to use. I can see available times, pick what works for me, and get instant confirmation. Much better than phone tag."
              </p>
              <cite className="text-sm font-semibold text-gray-600 dark:text-gray-400">- Customer review, Universal Windows</cite>
            </blockquote>
            
            <blockquote className="border-l-4 border-green-500 pl-4">
              <p className="text-gray-700 dark:text-gray-300 italic mb-2">
                "I love the text reminders. Never miss appointments anymore, and I always know when they're coming. Very professional service."
              </p>
              <cite className="text-sm font-semibold text-gray-600 dark:text-gray-400">- Customer feedback, Curb Appeal</cite>
            </blockquote>

            <blockquote className="border-l-4 border-purple-500 pl-4">
              <p className="text-gray-700 dark:text-gray-300 italic mb-2">
                "They followed up after the job to make sure everything was perfect. Then sent care instructions. Shows they really care about quality."
              </p>
              <cite className="text-sm font-semibold text-gray-600 dark:text-gray-400">- Customer testimonial, First Impression Driveways</cite>
            </blockquote>
          </div>
        </div>
      </div>

      {/* The Human Touch in Automation */}
      <div>
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
          The Secret: Automation Amplifies Your Human Touch
        </h2>
        <p className="text-lg text-gray-700 dark:text-gray-300 mb-8">
          The best automation doesn't replace human interaction—it enhances it. Here's how:
        </p>
        
        <div className="grid md:grid-cols-2 gap-6">
          <div className="bg-emerald-50 dark:bg-emerald-950/20 border border-emerald-200 dark:border-emerald-800/30 rounded-2xl p-6">
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-4">What Automation Handles:</h3>
            <ul className="space-y-2">
              <li className="flex items-center">
                <span className="w-2 h-2 bg-emerald-500 rounded-full mr-3"></span>
                <span className="text-gray-700 dark:text-gray-300">Appointment scheduling and confirmations</span>
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-emerald-500 rounded-full mr-3"></span>
                <span className="text-gray-700 dark:text-gray-300">Routine reminders and updates</span>
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-emerald-500 rounded-full mr-3"></span>
                <span className="text-gray-700 dark:text-gray-300">Basic information and FAQs</span>
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-emerald-500 rounded-full mr-3"></span>
                <span className="text-gray-700 dark:text-gray-300">Follow-up sequences and reviews</span>
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-emerald-500 rounded-full mr-3"></span>
                <span className="text-gray-700 dark:text-gray-300">Payment reminders and processing</span>
              </li>
            </ul>
          </div>

          <div className="bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800/30 rounded-2xl p-6">
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-4">What You Focus On:</h3>
            <ul className="space-y-2">
              <li className="flex items-center">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                <span className="text-gray-700 dark:text-gray-300">Complex customer consultations</span>
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                <span className="text-gray-700 dark:text-gray-300">Problem-solving and expertise</span>
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                <span className="text-gray-700 dark:text-gray-300">Building relationships and trust</span>
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                <span className="text-gray-700 dark:text-gray-300">Creative solutions and customization</span>
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                <span className="text-gray-700 dark:text-gray-300">Delivering exceptional service quality</span>
              </li>
            </ul>
          </div>
        </div>
      </div>

      {/* Common Automation Mistakes */}
      <div className="bg-gradient-to-br from-red-50 to-pink-50 dark:from-red-950/20 dark:to-pink-950/20 rounded-3xl p-8 border border-red-200/50 dark:border-red-800/30">
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6 text-center">
          5 Automation Mistakes That Actually Hurt Customer Satisfaction
        </h2>
        
        <div className="space-y-4">
          <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-700">
            <h4 className="font-bold text-gray-900 dark:text-gray-100 mb-2">❌ Mistake 1: Generic, Robotic Messages</h4>
            <p className="text-gray-700 dark:text-gray-300 text-sm">Using templates that sound like they came from a call center instead of your business.</p>
          </div>
          
          <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-700">
            <h4 className="font-bold text-gray-900 dark:text-gray-100 mb-2">❌ Mistake 2: Over-Automation</h4>
            <p className="text-gray-700 dark:text-gray-300 text-sm">Automating everything, including things that need a human touch.</p>
          </div>
          
          <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-700">
            <h4 className="font-bold text-gray-900 dark:text-gray-100 mb-2">❌ Mistake 3: No Escape Route</h4>
            <p className="text-gray-700 dark:text-gray-300 text-sm">Not providing easy ways for customers to reach a human when needed.</p>
          </div>
          
          <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-700">
            <h4 className="font-bold text-gray-900 dark:text-gray-100 mb-2">❌ Mistake 4: Poor Timing</h4>
            <p className="text-gray-700 dark:text-gray-300 text-sm">Sending messages at inappropriate times or too frequently.</p>
          </div>
          
          <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-700">
            <h4 className="font-bold text-gray-900 dark:text-gray-100 mb-2">❌ Mistake 5: Set-and-Forget Mentality</h4>
            <p className="text-gray-700 dark:text-gray-300 text-sm">Not monitoring, testing, and improving automated systems based on customer feedback.</p>
          </div>
        </div>
      </div>

      {/* The GenLogic Approach */}
      <div>
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
          The GenLogic Approach: Customer-First Automation
        </h2>
        <p className="text-lg text-gray-700 dark:text-gray-300 mb-8">
          We design automation that makes your customers happier, not just your life easier. Learn more about our <a href="/services/business-automation" className="text-emerald-600 dark:text-emerald-400 hover:underline">customer-first automation approach</a>.
        </p>
        
        <div className="grid md:grid-cols-2 gap-6">
          <div className="bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800/30 rounded-2xl p-6">
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-4">Our Customer-First Features:</h3>
            <ul className="space-y-2">
              <li className="flex items-center">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                <span className="text-gray-700 dark:text-gray-300">Personalized messaging with your brand voice</span>
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                <span className="text-gray-700 dark:text-gray-300">Smart timing based on customer preferences</span>
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                <span className="text-gray-700 dark:text-gray-300">Easy escalation to human support</span>
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                <span className="text-gray-700 dark:text-gray-300">Customer feedback integration</span>
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                <span className="text-gray-700 dark:text-gray-300">Continuous optimization based on results</span>
              </li>
            </ul>
          </div>

          <div className="bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800/30 rounded-2xl p-6">
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-4">What You Get:</h3>
            <ul className="space-y-2">
              <li className="flex items-center">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                <span className="text-gray-700 dark:text-gray-300">Higher customer satisfaction scores</span>
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                <span className="text-gray-700 dark:text-gray-300">More 5-star reviews and referrals</span>
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                <span className="text-gray-700 dark:text-gray-300">Improved customer retention</span>
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                <span className="text-gray-700 dark:text-gray-300">Professional brand reputation</span>
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                <span className="text-gray-700 dark:text-gray-300">More time for high-value customer interactions</span>
              </li>
            </ul>
          </div>
        </div>
      </div>

      {/* Call to Action */}
      <div className="bg-gradient-to-br from-emerald-600 to-green-600 rounded-3xl p-8 text-white text-center">
        <h2 className="text-3xl font-bold mb-6">
          Ready to Make Your Customers Even Happier?
        </h2>
        <p className="text-xl mb-8 opacity-90">
          Book a free customer satisfaction audit. We'll show you exactly how automation can improve your customer experience and boost your reviews.
        </p>
        <div className="space-y-4">
          <p className="text-lg opacity-90">
            ✅ Free customer experience assessment<br/>
            ✅ Personalized automation strategy<br/>
            ✅ Customer satisfaction improvement plan
          </p>
          <div className="pt-4">
            <p className="text-lg font-semibold">
              📞 +44 7401 137621<br/>
              📧 <EMAIL>
            </p>
          </div>
        </div>
      </div>

      {/* Final Thought */}
      <div className="text-center">
        <p className="text-lg text-gray-700 dark:text-gray-300 italic">
          "The best automation makes customers feel more valued, not less."
        </p>
        <p className="text-base text-gray-600 dark:text-gray-400 mt-2">
          Your customers are waiting for a better experience. Give it to them.
        </p>
      </div>

    </div>
  )
}

export const automationForCustomerSatisfactionPost: BlogPost = {
  id: '11',
  slug: 'automation-for-customer-satisfaction',
  title: 'How Automation Actually Improves Customer Satisfaction (Contrary to Popular Belief)',
  excerpt: 'Think automation makes businesses cold and impersonal? Think again. Here\'s how smart automation actually makes your customers happier and more loyal than ever before.',
  content: <BlogContent />,
  author: {
    name: 'Chedi Rach',
    role: 'Business Automation Specialist',
    avatar: '/logo/genlogic-icon-only.svg',
    bio: 'Chedi has helped over 500 UK businesses improve customer satisfaction through thoughtful automation design.'
  },
  publishedAt: '2025-07-20',
  readingTime: '11 min read',
  category: 'Customer Experience',
  tags: ['customer-satisfaction', 'automation', 'customer-experience', 'uk-business'],
  featured: true,
  image: '/blog-images/automation-for-customer-satisfaction.webp',
  views: 1654,
  likes: 312
}
