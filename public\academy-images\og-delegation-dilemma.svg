<svg width="1200" height="630" viewBox="0 0 1200 630" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#059669;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="cardGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.95" />
      <stop offset="100%" style="stop-color:#f0fdf4;stop-opacity:0.9" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="8" stdDeviation="16" flood-color="#000000" flood-opacity="0.1"/>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="1200" height="630" fill="url(#bgGradient)"/>
  
  <!-- Main Content Card -->
  <rect x="80" y="80" width="1040" height="470" rx="24" fill="url(#cardGradient)" filter="url(#shadow)"/>
  
  <!-- GenLogic Academy Badge -->
  <rect x="120" y="120" width="180" height="40" rx="20" fill="#1e40af"/>
  <text x="210" y="140" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="14" font-weight="bold">GENLOGIC ACADEMY</text>
  
  <!-- Clone Visual -->
  <g transform="translate(380, 130)">
    <!-- Original Person -->
    <circle cx="0" cy="15" r="15" fill="#10b981"/>
    <rect x="-10" y="30" width="20" height="25" rx="5" fill="#10b981"/>
    <text x="0" y="70" text-anchor="middle" fill="#10b981" font-family="Arial, sans-serif" font-size="10" font-weight="bold">YOU</text>
    
    <!-- Arrow -->
    <path d="M25,25 L55,25" stroke="#3b82f6" stroke-width="3" fill="none"/>
    <path d="M50,20 L55,25 L50,30" stroke="#3b82f6" stroke-width="3" fill="none"/>
    
    <!-- Cloned Team -->
    <circle cx="80" cy="15" r="12" fill="#3b82f6"/>
    <rect x="-72" y="30" width="16" height="20" rx="4" fill="#3b82f6"/>
    <circle cx="100" cy="15" r="12" fill="#3b82f6"/>
    <rect x="92" y="30" width="16" height="20" rx="4" fill="#3b82f6"/>
    <circle cx="120" cy="15" r="12" fill="#3b82f6"/>
    <rect x="112" y="30" width="16" height="20" rx="4" fill="#3b82f6"/>
    <text x="100" y="70" text-anchor="middle" fill="#3b82f6" font-family="Arial, sans-serif" font-size="10" font-weight="bold">TEAM</text>
  </g>
  
  <!-- Main Headline -->
  <text x="120" y="250" fill="#1e293b" font-family="Arial, sans-serif" font-size="42" font-weight="bold">
    <tspan x="120" dy="0">The Delegation</tspan>
    <tspan x="120" dy="52" fill="#10b981">Dilemma:</tspan>
    <tspan x="120" dy="52">How to Clone</tspan>
    <tspan x="120" dy="52" fill="#3b82f6">Yourself</tspan>
  </text>
  
  <!-- Subtitle -->
  <text x="120" y="420" fill="#64748b" font-family="Arial, sans-serif" font-size="18" font-weight="normal">
    <tspan x="120" dy="0">Why most delegation fails and the 3-step fix</tspan>
    <tspan x="120" dy="24">that lets you scale without losing quality</tspan>
  </text>
  
  <!-- Solutions Box -->
  <g transform="translate(650, 180)">
    <rect x="0" y="0" width="400" height="280" rx="20" fill="#f8fafc" stroke="#e2e8f0" stroke-width="2"/>
    <text x="200" y="35" text-anchor="middle" fill="#1e293b" font-family="Arial, sans-serif" font-size="18" font-weight="bold">The 3-Step Clone System</text>
    
    <!-- Step 1 -->
    <rect x="30" y="60" width="340" height="50" rx="10" fill="#f0fdf4"/>
    <text x="50" y="80" fill="#10b981" font-family="Arial, sans-serif" font-size="16" font-weight="bold">1. Decision Tree Method</text>
    <text x="50" y="95" fill="#64748b" font-family="Arial, sans-serif" font-size="12">Map every decision you make into teachable steps</text>
    
    <!-- Step 2 -->
    <rect x="30" y="125" width="340" height="50" rx="10" fill="#eff6ff"/>
    <text x="50" y="145" fill="#3b82f6" font-family="Arial, sans-serif" font-size="16" font-weight="bold">2. Quality Control Automation</text>
    <text x="50" y="160" fill="#64748b" font-family="Arial, sans-serif" font-size="12">Systems that maintain standards without micromanaging</text>
    
    <!-- Step 3 -->
    <rect x="30" y="190" width="340" height="50" rx="10" fill="#fef3c7"/>
    <text x="50" y="210" fill="#f59e0b" font-family="Arial, sans-serif" font-size="16" font-weight="bold">3. Scaling Success Stories</text>
    <text x="50" y="225" fill="#64748b" font-family="Arial, sans-serif" font-size="12">Real examples of businesses that successfully scaled</text>
    
    <!-- Reading Info -->
    <rect x="30" y="255" width="160" height="30" rx="8" fill="#dcfce7"/>
    <text x="45" y="275" fill="#16a34a" font-family="Arial, sans-serif" font-size="14" font-weight="bold">17 min read</text>
    
    <rect x="210" y="255" width="160" height="30" rx="8" fill="#dbeafe"/>
    <text x="225" y="275" fill="#3b82f6" font-family="Arial, sans-serif" font-size="14" font-weight="bold">Business Systems</text>
  </g>
  
  <!-- URL -->
  <text x="1080" y="600" text-anchor="end" fill="#64748b" font-family="Arial, sans-serif" font-size="14">genlogic.io/learn</text>
</svg>
