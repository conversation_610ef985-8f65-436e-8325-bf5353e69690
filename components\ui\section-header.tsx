'use client'

import { motion } from 'framer-motion'
import { cn } from '@/lib/utils'

interface SectionHeaderProps {
  eyebrow?: string
  title: string
  description?: string
  centered?: boolean
  className?: string
}

export function SectionHeader({
  eyebrow,
  title,
  description,
  centered = true,
  className,
}: SectionHeaderProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, ease: 'easeOut' }}
      viewport={{ once: true }}
      className={cn(
        "space-y-4",
        centered && "text-center",
        className
      )}
    >
      {eyebrow && (
        <div className="inline-flex items-center px-4 py-2 rounded-full bg-primary-100 text-primary-800 text-sm font-semibold tracking-wide uppercase dark:bg-primary-900/30 dark:text-primary-400">
          {eyebrow}
        </div>
      )}

      <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-foreground leading-tight">
        {title}
      </h2>

      {description && (
        <p className={cn(
          "text-lg md:text-xl text-muted-foreground leading-relaxed",
          centered && "max-w-3xl mx-auto"
        )}>
          {description}
        </p>
      )}
    </motion.div>
  )
}
