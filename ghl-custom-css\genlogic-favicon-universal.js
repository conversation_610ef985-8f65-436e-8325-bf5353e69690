/* ===== GENLOGIC UNIVERSAL FAVICON SCRIPT ===== */
/* Works on both login and dashboard pages */

(function() {
    'use strict';
    
    // GenLogic favicon URL
    var faviconURL = 'https://storage.googleapis.com/msgsndr/7QUdjUmbf0fj8DfBvyPk/media/68795f36db7a3e181fec1c7f.svg';
    
    function setFavicon() {
        try {
            // Remove existing favicon links
            var existingLinks = document.querySelectorAll('link[rel*="icon"]');
            existingLinks.forEach(function(link) {
                link.parentNode.removeChild(link);
            });
            
            // Create new favicon link
            var link = document.createElement('link');
            link.type = 'image/svg+xml';
            link.rel = 'icon';
            link.href = faviconURL;
            
            // Also create shortcut icon for better compatibility
            var shortcutLink = document.createElement('link');
            shortcutLink.type = 'image/svg+xml';
            shortcutLink.rel = 'shortcut icon';
            shortcutLink.href = faviconURL;
            
            // Add to head
            var head = document.getElementsByTagName('head')[0];
            head.appendChild(link);
            head.appendChild(shortcutLink);
            
            console.log('GenLogic favicon applied successfully');
        } catch (error) {
            console.error('Error setting GenLogic favicon:', error);
        }
    }
    
    // Set favicon immediately if DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', setFavicon);
    } else {
        setFavicon();
    }
    
    // Also set favicon after a delay to catch dynamic loading
    setTimeout(setFavicon, 1000);
    setTimeout(setFavicon, 3000);
    
    // Watch for navigation changes (for SPA behavior)
    var observer = new MutationObserver(function(mutations) {
        var titleChanged = false;
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList' && mutation.target.tagName === 'TITLE') {
                titleChanged = true;
            }
        });
        if (titleChanged) {
            setTimeout(setFavicon, 500);
        }
    });
    
    // Start observing
    if (document.head) {
        observer.observe(document.head, {
            childList: true,
            subtree: true
        });
    }
})();

/* ===== END GENLOGIC FAVICON SCRIPT ===== */
