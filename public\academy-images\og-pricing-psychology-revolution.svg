<svg width="1200" height="630" viewBox="0 0 1200 630" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8b5cf6;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#ec4899;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f59e0b;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="cardGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.95" />
      <stop offset="100%" style="stop-color:#fdf4ff;stop-opacity:0.9" />
    </linearGradient>
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8b5cf6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ec4899;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="8" stdDeviation="16" flood-color="#000000" flood-opacity="0.1"/>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="1200" height="630" fill="url(#bgGradient)"/>
  
  <!-- Decorative Elements -->
  <circle cx="100" cy="100" r="60" fill="#ffffff" opacity="0.1"/>
  <circle cx="1100" cy="530" r="80" fill="#ffffff" opacity="0.1"/>
  <path d="M950,50 L1150,150 L1050,250 L950,150 Z" fill="#ffffff" opacity="0.05"/>
  
  <!-- Main Content Card -->
  <rect x="80" y="80" width="1040" height="470" rx="24" fill="url(#cardGradient)" filter="url(#shadow)"/>
  
  <!-- GenLogic Academy Badge -->
  <rect x="120" y="120" width="180" height="40" rx="20" fill="#1e40af"/>
  <text x="210" y="140" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="14" font-weight="bold">GENLOGIC ACADEMY</text>
  
  <!-- Value Ladder Visual -->
  <g transform="translate(380, 125)">
    <!-- Value ladder steps -->
    <rect x="0" y="60" width="60" height="20" rx="4" fill="#3b82f6"/>
    <text x="30" y="73" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="10" font-weight="bold">£47</text>
    
    <rect x="0" y="35" width="60" height="20" rx="4" fill="#10b981"/>
    <text x="30" y="48" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="10" font-weight="bold">£297</text>
    
    <rect x="0" y="10" width="60" height="20" rx="4" fill="#8b5cf6"/>
    <text x="30" y="23" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="10" font-weight="bold">£1497</text>
    
    <rect x="0" y="-15" width="60" height="20" rx="4" fill="#f59e0b"/>
    <text x="30" y="-2" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="10" font-weight="bold">£5000+</text>
    
    <!-- Arrows showing progression -->
    <path d="M65,70 L75,45" stroke="#8b5cf6" stroke-width="2" marker-end="url(#arrowhead)"/>
    <path d="M65,45 L75,20" stroke="#8b5cf6" stroke-width="2" marker-end="url(#arrowhead)"/>
    <path d="M65,20 L75,-5" stroke="#8b5cf6" stroke-width="2" marker-end="url(#arrowhead)"/>
    
    <!-- Customer segments -->
    <text x="85" y="73" fill="#3b82f6" font-family="Arial, sans-serif" font-size="10">45% convert</text>
    <text x="85" y="48" fill="#10b981" font-family="Arial, sans-serif" font-size="10">67% convert</text>
    <text x="85" y="23" fill="#8b5cf6" font-family="Arial, sans-serif" font-size="10">23% convert</text>
    <text x="85" y="-2" fill="#f59e0b" font-family="Arial, sans-serif" font-size="10">8% convert</text>
  </g>
  
  <!-- Arrow marker definition -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#8b5cf6"/>
    </marker>
  </defs>
  
  <!-- Main Headline -->
  <text x="120" y="250" fill="#1e293b" font-family="Arial, sans-serif" font-size="40" font-weight="bold">
    <tspan x="120" dy="0">Pricing Psychology</tspan>
    <tspan x="120" dy="50" fill="#8b5cf6">Revolution:</tspan>
    <tspan x="120" dy="50">Stop Leaving Money</tspan>
    <tspan x="120" dy="50" fill="#ec4899">on the Table</tspan>
  </text>
  
  <!-- Subtitle -->
  <text x="120" y="420" fill="#64748b" font-family="Arial, sans-serif" font-size="18" font-weight="normal">
    <tspan x="120" dy="0">Why cost-plus pricing leaves 60% revenue on the table</tspan>
    <tspan x="120" dy="24">and the value ladder that doubles revenue</tspan>
  </text>
  
  <!-- Pricing Stats Box -->
  <g transform="translate(650, 180)">
    <rect x="0" y="0" width="400" height="280" rx="20" fill="#f8fafc" stroke="#e2e8f0" stroke-width="2"/>
    <text x="200" y="35" text-anchor="middle" fill="#1e293b" font-family="Arial, sans-serif" font-size="18" font-weight="bold">Value Ladder Impact</text>
    
    <!-- Single Price Problem -->
    <rect x="30" y="60" width="340" height="45" rx="10" fill="#fef2f2" stroke="#dc2626" stroke-width="1"/>
    <text x="50" y="80" fill="#dc2626" font-family="Arial, sans-serif" font-size="16" font-weight="bold">Single Price: Loses 67% of customers</text>
    <text x="50" y="95" fill="#64748b" font-family="Arial, sans-serif" font-size="12">Too expensive for some, too cheap for others</text>
    
    <!-- Value Ladder Solution -->
    <rect x="30" y="115" width="340" height="45" rx="10" fill="#f0fdf4" stroke="#10b981" stroke-width="1"/>
    <text x="50" y="135" fill="#10b981" font-family="Arial, sans-serif" font-size="16" font-weight="bold">Value Ladder: Captures every segment</text>
    <text x="50" y="150" fill="#64748b" font-family="Arial, sans-serif" font-size="12">Entry → Core → Premium → Elite pricing</text>
    
    <!-- Revenue Impact -->
    <rect x="30" y="170" width="340" height="45" rx="10" fill="#f3e8ff" stroke="#8b5cf6" stroke-width="1"/>
    <text x="50" y="190" fill="#8b5cf6" font-family="Arial, sans-serif" font-size="16" font-weight="bold">Result: 240% revenue increase</text>
    <text x="50" y="205" fill="#64748b" font-family="Arial, sans-serif" font-size="12">Without acquiring more customers</text>
    
    <!-- What You'll Get -->
    <text x="50" y="235" fill="#1e293b" font-family="Arial, sans-serif" font-size="14" font-weight="bold">Psychology Framework:</text>
    <text x="50" y="250" fill="#64748b" font-family="Arial, sans-serif" font-size="12">✓ 4-level value ladder</text>
    <text x="200" y="250" fill="#64748b" font-family="Arial, sans-serif" font-size="12">✓ Pricing psychology</text>
  </g>
  
  <!-- Bottom Stats -->
  <rect x="120" y="460" width="100" height="50" rx="10" fill="#fdf4ff"/>
  <text x="170" y="480" text-anchor="middle" fill="#8b5cf6" font-family="Arial, sans-serif" font-size="16" font-weight="bold">20 min</text>
  <text x="170" y="495" text-anchor="middle" fill="#64748b" font-family="Arial, sans-serif" font-size="12">read time</text>
  
  <rect x="240" y="460" width="120" height="50" rx="10" fill="#f3e8ff"/>
  <text x="300" y="480" text-anchor="middle" fill="#8b5cf6" font-family="Arial, sans-serif" font-size="16" font-weight="bold">Lesson 13</text>
  <text x="300" y="495" text-anchor="middle" fill="#64748b" font-family="Arial, sans-serif" font-size="12">of 15</text>
  
  <!-- URL -->
  <text x="1080" y="600" text-anchor="end" fill="#64748b" font-family="Arial, sans-serif" font-size="14">genlogic.io/learn</text>
</svg>
