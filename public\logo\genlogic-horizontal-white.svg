<?xml version="1.0" encoding="UTF-8"?>
<svg width="200" height="60" viewBox="0 0 200 60" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Logo Background with White/Light Gradient -->
  <defs>
    <linearGradient id="whiteGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#f8fafc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Logo Icon -->
  <rect x="5" y="10" width="40" height="40" rx="8" fill="url(#whiteGradient)" stroke="#e2e8f0" stroke-width="1"/>
  
  <!-- Letter G -->
  <text x="25" y="35" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#2563eb" text-anchor="middle">G</text>
  
  <!-- Company Name -->
  <text x="55" y="32" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="white">GenLogic</text>
  
  <!-- Tagline -->
  <text x="55" y="45" font-family="Arial, sans-serif" font-size="10" font-weight="500" fill="#e2e8f0">Business Automation</text>
  
  <!-- Sparkle Effect -->
  <circle cx="40" cy="15" r="1.5" fill="#f59e0b" opacity="0.8">
    <animate attributeName="opacity" values="0.8;0.3;0.8" dur="2s" repeatCount="indefinite"/>
  </circle>
  <circle cx="42" cy="12" r="1" fill="#f59e0b" opacity="0.6">
    <animate attributeName="opacity" values="0.6;0.2;0.6" dur="2.5s" repeatCount="indefinite"/>
  </circle>
</svg>
