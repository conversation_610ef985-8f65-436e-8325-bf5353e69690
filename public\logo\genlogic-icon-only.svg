<?xml version="1.0" encoding="UTF-8"?>
<svg width="50" height="50" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Logo Background with Gradient -->
  <defs>
    <linearGradient id="iconGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2563eb;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#1d4ed8;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1e40af;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Logo Icon -->
  <rect x="5" y="5" width="40" height="40" rx="8" fill="url(#iconGradient)"/>
  
  <!-- Letter G -->
  <text x="25" y="32" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="white" text-anchor="middle">G</text>
  
  <!-- Sparkle Effect -->
  <circle cx="40" cy="10" r="1.5" fill="#f59e0b" opacity="0.8">
    <animate attributeName="opacity" values="0.8;0.3;0.8" dur="2s" repeatCount="indefinite"/>
  </circle>
  <circle cx="42" cy="7" r="1" fill="#f59e0b" opacity="0.6">
    <animate attributeName="opacity" values="0.6;0.2;0.6" dur="2.5s" repeatCount="indefinite"/>
  </circle>
</svg>
