import { Metadata } from 'next'
import SilentRevolutionClient from './SilentRevolutionClient'

export const metadata: Metadata = {
  title: 'The Silent Revolution in Customer Communication | GenLogic Academy',
  description: 'Why traditional customer service is quietly being abandoned by thriving businesses. Discover the communication revolution that\'s transforming customer relationships.',
  keywords: 'customer communication, customer service evolution, business communication, customer experience, UK customer service',
  openGraph: {
    title: 'The Silent Revolution in Customer Communication',
    description: 'Why traditional customer service is quietly being abandoned by thriving businesses. Discover the communication revolution that\'s transforming customer relationships.',
    type: 'article',
    url: 'https://genlogic.io/learn/fundamentals/silent-revolution',
    images: [
      {
        url: '/academy-images/og-silent-revolution.webp',
        width: 1200,
        height: 630,
        alt: 'The Silent Revolution in Customer Communication',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'The Silent Revolution in Customer Communication',
    description: 'Why traditional customer service is quietly being abandoned by thriving businesses. Discover the communication revolution that\'s transforming customer relationships.',
    images: ['/academy-images/og-silent-revolution.webp'],
  },
}

export default function SilentRevolutionPage() {
  return <SilentRevolutionClient />
}
