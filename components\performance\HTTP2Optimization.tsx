import { Metadata } from 'next'
import { CriticalFontPreloader } from './OptimizedFontLoader'

// HTTP/2 Server Push optimization component
export function HTTP2ServerPush() {
  return (
    <>
      {/* Use optimized font preloader instead of direct preload */}
      <CriticalFontPreloader />

      {/* DNS prefetch for external domains */}
      <link rel="dns-prefetch" href="//www.google-analytics.com" />
      <link rel="dns-prefetch" href="//www.googletagmanager.com" />

      {/* Analytics preconnect only for production */}
      {process.env.NODE_ENV === 'production' && (
        <>
          <link rel="preconnect" href="https://www.google-analytics.com" crossOrigin="anonymous" />
          <link rel="preconnect" href="https://www.googletagmanager.com" crossOrigin="anonymous" />
        </>
      )}
    </>
  )
}

// HTTP/2 optimized metadata
export const http2Metadata: Metadata = {
  other: {
    // Remove Link header preloads to avoid warnings - use client-side loading instead
    
    // HTTP/2 optimization headers
    'X-Content-Type-Options': 'nosniff',
    'X-DNS-Prefetch-Control': 'on',
    'X-Frame-Options': 'DENY',
    
    // Performance hints for HTTP/2
    'Accept-CH': 'DPR, Viewport-Width, Width',
    'Critical-CH': 'DPR',
    
    // HTTP/2 push policy
    'Accept-Push-Policy': 'default',
    
    // Resource timing for HTTP/2
    'Timing-Allow-Origin': '*'
  }
}

// HTTP/2 performance monitoring
export function HTTP2PerformanceMonitor() {
  if (typeof window === 'undefined') return null

  // Monitor HTTP/2 usage
  const checkHTTP2Support = () => {
    if ('serviceWorker' in navigator) {
      // Check if HTTP/2 is being used
      const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection
      
      if (connection) {
        console.log('Connection type:', connection.effectiveType)
        console.log('Downlink speed:', connection.downlink)
      }

      // Check protocol in performance entries
      if ('performance' in window && 'getEntriesByType' in performance) {
        const navigationEntries = performance.getEntriesByType('navigation') as PerformanceNavigationTiming[]
        if (navigationEntries.length > 0) {
          const entry = navigationEntries[0]
          console.log('Protocol:', (entry as any).nextHopProtocol || 'Unknown')
          
          // Track if HTTP/2 is being used
          if (window.gtag) {
            window.gtag('event', 'http_protocol', {
              event_category: 'performance',
              event_label: (entry as any).nextHopProtocol || 'unknown',
              custom_parameter_1: 'protocol_version'
            })
          }
        }
      }
    }
  }

  // Run check after page load
  if (typeof window !== 'undefined') {
    window.addEventListener('load', checkHTTP2Support)
  }

  return null
}

// HTTP/2 resource bundling optimization
export const http2ResourceHints = {
  // Critical CSS that should be inlined for HTTP/2
  criticalCSS: `
    /* Critical above-the-fold styles for HTTP/2 */
    body { margin: 0; font-family: Inter, sans-serif; }
    .hero-section { min-height: 100vh; }
    .loading-spinner { display: none; }
  `,
  
  // Resources to preload via HTTP/2 Server Push
  preloadResources: [
    { href: '/favicon.svg', as: 'image', type: 'image/svg+xml' },
    { href: '/logo/genlogic-logo.svg', as: 'image', type: 'image/svg+xml' },
    { href: 'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap', as: 'style' }
  ],
  
  // DNS prefetch for HTTP/2 connections
  dnsPrefetch: [
    'fonts.googleapis.com',
    'fonts.gstatic.com',
    'www.google-analytics.com',
    'www.googletagmanager.com',
    'region1.google-analytics.com'
  ],
  
  // Preconnect for HTTP/2 multiplexing
  preconnect: [
    { href: 'https://fonts.googleapis.com', crossOrigin: true },
    { href: 'https://fonts.gstatic.com', crossOrigin: true },
    { href: 'https://www.google-analytics.com', crossOrigin: true },
    { href: 'https://www.googletagmanager.com', crossOrigin: true }
  ]
}

// HTTP/2 Service Worker for advanced caching
export const http2ServiceWorkerCode = `
// HTTP/2 optimized service worker
const CACHE_NAME = 'genlogic-http2-v1'
const STATIC_ASSETS = [
  '/',
  '/favicon.svg',
  '/manifest.json',
  'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap'
]

// Install event - cache critical resources
self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => cache.addAll(STATIC_ASSETS))
      .then(() => self.skipWaiting())
  )
})

// Fetch event - serve from cache with HTTP/2 fallback
self.addEventListener('fetch', (event) => {
  event.respondWith(
    caches.match(event.request)
      .then((response) => {
        // Return cached version or fetch from network
        return response || fetch(event.request)
      })
  )
})

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME) {
            return caches.delete(cacheName)
          }
        })
      )
    }).then(() => self.clients.claim())
  )
})
`
