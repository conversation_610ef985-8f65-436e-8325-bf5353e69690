'use client'

import { useEffect, useState } from 'react'
import { motion } from 'framer-motion'
import { 
  ArrowLeft, 
  Clock, 
  Users, 
  CheckCircle,
  ArrowRight,
  Brain,
  MessageSquare,
  Phone,
  Mail,
  Zap,
  TrendingUp,
  TrendingDown,
  Shield,
  Target,
  Lightbulb,
  AlertTriangle,
  Eye,
  Heart,
  Star,
  Headphones,
  Bot,
  UserCheck,
  Award,
  BarChart3,
  Gift,
  Share2
} from 'lucide-react'
import Link from 'next/link'

const referralMistakes = [
  {
    mistake: "Asking Too Early",
    traditional: "Ask for referrals immediately after service",
    problem: "Customer hasn't experienced full value yet",
    impact: "87% say no or ignore request",
    color: "red"
  },
  {
    mistake: "Generic Requests", 
    traditional: "Do you know anyone who might need our services?",
    problem: "Too vague, puts burden on customer",
    impact: "94% don't respond to generic asks",
    color: "orange"
  },
  {
    mistake: "No Incentive Structure",
    traditional: "Rely on goodwill alone",
    problem: "No motivation for customer to act",
    impact: "Only 3% refer without incentives",
    color: "yellow"
  },
  {
    mistake: "Manual Follow-up",
    traditional: "Remember to ask again later",
    problem: "Inconsistent, timing is wrong",
    impact: "78% of referral opportunities missed",
    color: "red"
  }
]

const referralTriggers = [
  {
    trigger: "Achievement Moment",
    timing: "When customer reaches a milestone",
    psychology: "Peak satisfaction and gratitude",
    examples: ["Weight loss goal achieved", "Business milestone reached", "Problem solved"],
    conversion: "67%",
    color: "green"
  },
  {
    trigger: "Surprise Delight",
    timing: "After unexpected positive experience", 
    psychology: "Emotional high, want to share",
    examples: ["Exceeded expectations", "Solved urgent problem", "Personal touch"],
    conversion: "54%",
    color: "blue"
  },
  {
    trigger: "Social Proof Moment",
    timing: "When they see others' success",
    psychology: "Validation and community feeling",
    examples: ["Success story shared", "Testimonial featured", "Case study published"],
    conversion: "43%",
    color: "purple"
  },
  {
    trigger: "Value Realization",
    timing: "When ROI becomes clear",
    psychology: "Logical justification to share",
    examples: ["Cost savings calculated", "Time saved measured", "Revenue increased"],
    conversion: "61%",
    color: "orange"
  }
]

export default function ReferralEngineClient() {
  // Referral ROI Calculator state
  const [referralData, setReferralData] = useState({
    monthlyCustomers: '',
    avgOrderValue: '',
    currentReferralRate: '',
    targetReferralRate: '',
    customerLifetime: '',
    acquisitionCost: ''
  })

  // Calculate referral ROI
  const currentReferrals = (parseFloat(referralData.monthlyCustomers) || 0) * (parseFloat(referralData.currentReferralRate) || 0) / 100
  const targetReferrals = (parseFloat(referralData.monthlyCustomers) || 0) * (parseFloat(referralData.targetReferralRate) || 0) / 100
  const additionalReferrals = targetReferrals - currentReferrals
  
  const referralValue = additionalReferrals * (parseFloat(referralData.avgOrderValue) || 0) * (parseFloat(referralData.customerLifetime) || 1)
  const acquisitionSavings = additionalReferrals * (parseFloat(referralData.acquisitionCost) || 0)
  const totalValue = referralValue + acquisitionSavings
  const annualValue = totalValue * 12

  const handleReferralInputChange = (field: string, value: string) => {
    setReferralData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  // Mark lesson as completed when user visits
  useEffect(() => {
    const markAsCompleted = () => {
      const saved = localStorage.getItem('genlogic-academy-progress')
      const completed = saved ? JSON.parse(saved) : []
      if (!completed.includes('referral-engine')) {
        completed.push('referral-engine')
        localStorage.setItem('genlogic-academy-progress', JSON.stringify(completed))
        
        // Dispatch custom event to notify other components
        window.dispatchEvent(new CustomEvent('academyProgressUpdate'))
      }
    }
    
    // Mark as completed after user has been on page for 30 seconds
    const timer = setTimeout(markAsCompleted, 30000)
    return () => clearTimeout(timer)
  }, [])

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-green-50/30 to-background dark:from-background dark:via-green-950/30 dark:to-background">
      {/* Navigation */}
      <div className="pt-40 pb-8">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-5xl mx-auto">
            <Link 
              href="/learn" 
              className="inline-flex items-center gap-2 bg-background/80 backdrop-blur-sm border border-border/50 rounded-2xl px-4 py-2 text-primary-600 hover:text-primary-700 font-medium transition-all duration-300 hover:shadow-md"
            >
              <ArrowLeft className="w-4 h-4" />
              Back to Academy
            </Link>
          </div>
        </div>
      </div>

      {/* Hero Section */}
      <section className="pb-16">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-5xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              {/* Lesson Meta */}
              <div className="flex items-center justify-center gap-6 mb-8">
                <div className="flex items-center gap-2 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 px-4 py-2 rounded-2xl">
                  <Share2 className="w-4 h-4" />
                  <span className="font-medium text-sm">Referral Systems</span>
                </div>
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Clock className="w-4 h-4" />
                  <span className="text-sm">16 min read</span>
                </div>
                <div className="bg-muted px-3 py-1 rounded-full text-xs font-medium text-muted-foreground">
                  Business Systems
                </div>
              </div>

              {/* Main Title */}
              <h1 className="text-4xl md:text-6xl font-bold text-foreground mb-6 leading-tight">
                The Referral Engine:
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-green-500 to-blue-500"> Turning Customers</span>
                <br />into
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-500 to-pink-500"> Salespeople</span>
              </h1>

              <p className="text-xl md:text-2xl text-muted-foreground mb-8 leading-relaxed max-w-4xl mx-auto">
                Something's happening across UK businesses that's making traditional referral requests obsolete. 
                <strong className="text-foreground"> While most companies still ask "Do you know anyone?", smart businesses discovered the automated referral system generating 40%+ of new business.</strong>
                Here's the psychology of referral timing and the 4-trigger system that turns customers into your best salespeople.
              </p>

              {/* Shocking Stats */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
                <div className="bg-gradient-to-br from-red-50 to-red-100 dark:from-red-950/30 dark:to-red-900/30 rounded-3xl p-6 border border-red-200/50 dark:border-red-800/50">
                  <div className="flex items-center gap-3 mb-3">
                    <TrendingDown className="w-6 h-6 text-red-500" />
                    <div className="text-3xl font-bold text-red-600 dark:text-red-400">3%</div>
                  </div>
                  <div className="text-sm text-red-700 dark:text-red-300 font-medium">Of customers refer without proper system</div>
                </div>
                <div className="bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-950/30 dark:to-orange-900/30 rounded-3xl p-6 border border-orange-200/50 dark:border-orange-800/50">
                  <div className="flex items-center gap-3 mb-3">
                    <AlertTriangle className="w-6 h-6 text-orange-500" />
                    <div className="text-3xl font-bold text-orange-600 dark:text-orange-400">87%</div>
                  </div>
                  <div className="text-sm text-orange-700 dark:text-orange-300 font-medium">Say no when asked too early</div>
                </div>
                <div className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950/30 dark:to-green-900/30 rounded-3xl p-6 border border-green-200/50 dark:border-green-800/50">
                  <div className="flex items-center gap-3 mb-3">
                    <TrendingUp className="w-6 h-6 text-green-500" />
                    <div className="text-3xl font-bold text-green-600 dark:text-green-400">40%</div>
                  </div>
                  <div className="text-sm text-green-700 dark:text-green-300 font-medium">Of new business from automated referrals</div>
                </div>
              </div>
            </motion.div>

            {/* What You'll Learn Card */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="bg-gradient-to-br from-green-50 to-blue-50 dark:from-green-950/50 dark:to-blue-950/50 rounded-3xl p-8 border border-green-200/50 dark:border-green-800/50 shadow-xl"
            >
              <div className="flex items-start gap-6">
                <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-blue-500 rounded-2xl flex items-center justify-center flex-shrink-0">
                  <Brain className="w-8 h-8 text-white" />
                </div>
                <div className="flex-1">
                  <h3 className="text-2xl font-bold text-foreground mb-4">What You'll Master in This Lesson</h3>
                  <div className="grid md:grid-cols-2 gap-4">
                    <div className="space-y-3">
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-foreground">Why 87% of referral requests fail and the psychology behind it</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-foreground">The 4 emotional triggers that make customers want to refer</span>
                      </div>
                    </div>
                    <div className="space-y-3">
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-foreground">Automated referral sequences that generate 40%+ new business</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-foreground">Case study: £156k in referral revenue with zero manual effort</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Main Content - Why Referral Requests Fail */}
      <section className="pb-16">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-4xl mx-auto space-y-12">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="bg-background/80 backdrop-blur-sm rounded-3xl p-8 border border-border/50 shadow-xl"
            >
              <div className="flex items-start gap-6 mb-8">
                <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-orange-500 rounded-xl flex items-center justify-center flex-shrink-0">
                  <AlertTriangle className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h2 className="text-3xl font-bold text-foreground mb-4">Why "Do You Know Anyone?" Kills Referrals</h2>
                  <p className="text-lg text-muted-foreground leading-relaxed">
                    Smart business owners discovered that traditional referral requests actually damage relationships and reduce referrals. Here's what's really happening in your customers' minds.
                  </p>
                </div>
              </div>

              <div className="space-y-6">
                {referralMistakes.map((mistake, index) => (
                  <div key={index} className={`bg-gradient-to-r ${
                    mistake.color === 'red' ? 'from-red-50 to-red-100 dark:from-red-950/30 dark:to-red-900/30' :
                    mistake.color === 'orange' ? 'from-orange-50 to-orange-100 dark:from-orange-950/30 dark:to-orange-900/30' :
                    mistake.color === 'yellow' ? 'from-yellow-50 to-yellow-100 dark:from-yellow-950/30 dark:to-yellow-900/30' :
                    'from-red-50 to-red-100 dark:from-red-950/30 dark:to-red-900/30'
                  } rounded-2xl p-6 border ${
                    mistake.color === 'red' ? 'border-red-200/50 dark:border-red-800/50' :
                    mistake.color === 'orange' ? 'border-orange-200/50 dark:border-orange-800/50' :
                    mistake.color === 'yellow' ? 'border-yellow-200/50 dark:border-yellow-800/50' :
                    'border-red-200/50 dark:border-red-800/50'
                  }`}>
                    <h4 className={`text-xl font-bold mb-4 ${
                      mistake.color === 'red' ? 'text-red-700 dark:text-red-300' :
                      mistake.color === 'orange' ? 'text-orange-700 dark:text-orange-300' :
                      mistake.color === 'yellow' ? 'text-yellow-700 dark:text-yellow-300' :
                      'text-red-700 dark:text-red-300'
                    }`}>Mistake #{index + 1}: {mistake.mistake}</h4>

                    <div className="grid md:grid-cols-2 gap-6">
                      <div>
                        <h5 className={`font-bold mb-2 ${
                          mistake.color === 'red' ? 'text-red-600 dark:text-red-400' :
                          mistake.color === 'orange' ? 'text-orange-600 dark:text-orange-400' :
                          mistake.color === 'yellow' ? 'text-yellow-600 dark:text-yellow-400' :
                          'text-red-600 dark:text-red-400'
                        }`}>What Most Do:</h5>
                        <p className={`text-sm mb-4 ${
                          mistake.color === 'red' ? 'text-red-600 dark:text-red-400' :
                          mistake.color === 'orange' ? 'text-orange-600 dark:text-orange-400' :
                          mistake.color === 'yellow' ? 'text-yellow-600 dark:text-yellow-400' :
                          'text-red-600 dark:text-red-400'
                        }`}>"{mistake.traditional}"</p>

                        <h5 className={`font-bold mb-2 ${
                          mistake.color === 'red' ? 'text-red-600 dark:text-red-400' :
                          mistake.color === 'orange' ? 'text-orange-600 dark:text-orange-400' :
                          mistake.color === 'yellow' ? 'text-yellow-600 dark:text-yellow-400' :
                          'text-red-600 dark:text-red-400'
                        }`}>The Problem:</h5>
                        <p className={`text-sm ${
                          mistake.color === 'red' ? 'text-red-600 dark:text-red-400' :
                          mistake.color === 'orange' ? 'text-orange-600 dark:text-orange-400' :
                          mistake.color === 'yellow' ? 'text-yellow-600 dark:text-yellow-400' :
                          'text-red-600 dark:text-red-400'
                        }`}>{mistake.problem}</p>
                      </div>

                      <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
                        <h5 className={`font-bold mb-2 ${
                          mistake.color === 'red' ? 'text-red-600 dark:text-red-400' :
                          mistake.color === 'orange' ? 'text-orange-600 dark:text-orange-400' :
                          mistake.color === 'yellow' ? 'text-yellow-600 dark:text-yellow-400' :
                          'text-red-600 dark:text-red-400'
                        }`}>Impact:</h5>
                        <div className={`text-2xl font-bold mb-2 ${
                          mistake.color === 'red' ? 'text-red-600 dark:text-red-400' :
                          mistake.color === 'orange' ? 'text-orange-600 dark:text-orange-400' :
                          mistake.color === 'yellow' ? 'text-yellow-600 dark:text-yellow-400' :
                          'text-red-600 dark:text-red-400'
                        }`}>{mistake.impact}</div>
                        <p className="text-xs text-muted-foreground">Customer response rate</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              <div className="bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-950/30 dark:to-blue-900/30 rounded-2xl p-6 border border-blue-200/50 dark:border-blue-800/50 mt-8">
                <h3 className="text-xl font-bold text-blue-700 dark:text-blue-300 mb-4">The Psychology Behind Referral Resistance</h3>
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-bold text-blue-600 dark:text-blue-400 mb-2">What Customers Think:</h4>
                    <ul className="space-y-1 text-sm text-blue-600 dark:text-blue-400">
                      <li>• "They're just trying to get more business"</li>
                      <li>• "I don't want to bother my friends"</li>
                      <li>• "What if they have a bad experience?"</li>
                      <li>• "I'm not a salesperson"</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-bold text-blue-600 dark:text-blue-400 mb-2">What They Need Instead:</h4>
                    <ul className="space-y-1 text-sm text-blue-600 dark:text-blue-400">
                      <li>• Emotional motivation to share</li>
                      <li>• Easy way to help friends</li>
                      <li>• Confidence in the outcome</li>
                      <li>• Personal benefit for referring</li>
                    </ul>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* The 4 Referral Triggers */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="bg-background/80 backdrop-blur-sm rounded-3xl p-8 border border-border/50 shadow-xl"
            >
              <div className="flex items-start gap-6 mb-8">
                <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-blue-500 rounded-xl flex items-center justify-center flex-shrink-0">
                  <Zap className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h2 className="text-3xl font-bold text-foreground mb-4">The 4 Emotional Triggers That Make Customers Want to Refer</h2>
                  <p className="text-lg text-muted-foreground leading-relaxed">
                    Smart businesses discovered that referrals happen naturally when you trigger the right emotions at the right moments. Here are the 4 psychological triggers that turn customers into advocates.
                  </p>
                </div>
              </div>

              <div className="space-y-8">
                {referralTriggers.map((trigger, index) => (
                  <div key={index} className={`bg-gradient-to-r ${
                    trigger.color === 'green' ? 'from-green-50 to-green-100 dark:from-green-950/30 dark:to-green-900/30' :
                    trigger.color === 'blue' ? 'from-blue-50 to-blue-100 dark:from-blue-950/30 dark:to-blue-900/30' :
                    trigger.color === 'purple' ? 'from-purple-50 to-purple-100 dark:from-purple-950/30 dark:to-purple-900/30' :
                    'from-orange-50 to-orange-100 dark:from-orange-950/30 dark:to-orange-900/30'
                  } rounded-2xl p-6 border ${
                    trigger.color === 'green' ? 'border-green-200/50 dark:border-green-800/50' :
                    trigger.color === 'blue' ? 'border-blue-200/50 dark:border-blue-800/50' :
                    trigger.color === 'purple' ? 'border-purple-200/50 dark:border-purple-800/50' :
                    'border-orange-200/50 dark:border-orange-800/50'
                  }`}>
                    <div className="flex items-start gap-4 mb-6">
                      <div className={`w-12 h-12 rounded-xl flex items-center justify-center flex-shrink-0 ${
                        trigger.color === 'green' ? 'bg-green-500' :
                        trigger.color === 'blue' ? 'bg-blue-500' :
                        trigger.color === 'purple' ? 'bg-purple-500' :
                        'bg-orange-500'
                      }`}>
                        <span className="text-white font-bold text-lg">{index + 1}</span>
                      </div>
                      <div className="flex-1">
                        <h4 className={`text-xl font-bold mb-2 ${
                          trigger.color === 'green' ? 'text-green-700 dark:text-green-300' :
                          trigger.color === 'blue' ? 'text-blue-700 dark:text-blue-300' :
                          trigger.color === 'purple' ? 'text-purple-700 dark:text-purple-300' :
                          'text-orange-700 dark:text-orange-300'
                        }`}>
                          {trigger.trigger}
                        </h4>
                        <p className={`mb-4 ${
                          trigger.color === 'green' ? 'text-green-600 dark:text-green-400' :
                          trigger.color === 'blue' ? 'text-blue-600 dark:text-blue-400' :
                          trigger.color === 'purple' ? 'text-purple-600 dark:text-purple-400' :
                          'text-orange-600 dark:text-orange-400'
                        }`}>
                          <strong>Timing:</strong> {trigger.timing}
                        </p>
                        <p className={`mb-4 ${
                          trigger.color === 'green' ? 'text-green-600 dark:text-green-400' :
                          trigger.color === 'blue' ? 'text-blue-600 dark:text-blue-400' :
                          trigger.color === 'purple' ? 'text-purple-600 dark:text-purple-400' :
                          'text-orange-600 dark:text-orange-400'
                        }`}>
                          <strong>Psychology:</strong> {trigger.psychology}
                        </p>
                      </div>
                      <div className="text-center">
                        <div className={`text-3xl font-bold ${
                          trigger.color === 'green' ? 'text-green-600 dark:text-green-400' :
                          trigger.color === 'blue' ? 'text-blue-600 dark:text-blue-400' :
                          trigger.color === 'purple' ? 'text-purple-600 dark:text-purple-400' :
                          'text-orange-600 dark:text-orange-400'
                        }`}>{trigger.conversion}</div>
                        <div className="text-xs text-muted-foreground">Conversion Rate</div>
                      </div>
                    </div>

                    <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
                      <h5 className={`font-bold mb-2 ${
                        trigger.color === 'green' ? 'text-green-600 dark:text-green-400' :
                        trigger.color === 'blue' ? 'text-blue-600 dark:text-blue-400' :
                        trigger.color === 'purple' ? 'text-purple-600 dark:text-purple-400' :
                        'text-orange-600 dark:text-orange-400'
                      }`}>Examples:</h5>
                      <div className="grid md:grid-cols-3 gap-2">
                        {trigger.examples.map((example, exampleIndex) => (
                          <div key={exampleIndex} className={`text-sm p-2 rounded ${
                            trigger.color === 'green' ? 'bg-green-50 dark:bg-green-900/30 text-green-600 dark:text-green-400' :
                            trigger.color === 'blue' ? 'bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400' :
                            trigger.color === 'purple' ? 'bg-purple-50 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400' :
                            'bg-orange-50 dark:bg-orange-900/30 text-orange-600 dark:text-orange-400'
                          }`}>
                            {example}
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>

            {/* Real Case Study: The £156k Referral Engine */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              className="bg-background/80 backdrop-blur-sm rounded-3xl p-8 border border-border/50 shadow-xl"
            >
              <div className="flex items-start gap-6 mb-8">
                <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl flex items-center justify-center flex-shrink-0">
                  <Award className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h2 className="text-3xl font-bold text-foreground mb-4">Case Study: How Elite Fitness Generated £156k in Referral Revenue</h2>
                  <p className="text-lg text-muted-foreground leading-relaxed">
                    James owned a premium fitness studio in Birmingham, but struggled with expensive customer acquisition. Here's how he built an automated referral engine that now generates 43% of his new business.
                  </p>
                </div>
              </div>

              <div className="bg-gradient-to-r from-red-50 to-red-100 dark:from-red-950/30 dark:to-red-900/30 rounded-2xl p-6 border border-red-200/50 dark:border-red-800/50 mb-8">
                <h3 className="text-xl font-bold text-red-700 dark:text-red-300 mb-4">The Referral Struggle</h3>
                <div className="grid md:grid-cols-3 gap-4 mb-4">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-red-600 dark:text-red-400 mb-2">£340</div>
                    <div className="text-sm text-red-700 dark:text-red-300">Cost per new member</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-red-600 dark:text-red-400 mb-2">2%</div>
                    <div className="text-sm text-red-700 dark:text-red-300">Referral rate</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-red-600 dark:text-red-400 mb-2">£89k</div>
                    <div className="text-sm text-red-700 dark:text-red-300">Annual marketing spend</div>
                  </div>
                </div>
                <p className="text-red-600 dark:text-red-400 text-sm">
                  "I was spending a fortune on Facebook ads and Google ads, but barely getting any referrals.
                  When I asked members for referrals, they'd say 'sure' but nothing happened. I was burning cash on acquisition."
                </p>
              </div>

              <div className="space-y-8">
                {/* Phase 1: Trigger Identification */}
                <div className="bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-950/30 dark:to-blue-900/30 rounded-2xl p-6 border border-blue-200/50 dark:border-blue-800/50">
                  <h4 className="text-lg font-bold text-blue-700 dark:text-blue-300 mb-4">Phase 1: Trigger Identification (Month 1)</h4>
                  <p className="text-blue-600 dark:text-blue-400 mb-4">
                    <strong>The Strategy:</strong> Map member journey to identify peak emotional moments for referrals.
                  </p>

                  <div className="bg-white dark:bg-gray-800 rounded-xl p-4 mb-4">
                    <h5 className="font-bold text-blue-700 dark:text-blue-300 mb-3">Emotional Trigger Mapping</h5>
                    <div className="space-y-4">
                      <div className="bg-blue-50 dark:bg-blue-900/30 rounded-lg p-3">
                        <strong className="text-blue-600">Achievement Moments:</strong>
                        <div className="mt-2 text-sm text-blue-600">
                          • First 5kg weight loss milestone
                          • Completing first month consistently
                          • Achieving personal fitness goal
                          • Receiving compliments from others
                        </div>
                      </div>
                      <div className="bg-blue-50 dark:bg-blue-900/30 rounded-lg p-3">
                        <strong className="text-blue-600">Surprise Delight Moments:</strong>
                        <div className="mt-2 text-sm text-blue-600">
                          • Personal trainer remembers their name
                          • Free nutrition consultation offered
                          • Birthday workout surprise
                          • Progress photos show dramatic change
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-green-100 dark:bg-green-900/30 rounded-xl p-4">
                    <h6 className="font-bold text-green-700 dark:text-green-300 mb-2">Phase 1 Results:</h6>
                    <div className="grid md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <div className="text-green-600 dark:text-green-400">✓ Identified 8 key trigger moments</div>
                        <div className="text-green-600 dark:text-green-400">✓ Mapped member emotional journey</div>
                      </div>
                      <div>
                        <div className="text-green-600 dark:text-green-400">✓ Created trigger-based messaging</div>
                        <div className="text-green-600 dark:text-green-400">✓ Set up automated detection system</div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Phase 2: Automated Sequences */}
                <div className="bg-gradient-to-r from-green-50 to-green-100 dark:from-green-950/30 dark:to-green-900/30 rounded-2xl p-6 border border-green-200/50 dark:border-green-800/50">
                  <h4 className="text-lg font-bold text-green-700 dark:text-green-300 mb-4">Phase 2: Automated Referral Sequences (Month 2-3)</h4>
                  <p className="text-green-600 dark:text-green-400 mb-4">
                    <strong>The Strategy:</strong> Create automated sequences triggered by emotional moments, not calendar dates.
                  </p>

                  <div className="bg-white dark:bg-gray-800 rounded-xl p-4 mb-4">
                    <h5 className="font-bold text-green-700 dark:text-green-300 mb-3">Achievement Trigger Sequence</h5>
                    <div className="space-y-3">
                      <div className="flex items-start gap-3">
                        <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center text-white text-xs font-bold">1</div>
                        <div>
                          <strong className="text-green-600">Immediate Celebration:</strong>
                          <div className="text-sm text-green-600 mt-1">
                            "🎉 Sarah, you've just hit your 5kg weight loss goal! This is HUGE! Your dedication is inspiring."
                          </div>
                        </div>
                      </div>
                      <div className="flex items-start gap-3">
                        <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center text-white text-xs font-bold">2</div>
                        <div>
                          <strong className="text-green-600">Social Proof Moment (24 hours later):</strong>
                          <div className="text-sm text-green-600 mt-1">
                            "Your transformation reminds me of Lisa, who lost 12kg and says it changed her life.
                            Do you know anyone struggling with their fitness journey who could use this kind of support?"
                          </div>
                        </div>
                      </div>
                      <div className="flex items-start gap-3">
                        <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center text-white text-xs font-bold">3</div>
                        <div>
                          <strong className="text-green-600">Easy Referral Tool (48 hours later):</strong>
                          <div className="text-sm text-green-600 mt-1">
                            "If someone comes to mind, just reply with their name and I'll send them a personal invitation
                            to try a free session. No pressure - just helping friends discover what you've discovered."
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-green-100 dark:bg-green-900/30 rounded-xl p-4">
                    <h6 className="font-bold text-green-700 dark:text-green-300 mb-2">Phase 2 Results (Month 3):</h6>
                    <div className="grid md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <div className="text-green-600 dark:text-green-400">✓ Referral rate: 2% → 18%</div>
                        <div className="text-green-600 dark:text-green-400">✓ Response rate: 94% vs 12% before</div>
                      </div>
                      <div>
                        <div className="text-green-600 dark:text-green-400">✓ 47 new members from referrals</div>
                        <div className="text-green-600 dark:text-green-400">✓ £23k in referral revenue</div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Phase 3: Incentive Optimization */}
                <div className="bg-gradient-to-r from-purple-50 to-purple-100 dark:from-purple-950/30 dark:to-purple-900/30 rounded-2xl p-6 border border-purple-200/50 dark:border-purple-800/50">
                  <h4 className="text-lg font-bold text-purple-700 dark:text-purple-300 mb-4">Phase 3: Incentive Optimization (Month 4-6)</h4>
                  <p className="text-purple-600 dark:text-purple-400 mb-4">
                    <strong>The Strategy:</strong> Test different incentive structures to maximize both referrals and lifetime value.
                  </p>

                  <div className="bg-white dark:bg-gray-800 rounded-xl p-4 mb-4">
                    <h5 className="font-bold text-purple-700 dark:text-purple-300 mb-3">Winning Incentive Structure</h5>
                    <div className="grid md:grid-cols-2 gap-4">
                      <div>
                        <h6 className="font-bold text-purple-600 dark:text-purple-400 mb-2">For Referrer:</h6>
                        <ul className="space-y-1 text-sm text-purple-600 dark:text-purple-400">
                          <li>• Free month when friend joins</li>
                          <li>• Personal training session bonus</li>
                          <li>• VIP member status upgrade</li>
                          <li>• Exclusive member events access</li>
                        </ul>
                      </div>
                      <div>
                        <h6 className="font-bold text-purple-600 dark:text-purple-400 mb-2">For New Member:</h6>
                        <ul className="space-y-1 text-sm text-purple-600 dark:text-purple-400">
                          <li>• 50% off first month</li>
                          <li>• Free fitness assessment</li>
                          <li>• Buddy workout sessions</li>
                          <li>• Nutrition consultation included</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <div className="bg-purple-100 dark:bg-purple-900/30 rounded-xl p-4">
                    <h6 className="font-bold text-purple-700 dark:text-purple-300 mb-2">Phase 3 Results (Month 6):</h6>
                    <div className="grid md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <div className="text-purple-600 dark:text-purple-400">✓ Referral rate: 18% → 43%</div>
                        <div className="text-purple-600 dark:text-purple-400">✓ Referred member retention: +89%</div>
                      </div>
                      <div>
                        <div className="text-purple-600 dark:text-purple-400">✓ 156 new members from referrals</div>
                        <div className="text-purple-600 dark:text-purple-400">✓ £156k total referral revenue</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-950/30 dark:to-blue-950/30 rounded-2xl p-6 border border-green-200/50 dark:border-green-800/50 mt-8">
                <h4 className="text-xl font-bold text-green-700 dark:text-green-300 mb-4">Total Transformation: £156,000 in Referral Revenue</h4>
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h5 className="font-bold text-green-600 dark:text-green-400 mb-2">Financial Impact:</h5>
                    <ul className="space-y-1 text-sm text-green-600 dark:text-green-400">
                      <li>• Referral revenue: £156k in 6 months</li>
                      <li>• Acquisition cost reduction: £67k saved</li>
                      <li>• Marketing spend: £89k → £34k</li>
                      <li><strong>• Total value created: £278k</strong></li>
                    </ul>
                  </div>
                  <div>
                    <h5 className="font-bold text-blue-600 dark:text-blue-400 mb-2">Business Impact:</h5>
                    <ul className="space-y-1 text-sm text-blue-600 dark:text-blue-400">
                      <li>• Referral rate: 2% → 43% (+2,050%)</li>
                      <li>• New member quality: +89% retention</li>
                      <li>• Member satisfaction: +156%</li>
                      <li><strong>• James's stress: Dramatically reduced</strong></li>
                    </ul>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Referral ROI Calculator */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
              className="bg-background/80 backdrop-blur-sm rounded-3xl p-8 border border-border/50 shadow-xl"
            >
              <div className="flex items-start gap-6 mb-8">
                <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-purple-500 rounded-xl flex items-center justify-center flex-shrink-0">
                  <BarChart3 className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h2 className="text-3xl font-bold text-foreground mb-4">Referral Engine ROI Calculator</h2>
                  <p className="text-lg text-muted-foreground leading-relaxed">
                    Calculate exactly how much revenue you could generate by implementing an automated referral system.
                  </p>
                </div>
              </div>

              <div className="bg-gradient-to-r from-green-50 to-purple-50 dark:from-green-950/30 dark:to-purple-950/30 rounded-2xl p-6 border border-green-200/50 dark:border-green-800/50">
                <h3 className="text-xl font-bold text-green-700 dark:text-green-300 mb-6">Interactive Referral Revenue Calculator</h3>

                <div className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
                      <h4 className="font-bold text-green-600 dark:text-green-400 mb-3">Current Business Metrics</h4>
                      <div className="space-y-3 text-sm">
                        <div className="flex items-center gap-2">
                          <label className="min-w-0 flex-1">Monthly customers:</label>
                          <input
                            type="number"
                            value={referralData.monthlyCustomers}
                            onChange={(e) => handleReferralInputChange('monthlyCustomers', e.target.value)}
                            className="w-20 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-foreground"
                            placeholder="100"
                          />
                        </div>
                        <div className="flex items-center gap-2">
                          <label className="min-w-0 flex-1">Average order value (£):</label>
                          <input
                            type="number"
                            value={referralData.avgOrderValue}
                            onChange={(e) => handleReferralInputChange('avgOrderValue', e.target.value)}
                            className="w-20 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-foreground"
                            placeholder="150"
                          />
                        </div>
                        <div className="flex items-center gap-2">
                          <label className="min-w-0 flex-1">Customer lifetime (months):</label>
                          <input
                            type="number"
                            value={referralData.customerLifetime}
                            onChange={(e) => handleReferralInputChange('customerLifetime', e.target.value)}
                            className="w-20 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-foreground"
                            placeholder="12"
                          />
                        </div>
                      </div>
                    </div>

                    <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
                      <h4 className="font-bold text-green-600 dark:text-green-400 mb-3">Referral Metrics</h4>
                      <div className="space-y-3 text-sm">
                        <div className="flex items-center gap-2">
                          <label className="min-w-0 flex-1">Current referral rate (%):</label>
                          <input
                            type="number"
                            value={referralData.currentReferralRate}
                            onChange={(e) => handleReferralInputChange('currentReferralRate', e.target.value)}
                            className="w-20 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-foreground"
                            placeholder="3"
                          />
                        </div>
                        <div className="flex items-center gap-2">
                          <label className="min-w-0 flex-1">Target referral rate (%):</label>
                          <input
                            type="number"
                            value={referralData.targetReferralRate}
                            onChange={(e) => handleReferralInputChange('targetReferralRate', e.target.value)}
                            className="w-20 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-foreground"
                            placeholder="25"
                          />
                        </div>
                        <div className="flex items-center gap-2">
                          <label className="min-w-0 flex-1">Acquisition cost (£):</label>
                          <input
                            type="number"
                            value={referralData.acquisitionCost}
                            onChange={(e) => handleReferralInputChange('acquisitionCost', e.target.value)}
                            className="w-20 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-foreground"
                            placeholder="200"
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-green-100 to-purple-100 dark:from-green-900/30 dark:to-purple-900/30 rounded-xl p-6 text-center border-2 border-green-300 dark:border-green-700">
                    <h4 className="text-2xl font-bold text-green-700 dark:text-green-300 mb-3">Annual Referral Revenue Potential</h4>
                    <div className="grid md:grid-cols-3 gap-4 mb-4">
                      <div>
                        <div className="text-lg font-bold text-blue-600 dark:text-blue-400">Additional Referrals</div>
                        <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">{Math.round(additionalReferrals * 12)}</div>
                        <div className="text-xs text-blue-500">per year</div>
                      </div>
                      <div>
                        <div className="text-lg font-bold text-green-600 dark:text-green-400">Referral Revenue</div>
                        <div className="text-2xl font-bold text-green-600 dark:text-green-400">{formatCurrency(referralValue * 12)}</div>
                        <div className="text-xs text-green-500">lifetime value</div>
                      </div>
                      <div>
                        <div className="text-lg font-bold text-purple-600 dark:text-purple-400">Acquisition Savings</div>
                        <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">{formatCurrency(acquisitionSavings * 12)}</div>
                        <div className="text-xs text-purple-500">marketing saved</div>
                      </div>
                    </div>
                    <div className="border-t pt-4">
                      <div className="text-3xl font-bold text-green-600 dark:text-green-400 mb-2">Total Annual Value</div>
                      <div className="text-5xl font-bold text-green-600 dark:text-green-400 mb-2">{formatCurrency(annualValue)}</div>
                      <div className="text-lg font-bold text-green-600 dark:text-green-400">Per Year</div>
                    </div>
                    {annualValue > 0 && (
                      <div className="mt-4 p-3 bg-green-200 dark:bg-green-800/50 rounded-lg">
                        <p className="text-sm font-bold text-green-700 dark:text-green-300">
                          This includes {Math.round(additionalReferrals * 12)} additional customers per year + marketing cost savings!
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Call to Action */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.7 }}
              className="bg-gradient-to-r from-green-500 to-purple-500 rounded-3xl p-8 text-white text-center"
            >
              <h2 className="text-3xl font-bold mb-4">Something's Changing</h2>
              <p className="text-xl mb-6 opacity-90">
                Smart business owners are quietly discovering what successful companies already know about referral systems. The shift is happening whether you participate or not.
              </p>
              <Link
                href="/demo"
                className="inline-flex items-center gap-2 bg-white text-green-600 px-8 py-4 rounded-2xl font-bold text-lg hover:bg-gray-100 transition-all duration-300 shadow-lg hover:shadow-xl"
              >
                See What They Know
                <ArrowRight className="w-5 h-5" />
              </Link>
              <p className="text-sm mt-4 opacity-75">
                Join the business owners who've already discovered the referral revolution
              </p>
            </motion.div>

            {/* Next Lesson Navigation */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.8 }}
              className="bg-background rounded-3xl border border-border shadow-lg p-8"
            >
              <h3 className="text-xl font-bold text-foreground mb-6 text-center">Continue Your Learning Journey</h3>

              <div className="grid md:grid-cols-2 gap-6">
                <Link
                  href="/learn/fundamentals/pricing-psychology-revolution"
                  className="group bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-950/30 dark:to-orange-900/30 rounded-2xl p-6 border border-orange-200/50 dark:border-orange-800/50 hover:shadow-lg transition-all duration-300"
                >
                  <div className="flex items-center gap-4 mb-4">
                    <div className="w-12 h-12 bg-orange-100 dark:bg-orange-900/50 rounded-xl flex items-center justify-center">
                      <ArrowRight className="w-6 h-6 text-orange-600 group-hover:translate-x-1 transition-transform" />
                    </div>
                    <div>
                      <h4 className="font-bold text-foreground">Next Lesson</h4>
                      <p className="text-sm text-muted-foreground">Lesson 13 of 15</p>
                    </div>
                  </div>
                  <h5 className="font-semibold text-foreground mb-2">The Pricing Psychology Revolution</h5>
                  <p className="text-sm text-muted-foreground">
                    How psychological pricing triggers can increase your revenue by 23% without changing your service
                  </p>
                </Link>

                <Link
                  href="/learn"
                  className="group bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-950/30 dark:to-gray-900/30 rounded-2xl p-6 border border-gray-200/50 dark:border-gray-800/50 hover:shadow-lg transition-all duration-300"
                >
                  <div className="flex items-center gap-4 mb-4">
                    <div className="w-12 h-12 bg-gray-100 dark:bg-gray-900/50 rounded-xl flex items-center justify-center">
                      <ArrowLeft className="w-6 h-6 text-gray-600 group-hover:-translate-x-1 transition-transform" />
                    </div>
                    <div>
                      <h4 className="font-bold text-foreground">Academy</h4>
                      <p className="text-sm text-muted-foreground">All lessons</p>
                    </div>
                  </div>
                  <h5 className="font-semibold text-foreground mb-2">Back to Academy</h5>
                  <p className="text-sm text-muted-foreground">
                    View all lessons and track your progress
                  </p>
                </Link>
              </div>
            </motion.div>
          </div>
        </div>
      </section>
    </div>
  )
}
