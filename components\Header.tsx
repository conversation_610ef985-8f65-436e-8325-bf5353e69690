'use client'

import Link from 'next/link'
import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Menu, X, ArrowR<PERSON>, Sparkles, ChevronDown } from 'lucide-react'
import { CustomButton } from '@/components/ui/custom-button'
import ThemeToggle from './ThemeToggle'

interface NavItem {
  href: string
  label: string
  submenu?: { href: string; label: string }[]
}

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isScrolled, setIsScrolled] = useState(false)
  const [openDropdown, setOpenDropdown] = useState<string | null>(null)
  const [dropdownPosition, setDropdownPosition] = useState({ left: 0, width: 0 })

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20)
    }
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  // Close mobile menu when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      if (isMenuOpen) setIsMenuOpen(false)
    }
    document.addEventListener('click', handleClickOutside)
    return () => document.removeEventListener('click', handleClickOutside)
  }, [isMenuOpen])

  const navItems: NavItem[] = [
    {
      href: '/solutions',
      label: 'Solutions',
      submenu: [
        { href: '/solutions', label: 'GoHighLevel Automation' },
        { href: '/services/business-automation', label: 'Business Automation' },
        { href: '/website-development', label: 'Website Development' }
      ]
    },
    { href: '/pricing', label: 'Pricing' },
    {
      href: '/learn',
      label: 'Resources',
      submenu: [
        { href: '/learn', label: 'Academy' },
        { href: '/blog', label: 'Blog' }
      ]
    },
    {
      href: '/about',
      label: 'Company',
      submenu: [
        { href: '/about', label: 'About Us' },
        { href: '/contact', label: 'Contact' }
      ]
    },
  ]

  return (
    <div>
      {/* Announcement Bar Container */}
      <motion.div
        initial={{ y: -50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.6, ease: 'easeOut' }}
        className="fixed top-0 left-0 right-0 z-50 px-4 sm:px-6 lg:px-8 pt-3"
      >
        <div className="max-w-6xl mx-auto bg-gradient-to-r from-primary-600 via-primary-700 to-primary-600 text-white py-3 px-6 text-center text-sm font-medium relative overflow-hidden rounded-2xl shadow-lg shadow-primary-500/20">
          <div className="relative z-10">
            <span className="hidden sm:inline">🎉 </span>
            <strong>Limited Time:</strong> Get 2 months free when you start your trial this week!
            <span className="hidden sm:inline"> 🎉</span>
          </div>
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent animate-pulse rounded-2xl"></div>
          <div className="absolute inset-0 rounded-2xl ring-1 ring-white/20"></div>
        </div>
      </motion.div>

      {/* Main Header Container */}
      <motion.div
        className="fixed top-16 left-0 right-0 z-40 px-4 sm:px-6 lg:px-8 pt-2"
        initial={{ y: -100 }}
        animate={{ y: 0 }}
        transition={{ duration: 0.6, ease: 'easeOut', delay: 0.2 }}
      >
        <motion.header
          className={`max-w-6xl mx-auto transition-all duration-500 rounded-2xl overflow-hidden ${
            isScrolled
              ? 'bg-background/95 backdrop-blur-md shadow-xl shadow-black/5 border border-border/50 scale-[0.98]'
              : 'bg-background/90 backdrop-blur-sm shadow-lg shadow-black/5 border border-border/30 scale-100'
          }`}
          layout
          style={{
            backdropFilter: 'blur(20px)',
            WebkitBackdropFilter: 'blur(20px)',
          }}
        >
          <div className="px-6 sm:px-8">
            <div className="flex justify-between items-center h-20">
              {/* Logo */}
              <motion.div
                className="flex-shrink-0"
                whileHover={{ scale: 1.02 }}
                transition={{ duration: 0.2 }}
              >
                <Link href="/" className="flex items-center group">
                  <div className="relative">
                    <div className="w-11 h-11 bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800 rounded-xl flex items-center justify-center mr-3 group-hover:shadow-lg group-hover:shadow-primary-500/25 transition-all duration-300">
                      <span className="text-white font-bold text-lg">G</span>
                      <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </div>
                    {/* Sparkle effect */}
                    <motion.div
                      className="absolute -top-0.5 -right-0.5"
                      animate={{
                        rotate: [0, 180, 360],
                        scale: [1, 1.2, 1]
                      }}
                      transition={{
                        duration: 3,
                        repeat: Infinity,
                        ease: "easeInOut"
                      }}
                    >
                      <Sparkles className="w-3.5 h-3.5 text-accent-500 opacity-60" />
                    </motion.div>
                  </div>
                  <div>
                    <span className="text-xl font-bold text-foreground group-hover:text-primary-700 transition-colors duration-300">
                      GenLogic
                    </span>
                    <div className="text-xs text-muted-foreground font-medium tracking-wide hidden sm:block">
                      Business Automation
                    </div>
                  </div>
                </Link>
              </motion.div>

              {/* Desktop Navigation */}
              <nav className="hidden lg:flex items-center space-x-1">
                {navItems.map((item, index) => (
                  <motion.div
                    key={item.href}
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                    className="relative"
                    onMouseEnter={(e) => {
                      if (item.submenu) {
                        const rect = e.currentTarget.getBoundingClientRect()
                        setDropdownPosition({
                          left: rect.left + rect.width / 2 - 112, // Center dropdown (224px width / 2)
                          width: rect.width
                        })
                        setOpenDropdown(item.label)
                      }
                    }}
                    onMouseLeave={() => setOpenDropdown(null)}
                  >
                    {item.submenu ? (
                      <div className="relative">
                        <button className="flex items-center px-3 py-2 text-muted-foreground hover:text-primary-700 font-medium transition-all duration-300 rounded-lg hover:bg-primary-50 dark:hover:bg-primary-900/20 group text-sm">
                          {item.label}
                          <ChevronDown className="w-4 h-4 ml-1 transition-transform duration-200 group-hover:rotate-180" />
                        </button>


                      </div>
                    ) : (
                      <Link
                        href={item.href}
                        className="relative px-3 py-2 text-muted-foreground hover:text-primary-700 font-medium transition-all duration-300 rounded-lg hover:bg-primary-50 dark:hover:bg-primary-900/20 group text-sm"
                      >
                        {item.label}
                        <motion.div
                          className="absolute bottom-0 left-1/2 w-0 h-0.5 bg-primary-600 group-hover:w-6 group-hover:left-1/2 group-hover:-translate-x-1/2 transition-all duration-300"
                        />
                      </Link>
                    )}
                  </motion.div>
                ))}
              </nav>

              {/* Desktop CTA & Theme Toggle */}
              <div className="hidden lg:flex items-center space-x-4">
                {/* Theme Toggle */}
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.3, delay: 0.3 }}
                >
                  <ThemeToggle />
                </motion.div>

                {/* Enhanced Login Button */}
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.3, delay: 0.35 }}
                >
                  <Link
                    href="https://app.genlogic.io"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center px-4 py-2.5 text-sm font-semibold text-primary-700 bg-gradient-to-r from-primary-50 to-primary-100 dark:from-primary-900/30 dark:to-primary-800/30 dark:text-primary-300 border border-primary-200 dark:border-primary-700 rounded-lg hover:from-primary-100 hover:to-primary-200 dark:hover:from-primary-800/40 dark:hover:to-primary-700/40 hover:border-primary-300 dark:hover:border-primary-600 hover:text-primary-800 dark:hover:text-primary-200 transition-all duration-300 shadow-sm hover:shadow-md hover:shadow-primary-500/20"
                  >
                    Login
                  </Link>
                </motion.div>

                {/* CTA Button */}
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.3, delay: 0.4 }}
                >
                  <CustomButton
                    variant="primary"
                    size="sm"
                    href="/demo"
                    icon={<ArrowRight className="w-4 h-4" />}
                    iconPosition="right"
                    className="shadow-lg hover:shadow-xl hover:shadow-primary-500/25 text-sm px-5 py-2.5"
                  >
                    Book Demo
                  </CustomButton>
                </motion.div>
              </div>

              {/* Mobile menu button & theme toggle */}
              <div className="lg:hidden flex items-center space-x-3">
                <ThemeToggle />
                <motion.button
                  onClick={(e) => {
                    e.stopPropagation()
                    setIsMenuOpen(!isMenuOpen)
                  }}
                  className="relative p-2 text-muted-foreground hover:text-primary-600 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-lg transition-colors duration-300"
                  whileTap={{ scale: 0.95 }}
                  aria-label="Toggle menu"
                >
                  <AnimatePresence mode="wait">
                    {isMenuOpen ? (
                      <motion.div
                        key="close"
                        initial={{ rotate: -90, opacity: 0 }}
                        animate={{ rotate: 0, opacity: 1 }}
                        exit={{ rotate: 90, opacity: 0 }}
                        transition={{ duration: 0.2 }}
                      >
                        <X className="h-5 w-5" />
                      </motion.div>
                    ) : (
                      <motion.div
                        key="menu"
                        initial={{ rotate: 90, opacity: 0 }}
                        animate={{ rotate: 0, opacity: 1 }}
                        exit={{ rotate: -90, opacity: 0 }}
                        transition={{ duration: 0.2 }}
                      >
                        <Menu className="h-5 w-5" />
                      </motion.div>
                    )}
                  </AnimatePresence>
                </motion.button>
              </div>
            </div>
          </div>
        </motion.header>
      </motion.div>

      {/* Desktop Dropdown Menus - Outside header container */}
      <AnimatePresence>
        {openDropdown && (
          <motion.div
            initial={{ opacity: 0, y: 10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 10, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className="fixed top-32 left-0 right-0 z-60 px-4 sm:px-6 lg:px-8 pointer-events-none"
            onMouseEnter={() => setOpenDropdown(openDropdown)}
            onMouseLeave={() => setOpenDropdown(null)}
          >
            <div className="relative">
              <div className="hidden lg:block">
                {navItems.map((item) => {
                  if (item.label === openDropdown && item.submenu) {
                    return (
                      <div
                        key={item.label}
                        className="absolute w-56 bg-background border border-primary-200/20 dark:border-primary-800/20 rounded-2xl shadow-xl backdrop-blur-sm pointer-events-auto"
                        style={{
                          left: `${dropdownPosition.left}px`,
                          transform: 'translateX(0)'
                        }}
                      >
                        <div className="py-2">
                          {item.submenu.map((subItem) => (
                            <Link
                              key={subItem.href}
                              href={subItem.href}
                              className="block px-4 py-3 text-sm text-muted-foreground hover:text-primary-700 hover:bg-primary-50 dark:hover:bg-primary-900/20 transition-all duration-200 first:rounded-t-2xl last:rounded-b-2xl"
                              onClick={() => setOpenDropdown(null)}
                            >
                              {subItem.label}
                            </Link>
                          ))}
                        </div>
                      </div>
                    )
                  }
                  return null
                })}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Mobile Navigation */}
      <AnimatePresence>
        {isMenuOpen && (
          <motion.div
            className="fixed top-32 left-0 right-0 z-30 px-4 sm:px-6 lg:px-8"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3, ease: 'easeInOut' }}
          >
            <motion.div
              className="max-w-6xl mx-auto lg:hidden bg-background/95 backdrop-blur-md border border-border/50 rounded-2xl shadow-xl"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="px-6 py-6 space-y-4">
                {/* Navigation Links */}
                <div className="space-y-1">
                  {navItems.map((item, index) => (
                    <motion.div
                      key={item.href}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.1 }}
                    >
                      {item.submenu ? (
                        <div className="space-y-1">
                          <div className="px-4 py-2 text-sm font-semibold text-primary-700 dark:text-primary-400 uppercase tracking-wide">
                            {item.label}
                          </div>
                          {item.submenu.map((subItem) => (
                            <Link
                              key={subItem.href}
                              href={subItem.href}
                              className="block px-6 py-2 text-muted-foreground hover:text-primary-700 hover:bg-primary-50 dark:hover:bg-primary-900/20 rounded-xl transition-all duration-300 font-medium text-sm"
                              onClick={() => setIsMenuOpen(false)}
                            >
                              {subItem.label}
                            </Link>
                          ))}
                        </div>
                      ) : (
                        <Link
                          href={item.href}
                          className="block px-4 py-3 text-muted-foreground hover:text-primary-700 hover:bg-primary-50 dark:hover:bg-primary-900/20 rounded-xl transition-all duration-300 font-medium"
                          onClick={() => setIsMenuOpen(false)}
                        >
                          {item.label}
                        </Link>
                      )}
                    </motion.div>
                  ))}
                </div>

                {/* Mobile Login & CTA */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: 0.4 }}
                  className="pt-4 border-t border-border space-y-3"
                >
                  {/* Enhanced Mobile Login Button */}
                  <Link
                    href="https://app.genlogic.io"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="block w-full text-center px-4 py-3 text-primary-700 font-semibold bg-gradient-to-r from-primary-50 to-primary-100 dark:from-primary-900/30 dark:to-primary-800/30 dark:text-primary-300 border border-primary-200 dark:border-primary-700 rounded-xl hover:from-primary-100 hover:to-primary-200 dark:hover:from-primary-800/40 dark:hover:to-primary-700/40 hover:border-primary-300 dark:hover:border-primary-600 hover:text-primary-800 dark:hover:text-primary-200 transition-all duration-300 shadow-sm hover:shadow-md hover:shadow-primary-500/20"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Login to App
                  </Link>

                  {/* Mobile CTA Button */}
                  <CustomButton
                    variant="primary"
                    size="lg"
                    href="/demo"
                    icon={<ArrowRight className="w-4 h-4" />}
                    iconPosition="right"
                    className="w-full justify-center"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Book Free Demo
                  </CustomButton>
                </motion.div>

                {/* Mobile Trust Indicators */}
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.3, delay: 0.5 }}
                  className="pt-3 text-center"
                >
                  <div className="flex items-center justify-center space-x-4 text-xs text-muted-foreground">
                    <span className="flex items-center">
                      <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                      500+ UK businesses
                    </span>
                    <span className="flex items-center">
                      <div className="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                      14-day free trial
                    </span>
                  </div>
                </motion.div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
