'use client'

import Link from 'next/link'
import { motion } from 'framer-motion'
import {
  Mail,
  Phone,
  MapPin,
  Clock,
  Star,
  Shield,
  Users,
  ArrowRight,
  Sparkles,
  Linkedin,
  Facebook,
  Instagram
} from 'lucide-react'

export default function Footer() {
  const currentYear = new Date().getFullYear()

  return (
    <footer className="bg-gray-50 dark:bg-gray-900 border-t border-border transition-colors duration-300">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Main Footer Content */}
        <div className="py-16">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-12">
            {/* Company Info */}
            <div className="lg:col-span-2">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
              >
                <Link href="/" className="flex items-center mb-6 group w-fit">
                  <div className="relative">
                    <div className="w-11 h-11 bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800 rounded-xl flex items-center justify-center mr-4 group-hover:shadow-lg group-hover:shadow-primary-500/25 transition-all duration-300">
                      <span className="text-white font-bold text-lg">G</span>
                      <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </div>
                    {/* Sparkle effect */}
                    <motion.div
                      className="absolute -top-0.5 -right-0.5"
                      animate={{
                        rotate: [0, 180, 360],
                        scale: [1, 1.2, 1]
                      }}
                      transition={{
                        duration: 3,
                        repeat: Infinity,
                        ease: "easeInOut"
                      }}
                    >
                      <Sparkles className="w-3.5 h-3.5 text-accent-500 opacity-60" />
                    </motion.div>
                  </div>
                  <div>
                    <span className="text-2xl font-bold text-foreground group-hover:text-primary-600 transition-colors duration-300">GenLogic</span>
                    <div className="text-sm text-green-600 font-medium">Customer Conversion</div>
                  </div>
                </Link>

                <p className="text-lg text-muted-foreground mb-6 max-w-md leading-relaxed">
                  Helping UK businesses convert 89% of prospects into customers through smart follow-up automation.
                  Turn more leads into revenue.
                </p>

                <div className="space-y-3 mb-8">
                  <div className="flex items-center text-muted-foreground">
                    <MapPin className="w-5 h-5 mr-3 text-primary-600" />
                    <span>Based in London, United Kingdom</span>
                  </div>
                  <div className="flex items-center text-muted-foreground">
                    <Users className="w-5 h-5 mr-3 text-primary-600" />
                    <span>Trusted by 500+ UK businesses</span>
                  </div>
                  <div className="flex items-center text-muted-foreground">
                    <Shield className="w-5 h-5 mr-3 text-primary-600" />
                    <span>GDPR compliant & secure</span>
                  </div>
                </div>

                {/* Social Proof */}
                <div className="bg-primary-50 dark:bg-primary-900/20 rounded-xl p-4 border border-primary-200 dark:border-primary-800">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="flex items-center mb-1">
                        {[...Array(5)].map((_, i) => (
                          <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                        ))}
                      </div>
                      <p className="text-sm text-primary-800 dark:text-primary-300 font-medium">
                        Rated 5/5 by local business owners
                      </p>
                    </div>
                    <div className="text-2xl font-bold text-primary-700 dark:text-primary-400">
                      500+
                    </div>
                  </div>
                </div>
              </motion.div>
            </div>

            {/* Product Links */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
            >
              <h3 className="text-sm font-semibold text-foreground uppercase tracking-wider mb-6">
                Product
              </h3>
              <ul className="space-y-4">
                <li>
                  <Link href="/pricing" className="text-muted-foreground hover:text-primary-600 transition-colors flex items-center group">
                    <span>Pricing</span>
                    <ArrowRight className="w-4 h-4 ml-1 opacity-0 group-hover:opacity-100 transition-opacity" />
                  </Link>
                </li>
                <li>
                  <Link href="/demo" className="text-muted-foreground hover:text-primary-600 transition-colors flex items-center group">
                    <span>Book Demo</span>
                    <ArrowRight className="w-4 h-4 ml-1 opacity-0 group-hover:opacity-100 transition-opacity" />
                  </Link>
                </li>
                <li>
                  <Link href="/about" className="text-muted-foreground hover:text-primary-600 transition-colors flex items-center group">
                    <span>About Us</span>
                    <ArrowRight className="w-4 h-4 ml-1 opacity-0 group-hover:opacity-100 transition-opacity" />
                  </Link>
                </li>
              </ul>

              {/* Contact Info */}
              <div className="mt-8">
                <h4 className="text-sm font-semibold text-foreground mb-4">Get in Touch</h4>
                <div className="space-y-3">
                  <div className="flex items-center text-muted-foreground">
                    <Mail className="w-4 h-4 mr-3 text-primary-600" />
                    <a href="mailto:<EMAIL>" className="hover:text-primary-600 transition-colors">
                      <EMAIL>
                    </a>
                  </div>
                  <div className="flex items-center text-muted-foreground">
                    <Phone className="w-4 h-4 mr-3 text-primary-600" />
                    <a href="tel:+447401137621" className="hover:text-primary-600 transition-colors">
                      +44 7401 137621
                    </a>
                  </div>
                  <div className="flex items-center text-muted-foreground">
                    <Clock className="w-4 h-4 mr-3 text-primary-600" />
                    <span>Mon-Fri, 9am-6pm GMT</span>
                  </div>
                </div>

                {/* Social Media Links */}
                <div className="mt-8">
                  <h4 className="text-sm font-semibold text-foreground mb-4">Follow Us</h4>
                  <div className="flex space-x-4">
                    <a
                      href="https://www.linkedin.com/company/genlogicio"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="group bg-primary-50 dark:bg-primary-900/20 hover:bg-primary-100 dark:hover:bg-primary-900/40 p-3 rounded-xl transition-all duration-300 border border-primary-200 dark:border-primary-800 hover:border-primary-300 dark:hover:border-primary-700"
                      aria-label="Follow GenLogic on LinkedIn"
                    >
                      <Linkedin className="w-5 h-5 text-primary-600 dark:text-primary-400 group-hover:text-primary-700 dark:group-hover:text-primary-300 transition-colors duration-300" />
                    </a>
                    <a
                      href="https://www.facebook.com/genlogic"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="group bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/40 p-3 rounded-xl transition-all duration-300 border border-blue-200 dark:border-blue-800 hover:border-blue-300 dark:hover:border-blue-700"
                      aria-label="Follow GenLogic on Facebook"
                    >
                      <Facebook className="w-5 h-5 text-blue-600 dark:text-blue-400 group-hover:text-blue-700 dark:group-hover:text-blue-300 transition-colors duration-300" />
                    </a>
                    <a
                      href="https://www.instagram.com/igenlogic"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="group bg-pink-50 dark:bg-pink-900/20 hover:bg-pink-100 dark:hover:bg-pink-900/40 p-3 rounded-xl transition-all duration-300 border border-pink-200 dark:border-pink-800 hover:border-pink-300 dark:hover:border-pink-700"
                      aria-label="Follow GenLogic on Instagram"
                    >
                      <Instagram className="w-5 h-5 text-pink-600 dark:text-pink-400 group-hover:text-pink-700 dark:group-hover:text-pink-300 transition-colors duration-300" />
                    </a>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Legal & Support */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <h3 className="text-sm font-semibold text-foreground uppercase tracking-wider mb-6">
                Legal & Support
              </h3>
              <ul className="space-y-4">
                <li>
                  <Link href="/privacy" className="text-muted-foreground hover:text-primary-600 transition-colors flex items-center group">
                    <span>Privacy Policy</span>
                    <ArrowRight className="w-4 h-4 ml-1 opacity-0 group-hover:opacity-100 transition-opacity" />
                  </Link>
                </li>
                <li>
                  <Link href="/terms" className="text-muted-foreground hover:text-primary-600 transition-colors flex items-center group">
                    <span>Terms of Service</span>
                    <ArrowRight className="w-4 h-4 ml-1 opacity-0 group-hover:opacity-100 transition-opacity" />
                  </Link>
                </li>
                <li>
                  <Link href="/contact" className="text-muted-foreground hover:text-primary-600 transition-colors flex items-center group">
                    <span>Contact Support</span>
                    <ArrowRight className="w-4 h-4 ml-1 opacity-0 group-hover:opacity-100 transition-opacity" />
                  </Link>
                </li>
              </ul>

              {/* Business Types */}
              <div className="mt-8">
                <h4 className="text-sm font-semibold text-foreground mb-4">Perfect For</h4>
                <div className="space-y-2 text-sm text-muted-foreground">
                  <div>Hair Salons & Beauty</div>
                  <div>Fitness Studios & Gyms</div>
                  <div>Healthcare & Dental</div>
                  <div>Professional Services</div>
                  <div>And many more...</div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="pt-8 border-t border-border">
          <motion.div
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            viewport={{ once: true }}
            className="flex flex-col lg:flex-row justify-between items-center gap-6"
          >
            <div className="flex flex-col lg:flex-row items-center gap-4 lg:gap-6 text-sm text-muted-foreground">
              <p className="text-center lg:text-left">© {currentYear} WEBBEC LTD. All rights reserved.</p>
              <div className="flex flex-col sm:flex-row items-center gap-2 sm:gap-4 text-center">
                <span>Company Registration: England & Wales</span>
                <span className="hidden sm:inline">•</span>
                <span>N: 14914722</span>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row items-center gap-4 text-sm text-muted-foreground">
              <div className="flex items-center">
                <Shield className="w-4 h-4 mr-2 text-success-500" />
                <span>GDPR Compliant</span>
              </div>
              <span className="hidden sm:inline">•</span>
              <div className="flex items-center">
                <Star className="w-4 h-4 mr-2 text-yellow-400" />
                <span>5-Star Rated</span>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Final CTA Strip */}
        <div className="mt-12 pt-8 border-t border-border">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            viewport={{ once: true }}
            className="bg-gradient-to-r from-primary-50 to-accent-50 dark:from-primary-900/20 dark:to-accent-900/20 rounded-2xl p-8 text-center border border-primary-200 dark:border-primary-800"
          >
            <h3 className="text-xl font-bold text-foreground mb-2">
              Ready to reclaim your evenings?
            </h3>
            <p className="text-muted-foreground mb-6">
              Join 500+ UK businesses saving 30+ hours per week with GenLogic
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/demo"
                className="bg-primary-700 hover:bg-primary-800 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl inline-flex items-center justify-center"
              >
                Book Free Demo
                <ArrowRight className="w-4 h-4 ml-2" />
              </Link>
              <Link
                href="/pricing"
                className="bg-background hover:bg-muted text-primary-700 border-2 border-primary-200 hover:border-primary-300 font-semibold py-3 px-6 rounded-xl transition-all duration-300 inline-flex items-center justify-center dark:bg-gray-800 dark:hover:bg-gray-700 dark:text-primary-400 dark:border-primary-700 dark:hover:border-primary-600"
              >
                View Pricing
              </Link>
            </div>
          </motion.div>
        </div>
      </div>
    </footer>
  )
}
