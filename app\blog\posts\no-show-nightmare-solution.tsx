import React from 'react'
import { BlogPost } from '../types'

const BlogContent = () => {
  return (
    <div className="max-w-4xl mx-auto space-y-12">

      {/* Opening Hook */}
      <div className="bg-gradient-to-br from-red-50 to-orange-50 dark:from-red-950/20 dark:to-orange-950/20 rounded-3xl p-8 border border-red-200/50 dark:border-red-800/30">
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6 text-center">
          Meet <PERSON>, owner of The Crown Restaurant in Manchester
        </h2>
        <p className="text-lg text-gray-700 dark:text-gray-300 mb-4 text-center">
          Six months ago, he was losing £15,000 monthly to no-shows. Today, his restaurant runs at 95% capacity with virtually zero no-shows.
        </p>
        <p className="text-lg text-gray-700 dark:text-gray-300 text-center font-medium">
          <strong>Here's exactly how he did it.</strong>
        </p>
      </div>

      {/* Problem Section */}
      <div>
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
          The Problem: Bleeding Money Every Night
        </h2>
        <p className="text-lg text-gray-700 dark:text-gray-300 mb-6">
          David's restaurant was bleeding money. Every night, 40% of his bookings simply didn't show up. Empty tables meant wasted food, disappointed staff, and mounting financial pressure.
        </p>
      </div>

      {/* Breaking Point */}
      <div>
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
          The Breaking Point
        </h2>
        <p className="text-lg text-gray-700 dark:text-gray-300 mb-8">
          It was a Saturday night in February when David hit rock bottom. He'd prepared for 80 covers, but only 45 customers showed up. The kitchen had prepped £800 worth of fresh ingredients that would go to waste.
        </p>

        <div className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 rounded-2xl p-6 border border-blue-200/50 dark:border-blue-800/30">
          <blockquote className="text-xl italic text-gray-800 dark:text-gray-200 mb-4">
            "I was standing in my empty restaurant, watching my staff clean tables that never got used. I knew something had to change, or I'd lose everything I'd worked for."
          </blockquote>
          <cite className="text-blue-600 dark:text-blue-400 font-semibold">David Thompson, The Crown Restaurant</cite>
        </div>
      </div>

      {/* Solution */}
      <div>
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
          The Solution: Smart Automation
        </h2>
        <p className="text-lg text-gray-700 dark:text-gray-300 mb-8">
          David implemented our three-tier no-show prevention system:
        </p>

        <div className="grid md:grid-cols-3 gap-6">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 text-center hover:shadow-lg transition-shadow">
            <div className="w-12 h-12 bg-blue-600 text-white rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4">1</div>
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3">Confirmation System</h3>
            <p className="text-gray-600 dark:text-gray-400">Automated SMS confirmations 24 hours before booking with easy yes/no response</p>
          </div>
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 text-center hover:shadow-lg transition-shadow">
            <div className="w-12 h-12 bg-blue-600 text-white rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4">2</div>
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3">Reminder Sequence</h3>
            <p className="text-gray-600 dark:text-gray-400">Follow-up reminders 2 hours before the booking with directions and contact info</p>
          </div>
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 text-center hover:shadow-lg transition-shadow">
            <div className="w-12 h-12 bg-blue-600 text-white rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4">3</div>
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3">Waitlist Management</h3>
            <p className="text-gray-600 dark:text-gray-400">Automatic waitlist notifications when cancellations occur</p>
          </div>
        </div>
      </div>

      {/* Results */}
      <div>
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
          The Results: 6-Month Transformation
        </h2>

        <div className="grid md:grid-cols-2 gap-6 mb-8">
          <div className="bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800/30 rounded-2xl p-6">
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-4">Before Automation</h3>
            <ul className="space-y-2">
              <li className="flex items-center text-gray-700 dark:text-gray-300">
                <span className="w-2 h-2 bg-red-500 rounded-full mr-3"></span>
                40% no-show rate
              </li>
              <li className="flex items-center text-gray-700 dark:text-gray-300">
                <span className="w-2 h-2 bg-red-500 rounded-full mr-3"></span>
                £15,000 monthly losses
              </li>
              <li className="flex items-center text-gray-700 dark:text-gray-300">
                <span className="w-2 h-2 bg-red-500 rounded-full mr-3"></span>
                Stressed staff and wasted food
              </li>
              <li className="flex items-center text-gray-700 dark:text-gray-300">
                <span className="w-2 h-2 bg-red-500 rounded-full mr-3"></span>
                Unpredictable revenue
              </li>
            </ul>
          </div>

          <div className="bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800/30 rounded-2xl p-6">
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-4">After Automation</h3>
            <ul className="space-y-2">
              <li className="flex items-center text-gray-700 dark:text-gray-300">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                5% no-show rate
              </li>
              <li className="flex items-center text-gray-700 dark:text-gray-300">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                £15,000 additional revenue
              </li>
              <li className="flex items-center text-gray-700 dark:text-gray-300">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                Happy staff and minimal waste
              </li>
              <li className="flex items-center text-gray-700 dark:text-gray-300">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                Predictable, growing business
              </li>
            </ul>
          </div>
        </div>

        <div className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-950/20 dark:to-emerald-950/20 rounded-3xl p-8 border border-green-200/50 dark:border-green-800/30 text-center">
          <div className="text-6xl font-black text-green-600 dark:text-green-400 mb-2">£30,000</div>
          <div className="text-xl font-semibold text-gray-700 dark:text-gray-300">monthly revenue swing - from losing £15k to gaining £15k</div>
        </div>
      </div>

      {/* Implementation */}
      <div>
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
          How It Works: The Technical Details
        </h2>

        <div className="grid md:grid-cols-2 gap-6">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700">
            <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold mb-4">1</div>
            <h3 className="text-lg font-bold text-gray-900 dark:text-gray-100 mb-3">Setup Confirmation System</h3>
            <p className="text-gray-600 dark:text-gray-400">Automated SMS sent 24 hours before booking with simple yes/no response options and automatic cancellation handling.</p>
          </div>
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700">
            <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold mb-4">2</div>
            <h3 className="text-lg font-bold text-gray-900 dark:text-gray-100 mb-3">Add Reminder Sequence</h3>
            <p className="text-gray-600 dark:text-gray-400">2-hour advance reminder with directions, contact information, and weather updates for outdoor seating.</p>
          </div>
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700">
            <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold mb-4">3</div>
            <h3 className="text-lg font-bold text-gray-900 dark:text-gray-100 mb-3">Implement Waitlist</h3>
            <p className="text-gray-600 dark:text-gray-400">Automatic notifications when spots open, priority booking for regular customers, and revenue recovery from last-minute cancellations.</p>
          </div>
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700">
            <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold mb-4">4</div>
            <h3 className="text-lg font-bold text-gray-900 dark:text-gray-100 mb-3">Optimize and Scale</h3>
            <p className="text-gray-600 dark:text-gray-400">A/B test message timing, personalize messages by customer type, and add special offers for confirmed bookings.</p>
          </div>
        </div>
      </div>

      {/* Progress Numbers */}
      <div>
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
          The Numbers Don't Lie
        </h2>

        <div className="grid md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 text-center">
            <h3 className="text-lg font-bold text-gray-900 dark:text-gray-100 mb-4">Month 1</h3>
            <div className="text-red-600 dark:text-red-400 mb-2">40% no-shows</div>
            <div className="text-2xl font-bold text-gray-600 dark:text-gray-400 mb-2">→</div>
            <div className="text-green-600 dark:text-green-400">25% no-shows</div>
          </div>
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 text-center">
            <h3 className="text-lg font-bold text-gray-900 dark:text-gray-100 mb-4">Month 2</h3>
            <div className="text-red-600 dark:text-red-400 mb-2">25% no-shows</div>
            <div className="text-2xl font-bold text-gray-600 dark:text-gray-400 mb-2">→</div>
            <div className="text-green-600 dark:text-green-400">15% no-shows</div>
          </div>
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 text-center">
            <h3 className="text-lg font-bold text-gray-900 dark:text-gray-100 mb-4">Month 3</h3>
            <div className="text-red-600 dark:text-red-400 mb-2">15% no-shows</div>
            <div className="text-2xl font-bold text-gray-600 dark:text-gray-400 mb-2">→</div>
            <div className="text-green-600 dark:text-green-400">5% no-shows (industry-leading)</div>
          </div>
        </div>
      </div>

      {/* Testimonial */}
      <div>
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
          What David Says Now
        </h2>

        <div className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 rounded-2xl p-6 border border-blue-200/50 dark:border-blue-800/30">
          <blockquote className="text-xl italic text-gray-800 dark:text-gray-200 mb-4">
            "The transformation has been incredible. We went from dreading Saturday nights to looking forward to them. Our staff are happier, our food waste is minimal, and we're actually making money instead of losing it."
          </blockquote>
          <cite className="text-blue-600 dark:text-blue-400 font-semibold">David Thompson, The Crown Restaurant</cite>
        </div>
      </div>

      {/* Final CTA */}
      <div>
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
          Your Restaurant's Transformation Starts Here
        </h2>
        <p className="text-lg text-gray-700 dark:text-gray-300 mb-8">
          David's story isn't unique. Across the UK, restaurants are discovering that the right automation doesn't just prevent no-shows - it transforms the entire business.
        </p>

        <div className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-950/20 dark:to-emerald-950/20 rounded-2xl p-6 border border-green-200/50 dark:border-green-800/30 mb-8">
          <p className="text-lg text-gray-700 dark:text-gray-300">
            The same system that saved David £15,000 monthly is available to implement in your restaurant. Most restaurants see results within the first week.
          </p>
        </div>

        <div className="text-center">
          <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
            The question isn't whether this will work for your restaurant.
          </h3>
          <h3 className="text-2xl font-bold text-blue-600 dark:text-blue-400">
            The question is: How much longer can you afford to lose money to no-shows?
          </h3>
        </div>
      </div>

    </div>
  )
}

export const noShowNightmarePost: BlogPost = {
  id: '2',
  slug: 'no-show-nightmare-solution',
  title: 'The No-Show Nightmare: How One Restaurant Saved £15,000 in Lost Revenue',
  excerpt: 'Meet David from Manchester who went from losing 40% of bookings to no-shows to having a 95% attendance rate. Here\'s exactly what he did.',
  content: <BlogContent />,
  author: {
    name: 'Chedi Rach',
    role: 'Business Automation Specialist',
    avatar: '/logo/genlogic-icon-only.svg',
    bio: 'Chedi works directly with UK restaurants and service businesses to eliminate no-shows.'
  },
  publishedAt: '2025-07-08',
  readingTime: '6 min read',
  category: 'Case Studies',
  tags: ['no-shows', 'restaurants', 'revenue'],
  featured: true,
  image: '/blog-images/no-show-nightmare-solution.webp',
  views: 1923,
  likes: 89
}
