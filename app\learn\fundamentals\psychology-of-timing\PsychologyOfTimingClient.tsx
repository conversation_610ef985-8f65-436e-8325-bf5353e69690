'use client'

import { useEffect } from 'react'
import { motion } from 'framer-motion'
import {
  ArrowLeft,
  Clock,
  TrendingUp,
  CheckCircle,
  ArrowRight,
  Brain,
  Target,
  Zap,
  Calendar,
  Timer,
  BarChart3,
  AlertCircle,
  Lightbulb,
  Users,
  Eye,
  Star,
  Calculator
} from 'lucide-react'
import Link from 'next/link'

const timingInsights = [
  {
    touchpoint: "1st-2nd Touch",
    percentage: "2%",
    psychology: "Information Gathering",
    description: "Prospects are just becoming aware of their problem",
    color: "red",
    timing: "Immediate - 24 hours"
  },
  {
    touchpoint: "3rd-4th Touch", 
    percentage: "25%",
    psychology: "Solution Exploration",
    description: "Actively researching options and comparing providers",
    color: "yellow",
    timing: "3-7 days"
  },
  {
    touchpoint: "5th-12th Touch",
    percentage: "73%",
    psychology: "Decision Window",
    description: "Ready to buy but need the right trigger moment",
    color: "green",
    timing: "2-8 weeks"
  }
]

export default function PsychologyOfTimingClient() {
  // Mark lesson as completed when user visits
  useEffect(() => {
    const markAsCompleted = () => {
      const saved = localStorage.getItem('genlogic-academy-progress')
      const completed = saved ? JSON.parse(saved) : []
      if (!completed.includes('psychology-of-timing')) {
        completed.push('psychology-of-timing')
        localStorage.setItem('genlogic-academy-progress', JSON.stringify(completed))
        
        // Dispatch custom event to notify other components
        window.dispatchEvent(new CustomEvent('academyProgressUpdate'))
      }
    }
    
    // Mark as completed after user has been on page for 30 seconds
    const timer = setTimeout(markAsCompleted, 30000)
    return () => clearTimeout(timer)
  }, [])

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-blue-50/30 to-background dark:from-background dark:via-blue-950/30 dark:to-background">
      {/* Navigation */}
      <div className="pt-40 pb-8">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-5xl mx-auto">
            <Link 
              href="/learn" 
              className="inline-flex items-center gap-2 bg-background/80 backdrop-blur-sm border border-border/50 rounded-2xl px-4 py-2 text-primary-600 hover:text-primary-700 font-medium transition-all duration-300 hover:shadow-md"
            >
              <ArrowLeft className="w-4 h-4" />
              Back to Academy
            </Link>
          </div>
        </div>
      </div>

      {/* Hero Section */}
      <section className="pb-16">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-5xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              {/* Lesson Meta */}
              <div className="flex items-center justify-center gap-6 mb-8">
                <div className="flex items-center gap-2 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 px-4 py-2 rounded-2xl">
                  <Timer className="w-4 h-4" />
                  <span className="font-medium text-sm">Timing Psychology</span>
                </div>
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Clock className="w-4 h-4" />
                  <span className="text-sm">16 min read</span>
                </div>
                <div className="bg-muted px-3 py-1 rounded-full text-xs font-medium text-muted-foreground">
                  Advanced Communication
                </div>
              </div>

              {/* Main Title */}
              <h1 className="text-4xl md:text-6xl font-bold text-foreground mb-6 leading-tight">
                The Psychology of 
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-500 to-purple-500"> Timing:</span>
                <br />When Prospects 
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-green-500 to-blue-500"> Actually Buy</span>
              </h1>

              <p className="text-xl md:text-2xl text-muted-foreground mb-8 leading-relaxed max-w-4xl mx-auto">
                Something shocking happens between the 5th and 12th touchpoint that most businesses never discover. 
                <strong className="text-foreground"> While you're giving up after 3 attempts, 73% of sales are waiting to happen.</strong>
                Here's the timing psychology that changes everything.
              </p>

              {/* Stats Row */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
                <div className="bg-gradient-to-br from-red-50 to-red-100 dark:from-red-950/30 dark:to-red-900/30 rounded-3xl p-6 border border-red-200/50 dark:border-red-800/50">
                  <div className="text-3xl font-bold text-red-600 dark:text-red-400 mb-2">2%</div>
                  <div className="text-sm text-red-700 dark:text-red-300 font-medium">Buy in first 2 touches</div>
                </div>
                <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 dark:from-yellow-950/30 dark:to-yellow-900/30 rounded-3xl p-6 border border-yellow-200/50 dark:border-yellow-800/50">
                  <div className="text-3xl font-bold text-yellow-600 dark:text-yellow-400 mb-2">25%</div>
                  <div className="text-sm text-yellow-700 dark:text-yellow-300 font-medium">Buy in touches 3-4</div>
                </div>
                <div className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950/30 dark:to-green-900/30 rounded-3xl p-6 border border-green-200/50 dark:border-green-800/50">
                  <div className="text-3xl font-bold text-green-600 dark:text-green-400 mb-2">73%</div>
                  <div className="text-sm text-green-700 dark:text-green-300 font-medium">Buy in touches 5-12</div>
                </div>
              </div>
            </motion.div>

            {/* What You'll Learn Card */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-950/50 dark:to-purple-950/50 rounded-3xl p-8 border border-blue-200/50 dark:border-blue-800/50 shadow-xl"
            >
              <div className="flex items-start gap-6">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-500 rounded-2xl flex items-center justify-center flex-shrink-0">
                  <Brain className="w-8 h-8 text-white" />
                </div>
                <div className="flex-1">
                  <h3 className="text-2xl font-bold text-foreground mb-4">What You'll Master in This Lesson</h3>
                  <div className="grid md:grid-cols-2 gap-4">
                    <div className="space-y-3">
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-foreground">Why 73% of sales happen on touches 5-12</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-foreground">The "buying window" psychology most miss</span>
                      </div>
                    </div>
                    <div className="space-y-3">
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-foreground">Real timing strategies from high-converters</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-foreground">How to automate perfect timing</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="pb-16">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-4xl mx-auto space-y-12">

            {/* The Hidden Pattern */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="bg-background/80 backdrop-blur-sm rounded-3xl p-8 border border-border/50 shadow-xl"
            >
              <div className="flex items-start gap-6 mb-8">
                <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-orange-500 rounded-xl flex items-center justify-center flex-shrink-0">
                  <AlertCircle className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h2 className="text-3xl font-bold text-foreground mb-4">The Hidden Pattern Most Businesses Miss</h2>
                  <p className="text-lg text-muted-foreground leading-relaxed">
                    Something's happening across UK businesses that's creating a massive opportunity gap. While most companies give up after 2-3 follow-ups,
                    successful businesses have discovered that <strong className="text-foreground">73% of their sales happen between the 5th and 12th touchpoint.</strong>
                  </p>
                </div>
              </div>

              <div className="bg-gradient-to-r from-red-50 to-orange-50 dark:from-red-950/30 dark:to-orange-950/30 rounded-2xl p-6 border border-red-200/50 dark:border-red-800/50">
                <h3 className="text-xl font-bold text-red-700 dark:text-red-300 mb-3">The Shocking Reality</h3>
                <div className="space-y-3 text-red-600 dark:text-red-400">
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                    <span><strong>48% of salespeople</strong> never follow up with a prospect</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                    <span><strong>25% make a second contact</strong> and stop</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                    <span><strong>12% make three contacts</strong> and give up</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span><strong>Only 10% make more than three contacts</strong> - and they get 80% of the sales</span>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* The Buying Window Psychology */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="bg-background/80 backdrop-blur-sm rounded-3xl p-8 border border-border/50 shadow-xl"
            >
              <div className="flex items-start gap-6 mb-8">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-xl flex items-center justify-center flex-shrink-0">
                  <Brain className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h2 className="text-3xl font-bold text-foreground mb-4">The Buying Window Psychology</h2>
                  <p className="text-lg text-muted-foreground leading-relaxed">
                    Here's what successful business owners understand about prospect psychology that changes everything about timing.
                  </p>
                </div>
              </div>

              <div className="space-y-6">
                {timingInsights.map((insight, index) => (
                  <div key={index} className={`bg-gradient-to-r ${
                    insight.color === 'red' ? 'from-red-50 to-red-100 dark:from-red-950/30 dark:to-red-900/30' :
                    insight.color === 'yellow' ? 'from-yellow-50 to-yellow-100 dark:from-yellow-950/30 dark:to-yellow-900/30' :
                    'from-green-50 to-green-100 dark:from-green-950/30 dark:to-green-900/30'
                  } rounded-2xl p-6 border ${
                    insight.color === 'red' ? 'border-red-200/50 dark:border-red-800/50' :
                    insight.color === 'yellow' ? 'border-yellow-200/50 dark:border-yellow-800/50' :
                    'border-green-200/50 dark:border-green-800/50'
                  }`}>
                    <div className="flex items-start gap-4">
                      <div className={`text-4xl font-bold ${
                        insight.color === 'red' ? 'text-red-600 dark:text-red-400' :
                        insight.color === 'yellow' ? 'text-yellow-600 dark:text-yellow-400' :
                        'text-green-600 dark:text-green-400'
                      }`}>
                        {insight.percentage}
                      </div>
                      <div className="flex-1">
                        <h3 className={`text-xl font-bold mb-2 ${
                          insight.color === 'red' ? 'text-red-700 dark:text-red-300' :
                          insight.color === 'yellow' ? 'text-yellow-700 dark:text-yellow-300' :
                          'text-green-700 dark:text-green-300'
                        }`}>
                          {insight.touchpoint}: {insight.psychology}
                        </h3>
                        <p className={`text-sm mb-2 ${
                          insight.color === 'red' ? 'text-red-600 dark:text-red-400' :
                          insight.color === 'yellow' ? 'text-yellow-600 dark:text-yellow-400' :
                          'text-green-600 dark:text-green-400'
                        }`}>
                          {insight.description}
                        </p>
                        <div className={`text-xs font-medium ${
                          insight.color === 'red' ? 'text-red-500 dark:text-red-400' :
                          insight.color === 'yellow' ? 'text-yellow-500 dark:text-yellow-400' :
                          'text-green-500 dark:text-green-400'
                        }`}>
                          Optimal Timing: {insight.timing}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>

            {/* Real Case Study: The £1.8M Timing Transformation */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              className="bg-background/80 backdrop-blur-sm rounded-3xl p-8 border border-border/50 shadow-xl"
            >
              <div className="flex items-start gap-6 mb-8">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-xl flex items-center justify-center flex-shrink-0">
                  <BarChart3 className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h2 className="text-3xl font-bold text-foreground mb-4">Case Study: How Perfect Timing Generated £1.8M</h2>
                  <p className="text-lg text-muted-foreground leading-relaxed">
                    Sarah owned a premium home renovation company in Leeds, struggling with inconsistent sales despite quality work. Here's how mastering timing psychology transformed her business.
                  </p>
                </div>
              </div>

              <div className="bg-gradient-to-r from-red-50 to-red-100 dark:from-red-950/30 dark:to-red-900/30 rounded-2xl p-6 border border-red-200/50 dark:border-red-800/50 mb-8">
                <h3 className="text-xl font-bold text-red-700 dark:text-red-300 mb-4">The Timing Disaster (Before)</h3>
                <div className="grid md:grid-cols-3 gap-4 mb-4">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-red-600 dark:text-red-400 mb-2">£420k</div>
                    <div className="text-sm text-red-700 dark:text-red-300">Annual revenue</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-red-600 dark:text-red-400 mb-2">8%</div>
                    <div className="text-sm text-red-700 dark:text-red-300">Conversion rate</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-red-600 dark:text-red-400 mb-2">67%</div>
                    <div className="text-sm text-red-700 dark:text-red-300">Prospects lost to timing</div>
                  </div>
                </div>
                <p className="text-red-600 dark:text-red-400 text-sm">
                  "I was following up too early, then giving up too soon. I'd contact prospects immediately after they inquired,
                  then maybe once more a week later. Most said 'not ready yet' and I never heard from them again."
                </p>
              </div>

              <div className="space-y-8">
                {/* The Timing Discovery */}
                <div className="bg-gradient-to-r from-yellow-50 to-yellow-100 dark:from-yellow-950/30 dark:to-yellow-900/30 rounded-2xl p-6 border border-yellow-200/50 dark:border-yellow-800/50">
                  <h4 className="text-lg font-bold text-yellow-700 dark:text-yellow-300 mb-4">The Timing Discovery</h4>

                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
                      <h5 className="font-bold text-yellow-600 dark:text-yellow-400 mb-3">What Sarah Learned</h5>
                      <ul className="space-y-2 text-sm text-yellow-600 dark:text-yellow-400">
                        <li>• Home renovation decisions take 3-8 weeks</li>
                        <li>• Prospects research 5-12 companies</li>
                        <li>• 73% buy between touches 5-12</li>
                        <li>• Timing beats price in 89% of cases</li>
                        <li>• Decision triggers are predictable</li>
                      </ul>
                    </div>

                    <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
                      <h5 className="font-bold text-yellow-600 dark:text-yellow-400 mb-3">The New Timing Strategy</h5>
                      <ul className="space-y-2 text-sm text-yellow-600 dark:text-yellow-400">
                        <li>• Touch 1-2: Educational content only</li>
                        <li>• Touch 3-4: Social proof and case studies</li>
                        <li>• Touch 5-8: Soft offers and consultations</li>
                        <li>• Touch 9-12: Urgency and scarcity triggers</li>
                        <li>• Touch 13+: Seasonal and event-based</li>
                      </ul>
                    </div>
                  </div>

                  <div className="bg-yellow-100 dark:bg-yellow-900/30 rounded-xl p-4 mt-6">
                    <h6 className="font-bold text-yellow-700 dark:text-yellow-300 mb-2">Key Breakthrough Moment:</h6>
                    <p className="text-sm text-yellow-600 dark:text-yellow-400">
                      "When I realized that 'not ready yet' doesn't mean 'not interested.' It means 'not ready yet.'
                      The timing of my follow-up was everything."
                    </p>
                  </div>
                </div>

                {/* The Implementation */}
                <div className="bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-950/30 dark:to-blue-900/30 rounded-2xl p-6 border border-blue-200/50 dark:border-blue-800/50">
                  <h4 className="text-lg font-bold text-blue-700 dark:text-blue-300 mb-4">The 12-Touch Timing System</h4>

                  <div className="space-y-4">
                    <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
                      <h5 className="font-bold text-blue-600 dark:text-blue-400 mb-3">Weeks 1-2: Education Phase</h5>
                      <div className="grid md:grid-cols-2 gap-4 text-sm">
                        <div>
                          <strong className="text-blue-600">Touch 1 (Day 1):</strong>
                          <span className="text-blue-600 dark:text-blue-400"> "5 Things to Consider Before Your Renovation"</span>
                        </div>
                        <div>
                          <strong className="text-blue-600">Touch 2 (Day 4):</strong>
                          <span className="text-blue-600 dark:text-blue-400"> "How to Avoid the 3 Costliest Renovation Mistakes"</span>
                        </div>
                      </div>
                    </div>

                    <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
                      <h5 className="font-bold text-blue-600 dark:text-blue-400 mb-3">Weeks 3-4: Social Proof Phase</h5>
                      <div className="grid md:grid-cols-2 gap-4 text-sm">
                        <div>
                          <strong className="text-blue-600">Touch 3 (Day 8):</strong>
                          <span className="text-blue-600 dark:text-blue-400"> Case study: "How the Johnsons Added £45k Value"</span>
                        </div>
                        <div>
                          <strong className="text-blue-600">Touch 4 (Day 12):</strong>
                          <span className="text-blue-600 dark:text-blue-400"> Video testimonials from recent clients</span>
                        </div>
                      </div>
                    </div>

                    <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
                      <h5 className="font-bold text-blue-600 dark:text-blue-400 mb-3">Weeks 5-8: Decision Phase</h5>
                      <div className="grid md:grid-cols-2 gap-4 text-sm">
                        <div>
                          <strong className="text-blue-600">Touch 5-8:</strong>
                          <span className="text-blue-600 dark:text-blue-400"> Free consultation offers, design previews, financing options</span>
                        </div>
                        <div>
                          <strong className="text-blue-600">Touch 9-12:</strong>
                          <span className="text-blue-600 dark:text-blue-400"> Seasonal promotions, limited availability, project showcases</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-blue-100 dark:bg-blue-900/30 rounded-xl p-4 mt-6">
                    <h6 className="font-bold text-blue-700 dark:text-blue-300 mb-2">12-Month Results:</h6>
                    <div className="grid md:grid-cols-3 gap-4 text-sm">
                      <div>
                        <div className="text-blue-600 dark:text-blue-400">✓ Revenue: £420k → £1.8M</div>
                        <div className="text-blue-600 dark:text-blue-400">✓ Conversion rate: 8% → 34%</div>
                      </div>
                      <div>
                        <div className="text-blue-600 dark:text-blue-400">✓ Average project value: +67%</div>
                        <div className="text-blue-600 dark:text-blue-400">✓ Customer lifetime value: +156%</div>
                      </div>
                      <div>
                        <div className="text-blue-600 dark:text-blue-400">✓ Referral rate: +89%</div>
                        <div className="text-blue-600 dark:text-blue-400">✓ Sales cycle: -45% shorter</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Interactive Timing Calculator */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
              className="bg-background/80 backdrop-blur-sm rounded-3xl p-8 border border-border/50 shadow-xl"
            >
              <div className="flex items-start gap-6 mb-8">
                <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-blue-500 rounded-xl flex items-center justify-center flex-shrink-0">
                  <Calculator className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h2 className="text-3xl font-bold text-foreground mb-4">Timing Psychology Calculator</h2>
                  <p className="text-lg text-muted-foreground leading-relaxed">
                    Calculate the optimal timing strategy for your industry and see the potential revenue impact.
                  </p>
                </div>
              </div>

              <div className="bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-950/30 dark:to-blue-950/30 rounded-2xl p-6 border border-green-200/50 dark:border-green-800/50">
                <h3 className="text-xl font-bold text-green-700 dark:text-green-300 mb-6">Interactive Timing ROI Calculator</h3>

                <div className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
                      <h4 className="font-bold text-green-600 dark:text-green-400 mb-3">Current Timing Strategy</h4>
                      <div className="space-y-3 text-sm">
                        <div className="flex items-center gap-2">
                          <label className="min-w-0 flex-1">Number of follow-ups:</label>
                          <input
                            type="number"
                            className="w-24 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-foreground"
                            placeholder="3"
                          />
                        </div>
                        <div className="flex items-center gap-2">
                          <label className="min-w-0 flex-1">Current conversion rate (%):</label>
                          <input
                            type="number"
                            className="w-24 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-foreground"
                            placeholder="8"
                          />
                        </div>
                        <div className="flex items-center gap-2">
                          <label className="min-w-0 flex-1">Average deal value (£):</label>
                          <input
                            type="number"
                            className="w-24 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-foreground"
                            placeholder="5000"
                          />
                        </div>
                        <div className="flex items-center gap-2">
                          <label className="min-w-0 flex-1">Monthly leads:</label>
                          <input
                            type="number"
                            className="w-24 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-foreground"
                            placeholder="50"
                          />
                        </div>
                      </div>
                    </div>

                    <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
                      <h4 className="font-bold text-green-600 dark:text-green-400 mb-3">Optimized Timing Strategy</h4>
                      <div className="space-y-3 text-sm">
                        <div className="flex items-center gap-2">
                          <label className="min-w-0 flex-1">Optimized follow-ups:</label>
                          <input
                            type="number"
                            className="w-24 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-foreground"
                            placeholder="12"
                          />
                        </div>
                        <div className="flex items-center gap-2">
                          <label className="min-w-0 flex-1">Projected conversion rate (%):</label>
                          <input
                            type="number"
                            className="w-24 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-foreground"
                            placeholder="28"
                          />
                        </div>
                        <div className="flex items-center gap-2">
                          <label className="min-w-0 flex-1">Timing optimization period:</label>
                          <select className="w-24 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-foreground">
                            <option>8 weeks</option>
                            <option>12 weeks</option>
                            <option>16 weeks</option>
                          </select>
                        </div>
                        <div className="flex items-center gap-2">
                          <label className="min-w-0 flex-1">Industry type:</label>
                          <select className="w-24 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-foreground">
                            <option>B2B</option>
                            <option>B2C</option>
                            <option>Service</option>
                          </select>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-blue-100 to-green-100 dark:from-blue-900/30 dark:to-green-900/30 rounded-xl p-6 text-center border-2 border-blue-300 dark:border-blue-700">
                    <h4 className="text-2xl font-bold text-blue-700 dark:text-blue-300 mb-3">Timing Optimization Impact</h4>
                    <div className="grid md:grid-cols-3 gap-4 mb-4">
                      <div>
                        <div className="text-lg font-bold text-green-600 dark:text-green-400">Additional Revenue</div>
                        <div className="text-2xl font-bold text-green-600 dark:text-green-400">£600,000</div>
                        <div className="text-xs text-green-500">Annual additional revenue</div>
                      </div>
                      <div>
                        <div className="text-lg font-bold text-blue-600 dark:text-blue-400">Conversion Increase</div>
                        <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">+250%</div>
                        <div className="text-xs text-blue-500">Conversion rate improvement</div>
                      </div>
                      <div>
                        <div className="text-lg font-bold text-purple-600 dark:text-purple-400">ROI Multiplier</div>
                        <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">3.5x</div>
                        <div className="text-xs text-purple-500">Return on timing investment</div>
                      </div>
                    </div>
                    <div className="border-t pt-4">
                      <div className="text-3xl font-bold text-green-600 dark:text-green-400 mb-2">Timing Psychology Impact</div>
                      <div className="text-5xl font-bold text-green-600 dark:text-green-400 mb-2">+350%</div>
                      <div className="text-lg font-bold text-green-600 dark:text-green-400">Revenue growth potential</div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Implementation Guide */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.7 }}
              className="bg-background/80 backdrop-blur-sm rounded-3xl p-8 border border-border/50 shadow-xl"
            >
              <div className="flex items-start gap-6 mb-8">
                <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl flex items-center justify-center flex-shrink-0">
                  <Lightbulb className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h2 className="text-3xl font-bold text-foreground mb-4">Your 30-Day Timing Transformation</h2>
                  <p className="text-lg text-muted-foreground leading-relaxed">
                    Implement the psychology of timing in your business with this proven 30-day roadmap.
                  </p>
                </div>
              </div>

              <div className="space-y-6">
                <div className="bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-950/30 dark:to-blue-900/30 rounded-2xl p-6 border border-blue-200/50 dark:border-blue-800/50">
                  <h3 className="text-xl font-bold text-blue-700 dark:text-blue-300 mb-4">Week 1: Timing Audit & Strategy</h3>
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-bold text-blue-600 dark:text-blue-400 mb-2">Days 1-3: Current State Analysis</h4>
                      <ul className="space-y-1 text-sm text-blue-600 dark:text-blue-400">
                        <li>• Analyze your current follow-up sequence</li>
                        <li>• Track when prospects actually convert</li>
                        <li>• Identify timing gaps in your process</li>
                        <li>• Map your customer decision journey</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-bold text-blue-600 dark:text-blue-400 mb-2">Days 4-7: Strategy Design</h4>
                      <ul className="space-y-1 text-sm text-blue-600 dark:text-blue-400">
                        <li>• Design your 12-touch sequence</li>
                        <li>• Create timing-based content themes</li>
                        <li>• Set up psychological trigger points</li>
                        <li>• Plan automation workflows</li>
                      </ul>
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-green-50 to-green-100 dark:from-green-950/30 dark:to-green-900/30 rounded-2xl p-6 border border-green-200/50 dark:border-green-800/50">
                  <h3 className="text-xl font-bold text-green-700 dark:text-green-300 mb-4">Week 2-3: Content Creation & Setup</h3>
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-bold text-green-600 dark:text-green-400 mb-2">Educational Phase Content</h4>
                      <ul className="space-y-1 text-sm text-green-600 dark:text-green-400">
                        <li>• Problem awareness content</li>
                        <li>• Industry insight reports</li>
                        <li>• Educational video series</li>
                        <li>• Helpful resource guides</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-bold text-green-600 dark:text-green-400 mb-2">Decision Phase Content</h4>
                      <ul className="space-y-1 text-sm text-green-600 dark:text-green-400">
                        <li>• Case studies and testimonials</li>
                        <li>• Social proof campaigns</li>
                        <li>• Consultation offers</li>
                        <li>• Urgency and scarcity triggers</li>
                      </ul>
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-purple-50 to-purple-100 dark:from-purple-950/30 dark:to-purple-900/30 rounded-2xl p-6 border border-purple-200/50 dark:border-purple-800/50">
                  <h3 className="text-xl font-bold text-purple-700 dark:text-purple-300 mb-4">Week 4: Launch & Optimization</h3>
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-bold text-purple-600 dark:text-purple-400 mb-2">Launch Strategy</h4>
                      <ul className="space-y-1 text-sm text-purple-600 dark:text-purple-400">
                        <li>• Implement timing sequences</li>
                        <li>• Test psychological triggers</li>
                        <li>• Monitor engagement patterns</li>
                        <li>• Track conversion timing</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-bold text-purple-600 dark:text-purple-400 mb-2">Optimization Process</h4>
                      <ul className="space-y-1 text-sm text-purple-600 dark:text-purple-400">
                        <li>• A/B test timing intervals</li>
                        <li>• Refine psychological triggers</li>
                        <li>• Optimize content for each phase</li>
                        <li>• Scale successful sequences</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-950/30 dark:to-blue-950/30 rounded-2xl p-6 border border-green-200/50 dark:border-green-800/50 mt-8">
                <h3 className="text-xl font-bold text-green-700 dark:text-green-300 mb-4">30-Day Timing Transformation Results</h3>
                <div className="grid md:grid-cols-3 gap-4 text-center">
                  <div>
                    <div className="text-3xl font-bold text-green-600 dark:text-green-400 mb-2">Week 1</div>
                    <div className="text-sm text-green-600 dark:text-green-400">Strategy & audit complete</div>
                  </div>
                  <div>
                    <div className="text-3xl font-bold text-green-600 dark:text-green-400 mb-2">Week 2-3</div>
                    <div className="text-sm text-green-600 dark:text-green-400">Content & sequences ready</div>
                  </div>
                  <div>
                    <div className="text-3xl font-bold text-green-600 dark:text-green-400 mb-2">Week 4</div>
                    <div className="text-sm text-green-600 dark:text-green-400">Live timing optimization</div>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Next Lesson Navigation */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.7 }}
              className="bg-background rounded-3xl border border-border shadow-lg p-8"
            >
              <h3 className="text-xl font-bold text-foreground mb-6 text-center">Continue Your Learning Journey</h3>

              <div className="grid md:grid-cols-2 gap-6">
                <Link
                  href="/learn/fundamentals/invisible-influence"
                  className="group bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950/30 dark:to-purple-900/30 rounded-2xl p-6 border border-purple-200/50 dark:border-purple-800/50 hover:shadow-lg transition-all duration-300"
                >
                  <div className="flex items-center gap-4 mb-4">
                    <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/50 rounded-xl flex items-center justify-center">
                      <ArrowRight className="w-6 h-6 text-purple-600 group-hover:translate-x-1 transition-transform" />
                    </div>
                    <div>
                      <h4 className="font-bold text-foreground">Next Lesson</h4>
                      <p className="text-sm text-muted-foreground">Lesson 7 of 15</p>
                    </div>
                  </div>
                  <h5 className="font-semibold text-foreground mb-2">The Invisible Influence</h5>
                  <p className="text-sm text-muted-foreground">
                    How words shape customer decisions and psychological triggers that make prospects say yes
                  </p>
                </Link>

                <Link
                  href="/learn"
                  className="group bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-950/30 dark:to-gray-900/30 rounded-2xl p-6 border border-gray-200/50 dark:border-gray-800/50 hover:shadow-lg transition-all duration-300"
                >
                  <div className="flex items-center gap-4 mb-4">
                    <div className="w-12 h-12 bg-gray-100 dark:bg-gray-900/50 rounded-xl flex items-center justify-center">
                      <ArrowLeft className="w-6 h-6 text-gray-600 group-hover:-translate-x-1 transition-transform" />
                    </div>
                    <div>
                      <h4 className="font-bold text-foreground">Academy</h4>
                      <p className="text-sm text-muted-foreground">All lessons</p>
                    </div>
                  </div>
                  <h5 className="font-semibold text-foreground mb-2">Back to Academy</h5>
                  <p className="text-sm text-muted-foreground">
                    View all lessons and track your progress
                  </p>
                </Link>
              </div>
            </motion.div>
          </div>
        </div>
      </section>
    </div>
  )
}
