'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { useAnalytics } from '@/hooks/useAnalytics'
import { CheckCircle, Mail, Phone, MapPin, Clock } from 'lucide-react'


export default function ContactPage() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: '',
    gdprConsent: false
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [validationErrors, setValidationErrors] = useState<{[key: string]: string}>({})
  const [submitError, setSubmitError] = useState<string>('')

  // Analytics tracking
  const analytics = useAnalytics()

  // Track form start when user first interacts
  useEffect(() => {
    let formStartTracked = false

    const trackFormStart = () => {
      if (!formStartTracked) {
        analytics.trackFormStart('contact_form')
        formStartTracked = true
      }
    }

    // Add event listeners to form fields
    const formFields = document.querySelectorAll('#contact-form input, #contact-form select, #contact-form textarea')
    formFields.forEach(field => {
      field.addEventListener('focus', trackFormStart, { once: true })
    })

    return () => {
      formFields.forEach(field => {
        field.removeEventListener('focus', trackFormStart)
      })
    }
  }, [])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    setSubmitError('')
    setValidationErrors({})

    try {
      const response = await fetch('/api/forms/submit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          formType: 'contact',
          gdprConsent: formData.gdprConsent
        })
      })

      const result = await response.json()

      if (result.success) {
        setIsSubmitted(true)
        setValidationErrors({})
        setSubmitError('')

        // Track successful contact submission
        analytics.trackContact(formData.subject, formData.name)
      } else {
        // Handle validation errors
        console.error('Form submission failed:', result.error)

        // Show detailed validation errors if available
        if (result.details && result.details.length > 0) {
          const errors: {[key: string]: string} = {}
          result.details.forEach((detail: any) => {
            errors[detail.field] = detail.message
          })
          setValidationErrors(errors)
          setSubmitError('Please fix the errors below and try again.')
        } else {
          setValidationErrors({})
          setSubmitError(result.error || 'Something went wrong. Please try again.')
        }
      }
    } catch (error) {
      console.error('Network error:', error)
      setSubmitError('Network error. Please check your connection and try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target
    const checked = (e.target as HTMLInputElement).checked

    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    })

    // Clear validation error for this field
    if (validationErrors[name]) {
      setValidationErrors({
        ...validationErrors,
        [name]: ''
      })
    }
  }

  if (isSubmitted) {
    return (
      <div className="min-h-screen flex items-center justify-center header-spacing py-20">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.6 }}
          className="max-w-md mx-auto text-center px-4"
        >
          <div className="bg-background border border-primary-200/20 dark:border-primary-800/20 rounded-3xl p-12 shadow-lg">
            <div className="w-16 h-16 bg-primary-100 dark:bg-primary-900/30 rounded-full flex items-center justify-center mx-auto mb-6">
              <CheckCircle className="w-8 h-8 text-primary-700 dark:text-primary-400" />
            </div>
            <h1 className="text-2xl font-bold text-foreground mb-4">
              Message Sent!
            </h1>
            <p className="text-muted-foreground mb-6">
              Thanks for contacting us. We'll get back to you within 24 hours.
            </p>
            <p className="text-sm text-muted-foreground">
              Check your email for a confirmation.
            </p>
          </div>
        </motion.div>
      </div>
    )
  }

  return (
    <div className="relative w-full overflow-hidden">
      {/* Header */}
      <section className="header-spacing py-20 relative">
        <div className="container relative z-10 mx-auto px-4 md:px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: 'easeOut' }}
            className="mx-auto max-w-4xl text-center"
          >
            <div className="bg-background border border-primary-200/20 dark:border-primary-800/20 rounded-3xl p-12 shadow-lg backdrop-blur-sm">
              <h1 className="text-4xl font-bold text-foreground sm:text-5xl md:text-6xl mb-6">
                Ready to Convert <span className="text-green-700 dark:text-green-400">89% of Your Prospects?</span>
              </h1>
              <p className="text-xl text-muted-foreground leading-relaxed mb-4">
                <strong className="text-foreground">Book your free sales consultation today.</strong> Our UK-based team will show you exactly how to turn more prospects into paying customers.
              </p>
              <p className="text-lg text-muted-foreground leading-relaxed">
                Whether you want to calculate your revenue potential or see how GenLogic can increase your conversion rate, we're here to help you grow.
              </p>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Contact Information & Form */}
      <section className="py-20">
        <div className="container mx-auto px-4 md:px-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 max-w-6xl mx-auto">

            {/* Contact Information */}
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, ease: 'easeOut' }}
              viewport={{ once: true }}
            >
              <div className="bg-background border border-primary-200/20 dark:border-primary-800/20 rounded-3xl p-8 shadow-lg h-full">
                <h2 className="text-2xl font-bold text-foreground mb-6">
                  We Understand Your <span className="text-primary-700 dark:text-primary-400">Frustration</span>
                </h2>
                <p className="text-muted-foreground mb-8 leading-relaxed">
                  <strong className="text-foreground">You're working too hard for too little time with your family.</strong> Let's change that. Our UK team is here to help you reclaim your evenings.
                </p>

                <div className="space-y-6">
                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-12 h-12 bg-primary-100 dark:bg-primary-900/30 rounded-2xl flex items-center justify-center">
                      <Phone className="w-6 h-6 text-primary-700 dark:text-primary-400" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg font-semibold text-foreground mb-2">Speak to a Real Person</h3>
                      <p className="text-muted-foreground">+44 7401 137621</p>
                      <p className="text-sm text-muted-foreground mt-1">Monday - Friday, 9am - 6pm GMT</p>
                      <p className="text-sm text-foreground font-medium mt-1">No phone trees, no waiting - just help</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-12 h-12 bg-primary-100 dark:bg-primary-900/30 rounded-2xl flex items-center justify-center">
                      <Mail className="w-6 h-6 text-primary-700 dark:text-primary-400" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg font-semibold text-foreground mb-2">Quick Questions</h3>
                      <p className="text-muted-foreground"><EMAIL></p>
                      <p className="text-sm text-muted-foreground mt-1">We typically respond within 24 hours</p>
                      <p className="text-sm text-foreground font-medium mt-1">Real answers from people who understand your business</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-12 h-12 bg-primary-100 dark:bg-primary-900/30 rounded-2xl flex items-center justify-center">
                      <MapPin className="w-6 h-6 text-primary-700 dark:text-primary-400" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg font-semibold text-foreground mb-2">Address</h3>
                      <p className="text-muted-foreground">
                        GenLogic Limited<br />
                        London, United Kingdom
                      </p>
                      <p className="text-sm text-muted-foreground mt-1">Registered in England & Wales</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-12 h-12 bg-primary-100 dark:bg-primary-900/30 rounded-2xl flex items-center justify-center">
                      <Clock className="w-6 h-6 text-primary-700 dark:text-primary-400" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg font-semibold text-foreground mb-2">Business Hours</h3>
                      <p className="text-muted-foreground">Monday - Friday, 9am - 6pm GMT</p>
                      <p className="text-sm text-muted-foreground mt-1">UK-based support team</p>
                    </div>
                  </div>
                </div>

                <div className="mt-8 p-6 bg-primary-50 dark:bg-primary-950/30 rounded-2xl border border-primary-200/30 dark:border-primary-800/30">
                  <h3 className="text-lg font-semibold text-foreground mb-4">
                    Want to see how you'll get your evenings back?
                  </h3>
                  <p className="text-muted-foreground mb-4">
                    <strong className="text-foreground">Book a 15-minute demo and see exactly how GenLogic will save you 20+ hours weekly.</strong> We'll show you real examples from businesses just like yours.
                  </p>
                  <a
                    href="/demo"
                    className="inline-flex items-center justify-center px-6 py-3 bg-primary-700 hover:bg-primary-800 dark:bg-primary-600 dark:hover:bg-primary-700 text-white rounded-2xl transition-all duration-300 font-medium"
                  >
                    Book Free Demo
                  </a>
                </div>

                {/* Social Media Links */}
                <div className="mt-8">
                  <h3 className="text-lg font-semibold text-foreground mb-4">
                    Follow Our Journey
                  </h3>
                  <p className="text-muted-foreground mb-4">
                    See how we're helping UK businesses grow and get tips for your own business.
                  </p>
                  <div className="flex space-x-4">
                    <a
                      href="https://www.linkedin.com/company/genlogicio"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="group bg-primary-50 dark:bg-primary-900/20 hover:bg-primary-100 dark:hover:bg-primary-900/40 p-3 rounded-xl transition-all duration-300 border border-primary-200 dark:border-primary-800 hover:border-primary-300 dark:hover:border-primary-700"
                      aria-label="Follow GenLogic on LinkedIn"
                    >
                      <svg className="w-6 h-6 text-primary-600 dark:text-primary-400 group-hover:text-primary-700 dark:group-hover:text-primary-300 transition-colors duration-300" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                      </svg>
                    </a>
                    <a
                      href="https://www.facebook.com/genlogic"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="group bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/40 p-3 rounded-xl transition-all duration-300 border border-blue-200 dark:border-blue-800 hover:border-blue-300 dark:hover:border-blue-700"
                      aria-label="Follow GenLogic on Facebook"
                    >
                      <svg className="w-6 h-6 text-blue-600 dark:text-blue-400 group-hover:text-blue-700 dark:group-hover:text-blue-300 transition-colors duration-300" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                      </svg>
                    </a>
                    <a
                      href="https://www.instagram.com/igenlogic"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="group bg-pink-50 dark:bg-pink-900/20 hover:bg-pink-100 dark:hover:bg-pink-900/40 p-3 rounded-xl transition-all duration-300 border border-pink-200 dark:border-pink-800 hover:border-pink-300 dark:hover:border-pink-700"
                      aria-label="Follow GenLogic on Instagram"
                    >
                      <svg className="w-6 h-6 text-pink-600 dark:text-pink-400 group-hover:text-pink-700 dark:group-hover:text-pink-300 transition-colors duration-300" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                      </svg>
                    </a>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Contact Form */}
            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, ease: 'easeOut' }}
              viewport={{ once: true }}
            >
              <div className="bg-background border border-primary-200/20 dark:border-primary-800/20 rounded-3xl p-8 shadow-lg h-full">
                <h2 className="text-2xl font-bold text-foreground mb-6">
                  Tell Us About Your <span className="text-primary-700 dark:text-primary-400">Challenges</span>
                </h2>
                <p className="text-muted-foreground mb-8 leading-relaxed">
                  <strong className="text-foreground">Every business is different, but the pain is the same.</strong> Tell us what's keeping you working late, and we'll show you how to fix it.
                </p>

                {/* Error Message */}
                {submitError && (
                  <div className="mb-6 p-4 bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800/30 rounded-2xl">
                    <p className="text-red-800 dark:text-red-300 text-sm font-medium">
                      {submitError}
                    </p>
                  </div>
                )}

                <form id="contact-form" onSubmit={handleSubmit} className="space-y-6">
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-foreground mb-2">
                      Full Name *
                    </label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      required
                      value={formData.name}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-primary-200/30 dark:border-primary-800/30 rounded-2xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-background text-foreground transition-all duration-300"
                      placeholder="Your full name"
                    />
                  </div>

                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-foreground mb-2">
                      Email Address *
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      required
                      value={formData.email}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-primary-200/30 dark:border-primary-800/30 rounded-2xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-background text-foreground transition-all duration-300"
                      placeholder="<EMAIL>"
                    />
                  </div>

                  <div>
                    <label htmlFor="phone" className="block text-sm font-medium text-foreground mb-2">
                      Phone Number
                    </label>
                    <input
                      type="tel"
                      id="phone"
                      name="phone"
                      value={formData.phone}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-primary-200/30 dark:border-primary-800/30 rounded-2xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-background text-foreground transition-all duration-300"
                      placeholder="07xxx xxx xxx"
                    />
                  </div>

                  <div>
                    <label htmlFor="subject" className="block text-sm font-medium text-foreground mb-2">
                      Subject *
                    </label>
                    <select
                      id="subject"
                      name="subject"
                      required
                      value={formData.subject}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-primary-200/30 dark:border-primary-800/30 rounded-2xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-background text-foreground transition-all duration-300"
                    >
                      <option value="">Select a subject</option>
                      <option value="sales_consultation">Free Sales Consultation</option>
                      <option value="conversion_audit">Conversion Rate Audit</option>
                      <option value="revenue_calculation">Revenue Potential Calculation</option>
                      <option value="automation">Customer Conversion System Demo</option>
                      <option value="website">High-Converting Website Quote</option>
                      <option value="general">General Inquiry</option>
                      <option value="support">Technical Support</option>
                      <option value="other">Other</option>
                    </select>
                  </div>

                  <div>
                    <label htmlFor="message" className="block text-sm font-medium text-foreground mb-2">
                      Message *
                    </label>
                    <textarea
                      id="message"
                      name="message"
                      rows={6}
                      required
                      value={formData.message}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-primary-200/30 dark:border-primary-800/30 rounded-2xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-background text-foreground transition-all duration-300 resize-none"
                      placeholder="How can we help you?"
                    />
                  </div>

                  <div className="flex items-start">
                    <input
                      type="checkbox"
                      id="gdprConsent"
                      name="gdprConsent"
                      required
                      checked={formData.gdprConsent}
                      onChange={handleChange}
                      className="mt-1 h-4 w-4 text-primary-600 focus:ring-primary-500 border-primary-300 rounded"
                    />
                    <label htmlFor="gdprConsent" className="ml-3 text-sm text-muted-foreground">
                      I consent to GenLogic processing my personal data to respond to my inquiry.
                      <strong className="text-foreground"> This consent is required under UK GDPR.</strong>
                      <br />
                      <span className="text-xs">
                        Your data will be processed according to our <a href="/privacy" className="text-primary-700 dark:text-primary-400 hover:underline">Privacy Policy</a>.
                      </span>
                    </label>
                  </div>

                  <button
                    type="submit"
                    disabled={isSubmitting || !formData.gdprConsent}
                    className="w-full px-6 py-3 bg-primary-700 hover:bg-primary-800 dark:bg-primary-600 dark:hover:bg-primary-700 text-white rounded-2xl transition-all duration-300 font-medium shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isSubmitting ? 'Sending...' : 'Send Message'}
                  </button>
                </form>
              </div>
            </motion.div>
          </div>
        </div>
      </section>
    </div>
  )
}
