'use client'

import { useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  ArrowLeft, 
  Clock, 
  TrendingUp, 
  CheckCircle,
  ArrowRight,
  Brain,
  Users,
  Target,
  Zap,
  AlertTriangle,
  Shield,
  BarChart3,
  Settings,
  Lightbulb,
  Timer,
  Eye,
  Workflow,
  Crown,
  Rocket
} from 'lucide-react'
import Link from 'next/link'

const transformationStages = [
  {
    stage: "Chaos Stage",
    title: "The Overwhelmed Owner",
    description: "Working 60+ hours, handling everything personally, constant firefighting",
    characteristics: [
      "You ARE the business - everything stops when you're not there",
      "Constantly switching between tasks and putting out fires",
      "Revenue plateaus because you're the bottleneck",
      "Stress levels through the roof, work-life balance non-existent"
    ],
    timeframe: "Years 1-3",
    color: "red"
  },
  {
    stage: "Systems Stage", 
    title: "The Strategic Builder",
    description: "Creating processes, delegating tasks, building team capabilities",
    characteristics: [
      "Documenting processes and creating standard procedures",
      "Hiring team members and training them properly",
      "Implementing tools and systems for efficiency",
      "Starting to work ON the business, not just IN it"
    ],
    timeframe: "Years 3-5",
    color: "yellow"
  },
  {
    stage: "Control Stage",
    title: "The Visionary Leader", 
    description: "Business runs without you, focusing on growth and strategy",
    characteristics: [
      "Business operates smoothly in your absence",
      "Team makes decisions independently using your systems",
      "You focus on strategy, growth, and new opportunities",
      "Multiple revenue streams and scalable processes"
    ],
    timeframe: "Year 5+",
    color: "green"
  }
]

export default function ChaosToControlClient() {
  // Mark lesson as completed when user visits
  useEffect(() => {
    const markAsCompleted = () => {
      const saved = localStorage.getItem('genlogic-academy-progress')
      const completed = saved ? JSON.parse(saved) : []
      if (!completed.includes('chaos-to-control')) {
        completed.push('chaos-to-control')
        localStorage.setItem('genlogic-academy-progress', JSON.stringify(completed))

        // Dispatch custom event to notify other components
        window.dispatchEvent(new CustomEvent('academyProgressUpdate'))
      }
    }

    // Mark as completed after user has been on page for 30 seconds
    const timer = setTimeout(markAsCompleted, 30000)
    return () => clearTimeout(timer)
  }, [])

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-primary-50/30 to-background dark:from-background dark:via-primary-950/30 dark:to-background">
      {/* Navigation */}
      <div className="pt-40 pb-8">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-5xl mx-auto">
            <Link 
              href="/learn" 
              className="inline-flex items-center gap-2 bg-background/80 backdrop-blur-sm border border-border/50 rounded-2xl px-4 py-2 text-primary-600 hover:text-primary-700 font-medium transition-all duration-300 hover:shadow-md"
            >
              <ArrowLeft className="w-4 h-4" />
              Back to Academy
            </Link>
          </div>
        </div>
      </div>

      {/* Hero Section */}
      <section className="pb-16">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-5xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              {/* Lesson Meta */}
              <div className="flex items-center justify-center gap-6 mb-8">
                <div className="flex items-center gap-2 bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-300 px-4 py-2 rounded-2xl">
                  <Crown className="w-4 h-4" />
                  <span className="font-medium text-sm">Business Evolution</span>
                </div>
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Clock className="w-4 h-4" />
                  <span className="text-sm">18 min read</span>
                </div>
                <div className="bg-muted px-3 py-1 rounded-full text-xs font-medium text-muted-foreground">
                  Business Transformation
                </div>
              </div>

              {/* Main Title */}
              <h1 className="text-4xl md:text-6xl font-bold text-foreground mb-6 leading-tight">
                From Chaos to Control: 
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-orange-500 to-red-500"> The Business Owner's Evolution</span>
              </h1>

              <p className="text-xl md:text-2xl text-muted-foreground mb-8 leading-relaxed max-w-4xl mx-auto">
                There's a predictable evolution that separates thriving business owners from those who remain trapped in their own success. 
                <strong className="text-foreground"> Most never make it past stage one.</strong>
                Here's how the successful ones break free.
              </p>

              {/* Stats Row */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
                <div className="bg-gradient-to-br from-red-50 to-red-100 dark:from-red-950/30 dark:to-red-900/30 rounded-3xl p-6 border border-red-200/50 dark:border-red-800/50">
                  <div className="text-3xl font-bold text-red-600 dark:text-red-400 mb-2">78%</div>
                  <div className="text-sm text-red-700 dark:text-red-300 font-medium">Stuck in chaos stage</div>
                </div>
                <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 dark:from-yellow-950/30 dark:to-yellow-900/30 rounded-3xl p-6 border border-yellow-200/50 dark:border-yellow-800/50">
                  <div className="text-3xl font-bold text-yellow-600 dark:text-yellow-400 mb-2">18%</div>
                  <div className="text-sm text-yellow-700 dark:text-yellow-300 font-medium">Building systems</div>
                </div>
                <div className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950/30 dark:to-green-900/30 rounded-3xl p-6 border border-green-200/50 dark:border-green-800/50">
                  <div className="text-3xl font-bold text-green-600 dark:text-green-400 mb-2">4%</div>
                  <div className="text-sm text-green-700 dark:text-green-300 font-medium">Achieve true control</div>
                </div>
              </div>
            </motion.div>

            {/* What You'll Learn Card */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-950/50 dark:to-red-950/50 rounded-3xl p-8 border border-orange-200/50 dark:border-orange-800/50 shadow-xl"
            >
              <div className="flex items-start gap-6">
                <div className="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-500 rounded-2xl flex items-center justify-center flex-shrink-0">
                  <Brain className="w-8 h-8 text-white" />
                </div>
                <div className="flex-1">
                  <h3 className="text-2xl font-bold text-foreground mb-4">What You'll Discover in This Lesson</h3>
                  <div className="grid md:grid-cols-2 gap-4">
                    <div className="space-y-3">
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-foreground">The 3 predictable stages of business evolution</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-foreground">Why 78% of owners never escape the chaos stage</span>
                      </div>
                    </div>
                    <div className="space-y-3">
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-foreground">Real transformation stories from UK business owners</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-foreground">The exact steps to accelerate your evolution</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="pb-20">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-5xl mx-auto space-y-16">

            {/* The Evolution Stages */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="bg-background rounded-3xl border border-border shadow-xl p-8"
            >
              <div className="text-center mb-12">
                <div className="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <Rocket className="w-8 h-8 text-white" />
                </div>
                <h2 className="text-3xl font-bold text-foreground mb-4">
                  The 3 Stages of Business Evolution
                </h2>
                <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
                  Every successful business owner goes through these predictable stages.
                  The key is recognizing where you are and accelerating your progression.
                </p>
              </div>

              <div className="space-y-8">
                {transformationStages.map((stage, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -30 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.6, delay: 0.4 + index * 0.1 }}
                    className={`bg-gradient-to-br ${
                      stage.color === 'red'
                        ? 'from-red-50 to-red-100 dark:from-red-950/30 dark:to-red-900/30 border-red-200/50 dark:border-red-800/50'
                        : stage.color === 'yellow'
                        ? 'from-yellow-50 to-yellow-100 dark:from-yellow-950/30 dark:to-yellow-900/30 border-yellow-200/50 dark:border-yellow-800/50'
                        : 'from-green-50 to-green-100 dark:from-green-950/30 dark:to-green-900/30 border-green-200/50 dark:border-green-800/50'
                    } rounded-2xl p-6 border`}
                  >
                    <div className="flex items-start gap-6">
                      <div className={`w-16 h-16 ${
                        stage.color === 'red'
                          ? 'bg-red-100 dark:bg-red-900/50'
                          : stage.color === 'yellow'
                          ? 'bg-yellow-100 dark:bg-yellow-900/50'
                          : 'bg-green-100 dark:bg-green-900/50'
                      } rounded-xl flex items-center justify-center flex-shrink-0`}>
                        <span className={`text-2xl font-bold ${
                          stage.color === 'red'
                            ? 'text-red-600'
                            : stage.color === 'yellow'
                            ? 'text-yellow-600'
                            : 'text-green-600'
                        }`}>
                          {index + 1}
                        </span>
                      </div>

                      <div className="flex-1">
                        <div className="flex items-center gap-4 mb-4">
                          <h3 className="text-2xl font-bold text-foreground">{stage.title}</h3>
                          <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                            stage.color === 'red'
                              ? 'bg-red-200 dark:bg-red-800 text-red-700 dark:text-red-300'
                              : stage.color === 'yellow'
                              ? 'bg-yellow-200 dark:bg-yellow-800 text-yellow-700 dark:text-yellow-300'
                              : 'bg-green-200 dark:bg-green-800 text-green-700 dark:text-green-300'
                          }`}>
                            {stage.timeframe}
                          </div>
                        </div>

                        <p className="text-lg text-muted-foreground mb-6">{stage.description}</p>

                        <div className="grid md:grid-cols-2 gap-4">
                          {stage.characteristics.map((characteristic, charIndex) => (
                            <div key={charIndex} className="flex items-start gap-3">
                              <div className={`w-2 h-2 rounded-full mt-2 flex-shrink-0 ${
                                stage.color === 'red'
                                  ? 'bg-red-500'
                                  : stage.color === 'yellow'
                                  ? 'bg-yellow-500'
                                  : 'bg-green-500'
                              }`}></div>
                              <span className="text-foreground text-sm">{characteristic}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            {/* The Breakthrough Moment */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              className="bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-950/30 dark:to-purple-950/30 rounded-3xl p-8 border border-blue-200/50 dark:border-blue-800/50"
            >
              <div className="text-center mb-8">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <Lightbulb className="w-8 h-8 text-white" />
                </div>
                <h2 className="text-3xl font-bold text-foreground mb-4">
                  The Breakthrough Moment
                </h2>
                <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
                  There's a specific moment when successful business owners make the shift.
                  Here's what triggers the transformation from chaos to control.
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-blue-200 dark:border-blue-700 mb-8">
                <h3 className="text-xl font-bold text-foreground mb-4">The Realization</h3>
                <p className="text-lg text-foreground italic mb-4">
                  "I built this business to give me freedom, but I've created a prison.
                  I'm working harder than I ever did as an employee, and I can't even take a holiday
                  without everything falling apart."
                </p>
                <p className="text-muted-foreground">
                  This moment of clarity usually happens around year 2-3, when the initial excitement
                  of entrepreneurship wears off and the reality of being trapped in your own success hits hard.
                </p>
              </div>

              <div className="grid md:grid-cols-2 gap-6">
                <div className="bg-red-50 dark:bg-red-950/30 rounded-2xl p-6 border border-red-200 dark:border-red-800">
                  <h4 className="font-bold text-foreground mb-3 flex items-center gap-2">
                    <AlertTriangle className="w-5 h-5 text-red-500" />
                    The Trap
                  </h4>
                  <ul className="space-y-2 text-muted-foreground text-sm">
                    <li>• You're the bottleneck for every decision</li>
                    <li>• Revenue growth stalls at your capacity limit</li>
                    <li>• Quality drops when you're not personally involved</li>
                    <li>• You can't scale because everything depends on you</li>
                  </ul>
                </div>

                <div className="bg-green-50 dark:bg-green-950/30 rounded-2xl p-6 border border-green-200 dark:border-green-800">
                  <h4 className="font-bold text-foreground mb-3 flex items-center gap-2">
                    <Shield className="w-5 h-5 text-green-500" />
                    The Solution
                  </h4>
                  <ul className="space-y-2 text-muted-foreground text-sm">
                    <li>• Build systems that work without you</li>
                    <li>• Create processes others can follow</li>
                    <li>• Develop team members who think like you</li>
                    <li>• Focus on working ON the business, not IN it</li>
                  </ul>
                </div>
              </div>
            </motion.div>

            {/* Real Case Study: The £2.3M Evolution */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              className="bg-background/80 backdrop-blur-sm rounded-3xl p-8 border border-border/50 shadow-xl"
            >
              <div className="flex items-start gap-6 mb-8">
                <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-blue-500 rounded-xl flex items-center justify-center flex-shrink-0">
                  <BarChart3 className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h2 className="text-3xl font-bold text-foreground mb-4">Case Study: How Elite Consulting Evolved from £180k to £2.3M</h2>
                  <p className="text-lg text-muted-foreground leading-relaxed">
                    Marcus owned a business consultancy in Manchester, stuck at £180k revenue working 70+ hours a week. Here's his complete evolution from chaos to control.
                  </p>
                </div>
              </div>

              <div className="bg-gradient-to-r from-red-50 to-red-100 dark:from-red-950/30 dark:to-red-900/30 rounded-2xl p-6 border border-red-200/50 dark:border-red-800/50 mb-8">
                <h3 className="text-xl font-bold text-red-700 dark:text-red-300 mb-4">The Chaos Stage (Years 1-2)</h3>
                <div className="grid md:grid-cols-3 gap-4 mb-4">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-red-600 dark:text-red-400 mb-2">70+</div>
                    <div className="text-sm text-red-700 dark:text-red-300">Hours worked per week</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-red-600 dark:text-red-400 mb-2">£180k</div>
                    <div className="text-sm text-red-700 dark:text-red-300">Revenue plateau</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-red-600 dark:text-red-400 mb-2">0</div>
                    <div className="text-sm text-red-700 dark:text-red-300">Days off per month</div>
                  </div>
                </div>
                <p className="text-red-600 dark:text-red-400 text-sm">
                  "I was the business. Every client wanted to speak to me personally. I couldn't take a holiday without losing clients.
                  I was making decent money but felt like a prisoner in my own company."
                </p>
              </div>

              <div className="space-y-8">
                {/* Stage 1: Systems Implementation */}
                <div className="bg-gradient-to-r from-yellow-50 to-yellow-100 dark:from-yellow-950/30 dark:to-yellow-900/30 rounded-2xl p-6 border border-yellow-200/50 dark:border-yellow-800/50">
                  <h4 className="text-lg font-bold text-yellow-700 dark:text-yellow-300 mb-4">Stage 1: Systems Implementation (Months 3-8)</h4>

                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
                      <h5 className="font-bold text-yellow-600 dark:text-yellow-400 mb-3">What Marcus Built</h5>
                      <ul className="space-y-2 text-sm text-yellow-600 dark:text-yellow-400">
                        <li>• Client onboarding automation (saved 8 hours/week)</li>
                        <li>• Standardized consulting frameworks</li>
                        <li>• Project management templates</li>
                        <li>• Automated reporting systems</li>
                        <li>• Client communication workflows</li>
                      </ul>
                    </div>

                    <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
                      <h5 className="font-bold text-yellow-600 dark:text-yellow-400 mb-3">Immediate Results</h5>
                      <ul className="space-y-2 text-sm text-yellow-600 dark:text-yellow-400">
                        <li>• Work hours: 70 → 55 hours/week</li>
                        <li>• Client satisfaction: +34%</li>
                        <li>• Project delivery time: -40%</li>
                        <li>• Revenue: £180k → £280k</li>
                        <li>• Stress levels: Significantly reduced</li>
                      </ul>
                    </div>
                  </div>

                  <div className="bg-yellow-100 dark:bg-yellow-900/30 rounded-xl p-4 mt-6">
                    <h6 className="font-bold text-yellow-700 dark:text-yellow-300 mb-2">Key Breakthrough Moment:</h6>
                    <p className="text-sm text-yellow-600 dark:text-yellow-400">
                      "When I realized I could deliver better results with systems than I could manually.
                      My frameworks were actually more comprehensive than my ad-hoc approach."
                    </p>
                  </div>
                </div>

                {/* Stage 2: Team Building */}
                <div className="bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-950/30 dark:to-blue-900/30 rounded-2xl p-6 border border-blue-200/50 dark:border-blue-800/50">
                  <h4 className="text-lg font-bold text-blue-700 dark:text-blue-300 mb-4">Stage 2: Team Building & Delegation (Months 9-18)</h4>

                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
                      <h5 className="font-bold text-blue-600 dark:text-blue-400 mb-3">Team Evolution</h5>
                      <div className="space-y-3 text-sm">
                        <div>
                          <strong className="text-blue-600">Month 9:</strong>
                          <span className="text-blue-600 dark:text-blue-400"> Hired first junior consultant</span>
                        </div>
                        <div>
                          <strong className="text-blue-600">Month 12:</strong>
                          <span className="text-blue-600 dark:text-blue-400"> Added project manager</span>
                        </div>
                        <div>
                          <strong className="text-blue-600">Month 15:</strong>
                          <span className="text-blue-600 dark:text-blue-400"> Hired senior consultant</span>
                        </div>
                        <div>
                          <strong className="text-blue-600">Month 18:</strong>
                          <span className="text-blue-600 dark:text-blue-400"> Added business development role</span>
                        </div>
                      </div>
                    </div>

                    <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
                      <h5 className="font-bold text-blue-600 dark:text-blue-400 mb-3">Delegation Framework</h5>
                      <ul className="space-y-2 text-sm text-blue-600 dark:text-blue-400">
                        <li>• Created detailed role descriptions</li>
                        <li>• Built training programs for each role</li>
                        <li>• Implemented quality control systems</li>
                        <li>• Established performance metrics</li>
                        <li>• Created escalation procedures</li>
                      </ul>
                    </div>
                  </div>

                  <div className="bg-blue-100 dark:bg-blue-900/30 rounded-xl p-4 mt-6">
                    <h6 className="font-bold text-blue-700 dark:text-blue-300 mb-2">18-Month Results:</h6>
                    <div className="grid md:grid-cols-3 gap-4 text-sm">
                      <div>
                        <div className="text-blue-600 dark:text-blue-400">✓ Work hours: 55 → 35 hours/week</div>
                        <div className="text-blue-600 dark:text-blue-400">✓ Revenue: £280k → £650k</div>
                      </div>
                      <div>
                        <div className="text-blue-600 dark:text-blue-400">✓ Team size: 1 → 5 people</div>
                        <div className="text-blue-600 dark:text-blue-400">✓ Client capacity: +180%</div>
                      </div>
                      <div>
                        <div className="text-blue-600 dark:text-blue-400">✓ Profit margin: +67%</div>
                        <div className="text-blue-600 dark:text-blue-400">✓ Time off: 4 weeks/year</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Stage 3: Scale & Leadership */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
              className="bg-background/80 backdrop-blur-sm rounded-3xl p-8 border border-border/50 shadow-xl"
            >
              <div className="bg-gradient-to-r from-green-50 to-green-100 dark:from-green-950/30 dark:to-green-900/30 rounded-2xl p-6 border border-green-200/50 dark:border-green-800/50">
                <h4 className="text-lg font-bold text-green-700 dark:text-green-300 mb-4">Stage 3: Scale & Leadership (Months 19-36)</h4>

                <div className="grid md:grid-cols-2 gap-6">
                  <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
                    <h5 className="font-bold text-green-600 dark:text-green-400 mb-3">Leadership Evolution</h5>
                    <ul className="space-y-2 text-sm text-green-600 dark:text-green-400">
                      <li>• Developed department heads</li>
                      <li>• Created autonomous teams</li>
                      <li>• Built company culture systems</li>
                      <li>• Implemented strategic planning</li>
                      <li>• Established growth frameworks</li>
                    </ul>
                  </div>

                  <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
                    <h5 className="font-bold text-green-600 dark:text-green-400 mb-3">Business Transformation</h5>
                    <ul className="space-y-2 text-sm text-green-600 dark:text-green-400">
                      <li>• Multiple service lines launched</li>
                      <li>• Recurring revenue model: 60%</li>
                      <li>• Geographic expansion: 3 cities</li>
                      <li>• Strategic partnerships: 12</li>
                      <li>• Industry recognition & awards</li>
                    </ul>
                  </div>
                </div>

                <div className="bg-green-100 dark:bg-green-900/30 rounded-xl p-4 mt-6">
                  <h6 className="font-bold text-green-700 dark:text-green-300 mb-2">Final Transformation Results:</h6>
                  <div className="grid md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <div className="text-green-600 dark:text-green-400">✓ Revenue: £2.3M annually</div>
                      <div className="text-green-600 dark:text-green-400">✓ Work hours: 25/week</div>
                    </div>
                    <div>
                      <div className="text-green-600 dark:text-green-400">✓ Team: 18 people</div>
                      <div className="text-green-600 dark:text-green-400">✓ Profit margin: 45%</div>
                    </div>
                    <div>
                      <div className="text-green-600 dark:text-green-400">✓ Vacation: 8 weeks/year</div>
                      <div className="text-green-600 dark:text-green-400">✓ Business value: £4.2M</div>
                    </div>
                    <div>
                      <div className="text-green-600 dark:text-green-400">✓ Stress level: Minimal</div>
                      <div className="text-green-600 dark:text-green-400">✓ Life satisfaction: High</div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-950/30 dark:to-blue-950/30 rounded-2xl p-6 border border-green-200/50 dark:border-green-800/50 mt-8">
                <h4 className="text-xl font-bold text-green-700 dark:text-green-300 mb-4">Marcus's Evolution: £180k → £2.3M in 3 Years</h4>
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h5 className="font-bold text-green-600 dark:text-green-400 mb-2">Business Metrics:</h5>
                    <ul className="space-y-1 text-sm text-green-600 dark:text-green-400">
                      <li>• Revenue growth: +1,178%</li>
                      <li>• Profit margin improvement: +67%</li>
                      <li>• Team productivity: +340%</li>
                      <li>• Client satisfaction: +89%</li>
                      <li>• Business valuation: £4.2M</li>
                    </ul>
                  </div>
                  <div>
                    <h5 className="font-bold text-blue-600 dark:text-blue-400 mb-2">Personal Transformation:</h5>
                    <ul className="space-y-1 text-sm text-blue-600 dark:text-blue-400">
                      <li>• Work hours: 70 → 25 per week</li>
                      <li>• Stress levels: High → Minimal</li>
                      <li>• Vacation time: 0 → 8 weeks/year</li>
                      <li>• Role: Doer → Strategic leader</li>
                      <li>• Freedom: Complete time control</li>
                    </ul>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Evolution Impact Calculator */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.7 }}
              className="bg-background/80 backdrop-blur-sm rounded-3xl p-8 border border-border/50 shadow-xl"
            >
              <div className="flex items-start gap-6 mb-8">
                <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl flex items-center justify-center flex-shrink-0">
                  <Target className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h2 className="text-3xl font-bold text-foreground mb-4">Business Evolution Calculator</h2>
                  <p className="text-lg text-muted-foreground leading-relaxed">
                    Calculate the potential impact of evolving from chaos to control in your business.
                  </p>
                </div>
              </div>

              <div className="bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-950/30 dark:to-pink-950/30 rounded-2xl p-6 border border-purple-200/50 dark:border-purple-800/50">
                <h3 className="text-xl font-bold text-purple-700 dark:text-purple-300 mb-6">Interactive Evolution ROI Calculator</h3>

                <div className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
                      <h4 className="font-bold text-purple-600 dark:text-purple-400 mb-3">Current Chaos State</h4>
                      <div className="space-y-3 text-sm">
                        <div className="flex items-center gap-2">
                          <label className="min-w-0 flex-1">Current annual revenue (£):</label>
                          <input
                            type="number"
                            className="w-24 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-foreground"
                            placeholder="180000"
                          />
                        </div>
                        <div className="flex items-center gap-2">
                          <label className="min-w-0 flex-1">Hours worked per week:</label>
                          <input
                            type="number"
                            className="w-24 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-foreground"
                            placeholder="70"
                          />
                        </div>
                        <div className="flex items-center gap-2">
                          <label className="min-w-0 flex-1">Current team size:</label>
                          <input
                            type="number"
                            className="w-24 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-foreground"
                            placeholder="1"
                          />
                        </div>
                        <div className="flex items-center gap-2">
                          <label className="min-w-0 flex-1">Vacation weeks per year:</label>
                          <input
                            type="number"
                            className="w-24 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-foreground"
                            placeholder="0"
                          />
                        </div>
                      </div>
                    </div>

                    <div className="bg-white dark:bg-gray-800 rounded-xl p-4">
                      <h4 className="font-bold text-purple-600 dark:text-purple-400 mb-3">Target Control State</h4>
                      <div className="space-y-3 text-sm">
                        <div className="flex items-center gap-2">
                          <label className="min-w-0 flex-1">Target annual revenue (£):</label>
                          <input
                            type="number"
                            className="w-24 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-foreground"
                            placeholder="1200000"
                          />
                        </div>
                        <div className="flex items-center gap-2">
                          <label className="min-w-0 flex-1">Target hours per week:</label>
                          <input
                            type="number"
                            className="w-24 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-foreground"
                            placeholder="30"
                          />
                        </div>
                        <div className="flex items-center gap-2">
                          <label className="min-w-0 flex-1">Target team size:</label>
                          <input
                            type="number"
                            className="w-24 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-foreground"
                            placeholder="12"
                          />
                        </div>
                        <div className="flex items-center gap-2">
                          <label className="min-w-0 flex-1">Target vacation weeks:</label>
                          <input
                            type="number"
                            className="w-24 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-foreground"
                            placeholder="6"
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-green-100 to-blue-100 dark:from-green-900/30 dark:to-blue-900/30 rounded-xl p-6 text-center border-2 border-green-300 dark:border-green-700">
                    <h4 className="text-2xl font-bold text-green-700 dark:text-green-300 mb-3">Evolution Impact Projection</h4>
                    <div className="grid md:grid-cols-3 gap-4 mb-4">
                      <div>
                        <div className="text-lg font-bold text-blue-600 dark:text-blue-400">Revenue Increase</div>
                        <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">£1,020,000</div>
                        <div className="text-xs text-blue-500">Annual additional revenue</div>
                      </div>
                      <div>
                        <div className="text-lg font-bold text-green-600 dark:text-green-400">Time Freedom</div>
                        <div className="text-2xl font-bold text-green-600 dark:text-green-400">40 hours</div>
                        <div className="text-xs text-green-500">Less work per week</div>
                      </div>
                      <div>
                        <div className="text-lg font-bold text-purple-600 dark:text-purple-400">Life Quality</div>
                        <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">6 weeks</div>
                        <div className="text-xs text-purple-500">Additional vacation</div>
                      </div>
                    </div>
                    <div className="border-t pt-4">
                      <div className="text-3xl font-bold text-green-600 dark:text-green-400 mb-2">Business Transformation</div>
                      <div className="text-5xl font-bold text-green-600 dark:text-green-400 mb-2">+567%</div>
                      <div className="text-lg font-bold text-green-600 dark:text-green-400">Revenue growth potential</div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Implementation Guide */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.8 }}
              className="bg-background/80 backdrop-blur-sm rounded-3xl p-8 border border-border/50 shadow-xl"
            >
              <div className="flex items-start gap-6 mb-8">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-green-500 rounded-xl flex items-center justify-center flex-shrink-0">
                  <Lightbulb className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h2 className="text-3xl font-bold text-foreground mb-4">Your 90-Day Evolution Roadmap</h2>
                  <p className="text-lg text-muted-foreground leading-relaxed">
                    Follow this proven roadmap to evolve from chaos to control and transform your business within 90 days.
                  </p>
                </div>
              </div>

              <div className="space-y-6">
                <div className="bg-gradient-to-r from-red-50 to-orange-50 dark:from-red-950/30 dark:to-orange-950/30 rounded-2xl p-6 border border-red-200/50 dark:border-red-800/50">
                  <h3 className="text-xl font-bold text-red-700 dark:text-red-300 mb-4">Days 1-30: Systems Foundation</h3>
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-bold text-red-600 dark:text-red-400 mb-2">Week 1-2: Process Documentation</h4>
                      <ul className="space-y-1 text-sm text-red-600 dark:text-red-400">
                        <li>• Document your top 5 recurring tasks</li>
                        <li>• Create step-by-step procedures</li>
                        <li>• Identify automation opportunities</li>
                        <li>• Map current workflow bottlenecks</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-bold text-red-600 dark:text-red-400 mb-2">Week 3-4: Quick Wins Implementation</h4>
                      <ul className="space-y-1 text-sm text-red-600 dark:text-red-400">
                        <li>• Automate client onboarding</li>
                        <li>• Set up project templates</li>
                        <li>• Create email sequences</li>
                        <li>• Implement time tracking</li>
                      </ul>
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-yellow-50 to-yellow-100 dark:from-yellow-950/30 dark:to-yellow-900/30 rounded-2xl p-6 border border-yellow-200/50 dark:border-yellow-800/50">
                  <h3 className="text-xl font-bold text-yellow-700 dark:text-yellow-300 mb-4">Days 31-60: Team Building & Delegation</h3>
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-bold text-yellow-600 dark:text-yellow-400 mb-2">Week 5-6: Role Definition</h4>
                      <ul className="space-y-1 text-sm text-yellow-600 dark:text-yellow-400">
                        <li>• Define key roles needed</li>
                        <li>• Create detailed job descriptions</li>
                        <li>• Establish performance metrics</li>
                        <li>• Design training programs</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-bold text-yellow-600 dark:text-yellow-400 mb-2">Week 7-8: Hiring & Training</h4>
                      <ul className="space-y-1 text-sm text-yellow-600 dark:text-yellow-400">
                        <li>• Recruit first team member</li>
                        <li>• Implement training systems</li>
                        <li>• Create quality control processes</li>
                        <li>• Establish communication protocols</li>
                      </ul>
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-green-50 to-green-100 dark:from-green-950/30 dark:to-green-900/30 rounded-2xl p-6 border border-green-200/50 dark:border-green-800/50">
                  <h3 className="text-xl font-bold text-green-700 dark:text-green-300 mb-4">Days 61-90: Leadership & Scale</h3>
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-bold text-green-600 dark:text-green-400 mb-2">Week 9-10: Leadership Development</h4>
                      <ul className="space-y-1 text-sm text-green-600 dark:text-green-400">
                        <li>• Develop team leaders</li>
                        <li>• Create decision-making frameworks</li>
                        <li>• Implement regular team meetings</li>
                        <li>• Establish company culture</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-bold text-green-600 dark:text-green-400 mb-2">Week 11-12: Growth Systems</h4>
                      <ul className="space-y-1 text-sm text-green-600 dark:text-green-400">
                        <li>• Create strategic planning process</li>
                        <li>• Implement growth metrics</li>
                        <li>• Design scalable operations</li>
                        <li>• Plan next phase expansion</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-950/30 dark:to-blue-950/30 rounded-2xl p-6 border border-green-200/50 dark:border-green-800/50 mt-8">
                <h3 className="text-xl font-bold text-green-700 dark:text-green-300 mb-4">90-Day Transformation Guarantee</h3>
                <div className="grid md:grid-cols-3 gap-4 text-center">
                  <div>
                    <div className="text-3xl font-bold text-green-600 dark:text-green-400 mb-2">30 Days</div>
                    <div className="text-sm text-green-600 dark:text-green-400">20% time savings</div>
                  </div>
                  <div>
                    <div className="text-3xl font-bold text-green-600 dark:text-green-400 mb-2">60 Days</div>
                    <div className="text-sm text-green-600 dark:text-green-400">50% capacity increase</div>
                  </div>
                  <div>
                    <div className="text-3xl font-bold text-green-600 dark:text-green-400 mb-2">90 Days</div>
                    <div className="text-sm text-green-600 dark:text-green-400">Complete transformation</div>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Call to Action */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
              className="bg-gradient-to-br from-primary-50 to-accent-50 dark:from-primary-950/50 dark:to-accent-950/50 rounded-3xl p-8 border border-primary-200/50 dark:border-primary-800/50 shadow-xl text-center"
            >
              <div className="w-16 h-16 bg-gradient-to-br from-primary-500 to-accent-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <Zap className="w-8 h-8 text-white" />
              </div>

              <h2 className="text-3xl font-bold text-foreground mb-6">
                Something's Changing
              </h2>

              <p className="text-lg text-muted-foreground mb-8 max-w-3xl mx-auto">
                Smart business owners are quietly discovering what successful companies already know about business evolution. The shift is happening whether you participate or not.
              </p>

              <Link
                href="/demo"
                className="inline-flex items-center gap-3 bg-gradient-to-r from-primary-600 to-accent-600 hover:from-primary-700 hover:to-accent-700 text-white px-8 py-4 rounded-2xl font-bold text-lg transition-all duration-300 hover:shadow-xl hover:shadow-primary-500/25 hover:scale-105"
              >
                See What They Know
                <ArrowRight className="w-5 h-5" />
              </Link>
            </motion.div>

            {/* Next Lesson Navigation */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.7 }}
              className="bg-background rounded-3xl border border-border shadow-lg p-8"
            >
              <h3 className="text-xl font-bold text-foreground mb-6 text-center">Continue Your Learning Journey</h3>

              <div className="grid md:grid-cols-2 gap-6">
                <Link
                  href="/learn/fundamentals/silent-revolution"
                  className="group bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950/30 dark:to-purple-900/30 rounded-2xl p-6 border border-purple-200/50 dark:border-purple-800/50 hover:shadow-lg transition-all duration-300"
                >
                  <div className="flex items-center gap-4 mb-4">
                    <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/50 rounded-xl flex items-center justify-center">
                      <ArrowRight className="w-6 h-6 text-purple-600 group-hover:translate-x-1 transition-transform" />
                    </div>
                    <div>
                      <h4 className="font-bold text-foreground">Next Lesson</h4>
                      <p className="text-sm text-muted-foreground">Lesson 5 of 15</p>
                    </div>
                  </div>
                  <h5 className="font-semibold text-foreground mb-2">The Silent Revolution</h5>
                  <p className="text-sm text-muted-foreground">
                    Why traditional customer service is quietly being abandoned by thriving businesses
                  </p>
                </Link>

                <Link
                  href="/learn"
                  className="group bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-950/30 dark:to-gray-900/30 rounded-2xl p-6 border border-gray-200/50 dark:border-gray-800/50 hover:shadow-lg transition-all duration-300"
                >
                  <div className="flex items-center gap-4 mb-4">
                    <div className="w-12 h-12 bg-gray-100 dark:bg-gray-900/50 rounded-xl flex items-center justify-center">
                      <ArrowLeft className="w-6 h-6 text-gray-600 group-hover:-translate-x-1 transition-transform" />
                    </div>
                    <div>
                      <h4 className="font-bold text-foreground">Academy</h4>
                      <p className="text-sm text-muted-foreground">All lessons</p>
                    </div>
                  </div>
                  <h5 className="font-semibold text-foreground mb-2">Back to Academy</h5>
                  <p className="text-sm text-muted-foreground">
                    View all lessons and track your progress
                  </p>
                </Link>
              </div>
            </motion.div>
          </div>
        </div>
      </section>
    </div>
  )
}
