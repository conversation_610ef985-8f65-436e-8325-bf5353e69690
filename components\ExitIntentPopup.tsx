'use client'

import { useState, useEffect, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { X, Clock, Star, CheckCircle, ArrowRight, Gift, Smartphone } from 'lucide-react'
import { CustomButton } from '@/components/ui/custom-button'
import { useExitIntent } from '@/hooks/useExitIntent'

interface ExitIntentPopupProps {
  onClose?: () => void
  onDemo?: () => void
  onPricing?: () => void
}

export default function ExitIntentPopup({ onClose, onDemo, onPricing }: ExitIntentPopupProps) {
  const [isVisible, setIsVisible] = useState(false)
  const [hasShown, setHasShown] = useState(false)
  const [isMobile, setIsMobile] = useState(false)

  // Check if popup has been shown this session and detect mobile
  useEffect(() => {
    const shown = sessionStorage.getItem('exit-intent-shown')
    if (shown) {
      setHasShown(true)
    }

    // Mobile detection
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768 || /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent))
    }

    checkMobile()
    window.addEventListener('resize', checkMobile)

    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // Handle exit intent trigger
  const handleExitIntent = useCallback(() => {
    if (!hasShown && !isVisible) {
      setIsVisible(true)
      setHasShown(true)
      sessionStorage.setItem('exit-intent-shown', 'true')

      // Analytics tracking
      if (typeof window !== 'undefined' && (window as any).gtag) {
        (window as any).gtag('event', 'exit_intent_popup_shown', {
          event_category: 'engagement',
          event_label: isMobile ? 'mobile_exit_intent' : 'desktop_exit_intent'
        })
      }
    }
  }, [hasShown, isVisible, isMobile])

  // Use exit intent hook with different settings for mobile/desktop
  useExitIntent({
    enabled: !hasShown,
    threshold: isMobile ? 50 : 0, // Higher threshold for mobile
    delay: isMobile ? 500 : 0, // Small delay for mobile
    onExitIntent: handleExitIntent
  })

  // Time-based trigger for mobile users (fallback)
  useEffect(() => {
    if (isMobile && !hasShown && !isVisible) {
      const timer = setTimeout(() => {
        // Only show if user has been on page for 30 seconds and scrolled
        const scrolled = window.scrollY > 500
        if (scrolled) {
          handleExitIntent()
        }
      }, 30000) // 30 seconds

      return () => clearTimeout(timer)
    }
  }, [isMobile, hasShown, isVisible, handleExitIntent])

  // ESC key handler
  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    if (e.key === 'Escape' && isVisible) {
      handleClose()
    }
  }, [isVisible])

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [handleKeyDown])

  const handleClose = () => {
    setIsVisible(false)
    onClose?.()
    
    // Analytics tracking
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('event', 'exit_intent_popup_closed', {
        event_category: 'engagement',
        event_label: 'dismissed'
      })
    }
  }

  const handleDemo = () => {
    setIsVisible(false)
    onDemo?.()
    
    // Analytics tracking
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('event', 'exit_intent_demo_clicked', {
        event_category: 'conversion',
        event_label: 'demo_booking'
      })
    }
    
    // Navigate to demo page
    window.location.href = '/demo'
  }

  const handlePricing = () => {
    setIsVisible(false)
    onPricing?.()
    
    // Analytics tracking
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('event', 'exit_intent_pricing_clicked', {
        event_category: 'engagement',
        event_label: 'pricing_view'
      })
    }
    
    // Navigate to pricing page
    window.location.href = '/pricing'
  }

  if (hasShown && !isVisible) {
    return null
  }

  return (
    <AnimatePresence>
      {isVisible && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50"
            onClick={handleClose}
          />

          {/* Popup Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.8, y: 50 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.8, y: 50 }}
            transition={{
              duration: 0.5,
              ease: [0.25, 0.46, 0.45, 0.94],
              type: "spring",
              stiffness: 300,
              damping: 30
            }}
            className="fixed inset-0 z-50 flex items-center justify-center p-4"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="relative bg-background backdrop-blur-xl border border-primary-200/20 dark:border-primary-800/20 rounded-3xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-hidden">
              {/* Sophisticated Background Pattern */}
              <div className="absolute inset-0 overflow-hidden rounded-3xl">
                <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-primary-100/10 to-primary-200/10 dark:from-primary-800/10 dark:to-primary-900/10 rounded-full blur-2xl"></div>
                <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-primary-100/10 to-primary-200/10 dark:from-primary-800/10 dark:to-primary-900/10 rounded-full blur-2xl"></div>
              </div>

              {/* Scrollable Content Container */}
              <div className="relative z-10 max-h-[90vh] overflow-y-auto scrollbar-thin scrollbar-thumb-primary-200 dark:scrollbar-thumb-primary-800 scrollbar-track-transparent hover:scrollbar-thumb-primary-300 dark:hover:scrollbar-thumb-primary-700">
              {/* Header */}
              <div className="relative p-8 pb-6 z-10">
                <motion.button
                  onClick={handleClose}
                  className="absolute top-6 right-6 p-3 text-muted-foreground hover:text-foreground rounded-xl transition-all duration-300 hover:bg-muted/50 backdrop-blur-sm border border-transparent hover:border-border"
                  aria-label="Close popup"
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <X className="w-5 h-5" />
                </motion.button>

                {/* Special Offer Badge */}
                <motion.div
                  className="inline-flex items-center px-4 py-2 rounded-full bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-400 text-sm font-semibold tracking-wide mb-6 border border-primary-200/50 dark:border-primary-800/50"
                  initial={{ scale: 0.8, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ delay: 0.2, duration: 0.4 }}
                >
                  <div className="flex items-center">
                    {isMobile ? <Smartphone className="w-4 h-4 mr-2" /> : <Gift className="w-4 h-4 mr-2" />}
                    {isMobile ? 'Mobile Exclusive Offer' : 'Limited Time Offer'}
                    <div className="ml-2 w-2 h-2 bg-primary-500 rounded-full animate-pulse"></div>
                  </div>
                </motion.div>

                {/* Headline */}
                <motion.h2
                  className="text-3xl md:text-4xl font-bold text-foreground mb-4 leading-tight"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3, duration: 0.5 }}
                >
                  Wait! Don't let another evening slip away to{' '}
                  <span className="text-primary-700 dark:text-primary-400">
                    admin work
                  </span>
                </motion.h2>

                <motion.p
                  className="text-lg text-muted-foreground mb-6 leading-relaxed"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4, duration: 0.5 }}
                >
                  We know you're busy, but imagine finishing work at 6pm every day instead of 9pm.
                  <strong className="text-foreground bg-primary-50 dark:bg-primary-900/20 px-2 py-1 rounded-lg ml-1">
                    That's 15 extra hours per week with your family.
                  </strong>
                </motion.p>
              </div>

              {/* Content */}
              <div className="px-8 pb-8 relative z-10">
                {/* Special Offer */}
                <motion.div
                  className="relative bg-primary-50 dark:bg-primary-950/30 rounded-3xl p-8 mb-8 border border-primary-200/30 dark:border-primary-800/30 shadow-lg"
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5, duration: 0.6 }}
                >
                  <div className="text-center">
                    <motion.div
                      className="text-4xl font-bold text-primary-700 dark:text-primary-400 mb-3"
                      initial={{ scale: 0.8 }}
                      animate={{ scale: 1 }}
                      transition={{ delay: 0.7, duration: 0.4, type: "spring" }}
                    >
                      Get 2 Months FREE
                    </motion.div>

                    <motion.p
                      className="text-lg font-medium text-foreground mb-4"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.8, duration: 0.4 }}
                    >
                      When you book your demo today. That's <span className="text-primary-600 dark:text-primary-400 font-bold">£98 in savings!</span>
                    </motion.p>

                    <motion.div
                      className="flex items-center justify-center text-sm"
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.9, duration: 0.4 }}
                    >
                      <div className="flex items-center bg-background border border-primary-200 dark:border-primary-800 px-4 py-2 rounded-full">
                        <Clock className="w-4 h-4 mr-2 text-primary-500" />
                        <span className="font-medium text-muted-foreground">Offer expires in 24 hours</span>
                      </div>
                    </motion.div>
                  </div>
                </motion.div>

                {/* Quick Benefits */}
                <motion.div
                  className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 1.0, duration: 0.6 }}
                >
                  {[
                    { text: "85% fewer no-shows guaranteed", delay: 1.1 },
                    { text: "30+ hours saved weekly", delay: 1.2 },
                    { text: "Setup in under 10 minutes", delay: 1.3 },
                    { text: "UK-based support team", delay: 1.4 }
                  ].map((benefit, index) => (
                    <motion.div
                      key={index}
                      className="flex items-center bg-background border border-primary-200/20 dark:border-primary-800/20 p-4 rounded-2xl hover:border-primary-300/30 dark:hover:border-primary-700/30 transition-all duration-300"
                      initial={{ opacity: 0, x: index % 2 === 0 ? -20 : 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: benefit.delay, duration: 0.4 }}
                      whileHover={{ scale: 1.02 }}
                    >
                      <div className="w-8 h-8 bg-primary-100 dark:bg-primary-900/30 rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                        <CheckCircle className="w-5 h-5 text-primary-600 dark:text-primary-400" />
                      </div>
                      <span className="text-foreground font-medium">{benefit.text}</span>
                    </motion.div>
                  ))}
                </motion.div>

                {/* Social Proof */}
                <motion.div
                  className="bg-muted/30 dark:bg-muted/20 rounded-3xl p-6 mb-8 border border-primary-200/20 dark:border-primary-800/20"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 1.5, duration: 0.6 }}
                >
                  <motion.div
                    className="flex items-center mb-4"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 1.6, duration: 0.4 }}
                  >
                    <div className="flex items-center">
                      {[...Array(5)].map((_, i) => (
                        <motion.div
                          key={i}
                          initial={{ opacity: 0, scale: 0 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{ delay: 1.7 + i * 0.1, duration: 0.3 }}
                        >
                          <Star className="w-5 h-5 text-yellow-400 fill-current" />
                        </motion.div>
                      ))}
                    </div>
                    <span className="ml-3 text-sm font-semibold text-foreground bg-primary-100 dark:bg-primary-900/30 px-3 py-1 rounded-full">
                      500+ UK businesses
                    </span>
                  </motion.div>

                  <motion.blockquote
                    className="text-base text-foreground italic font-medium leading-relaxed mb-3"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 1.8, duration: 0.6 }}
                  >
                    "I used to work until 9pm every night. Now I'm home by 6pm and my revenue increased by 30%.
                    <span className="text-primary-700 dark:text-primary-400 font-bold not-italic"> GenLogic gave me my life back.</span>"
                  </motion.blockquote>

                  <motion.cite
                    className="text-sm text-muted-foreground block font-medium"
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 1.9, duration: 0.4 }}
                  >
                    — Sarah, Hair Salon Owner, Manchester
                  </motion.cite>
                </motion.div>

                {/* Action Buttons */}
                <motion.div
                  className="flex flex-col sm:flex-row gap-4"
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 2.0, duration: 0.6 }}
                >
                  <motion.div
                    className="flex-1"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <CustomButton
                      variant="primary"
                      size="lg"
                      onClick={handleDemo}
                      icon={<ArrowRight className="w-5 h-5" />}
                      iconPosition="right"
                      className="w-full shadow-xl hover:shadow-2xl font-bold py-4 px-8 text-lg"
                    >
                      Claim 2 Months FREE
                    </CustomButton>
                  </motion.div>

                  <motion.div
                    className="flex-1"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <CustomButton
                      variant="outline"
                      size="lg"
                      onClick={handlePricing}
                      className="w-full border-2 border-primary-300 dark:border-primary-700 hover:bg-primary-50 dark:hover:bg-primary-900/20 hover:border-primary-400 dark:hover:border-primary-600 font-semibold py-4 px-8 text-lg transition-all duration-300"
                    >
                      View Pricing
                    </CustomButton>
                  </motion.div>
                </motion.div>

                {/* Dismissal Option */}
                <motion.div
                  className="text-center mt-6"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 2.2, duration: 0.4 }}
                >
                  <button
                    onClick={handleClose}
                    className="text-sm text-muted-foreground hover:text-foreground transition-colors underline hover:no-underline px-4 py-2 rounded-lg hover:bg-muted/30"
                  >
                    No thanks, I'll keep working evenings
                  </button>
                </motion.div>
              </div>
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  )
}
